import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PurchasePlanEntity } from './purchasePlan.entity'

/** <PERSON><PERSON><PERSON> sử của YCMH */
@Entity('purchase_plan_history')
export class PurchasePlanHistoryEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  purchasePlanId: string
  @ManyToOne(() => PurchasePlanEntity, (p) => p.histories)
  @JoinColumn({ name: 'purchasePlanId', referencedColumnName: 'id' })
  purchasePlan: Promise<PurchasePlanEntity>

  /** Trạng thái hiện tại */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  statusCurrent: string

  /** Trạng thái chuyển đổi */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  statusConvert: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  createdByName: string
}
