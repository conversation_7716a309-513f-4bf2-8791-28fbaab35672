import { <PERSON><PERSON><PERSON>, <PERSON>umn, Jo<PERSON><PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { OfferEntity } from './offer.entity'
import { SupplierEntity } from './supplier.entity'
import { OfferSupplierServiceEntity } from './offerSupplierService.entity'
import { OfferSupplierTechValueEntity } from './offerSupTechValue.entity'
import { OfferSupplierTradeValueEntity } from './offerSupplierTradeValue.entity'
import { OfferSupplierPriceValueEntity } from './offerSupplierPriceValue.entity'
import { OfferSupplierPriceColValueEntity } from './offerSupplierPriceColValue.entity'
import { OfferSupplierPriceEntity } from './offerSupplierPrice.entity'
import { OfferSupplierCustomPriceValueEntity } from './offerSupplierCustomPriceValues.entity'
import { OfferSupplierShipmentValueEntity } from './offerSupplierShipmentValue.entity'

/** Bảng chào giá nhanh */
@Entity('offer_supplier')
export class OfferSupplierEntity extends BaseEntity {
  /** offer */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerId: string
  @ManyToOne(() => OfferEntity, (p) => p.offerSupplier)
  @JoinColumn({ name: 'offerId', referencedColumnName: 'id' })
  offer: Promise<OfferEntity>

  /** Trạng thái hoàn tất thầu */
  @Column({
    nullable: true,
    default: false,
  })
  isSuccessBid: boolean

  /** offer */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.offerSupplier)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Điểm Kỹ thuật */
  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  scoreTech: number

  /** Điểm giá */
  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  scorePrice: number

  /** Điểm DKTM */
  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  scoreTrade: number

  /** Điểm HĐXT Kỹ thuật */
  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  scoreManualTech: number

  /** Điểm HĐXT giá */
  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  scoreManualPrice: number

  /** Điểm HĐXT DKTM */
  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  scoreManualTrade: number

  /** Mã */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  supplierCode: string

  /** Tên */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  supplierName: string

  @OneToMany(() => OfferSupplierServiceEntity, (p) => p.offerSupplier)
  offerSupplierService: Promise<OfferSupplierServiceEntity[]>

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => OfferSupplierTechValueEntity, (p) => p.offerSupplier)
  offerSupplierTechValue: Promise<OfferSupplierTechValueEntity[]>

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => OfferSupplierTradeValueEntity, (p) => p.offerSupplier)
  offerSupplierTradeValue: Promise<OfferSupplierTradeValueEntity[]>

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => OfferSupplierPriceValueEntity, (p) => p.offerSupplier)
  offerSupplierPriceValue: Promise<OfferSupplierPriceValueEntity[]>

  /** Danh sách giá trị dữ liệu các cột động mà Doanh nghiệp nhập */
  @OneToMany(() => OfferSupplierPriceColValueEntity, (p) => p.offerSupplier)
  offerSupplierPriceColValue: Promise<OfferSupplierPriceColValueEntity[]>

  @OneToMany(() => OfferSupplierCustomPriceValueEntity, (p) => p.offerSupplier)
  offerSupplierCustomPriceValue: Promise<OfferSupplierCustomPriceValueEntity[]>

  @OneToMany(() => OfferSupplierPriceEntity, (p) => p.offerSupplier)
  offerSupplierPrices: Promise<OfferSupplierPriceEntity[]>

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => OfferSupplierShipmentValueEntity, (p) => p.offerSupplier)
  offerSupplierShipmentTechValue: Promise<OfferSupplierShipmentValueEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId?: string
  /** Cha */
  @ManyToOne(() => OfferSupplierEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: Promise<OfferSupplierEntity>

  /** Con - 1 gói thầu sẽ có thể có nhiều gói thầu con */
  @OneToMany(() => OfferSupplierEntity, (p) => p.parent)
  childs: Promise<OfferSupplierEntity[]>

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  statusFile: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  statusTech: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  statusTrade: string

  /** Trạng thái hồ sơ giá */
  @Column({
    nullable: false,
    default: true,
  })
  isPriceValid: boolean

  /** Trạng thái hồ sơ giá */
  @Column({
    nullable: true,
    default: false,
  })
  isJoin: boolean

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
    default: 'KhongYeuCau',
  })
  statusResetPrice: string

  @Column({
    type: 'text',
    nullable: true,
  })
  noteMPOLeader: string

  @Column({
    type: 'text',
    nullable: true,
  })
  noteTrade: string

  /** Trạng thái hồ sơ thương mại */
  @Column({
    nullable: false,
    default: true,
  })
  isTradeValid: boolean

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  @Column({
    type: 'text',
    nullable: true,
  })
  notePrice: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  statusPrice: string

  /** File dính kèm */
  @Column({
    type: 'varchar',
    length: 250,

    nullable: true,
  })
  fileAttach: string

  /** File chi tiết kĩ thuật */
  @Column({
    type: 'varchar',
    length: 250,

    nullable: true,
  })
  fileTech: string

  /**File chi tiết giá */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  filePrice: string

  /** Link drive cho các file đính kèm */
  @Column({
    type: 'varchar',
    length: 400,
    nullable: true,
  })
  linkDrive: string

  /** Ghi chú*/
  @Column({
    type: 'text',
    nullable: true,
  })
  note: string
}
