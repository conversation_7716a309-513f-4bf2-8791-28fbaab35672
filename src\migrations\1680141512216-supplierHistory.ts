import { MigrationInterface, QueryRunner } from 'typeorm'

export class supplierHistory1680141512216 implements MigrationInterface {
  name = 'supplierHistory1680141512216'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`supplier_history\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`supplierId\` varchar(36) NOT NULL, \`status\` varchar(50) NOT NULL, \`description\` varchar(250) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_history\` ADD CONSTRAINT \`FK_0a04398f3acc707810153fdc5c6\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`supplier_history\` DROP FOREIGN KEY \`FK_0a04398f3acc707810153fdc5c6\``)
    await queryRunner.query(`DROP TABLE \`supplier_history\``)
  }
}
