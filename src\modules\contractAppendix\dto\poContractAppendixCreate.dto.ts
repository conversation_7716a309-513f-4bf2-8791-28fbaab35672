import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class ContractAppendixCreate {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  title: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  contractId: string

  @ApiPropertyOptional()
  @IsOptional()
  effectiveDate: Date

  @ApiPropertyOptional()
  @IsOptional()
  expiredDate: Date

  @ApiPropertyOptional()
  @IsOptional()
  fileAttach: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  type: string

  @ApiPropertyOptional()
  @IsOptional()
  description: string

  @ApiPropertyOptional()
  @IsOptional()
  objectId: string
}
