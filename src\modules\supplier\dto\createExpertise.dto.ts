import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsArray, IsString, IsNotEmpty, IsOptional, IsBoolean } from 'class-validator'

export class CreateExpertiseDto {
  @ApiPropertyOptional()
  @IsArray()
  supplierServiceIds: string[]

  @ApiPropertyOptional()
  employeeLawId: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  note: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  changeDate: Date

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isCheckLaw: boolean

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isCheckCapacity: boolean

  @ApiPropertyOptional()
  @IsArray()
  members: string[]
}
