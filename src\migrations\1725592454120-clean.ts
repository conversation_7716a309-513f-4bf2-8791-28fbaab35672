import { MigrationInterface, QueryRunner } from "typeorm";

export class clean1725592454120 implements MigrationInterface {
    name = 'clean1725592454120'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`po\` DROP FOREIGN KEY \`FK_683006cef22b658ae6def587003\``);
        await queryRunner.query(`ALTER TABLE \`bid\` DROP FOREIGN KEY \`FK_3ac0331f78950889ddeb7e39994\``);
        await queryRunner.query(`ALTER TABLE \`bid\` DROP FOREIGN KEY \`FK_93cd7713f0252a277b7062bdb96\``);
        await queryRunner.query(`ALTER TABLE \`item_tech\` DROP FOREIGN KEY \`FK_a7252181033db912eaf7aa5e70c\``);
        await queryRunner.query(`ALTER TABLE \`branch_member\` DROP FOREIGN KEY \`FK_ce789a3ab0a31e3dba2452ef278\``);
        await queryRunner.query(`ALTER TABLE \`faq\` DROP FOREIGN KEY \`FK_953d55203e245c23dc0882cf1d6\``);
        await queryRunner.query(`ALTER TABLE \`po\` CHANGE \`schemeId\` \`orderStatus\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`bid\` DROP COLUMN \`prChilId\``);
        await queryRunner.query(`ALTER TABLE \`bid\` DROP COLUMN \`prItemChildId\``);
        await queryRunner.query(`ALTER TABLE \`item_tech\` DROP COLUMN \`prItemChildId\``);
        await queryRunner.query(`ALTER TABLE \`employee\` DROP COLUMN \`kpi\``);
        await queryRunner.query(`ALTER TABLE \`po\` DROP COLUMN \`orderStatus\``);
        await queryRunner.query(`ALTER TABLE \`po\` ADD \`orderStatus\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`branch_member\` ADD CONSTRAINT \`FK_1aba2794e0422ebadd0721083a8\` FOREIGN KEY (\`branchId\`) REFERENCES \`branch\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`faq\` ADD CONSTRAINT \`FK_89ec0666829fad019cdf0dcac22\` FOREIGN KEY (\`categoryId\`) REFERENCES \`faq_category\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`faq\` DROP FOREIGN KEY \`FK_89ec0666829fad019cdf0dcac22\``);
        await queryRunner.query(`ALTER TABLE \`branch_member\` DROP FOREIGN KEY \`FK_1aba2794e0422ebadd0721083a8\``);
        await queryRunner.query(`ALTER TABLE \`po\` DROP COLUMN \`orderStatus\``);
        await queryRunner.query(`ALTER TABLE \`po\` ADD \`orderStatus\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`employee\` ADD \`kpi\` float NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`item_tech\` ADD \`prItemChildId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`bid\` ADD \`prItemChildId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`bid\` ADD \`prChilId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`po\` CHANGE \`orderStatus\` \`schemeId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`faq\` ADD CONSTRAINT \`FK_953d55203e245c23dc0882cf1d6\` FOREIGN KEY (\`categoryId\`) REFERENCES \`faq_category\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`branch_member\` ADD CONSTRAINT \`FK_ce789a3ab0a31e3dba2452ef278\` FOREIGN KEY (\`branchId\`) REFERENCES \`branch\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`item_tech\` ADD CONSTRAINT \`FK_a7252181033db912eaf7aa5e70c\` FOREIGN KEY (\`prItemChildId\`) REFERENCES \`pr_item_child\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`bid\` ADD CONSTRAINT \`FK_93cd7713f0252a277b7062bdb96\` FOREIGN KEY (\`prItemChildId\`) REFERENCES \`pr_item_child\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`bid\` ADD CONSTRAINT \`FK_3ac0331f78950889ddeb7e39994\` FOREIGN KEY (\`prChilId\`) REFERENCES \`pr_child\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`po\` ADD CONSTRAINT \`FK_683006cef22b658ae6def587003\` FOREIGN KEY (\`schemeId\`) REFERENCES \`scheme\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
