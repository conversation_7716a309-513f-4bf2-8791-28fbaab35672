import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, Join<PERSON>olumn } from 'typeorm'
import { InvoiceSuggestEntity } from './invoiceSuggest.entity'

/** File Đề nghị thanh toán */
@Entity('invoice_suggest_file')
export class InvoiceSuggestFileEntity extends BaseEntity {
  /** Đường dẫn file */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  fileUrl: string

  /** Tên file */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileName: string

  /** Đường dẫn  */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  url: string

  /** Số hóa đơn */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  invoiceNo: string

  /** Data liên quan */
  @Column({
    type: 'text',
    nullable: true,
  })
  data: string

  /** Đề nghị thanh toán */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  invoiceSuggestId: string
  /** Đề nghị thanh toán */
  @ManyToOne(() => InvoiceSuggestEntity, (p) => p.files)
  @JoinColumn({ name: 'invoiceSuggestId', referencedColumnName: 'id' })
  invoiceSuggest: Promise<InvoiceSuggestEntity>
}
