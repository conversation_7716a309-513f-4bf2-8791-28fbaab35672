import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { OfferSupplierEntity } from './offerSupplier.entity'
import { OfferPriceEntity } from './offerPrice.entity'
import { OfferServiceEntity } from './offerService.entity'

/** Bảng chào giá nhanh */
@Entity('offer_supplier_service')
export class OfferSupplierServiceEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerSupplierId: string
  @ManyToOne(() => OfferSupplierEntity, (p) => p.offerSupplierService)
  @JoinColumn({ name: 'offerSupplierId', referencedColumnName: 'id' })
  offerSupplier: Promise<OfferSupplierEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerServiceId: string
  @ManyToOne(() => OfferServiceEntity, (p) => p.offerSupplierService)
  @JoinColumn({ name: 'offerServiceId', referencedColumnName: 'id' })
  offerService: Promise<OfferServiceEntity>
}
