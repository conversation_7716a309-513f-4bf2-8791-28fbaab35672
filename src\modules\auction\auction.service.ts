import { Injectable, NotAcceptableException } from '@nestjs/common'
import { ACTION_SUCCESS, CREATE_SUCCESS, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION, UPDATE_SUCCESS, enumData } from '../../constants'
import { IsNull, Like, Not } from 'typeorm'
import { PaginationDto, UserDto } from '../../dto'
import { AuctionHistoryRepository, AuctionRepository, AuctionSupplierRepository } from '../../repositories'
import { AuctionAddSupplier, AuctionCancel, AuctionCreate, AuctionSubmit, AuctionUpdate } from './dto'
import { AuctionEntity, AuctionHistoryEntity, AuctionSupplierEntity } from '../../entities'
import { EmailService } from '../email/email.service'
import { apeAuthApiHelper } from '../../helpers'
@Injectable()
export class AuctionService {
  constructor(
    private readonly repo: AuctionRepository,
    private readonly emailService: EmailService,
    private readonly auctionSupplierRepo: AuctionSupplierRepository,
    private readonly auctionHistoryRepo: AuctionHistoryRepository,
  ) {}

  //#region ADMIN

  /** Chi tiết đấu giá */
  public async findDetail(data: { id: string }, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const res: any = await this.repo.findOne({
      where: { id: data.id },
      relations: { bid: true, auctionSuppliers: { supplier: true }, auctionHistorys: true },
      order: { auctionSuppliers: { submitPrice: 'ASC' }, auctionHistorys: { createdAt: 'DESC' } },
    })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    res.bidName = res.__bid__?.name
    delete res.__bid__
    res.lstHistory = res.__auctionHistorys__ || []
    delete res.__auctionHistorys__

    res.lstSupplier = res.__auctionSuppliers__ || []
    res.lstSupplierId = []
    for (const item of res.lstSupplier) {
      item.supplierName = item.__supplier__.name
      res.lstSupplierId.push(item.supplierId)
      delete item.__supplier__
    }
    delete res.__auctionSuppliers__
    res.statusName = enumData.AuctionStatus[res.status]?.name

    return res
  }

  /** DS đấu giá có phân trang */
  public async pagination(data: PaginationDto, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const whereCon: any = { companyId: user.companyId }
    if (data.where.title) whereCon.title = Like(`%${data.where.title}%`)
    if (data.where.bidId) whereCon.bidId = data.where.bidId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res: any[] = await this.repo.findAndCount({
      where: data.where,
      skip: data.skip,
      take: data.take,
      relations: { bid: true },
      order: { createdAt: 'DESC' },
    })
    if (res[0].length == 0) return res

    for (const item of res[0]) {
      //#region Xử lý trạng thái
      /** Chưa đến hạn? */
      const isNotYet = new Date(item.dateStart).getTime() > new Date().getTime()
      /** Kết thúc? */
      const isEnd = new Date(item.dateEnd).getTime() < new Date().getTime()
      const isCancel = item.status == enumData.AuctionStatus.CANCEL.code
      const isDone = item.status == enumData.AuctionStatus.DONE.code
      item.isShowEdit = isNotYet && !isCancel
      item.isShowCancel = isNotYet && !isCancel
      if (!isNotYet && !isCancel && !isDone) {
        let statusTo = enumData.AuctionStatus.DOING.code
        if (isEnd) statusTo = enumData.AuctionStatus.DONE.code
        if (item.status != statusTo) {
          item.status = statusTo
          await this.repo.update(item.id, { status: statusTo })
        }
      }

      item.statusName = enumData.AuctionStatus[item.status]?.name
      //#endregion

      item.bidName = item.__bid__?.name
      delete item.__bid__
    }

    return res
  }

  /** Tạo đấu giá */
  public async createData(data: AuctionCreate, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.lstSupplierId?.length) throw new Error(`Vui lòng chọn NCC tham gia đấu giá`)

    const auctionNew = new AuctionEntity()
    auctionNew.companyId = user.companyId
    auctionNew.createdBy = user.id
    if (data.bidId) auctionNew.bidId = data.bidId
    auctionNew.title = data.title
    auctionNew.dateStart = data.dateStart
    auctionNew.dateEnd = data.dateEnd
    auctionNew.price = data.price
    auctionNew.description = data.description || ''
    auctionNew.numSupplier = data.lstSupplierId.length || 0

    const auction = await this.repo.save(auctionNew)

    for (const supplierId of data.lstSupplierId) {
      const auctionSupplierNew = new AuctionSupplierEntity()
      auctionSupplierNew.companyId = user.companyId
      auctionSupplierNew.auctionId = auction.id
      auctionSupplierNew.supplierId = supplierId
      auctionSupplierNew.createdBy = user.id
      await this.auctionSupplierRepo.insert(auctionSupplierNew)
    }

    const historyNew = new AuctionHistoryEntity()
    historyNew.companyId = user.companyId
    historyNew.auctionId = auction.id
    historyNew.createdBy = user.id
    historyNew.description = `Nhân viên ${user.username} tạo phiên đấu giá`
    await this.auctionHistoryRepo.insert(historyNew)

    //send mail cho các Doanh nghiệp
    await this.emailService.ThongBaoNccThamGiaDauGiaNhanh(auction.id)

    return { message: CREATE_SUCCESS }
  }

  /** Chỉnh sửa đấu giá */
  public async updateData(data: AuctionUpdate, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const auction = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!auction) throw new Error(ERROR_NOT_FOUND_DATA)

    const isCancel = auction.status == enumData.AuctionStatus.CANCEL.code
    if (isCancel) throw new Error(`Phiên đấu giá đã hủy, không được chỉnh sửa!`)

    /** Chưa đến hạn? */
    const isNotYet = new Date(auction.dateStart).getTime() > new Date().getTime()
    if (!isNotYet) throw new Error(`Chỉ được chỉnh sửa khi phiên đấu giá chưa đến hạn!`)

    auction.dateStart = data.dateStart
    auction.dateEnd = data.dateEnd
    auction.price = data.price
    auction.description = data.description || ''
    await this.repo.update(auction.id, auction)

    const historyNew = new AuctionHistoryEntity()
    historyNew.companyId = user.companyId
    historyNew.auctionId = auction.id
    historyNew.createdBy = user.id
    historyNew.description = `Nhân viên ${user.username} chỉnh sửa phiên đấu giá`
    await this.auctionHistoryRepo.insert(historyNew)

    //send mail cho các Doanh nghiệp
    await this.emailService.ThongBaoNccCapNhatDauGiaNhanh(auction.id)

    return { message: UPDATE_SUCCESS }
  }

  /** Mời thêm NCC */
  public async addSupplier(data: AuctionAddSupplier, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const auction = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!auction) throw new Error(ERROR_NOT_FOUND_DATA)

    const isCancel = auction.status == enumData.AuctionStatus.CANCEL.code
    if (isCancel) throw new Error(`Phiên đấu giá đã hủy, không được chỉnh sửa!`)

    /** Chưa đến hạn? */
    const isNotYet = new Date(auction.dateStart).getTime() > new Date().getTime()
    if (!isNotYet) throw new Error(`Chỉ được chỉnh sửa khi phiên đấu giá chưa đến hạn!`)

    if (!data.lstSupplierId || data.lstSupplierId.length == 0) throw new Error('Vui lòng chọn NCC mời thêm!')

    for (const supplierId of data.lstSupplierId) {
      const auctionSupplierNew = new AuctionSupplierEntity()
      auctionSupplierNew.companyId = user.companyId
      auctionSupplierNew.auctionId = auction.id
      auctionSupplierNew.supplierId = supplierId
      auctionSupplierNew.createdBy = user.id
      await this.auctionSupplierRepo.insert(auctionSupplierNew)
    }

    const historyNew = new AuctionHistoryEntity()
    historyNew.companyId = user.companyId
    historyNew.auctionId = auction.id
    historyNew.createdBy = user.id
    historyNew.description = `Nhân viên ${user.username} mời thêm ${data.lstSupplierId.length} NCC`
    await this.auctionHistoryRepo.insert(historyNew)

    //send mail cho các Doanh nghiệp đc mời thêm
    await this.emailService.ThongBaoNccThamGiaDauGiaNhanh(auction.id, data.lstSupplierId)

    return { message: ACTION_SUCCESS }
  }

  /** Hủy đấu giá */
  public async cancelData(data: AuctionCancel, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const auction = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!auction) throw new Error(ERROR_NOT_FOUND_DATA)

    const isCancel = auction.status == enumData.AuctionStatus.CANCEL.code
    if (isCancel) throw new Error(`Phiên đấu giá đã hủy trước đó!`)

    /** Chưa đến hạn? */
    const isNotYet = new Date(auction.dateStart).getTime() > new Date().getTime()
    if (!isNotYet) throw new Error(`Chỉ được hủy khi phiên đấu giá chưa đến hạn!`)

    await this.repo.update(auction.id, { status: enumData.AuctionStatus.CANCEL.code, updatedBy: user.id, updatedAt: new Date() })

    const historyNew = new AuctionHistoryEntity()
    historyNew.companyId = user.companyId
    historyNew.auctionId = auction.id
    historyNew.createdBy = user.id
    historyNew.description = `Nhân viên ${user.username} hủy phiên đấu giá`
    await this.auctionHistoryRepo.insert(historyNew)

    //send mail cho các Doanh nghiệp
    await this.emailService.ThongBaoNccHuyDauGiaNhanh(auction.id)

    return { message: ACTION_SUCCESS }
  }

  //#endregion

  //#region CLIENT

  /** Chi tiết đấu giá */
  public async findDetailAuctionSupplier(data: { id: string }, user: UserDto) {
    if (!user.supplierId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const res: any = await this.auctionSupplierRepo.findOne({
      where: { auctionId: data.id, supplierId: user.supplierId },
      relations: { auction: { bid: true } },
    })
    if (!res) throw new Error(`Không tìm thấy phiên đấu giá, hoặc bạn không được mời tham gia phiên đấu giá này!`)
    if (res.status == enumData.AuctionStatus.CANCEL.code) {
      throw new Error(`Phiên đấu giá này đã bị hủy!`)
    }
    if (res.status == enumData.AuctionStatus.DONE.code) {
      throw new Error(`Phiên đấu giá này đã kết thúc!`)
    }

    res.title = res.__auction__.title
    res.dateStart = res.__auction__.dateStart
    res.dateEnd = res.__auction__.dateEnd
    res.price = res.__auction__.price
    res.minPrice = res.__auction__.minPrice
    res.description = res.__auction__.description

    if (res.__auction__.bidId) {
      res.bidId = res.__auction__.bidId
      res.bidName = res.__auction__.__bid__?.name

      const domainObj = await apeAuthApiHelper.getDomain(res.companyId)
      res.bidUrl = `${domainObj?.clientUrl}/detail?bidid=${res.bidId}"`
    }

    res.numSupplier = res.__auction__.numSupplier || 0

    delete res.__auction__

    return res
  }

  /** NCC đấu giá */
  public async submitData(data: AuctionSubmit, user: UserDto) {
    //#region Check auction
    if (!user.supplierId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (+data.submitPrice <= 0) throw new Error(`Giá không hợp lệ!`)

    const auctionSupplier = await this.auctionSupplierRepo.findOne({
      where: { auctionId: data.auctionId, supplierId: user.supplierId, isDeleted: false },
    })
    if (!auctionSupplier) throw new Error(`Bạn không được mời tham gia phiên đấu giá này!`)

    const auction = await auctionSupplier.auction
    const dateNow = new Date()
    if (auction.status == enumData.AuctionStatus.CANCEL.code) {
      throw new Error(`Phiên đấu giá này đã bị hủy!`)
    }
    if (auction.dateStart.getTime() > dateNow.getTime()) {
      throw new Error(`Phiên đấu giá này chưa bắt đầu!`)
    }
    if (auction.dateEnd.getTime() < dateNow.getTime()) {
      throw new Error(`Phiên đấu giá này đã kết thúc!`)
    }
    if (auctionSupplier.submitPrice && +auctionSupplier.submitPrice <= +data.submitPrice) {
      throw new Error(`Giá phải nhỏ hơn lần đấu giá trước!`)
    }
    //#endregion

    await this.auctionSupplierRepo.update(auctionSupplier.id, {
      submitPrice: data.submitPrice,
      submitPriceOld: auctionSupplier.submitPrice,
      submitDate: dateNow,
    })

    // Tính lại rank
    await this.reRank(data.auctionId)

    const historyNew = new AuctionHistoryEntity()
    historyNew.companyId = user.companyId
    historyNew.auctionId = auction.id
    historyNew.createdBy = user.id
    historyNew.description = `Nhà cung cấp ${user.username} đấu giá với số tiền ${data.submitPrice}`
    await this.auctionHistoryRepo.insert(historyNew)

    return { message: CREATE_SUCCESS }
  }

  private async reRank(auctionId: string) {
    const lstAuctionSupplier = await this.auctionSupplierRepo.find({
      where: { auctionId, isDeleted: false, submitPrice: Not(IsNull()) },
      order: { submitPrice: 'ASC' },
      select: { id: true, submitPrice: true, rank: true },
    })
    let rank = 1
    for (const item of lstAuctionSupplier) {
      await this.auctionSupplierRepo.update(item.id, { rank: rank })
      if (rank == 1) {
        await this.repo.update(auctionId, { minPrice: item.submitPrice })
      }
      rank++
    }
  }
  //#endregion
}
