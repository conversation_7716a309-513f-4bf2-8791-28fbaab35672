import { MigrationInterface, QueryRunner } from "typeorm";

export class fixDB1670214502764 implements MigrationInterface {
    name = 'fixDB1670214502764'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid\` DROP COLUMN \`createdByName\``);
        await queryRunner.query(`ALTER TABLE \`bid\` ADD \`prId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`bid\` ADD \`prItemId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`bid\` ADD \`parentId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`bid\` CHANGE \`serviceId\` \`serviceId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`bid\` ADD CONSTRAINT \`FK_4b510836b0248b8ea62ce46723c\` FOREIGN KEY (\`prId\`) REFERENCES \`pr\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`bid\` ADD CONSTRAINT \`FK_b63d03bb57eb5846ffcf79a036c\` FOREIGN KEY (\`prItemId\`) REFERENCES \`pr_item\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`bid\` ADD CONSTRAINT \`FK_4967726b62bb9e095395d7cee50\` FOREIGN KEY (\`parentId\`) REFERENCES \`bid\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid\` DROP FOREIGN KEY \`FK_4967726b62bb9e095395d7cee50\``);
        await queryRunner.query(`ALTER TABLE \`bid\` DROP FOREIGN KEY \`FK_b63d03bb57eb5846ffcf79a036c\``);
        await queryRunner.query(`ALTER TABLE \`bid\` DROP FOREIGN KEY \`FK_4b510836b0248b8ea62ce46723c\``);
        await queryRunner.query(`ALTER TABLE \`bid\` CHANGE \`serviceId\` \`serviceId\` varchar(36) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`bid\` DROP COLUMN \`parentId\``);
        await queryRunner.query(`ALTER TABLE \`bid\` DROP COLUMN \`prItemId\``);
        await queryRunner.query(`ALTER TABLE \`bid\` DROP COLUMN \`prId\``);
        await queryRunner.query(`ALTER TABLE \`bid\` ADD \`createdByName\` varchar(50) NULL`);
    }

}
