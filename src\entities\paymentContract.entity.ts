import { BaseEntity } from './base.entity'
import { Entity, Column, JoinColumn, ManyToOne } from 'typeorm'
import { PaymentEntity } from './payment.entity'
import { ContractEntity } from './contract.entity'

@Entity('payment_contract')
export class PaymentContractEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  contractId: string
  @ManyToOne(() => ContractEntity, (p) => p.paymentContracts)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  paymentId: string
  @ManyToOne(() => PaymentEntity, (p) => p.paymentContracts)
  @JoinColumn({ name: 'paymentId', referencedColumnName: 'id' })
  payment: Promise<PaymentEntity>
}
