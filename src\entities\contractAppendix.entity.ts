import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ContractEntity } from './contract.entity'
import { ObjectEntity } from './object.entity'

/** <PERSON><PERSON> lục hợp đồng */
@Entity({ name: 'contract_appendix' })
export class ContractAppendixEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  title: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  contractId: string
  @ManyToOne(() => ContractEntity, (p) => p.appendixs)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileAttach: string

  /** <PERSON><PERSON><PERSON> hi<PERSON> l<PERSON> */
  @Column({
    nullable: false,
  })
  effectiveDate: Date

  /** <PERSON><PERSON><PERSON> hết hạn */
  @Column({
    nullable: false,
  })
  expiredDate: Date

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  createdBy: string

  /** Đối tượng */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  objectId: string
  @ManyToOne(() => ObjectEntity, (p) => p.contractAppendix)
  @JoinColumn({ name: 'objectId', referencedColumnName: 'id' })
  object: Promise<ObjectEntity>
}
