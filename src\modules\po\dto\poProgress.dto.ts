import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class PoProgressDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  poId: string

  @ApiPropertyOptional()
  @IsOptional()
  percent: number

  @ApiPropertyOptional()
  @IsOptional()
  time: Date

  @ApiPropertyOptional()
  @IsOptional()
  money: number
  @ApiPropertyOptional()
  @IsOptional()
  description: string

  @ApiPropertyOptional()
  @IsOptional()
  name: string
}
