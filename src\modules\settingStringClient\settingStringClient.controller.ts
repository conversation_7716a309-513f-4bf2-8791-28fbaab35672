import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { enumProject } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { SettingStringClientCreateDto, SettingStringClientUpdateDto } from './dto'
import { SettingStringClientService } from './settingStringClient.service'

@ApiBearerAuth()
@ApiTags('Client')
@Controller('settingStringClients')
export class SettingStringClientController {
  constructor(private readonly service: SettingStringClientService) {}

  @ApiOperation({ summary: 'Danh sách footer phân trang' })
  @Roles(enumProject.Features.SETTING_021.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination_footer')
  public async paginationFooter(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.paginationFooter(user, data)
  }

  @ApiOperation({ summary: 'Danh sách header phân trang' })
  @Roles(enumProject.Features.SETTING_021.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination_header')
  public async paginationHeader(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.paginationHeader(user, data)
  }

  @ApiOperation({ summary: 'Tạo footer/header' })
  @Roles(enumProject.Features.SETTING_021.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: SettingStringClientCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật footer/header' })
  @Roles(enumProject.Features.SETTING_021.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: SettingStringClientUpdateDto) {
    return await this.service.updateData(user, data)
  }
}
