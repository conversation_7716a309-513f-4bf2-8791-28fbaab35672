import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { CurrentUser } from '../common/decorators'
import { PaginationDto, UserDto } from '../../dto'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { PaymentService } from './payment.service'
import { PaymentCreateDto, PaymentUpdateDto } from './dto'
import { ApeAuthGuard, RoleGuard } from '../common/guards'

@ApiBearerAuth()
@ApiTags('PAYMENT')
@Controller('payment')
export class PaymentController {
  constructor(private readonly service: PaymentService) {}

  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách hồ sơ thanh toán' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: '<PERSON><PERSON> sách hồ sơ thanh toán phân trang' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo hồ sơ thanh toán' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: PaymentCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật hồ sơ thanh toán' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: PaymentUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động hồ sơ thanh toán' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@Body() data: { id: string }, @CurrentUser() user: UserDto) {
    const warehouse = await this.service.updateIsDelete(data, user)
    return warehouse
  }

  @ApiOperation({ summary: 'Chi tiết hồ sơ thanh toán' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetail(user, data)
  }

  @ApiOperation({ summary: 'API Cập nhật kiểm tra hồ sơ' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_checking')
  public async updateChecking(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateChecking(user, data)
  }

  @ApiOperation({ summary: 'API Cập nhật xác nhận hợp lệ' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_valid_confirm')
  public async updateValidConfirm(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateValidConfirm(user, data)
  }

  @ApiOperation({ summary: 'API Cập nhật yêu cầu kiểm tra lại' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_recheck')
  public async updateRecheck(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateRecheck(user, data)
  }

  @ApiOperation({ summary: 'Yêu cầu duyệt hồ sơ thanh toán' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('request_approve')
  public async requestApprovePayment(@Body() data: { id: string }, @CurrentUser() user: UserDto) {
    return await this.service.requestApprovePayment(user, data.id)
  }

  @ApiOperation({ summary: 'Duyệt hợp hồ sơ thanh toán' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('approve_payment')
  public async approvedPayment(@Body() data: { id: string }, @CurrentUser() user: UserDto) {
    return await this.service.approvedPayment(data, user)
  }

  @ApiOperation({ summary: 'Yêu cầu xác nhận lại' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_request_confirm')
  public async updateRequestConfirm(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateRequestConfirm(user, data)
  }
}
