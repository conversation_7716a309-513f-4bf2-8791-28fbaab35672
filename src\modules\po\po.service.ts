import { Injectable, MethodNotAllowedException, NotAcceptableException } from '@nestjs/common'
import * as moment from 'moment'
import { In, IsNull, Like, Raw } from 'typeorm'
import { enumData, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION, UPDATE_ACTIVE_SUCCESS } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import {
  BidEntity,
  ContractEntity,
  EmployeeEntity,
  ObjectEntity,
  PaymentProgressEntity,
  POEntity,
  POHistoryEntity,
  POMemberEntity,
  POProductEntity,
  PrEntity,
  ServiceEntity,
  SupplierEntity,
} from '../../entities'
import { EmailService } from '../email/email.service'
import { coreHelper } from '../../helpers'
import { EmployeeRepository, POProductRepository, PORepository, SettingStringRepository } from '../../repositories'
import { P<PERSON>reateDto, POCreateExcelDto, POUpdateDeliveryDateDto, POUpdateDto, POUpdateStatusDto } from './dto'

@Injectable()
export class POService {
  constructor(
    private readonly emailService: EmailService,
    private repo: PORepository,
    private employeeRepo: EmployeeRepository,
    private settingStringRepo: SettingStringRepository,
    private poProductRepo: POProductRepository,
  ) {}

  public async createData(user: UserDto, data: POCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const objectRepo = this.repo.manager.getRepository(ObjectEntity)
    const serviceRepo = this.repo.manager.getRepository(ServiceEntity)
    const contractRepo = this.repo.manager.getRepository(ContractEntity)
    const prRepo = this.repo.manager.getRepository(PrEntity)

    const employee: any = await this.employeeRepo.findOne({
      where: { id: user.employeeId, companyId: user.companyId },
      relations: { branch: true },
    })
    const object = await objectRepo.findOne({ where: { id: data.objectId, companyId: user.companyId } })
    const serviceLevel1 = await serviceRepo.findOne({ where: { id: data.serviceLevel1, companyId: user.companyId } })
    const poLast = await this.repo.findOne({
      where: { code: Like(`%00%`), companyId: user.companyId },
      order: { code: 'DESC' },
    })
    let sortString = '0'
    if (poLast) {
      sortString = poLast.code.substring(0, 4)
    }
    const lastSort = +sortString
    sortString = ('000' + (lastSort + 1)).slice(-4)
    let code = sortString // STT
    code += '/' + new Date().getFullYear().toString().slice(-2) // YY
    if (employee && employee.__branch__) {
      code += '/' + employee.__branch__.code // XXX
    }
    if (object) {
      code += '.' + object.code // ZZZ
    }
    if (serviceLevel1) {
      code += '.' + serviceLevel1.code // AAA
    }
    code += '-' + 'PO'

    let supplierId = data.supplierId
    let canGenchild = false
    if (data.contractId) {
      const objContract = await contractRepo.findOne({
        where: { id: data.contractId, companyId: user.companyId },
        select: { supplierId: true, status: true, isGenChild: true, isChild: true },
      })
      if (objContract && objContract.status === enumData.ContractStatus.Cancel.code) {
        throw new Error('Hợp đồng đã hủy. Vui lòng kiểm tra lại')
      }

      if (objContract) {
        if (!objContract.isChild) canGenchild = true
        supplierId = objContract.supplierId
      }
    }

    const poNew = new POEntity()
    poNew.companyId = user.companyId
    poNew.createdBy = user.id
    poNew.code = code
    poNew.paymentPlanType = data.paymentPlanType
    if (poNew.paymentPlanType == enumData.ContractTypePo.Contract.code) {
      poNew.contractId = data.contractId
      poNew.contractPaymentPlanId = data.contractPaymentPlanId
    } else {
      poNew.bidId = data.bidId
      poNew.prId = data.prId
    }
    const lstItem = []
    poNew.title = data.title
    poNew.supplierId = supplierId
    poNew.operator = data.operator
    poNew.type = data.type
    poNew.region = data.region
    poNew.currency = data.currency
    if (employee?.branchId) poNew.branchId = employee?.branchId
    const sum = data.lstProduct.reduce((sum: any, current: any) => +sum + +current.money, 0)
    poNew.money = sum
    poNew.company = data.company
    poNew.email = data.email
    poNew.phone = data.phone
    poNew.status = enumData.PurchaseOrderStatus.Open.code
    poNew.description = data.description
    poNew.objectId = data.objectId
    poNew.deliveryDate = data.deliveryDate
    const employeeCheck = await this.employeeRepo.findOne({ where: { userId: user.id } })
    if (employeeCheck && employeeCheck.branchId) {
      poNew.isChild = true
    } else {
      poNew.isChild = false
    }

    const poEntity = await poNew.save()

    // người duyệt PO
    {
      const poConFirmMember = new POMemberEntity()
      poConFirmMember.companyId = user.companyId
      poConFirmMember.createdBy = user.id
      poConFirmMember.employeeId = data.confirmId
      poConFirmMember.poRoleCode = enumData.PORoleCode.Confirm.code
      poConFirmMember.description = enumData.PORoleCode.Confirm.description
      poConFirmMember.poId = poEntity.id
      await poConFirmMember.save()

      const poConFirmViewMember = new POMemberEntity()
      poConFirmViewMember.companyId = user.companyId
      poConFirmViewMember.createdBy = user.id
      poConFirmViewMember.employeeId = data.confirmId
      poConFirmViewMember.poRoleCode = enumData.PORoleCode.View.code
      poConFirmViewMember.description = enumData.PORoleCode.View.description
      poConFirmViewMember.poId = poEntity.id
      await poConFirmViewMember.save()
    }

    // người thanh toán PO
    {
      const poPaymentPOMember = new POMemberEntity()
      poPaymentPOMember.companyId = user.companyId
      poPaymentPOMember.createdBy = user.id
      poPaymentPOMember.employeeId = data.poPaymentId
      poPaymentPOMember.poRoleCode = enumData.PORoleCode.PurchaseOrderPayMent.code
      poPaymentPOMember.description = enumData.PORoleCode.PurchaseOrderPayMent.description
      poPaymentPOMember.poId = poEntity.id
      await poPaymentPOMember.save()

      const poPaymentPOViewMember = new POMemberEntity()
      poPaymentPOViewMember.companyId = user.companyId
      poPaymentPOViewMember.createdBy = user.id
      poPaymentPOViewMember.employeeId = data.poPaymentId
      poPaymentPOViewMember.poRoleCode = enumData.PORoleCode.View.code
      poPaymentPOViewMember.description = enumData.PORoleCode.View.description
      poPaymentPOViewMember.poId = poEntity.id
      await poPaymentPOViewMember.save()
    }

    // người chỉnh sửa PO
    {
      const poEditPOMember = new POMemberEntity()
      poEditPOMember.companyId = user.companyId
      poEditPOMember.createdBy = user.id
      poEditPOMember.employeeId = data.editPOId
      poEditPOMember.poRoleCode = enumData.PORoleCode.Edit.code
      poEditPOMember.description = enumData.PORoleCode.Edit.description
      poEditPOMember.poId = poEntity.id
      await poEditPOMember.save()

      const poEditViewMember = new POMemberEntity()
      poEditViewMember.companyId = user.companyId
      poEditViewMember.createdBy = user.id
      poEditViewMember.employeeId = data.editPOId
      poEditViewMember.poRoleCode = enumData.PORoleCode.View.code
      poEditViewMember.description = enumData.PORoleCode.View.description
      poEditViewMember.poId = poEntity.id
      await poEditViewMember.save()
    }

    // người xem PO
    for (const item of data.anotherRoleIds) {
      const poMember = new POMemberEntity()
      poMember.companyId = user.companyId
      poMember.createdBy = user.id
      poMember.employeeId = item
      poMember.poRoleCode = enumData.PORoleCode.View.code
      poMember.description = enumData.PORoleCode.View.description
      poMember.poId = poEntity.id
      await poMember.save()
    }

    // người hủy PO
    {
      const poCancelMember = new POMemberEntity()
      poCancelMember.companyId = user.companyId
      poCancelMember.createdBy = user.id
      poCancelMember.employeeId = data.cancelId
      poCancelMember.poRoleCode = enumData.PORoleCode.Cancel.code
      poCancelMember.description = enumData.PORoleCode.Cancel.description
      poCancelMember.poId = poEntity.id
      await poCancelMember.save()

      const poCancelViewMember = new POMemberEntity()
      poCancelViewMember.companyId = user.companyId
      poCancelViewMember.createdBy = user.id
      poCancelViewMember.employeeId = data.cancelId
      poCancelViewMember.poRoleCode = enumData.PORoleCode.View.code
      poCancelViewMember.description = enumData.PORoleCode.View.description
      poCancelViewMember.poId = poEntity.id
      await poCancelViewMember.save()
    }

    const historyObj = new POHistoryEntity()
    historyObj.companyId = user.companyId
    historyObj.createdBy = user.id
    historyObj.poId = poEntity.id
    historyObj.statusConvert = enumData.PurchaseOrderStatus.Open.code
    historyObj.statusCurrent = enumData.PurchaseOrderStatus.Open.code
    historyObj.description = `Nhân viên [${user.username}] - Vừa tạo PO có mã là [${poEntity.code}]`
    historyObj.employeeId = user.employeeId
    await historyObj.save()

    // Danh sách hàng hóa
    for (const item of data.lstProduct) {
      const poPurchaseProduct = new POProductEntity()
      poPurchaseProduct.companyId = user.companyId
      poPurchaseProduct.createdBy = user.id
      poPurchaseProduct.poId = poEntity.id
      poPurchaseProduct.description = item.description
      poPurchaseProduct.type = item.type
      poPurchaseProduct.group = item.group
      poPurchaseProduct.itemId = item.itemId
      poPurchaseProduct.name = item.name
      poPurchaseProduct.note = item.note
      poPurchaseProduct.unit = item.unit
      poPurchaseProduct.quantity = item.quantity
      poPurchaseProduct.price = item.price
      poPurchaseProduct.money = item.money
      poPurchaseProduct.serviceId = item.serviceId
      poPurchaseProduct.itemCode = coreHelper.codeDefaultItem()
      await poPurchaseProduct.save()
    }

    // Tiến độ thanh toán
    for (const item of data.lstPaymentProgress) {
      const new1 = new PaymentProgressEntity()
      new1.companyId = user.companyId
      new1.createdBy = user.id
      new1.name = item.name
      new1.poId = poEntity.id
      new1.percent = item.percent
      new1.time = item.time
      new1.description = item.description
      new1.money = (new1.percent * +poEntity.money) / 100
      await new1.save()
    }
    //nếu có pr con thì tạo po con
    if (lstItem && lstItem.length > 0 && canGenchild) {
      let count = 1
      for (const dataChild of lstItem) {
        const poNew = new POEntity()
        poNew.companyId = user.companyId
        poNew.createdBy = user.id
        poNew.code = code + `${count}`
        count++
        poNew.paymentPlanType = data.paymentPlanType
        if (poNew.paymentPlanType == enumData.ContractTypePo.Contract.code) {
          poNew.contractId = data.contractId
          poNew.contractPaymentPlanId = data.contractPaymentPlanId
          const objContract: any = await contractRepo.find({
            where: { id: data.contractId, companyId: user.companyId },
            select: { bid: true },
            relations: { bid: true },
          })
        } else {
          poNew.bidId = data.bidId
          poNew.prId = data.prId
        }
        poNew.title = data.title
        poNew.parentId = poEntity.id
        poNew.supplierId = supplierId
        poNew.operator = data.operator
        poNew.isChild = true
        poNew.type = data.type
        poNew.region = data.region
        poNew.currency = data.currency
        poNew.branchId = dataChild.branchId
        const sum = data.lstProduct.reduce((sum: any, current: any) => +sum + +current.money, 0)
        poNew.money = sum
        poNew.company = data.company
        poNew.email = data.email
        poNew.phone = data.phone
        poNew.status = enumData.PurchaseOrderStatus.Open.code
        poNew.description = data.description
        poNew.objectId = data.objectId
        poNew.deliveryDate = data.deliveryDate
        const poEntityChild = await poNew.save()
        // người duyệt PO
        {
          const poConFirmMember = new POMemberEntity()
          poConFirmMember.companyId = user.companyId
          poConFirmMember.createdBy = user.id
          poConFirmMember.employeeId = data.confirmId
          poConFirmMember.poRoleCode = enumData.PORoleCode.Confirm.code
          poConFirmMember.description = enumData.PORoleCode.Confirm.description
          poConFirmMember.poId = poEntityChild.id
          await poConFirmMember.save()

          const poConFirmViewMember = new POMemberEntity()
          poConFirmViewMember.companyId = user.companyId
          poConFirmViewMember.createdBy = user.id
          poConFirmViewMember.employeeId = data.confirmId
          poConFirmViewMember.poRoleCode = enumData.PORoleCode.View.code
          poConFirmViewMember.description = enumData.PORoleCode.View.description
          poConFirmViewMember.poId = poEntityChild.id
          await poConFirmViewMember.save()
        }

        // người thanh toán PO
        {
          const poPaymentPOMember = new POMemberEntity()
          poPaymentPOMember.companyId = user.companyId
          poPaymentPOMember.createdBy = user.id
          poPaymentPOMember.employeeId = data.poPaymentId
          poPaymentPOMember.poRoleCode = enumData.PORoleCode.PurchaseOrderPayMent.code
          poPaymentPOMember.description = enumData.PORoleCode.PurchaseOrderPayMent.description
          poPaymentPOMember.poId = poEntityChild.id
          await poPaymentPOMember.save()

          const poPaymentPOViewMember = new POMemberEntity()
          poPaymentPOViewMember.companyId = user.companyId
          poPaymentPOViewMember.createdBy = user.id
          poPaymentPOViewMember.employeeId = data.poPaymentId
          poPaymentPOViewMember.poRoleCode = enumData.PORoleCode.View.code
          poPaymentPOViewMember.description = enumData.PORoleCode.View.description
          poPaymentPOViewMember.poId = poEntityChild.id
          await poPaymentPOViewMember.save()
        }
        // người chỉnh sửa PO
        {
          const poEditPOMember = new POMemberEntity()
          poEditPOMember.companyId = user.companyId
          poEditPOMember.createdBy = user.id
          poEditPOMember.employeeId = data.editPOId
          poEditPOMember.poRoleCode = enumData.PORoleCode.Edit.code
          poEditPOMember.description = enumData.PORoleCode.Edit.description
          poEditPOMember.poId = poEntityChild.id
          await poEditPOMember.save()

          const poEditViewMember = new POMemberEntity()
          poEditViewMember.companyId = user.companyId
          poEditViewMember.createdBy = user.id
          poEditViewMember.employeeId = data.editPOId
          poEditViewMember.poRoleCode = enumData.PORoleCode.View.code
          poEditViewMember.description = enumData.PORoleCode.View.description
          poEditViewMember.poId = poEntityChild.id
          await poEditViewMember.save()
        }
        // người xem PO
        for (const item of data.anotherRoleIds) {
          const poMember = new POMemberEntity()
          poMember.companyId = user.companyId
          poMember.createdBy = user.id
          poMember.employeeId = item
          poMember.poRoleCode = enumData.PORoleCode.View.code
          poMember.description = enumData.PORoleCode.View.description
          poMember.poId = poEntityChild.id
          await poMember.save()
        }

        // người hủy PO
        {
          const poCancelMember = new POMemberEntity()
          poCancelMember.companyId = user.companyId
          poCancelMember.createdBy = user.id
          poCancelMember.employeeId = data.cancelId
          poCancelMember.poRoleCode = enumData.PORoleCode.Cancel.code
          poCancelMember.description = enumData.PORoleCode.Cancel.description
          poCancelMember.poId = poEntityChild.id
          await poCancelMember.save()

          const poCancelViewMember = new POMemberEntity()
          poCancelViewMember.companyId = user.companyId
          poCancelViewMember.createdBy = user.id
          poCancelViewMember.employeeId = data.cancelId
          poCancelViewMember.poRoleCode = enumData.PORoleCode.View.code
          poCancelViewMember.description = enumData.PORoleCode.View.description
          poCancelViewMember.poId = poEntityChild.id
          await poCancelViewMember.save()
        }

        const item = data.lstProduct[0]
        const poPurchaseProduct = new POProductEntity()
        poPurchaseProduct.companyId = user.companyId
        poPurchaseProduct.createdBy = user.id
        poPurchaseProduct.poId = poEntityChild.id
        poPurchaseProduct.description = item.description
        poPurchaseProduct.type = item.type
        poPurchaseProduct.group = item.group
        poPurchaseProduct.name = dataChild.productName
        poPurchaseProduct.note = item.note
        poPurchaseProduct.unit = dataChild.unit
        poPurchaseProduct.quantity = dataChild.quantity
        poPurchaseProduct.price = item.price
        poPurchaseProduct.money = item.money
        poPurchaseProduct.serviceId = dataChild.serviceId
        poPurchaseProduct.itemCode = coreHelper.codeDefaultItem()
        await poPurchaseProduct.save()
      }
    }
    // throw new Error('sadasdasdasd')
    return { message: 'PO đã được tạo thành công.', success: true }
  }

  public async updateData(user: UserDto, data: POUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const entity = await this.repo.findOne({
      where: { id: data.id, companyId: user.companyId },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPOEdit(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    const contractRepo = this.repo.manager.getRepository(ContractEntity)
    const poMemberRepo = this.repo.manager.getRepository(POMemberEntity)
    const poProductRepo = this.repo.manager.getRepository(POProductEntity)
    const poPaymentPlanRepo = this.repo.manager.getRepository(PaymentProgressEntity)

    // Check người duyệt PO
    const checkEmployeeConfirm = await this.employeeRepo.findOne({
      where: {
        id: data.confirmId,
        companyId: user.companyId,
        isDeleted: false,
      },
    })
    if (!checkEmployeeConfirm) throw new Error(`Nhân viên duyệt không tồn tại. Vui lòng kiểm tra lại`)

    if (data.contractId) {
      const objContract = await contractRepo.findOne({
        where: { id: data.contractId, companyId: user.companyId },
        select: { status: true },
      })
      if (objContract && objContract.status === enumData.ContractStatus.Cancel.code) {
        throw new Error('Hợp đồng đã hủy. Vui lòng kiểm tra lại')
      }
    }

    entity.contractId = data.contractId
    entity.title = data.title
    entity.supplierId = data.supplierId
    entity.contractPaymentPlanId = data.contractPaymentPlanId
    entity.bidId = data.bidId
    entity.prId = data.prId
    entity.operator = data.operator
    entity.type = data.type
    entity.region = data.region
    entity.currency = data.currency
    entity.createdBy = user.id
    entity.objectId = data.objectId
    entity.deliveryDate = data.deliveryDate
    const employee = await this.employeeRepo.findOne({
      where: {
        userId: user.id,
        companyId: user.companyId,
        isDeleted: false,
      },
    })
    if (employee) {
      entity.branchId = employee.branchId
    }
    const sum = data.lstProduct.reduce((sum: any, current: any) => +sum + +current.money, 0)
    entity.money = sum
    entity.company = data.company
    entity.email = data.email
    entity.phone = data.phone
    const employeeCheck = await this.employeeRepo.findOne({ where: { userId: user.id } })
    if (employeeCheck && employeeCheck.branchId) {
      entity.isChild = true
    } else {
      entity.isChild = false
    }
    const oldStatus = entity.status
    entity.status = enumData.PurchaseOrderStatus.Open.code
    entity.updatedBy = user.id
    await entity.save()

    const historyNew = new POHistoryEntity()
    historyNew.companyId = user.companyId
    historyNew.createdBy = user.id
    historyNew.poId = entity.id
    historyNew.statusCurrent = oldStatus
    historyNew.statusConvert = entity.status
    historyNew.description = `Nhân viên [${user.username}] - vừa thao tác chỉnh sửa thông tin PO`
    historyNew.employeeId = user.employeeId
    await historyNew.save()

    // xóa hết DS hàng hóa của PO
    if (data.isChangeProduct) {
      await poProductRepo.delete({ poId: entity.id })
      for (let item of data.lstProduct) {
        const poProduct = new POProductEntity()
        poProduct.companyId = user.companyId
        poProduct.createdBy = user.id
        poProduct.poId = entity.id
        poProduct.description = item.description
        poProduct.type = item.type
        poProduct.itemId = item.itemId
        poProduct.group = item.group
        poProduct.name = item.name
        poProduct.note = item.note
        poProduct.unit = item.unit
        poProduct.quantity = item.quantity
        poProduct.price = item.price
        poProduct.money = item.money
        poProduct.serviceId = item.serviceId
        poProduct.itemCode = coreHelper.codeDefaultItem()
        await poProduct.save()
      }
    }

    // Tiến độ thanh toán
    if (data.isChangePaymentProgress) {
      await poPaymentPlanRepo.delete({ poId: entity.id })
      for (const item of data.lstPaymentProgress) {
        const new1 = new PaymentProgressEntity()
        new1.companyId = user.companyId
        new1.createdBy = user.id
        new1.name = item.name
        new1.poId = entity.id
        new1.percent = item.percent
        new1.time = item.time
        new1.description = item.description
        new1.money = (new1.percent * +entity.money) / 100
        await new1.save()
      }
    }

    // xóa hết thành viên của PO
    await poMemberRepo.delete({ poId: entity.id })

    // người duyệt PO
    {
      const poConFirmMember = new POMemberEntity()
      poConFirmMember.companyId = user.companyId
      poConFirmMember.createdBy = user.id
      poConFirmMember.employeeId = data.confirmId
      poConFirmMember.poRoleCode = enumData.PORoleCode.Confirm.code
      poConFirmMember.description = enumData.PORoleCode.Confirm.description
      poConFirmMember.poId = entity.id
      await poConFirmMember.save()

      const poConFirmViewMember = new POMemberEntity()
      poConFirmViewMember.companyId = user.companyId
      poConFirmViewMember.createdBy = user.id
      poConFirmViewMember.employeeId = data.confirmId
      poConFirmViewMember.poRoleCode = enumData.PORoleCode.View.code
      poConFirmViewMember.description = enumData.PORoleCode.View.description
      poConFirmViewMember.poId = entity.id
      await poConFirmViewMember.save()
    }

    // người thanh toán PO
    {
      const poPaymentPOMember = new POMemberEntity()
      poPaymentPOMember.companyId = user.companyId
      poPaymentPOMember.createdBy = user.id
      poPaymentPOMember.employeeId = data.poPaymentId
      poPaymentPOMember.poRoleCode = enumData.PORoleCode.PurchaseOrderPayMent.code
      poPaymentPOMember.description = enumData.PORoleCode.PurchaseOrderPayMent.description
      poPaymentPOMember.poId = entity.id
      await poPaymentPOMember.save()

      const poPaymentPOViewMember = new POMemberEntity()
      poPaymentPOViewMember.companyId = user.companyId
      poPaymentPOViewMember.createdBy = user.id
      poPaymentPOViewMember.employeeId = data.poPaymentId
      poPaymentPOViewMember.poRoleCode = enumData.PORoleCode.View.code
      poPaymentPOViewMember.description = enumData.PORoleCode.View.description
      poPaymentPOViewMember.poId = entity.id
      await poPaymentPOViewMember.save()
    }

    // người chỉnh sửa PO
    {
      const poEditPOMember = new POMemberEntity()
      poEditPOMember.companyId = user.companyId
      poEditPOMember.createdBy = user.id
      poEditPOMember.employeeId = data.editPOId
      poEditPOMember.poRoleCode = enumData.PORoleCode.Edit.code
      poEditPOMember.description = enumData.PORoleCode.Edit.description
      poEditPOMember.poId = entity.id
      await poEditPOMember.save()

      const poEditViewMember = new POMemberEntity()
      poEditViewMember.companyId = user.companyId
      poEditViewMember.createdBy = user.id
      poEditViewMember.employeeId = data.editPOId
      poEditViewMember.poRoleCode = enumData.PORoleCode.View.code
      poEditViewMember.description = enumData.PORoleCode.View.description
      poEditViewMember.poId = entity.id
      await poEditViewMember.save()
    }

    // người xem PO
    for (const item of data.anotherRoleIds) {
      const poMember = new POMemberEntity()
      poMember.companyId = user.companyId
      poMember.createdBy = user.id
      poMember.employeeId = item
      poMember.poRoleCode = enumData.PORoleCode.View.code
      poMember.description = enumData.PORoleCode.View.description
      poMember.poId = entity.id
      await poMember.save()
    }

    // người hủy PO
    {
      const poCancelMember = new POMemberEntity()
      poCancelMember.companyId = user.companyId
      poCancelMember.createdBy = user.id
      poCancelMember.employeeId = data.cancelId
      poCancelMember.poRoleCode = enumData.PORoleCode.Cancel.code
      poCancelMember.description = enumData.PORoleCode.Cancel.description
      poCancelMember.poId = entity.id
      await poCancelMember.save()

      const poCancelViewMember = new POMemberEntity()
      poCancelViewMember.companyId = user.companyId
      poCancelViewMember.createdBy = user.id
      poCancelViewMember.employeeId = data.cancelId
      poCancelViewMember.poRoleCode = enumData.PORoleCode.View.code
      poCancelViewMember.description = enumData.PORoleCode.View.description
      poCancelViewMember.poId = entity.id
      await poCancelViewMember.save()
    }

    return { message: 'PO đã được cập nhật thành công.', success: true }
  }

  public async updateStatus(user: UserDto, data: POUpdateStatusDto) {
    const entity = await this.repo.findOne({
      where: { id: data.id, companyId: user.companyId },
    })
    if (entity.status === enumData.PurchaseOrderStatus.Cancel.code)
      throw new Error(`Trạng thái hiện tại không thể cập nhật ngày giao hàng dự kiến. Vui lòng kiểm tra lại`)

    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (data.isSupplier) {
      if (entity.supplierId !== user.supplierId)
        throw new Error(`Bạn không phải là nhà cung cấp của PO này nên không thể xác nhận giao hàng. Vui lòng kiểm tra lại`)
    } else {
      if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
      const objPermission = await this.checkPermissionPOEdit(user, data.id)
      if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    }
    entity.orderStatus = data.orderStatus
    await this.repo.update(entity.id, entity)
    return { message: 'PO đã được cập nhật thành công.', success: true }
  }

  public async updateDeliveryDate(user: UserDto, data: POUpdateDeliveryDateDto) {
    const entity = await this.repo.findOne({
      where: { id: data.id, companyId: user.companyId },
    })
    if (entity.status === enumData.PurchaseOrderStatus.Cancel.code)
      throw new Error(`Trạng thái hiện tại không thể cập nhật ngày giao hàng dự kiến. Vui lòng kiểm tra lại`)

    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (data.isSupplier) {
      if (entity.supplierId !== user.supplierId)
        throw new Error(`Bạn không phải là nhà cung cấp của PO này nên không thể xác nhận giao hàng. Vui lòng kiểm tra lại`)
    } else {
      if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
      const objPermission = await this.checkPermissionPOEdit(user, data.id)
      if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    }
    entity.deliveryDate = data.deliveryDate
    await this.repo.update(entity.id, entity)
    this.emailService.GuiThayDoiNgayGiaoHang(entity.id)
    return { message: 'PO đã được cập nhật thành công.', success: true }
  }

  public async findDetail2(user: UserDto, data: { id: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.id) throw new Error('Vui lòng chọn PO trước!')
    const res: any = await this.repo.findOne({
      where: { id: data.id, companyId: user.companyId },
      relations: {
        members: { employee: true },
        bid: true,
        pr: true,
        supplier: true,
        paymentPlan: true,
        poHistorys: { employee: true, supplier: true },
        childs: { paymentPlan: true, products: true },
        products: { service: true },
        contract: true,
        contractPaymentPlan: true,
        branch: true,
      },
      order: {
        contractPaymentPlan: { time: 'DESC' },
        poHistorys: { createdAt: 'DESC' },
        products: { createdAt: 'DESC' },
      },
    })
    if (!res) throw new Error('PO không tồn tại!')

    if (res.__contract__) res.contractCode = res.__contract__.code
    if (res.__branch__) res.branchName = res.__branch__?.name

    if (res.__contractPaymentPlan__) {
      res.contractPaymentPlanName = res.__contractPaymentPlan__.name
      res.contractPaymentPlanPercent = res.__contractPaymentPlan__.percent
    }
    delete res.__contractPaymentPlan__
    if (res.__bid__) {
      res.bidName = res.__bid__.name
    }
    if (res.__childs__) {
      res.lstChild = res.__childs__

      const dicStatusColor: any = {}
      const dicStatus: any = {}
      {
        const lstPOStatus = coreHelper.convertObjToArray(enumData.PurchaseOrderStatus)
        lstPOStatus.forEach((c) => (dicStatus[c.code] = c.name))
        lstPOStatus.forEach((c) => (dicStatusColor[c.code] = c.color))
      }
      for (const item of res.lstChild) {
        item.statusName = dicStatus[item.status]
        item.statusColor = dicStatusColor[item.status]
      }
      delete res.__childs__
    }
    delete res.__bid__
    if (res.__prs__) {
      res.prName = res.__prs__.name
    }
    delete res.__prs__
    if (res.__supplier__) {
      res.supplierName = res.__supplier__.name
    }
    delete res.__supplier__
    res.paymentPlanName = ''

    const dicStatus: any = {}
    {
      const lstPOStatus = coreHelper.convertObjToArray(enumData.PurchaseOrderStatus)
      lstPOStatus.forEach((c) => (dicStatus[c.code] = c))
    }
    res.statusName = dicStatus[res.status]?.name
    res.statusColor = dicStatus[res.status]?.color

    const lstMember = res.__members__ || []
    res.anotherRoleIds = lstMember.filter((c: any) => c.poRoleCode === enumData.PORoleCode.View.code).map((c: any) => c.employeeId)
    res.anotherView = lstMember.filter((c: any) => c.poRoleCode === enumData.PORoleCode.View.code).map((c: any) => c.__employee__?.name)
    res.anotherView = res.anotherView.filter(function (elem: any, index: any, self: string | any[]) {
      return index === self.indexOf(elem)
    })

    const objConfirm = lstMember.find((c: any) => c.poRoleCode === enumData.PORoleCode.Confirm.code)
    if (objConfirm) res.confirmId = objConfirm.employeeId
    if (objConfirm) res.confirmName = await objConfirm?.__employee__?.name

    const objPoPayMent = lstMember.find((c: any) => c.poRoleCode === enumData.PORoleCode.PurchaseOrderPayMent.code)
    if (objPoPayMent) res.poPaymentId = objPoPayMent.employeeId
    if (objPoPayMent) res.paymentName = await objPoPayMent?.__employee__?.name

    const objEdit = lstMember.find((c: any) => c.poRoleCode === enumData.PORoleCode.Edit.code)
    if (objEdit) res.editPOId = objEdit.employeeId
    if (objEdit) res.editName = await objEdit?.__employee__?.name

    const objCancel = lstMember.find((c: any) => c.poRoleCode === enumData.PORoleCode.Cancel.code)
    if (objCancel) res.cancelId = objCancel.employeeId
    if (objCancel) res.cancelName = await objCancel?.__employee__?.name
    delete res.__members__

    res.lstPaymentProgress = res.__paymentPlan__ || []
    delete res.__paymentPlan__

    res.lstHistory = res.__poHistorys__ || []

    for (const item of res.lstHistory) {
      if (item.__employee__) {
        item.employeeName = item.__employee__.name
      }
      delete item.__employee__

      if (item.__supplier__) {
        item.supplierName = item.__supplier__.name
      }
      delete item.__supplier__

      item.statusCurrentName = dicStatus[item.statusCurrent].name
      item.statusConvertName = dicStatus[item.statusConvert].name
    }
    delete res.__poHistorys__

    res.lstProduct = res.__products__ || []

    for (const product of res.lstProduct) {
      product.serviceName = product.__service__.code
      delete product.__service__
    }
    res.prCode = res.__pr__?.code
    delete res.__products__
    delete res.__contract__
    delete res.__pr__
    delete res.__branch__
    delete res.__childs__
    return res
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const whereCon: any = { companyId: user.companyId, isDeleted: false }
    const order: any = {}
    // if (user.)
    const employee = await this.employeeRepo.find({ where: { userId: user.id } })
    if (!employee[0]) {
      throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    } else {
      if (employee[0].branchId) {
        whereCon.isChild = true
        if (data.where.branchId) whereCon.branchId = employee[0].branchId
      } else {
        whereCon.isChild = false
      }
    }
    if (data.where.contractId) whereCon.contractId = data.where.contractId
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.title) whereCon.title = Like(`%${data.where.title}%`)
    if (data.where.supplierId) whereCon.supplierId = data.where.supplierId
    if (data.where.bidId) whereCon.bidId = data.where.bidId
    if (data.where.status) whereCon.status = data.where.status
    if (data.where.getManager) {
      whereCon.status = In([
        enumData.PurchaseOrderStatus.Open.code,
        enumData.PurchaseOrderStatus.Approved.code,
        enumData.PurchaseOrderStatus.Confirm.code,
        enumData.PurchaseOrderStatus.DeliveryRefuse.code,
        enumData.PurchaseOrderStatus.Delivery.code,
        enumData.PurchaseOrderStatus.Complete.code,
      ])
      order.deliveryDate = 'DESC'
    }
    order.createdAt = 'DESC'
    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.createdAt = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    }

    const lstBranch = await this.employeeRepo.getListBranchView(user)
    if (lstBranch && lstBranch.length > 0) whereCon.branchId = In(lstBranch)

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      order: order,
      skip: data.skip,
      take: data.take,
      relations: {
        supplier: true,
        bid: true,
        contract: true,
        poHistorys: true,
        members: true,
        contractPaymentPlan: true,
        products: { service: { parent: true } },
      },
    })

    const dicStatus: any = {}
    const dicStatusColor: any = {}
    {
      const lstPOStatus = coreHelper.convertObjToArray(enumData.PurchaseOrderStatus)
      lstPOStatus.forEach((c) => (dicStatus[c.code] = c.name))
      lstPOStatus.forEach((c) => (dicStatusColor[c.code] = c.color))
    }
    const dicStatusOrder: any = {}
    const dicStatusOrderColor: any = {}

    {
      const lstPOOrderStatus = coreHelper.convertObjToArray(enumData.PoOrder)
      lstPOOrderStatus.forEach((c) => (dicStatusOrder[c.code] = c.name))
      lstPOOrderStatus.forEach((c) => (dicStatusOrderColor[c.code] = c.color))
    }
    for (const item of res[0]) {
      if (item.deliveryDate) {
        const nowDate = new Date()
        const deliveryDate = new Date(item.deliveryDate)
        nowDate.setHours(0, 0, 0, 0)
        deliveryDate.setHours(0, 0, 0, 0)
        if (nowDate.getTime() === deliveryDate.getTime()) item.deliveryColor = enumData.DeliveryDateStatus.EQUAL.color
        if (nowDate.getTime() > deliveryDate.getTime()) item.deliveryColor = enumData.DeliveryDateStatus.MISS.color
        if (nowDate.getTime() < deliveryDate.getTime()) item.deliveryColor = enumData.DeliveryDateStatus.UP_COMMING.color
      }
      if (item.__bid__) {
        item.bidName = item.__bid__.name
        delete item.__bid__
      }
      if (item.__supplier__) {
        item.supplierName = item.__supplier__.name
      }
      delete item.__supplier__

      if (item.__contract__) {
        item.contractCode = item.__contract__.code
      }
      delete item.__contract__

      if (item.__contractPaymentPlan__) {
        item.contractPaymentPlanName = item.__contractPaymentPlan__.name
        item.contractPaymentPlanPercent = item.__contractPaymentPlan__.percent
      }
      delete item.__contractPaymentPlan__

      item.statusName = dicStatus[item.status]
      item.statusColor = dicStatusColor[item.status]
      item.statusOrderName = dicStatusOrder[item.orderStatus]
      item.statusOrderColor = dicStatusOrderColor[item.orderStatus]

      const poEdit = await this.checkPermissionPOEdit(user, item.id)
      item.isAllowEditPO = poEdit.hasPermission
      const poApproved = await this.checkPermissionPOApproved(user, item.id)
      item.isAllowApprovedPO = poApproved.hasPermission
      const poPayment = await this.checkPermissionPOPayment(user, item.id)
      item.isAllowPaymentPO = poPayment.hasPermission

      const poView = await this.checkPermissionPOView(user, item.id)
      item.isAllowViewPO = poView.hasPermission

      const poCancel = await this.checkPermissionPOCancel(user, item.id)
      item.isAllowCancelPO = poCancel.hasPermission
    }

    return res
  }

  public async findDetail(user: UserDto, dataId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res: any = await this.repo.findOne({
      where: { id: dataId, companyId: user.companyId },
      relations: {
        supplier: true,
        bid: true,
        contract: true,
        poHistorys: true,
        members: true,
        paymentPlan: true,
        products: { service: { parent: true } },
      },
    })
    if (res) {
      let poEdit = await this.checkPermissionPOEdit(user, dataId)
      res.isAllowEditPO = poEdit.hasPermission
      let poApproved = await this.checkPermissionPOApproved(user, dataId)
      res.isAllowApprovedPO = poApproved.hasPermission
      let poPayment = await this.checkPermissionPOPayment(user, dataId)
      res.isAllowPaymentPO = poPayment.hasPermission
      let poView = await this.checkPermissionPOView(user, dataId)
      res.isAllowViewPO = poView.hasPermission

      const poCancel = await this.checkPermissionPOCancel(user, dataId)
      res.isAllowCancelPO = poCancel.hasPermission
    }
    return res
  }

  public async find(user: UserDto, data: { contractId?: string; status?: any; products?: any }) {
    // if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const whereCon: any = { companyId: user.companyId, isDeleted: false }
    if (data.contractId) {
      if (data.contractId == 'isNull') whereCon.contractId = IsNull()
      else whereCon.contractId = data.contractId
    }
    if (data.status) {
      if (typeof data.status == 'string') {
        whereCon.status = data.status
      } else {
        whereCon.status = In(data.status)
      }
    }
    if (data.products) whereCon.products = { service: { id: data.products.serviceId } }
    const res: any[] = await this.repo.find({
      where: whereCon,
      relations: { supplier: true, products: { service: { parent: true } } },
      order: { code: 'ASC' },
    })
    for (const item of res) {
      if (item.supplierId) {
        item.supplierName = item.__supplier__.name
        item.supplierBankName = item.__supplier__.bankname
        item.supplierBankNumber = item.__supplier__.bankNumber
      }
      delete item.__supplier__
    }

    return res
  }

  public async createDataExcel(user: UserDto, data: POCreateExcelDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    return await this.repo.manager.transaction(async (manager) => {
      const poRepo = manager.getRepository(POEntity)
      const objectRepo = manager.getRepository(ObjectEntity)
      const serviceRepo = manager.getRepository(ServiceEntity)
      const poMemberRepo = manager.getRepository(POMemberEntity)
      const employeeRepo = manager.getRepository(EmployeeEntity)
      const bidRepo = manager.getRepository(BidEntity)
      const supplierRepo = manager.getRepository(SupplierEntity)
      const poHistoryRepo = manager.getRepository(POHistoryEntity)
      const poProductRepo = manager.getRepository(POProductEntity)
      const poPaymentProgressRepo = manager.getRepository(PaymentProgressEntity)
      const contractRepo = manager.getRepository(ContractEntity)
      let dataNof = []
      if (data.lstDataTable1 && data.lstDataTable1.length > 0) {
        for (let item of data.lstDataTable1) {
          const purchaseOrder = new POEntity()
          purchaseOrder.companyId = user.companyId
          purchaseOrder.createdBy = user.id
          const employee = await employeeRepo.findOne({
            where: { id: user.employeeId, companyId: user.companyId },
            relations: { branch: true },
          })
          const object = await objectRepo.findOne({ where: { code: item.objectCode, companyId: user.companyId } })
          const serviceLevel1 = await serviceRepo.findOne({ where: { id: item.serviceLevel1, companyId: user.companyId } })
          let codeSC = '00'
          const contractLast = await poRepo.findOne({
            where: {
              code: Like(`%${codeSC}%`),
              companyId: user.companyId,
            },
            order: { code: 'DESC' },
          })
          let sortString = '0'
          if (contractLast) {
            sortString = contractLast.code.substring(0, 4)
          }
          const lastSort = +sortString
          sortString = ('000' + (lastSort + 1)).slice(-4)
          let code = sortString // STT
          code += '/' + new Date().getFullYear().toString().slice(-2) // YY
          if (employee && (await employee.branch)) {
            code += '/' + (await employee.branch).code // XXX
          }
          if (object) {
            code += '.' + object.code
            purchaseOrder.objectId = object.id // ZZZ
          }
          if (serviceLevel1) {
            code += '.' + serviceLevel1.code // AAA
          }
          code += '-' + 'PO'

          const supplier = await supplierRepo.findOne({
            where: {
              code: item.supplierCode,
              companyId: user.companyId,
              isDeleted: false,
            },
          })
          if (!supplier) {
            throw new Error('Nhà cung cấp không tồn tại. Vui lòng kiểm tra lại')
          }
          purchaseOrder.supplierId = supplier.id
          if (item.bidCode) {
            const bid = await bidRepo.findOne({
              where: {
                code: item.bidCode,
                companyId: user.companyId,
                isDeleted: false,
              },
              select: { id: true },
            })
            if (!bid) throw new Error('Nhà cung cấp không tồn tại. Vui lòng kiểm tra lại')

            purchaseOrder.bidId = bid.id
          }
          purchaseOrder.code = code
          purchaseOrder.title = item.title
          purchaseOrder.operator = item.operator
          purchaseOrder.type = item.type
          purchaseOrder.region = item.region
          purchaseOrder.currency = item.currency
          let employeeBranch = await employee.branchId
          if (employeeBranch) purchaseOrder.branchId = employeeBranch
          const list = data.lstDataTable2.filter((x: any) => x.zenListId == item.zenId)
          const sum = list.reduce((sum: any, current: any) => +sum + +current.money, 0)
          purchaseOrder.money = sum
          purchaseOrder.company = item.company
          purchaseOrder.email = item.email
          purchaseOrder.phone = item.phone
          purchaseOrder.deliveryDate = item.deliveryDate
          purchaseOrder.status = enumData.PurchaseOrderStatus.Open.code
          purchaseOrder.description = item.description
          purchaseOrder.paymentPlanType = item.paymentPlanType
          const poEntity = await poRepo.save(purchaseOrder)

          const historyNew = new POHistoryEntity()
          historyNew.companyId = user.companyId
          historyNew.createdBy = user.id
          historyNew.poId = poEntity.id
          historyNew.statusConvert = enumData.PurchaseOrderStatus.Open.code
          historyNew.statusCurrent = enumData.PurchaseOrderStatus.Open.code
          const description = `Nhân viên [${user.username}] - tạo mới PO [${purchaseOrder.code}] - Tạo bằng tính năng upload Excel`
          historyNew.description = description
          historyNew.employeeId = user.employeeId
          await poHistoryRepo.save(historyNew)

          if (item.anotherRoleIds) {
            let arrMember: any = item.anotherRoleIds.split(',')
            for (let member of arrMember) {
              const poMember = new POMemberEntity()
              const employee = await employeeRepo.findOne({
                where: {
                  code: member,
                  companyId: user.companyId,
                  isDeleted: false,
                },
              })
              if (!employee) {
                throw new Error('Mã nhân viên có quyền xem không tồn tại. Vui lòng kiểm tra lại')
              }
              if (employee) {
                poMember.employeeId = employee.id
              }
              poMember.companyId = user.companyId
              poMember.createdBy = user.id
              poMember.poRoleCode = enumData.PORoleCode.View.code
              poMember.description = enumData.PORoleCode.View.description
              poMember.poId = poEntity.id
              await poMemberRepo.save(poMember)
            }
          } else {
            throw new Error('Nhân viên có quyền xem không tồn tại. Vui lòng kiểm tra lại')
          }

          const employeePayment = await employeeRepo.findOne({
            where: {
              code: item.poPaymentId,
              companyId: user.companyId,
              isDeleted: false,
            },
            select: { id: true },
          })
          if (!employeePayment) {
            throw new Error('Mã nhân viên có quyền thanh toán không tồn tại. Vui lòng kiểm tra lại')
          }
          // người thanh toán PO
          let poPaymentPOMember = new POMemberEntity()
          poPaymentPOMember.companyId = user.companyId
          poPaymentPOMember.createdBy = user.id
          poPaymentPOMember.employeeId = employeePayment.id
          poPaymentPOMember.poRoleCode = enumData.PORoleCode.PurchaseOrderPayMent.code
          poPaymentPOMember.description = enumData.PORoleCode.PurchaseOrderPayMent.description
          poPaymentPOMember.poId = poEntity.id
          await poMemberRepo.save(poPaymentPOMember)

          // người thanh toán PO
          let poPaymentPOViewMember = new POMemberEntity()
          poPaymentPOViewMember.companyId = user.companyId
          poPaymentPOViewMember.createdBy = user.id
          poPaymentPOViewMember.employeeId = employeePayment.id
          poPaymentPOViewMember.poRoleCode = enumData.PORoleCode.View.code
          poPaymentPOViewMember.description = enumData.PORoleCode.View.description
          poPaymentPOViewMember.poId = poEntity.id
          await poMemberRepo.save(poPaymentPOViewMember)

          const employeeConfirm = await employeeRepo.findOne({
            where: {
              code: item.confirmId,
              companyId: user.companyId,
              isDeleted: false,
            },
          })
          if (!employeeConfirm) {
            throw new Error('Mã nhân viên có quyền duyệt không tồn tại. Vui lòng kiểm tra lại')
          }

          // người duyệt PO
          let poConFirmMember = new POMemberEntity()
          poConFirmMember.companyId = user.companyId
          poConFirmMember.createdBy = user.id
          poConFirmMember.employeeId = employeeConfirm.id
          poConFirmMember.poRoleCode = enumData.PORoleCode.Confirm.code
          poConFirmMember.description = enumData.PORoleCode.Confirm.description
          poConFirmMember.poId = poEntity.id
          await poMemberRepo.save(poConFirmMember)

          // người duyệt PO
          let poConFirmViewMember = new POMemberEntity()
          poConFirmViewMember.companyId = user.companyId
          poConFirmViewMember.createdBy = user.id
          poConFirmViewMember.employeeId = employeeConfirm.id
          poConFirmViewMember.poRoleCode = enumData.PORoleCode.View.code
          poConFirmViewMember.description = enumData.PORoleCode.View.description
          poConFirmViewMember.poId = poEntity.id
          await poMemberRepo.save(poConFirmViewMember)

          const employeeEditPo = await employeeRepo.findOne({
            where: {
              code: item.editPOId,
              companyId: user.companyId,
              isDeleted: false,
            },
          })
          if (!employeeEditPo) {
            throw new Error('Mã nhân viên có quyền chỉnh sửa không tồn tại. Vui lòng kiểm tra lại')
          }

          // người chỉnh sửa PO
          let poEditPOMember = new POMemberEntity()
          poEditPOMember.companyId = user.companyId
          poEditPOMember.createdBy = user.id
          poEditPOMember.employeeId = employeeEditPo.id
          poEditPOMember.poRoleCode = enumData.PORoleCode.Edit.code
          poEditPOMember.description = enumData.PORoleCode.Edit.description
          poEditPOMember.poId = poEntity.id
          await poMemberRepo.save(poEditPOMember)
          // người chỉnh sửa PO
          let poEditPOEditMember = new POMemberEntity()
          poEditPOEditMember.companyId = user.companyId
          poEditPOEditMember.createdBy = user.id
          poEditPOEditMember.employeeId = employeeEditPo.id
          poEditPOEditMember.poRoleCode = enumData.PORoleCode.View.code
          poEditPOEditMember.description = enumData.PORoleCode.View.description
          poEditPOEditMember.poId = poEntity.id
          await poMemberRepo.save(poEditPOEditMember)

          const employeeCancelPo = await employeeRepo.findOne({
            where: {
              code: item.cancelPOId,
              companyId: user.companyId,
              isDeleted: false,
            },
          })
          if (!employeeCancelPo) {
            throw new Error('Mã nhân viên có quyền chỉnh sửa không tồn tại. Vui lòng kiểm tra lại')
          }

          // người hủy PO
          let poCancelPOMember = new POMemberEntity()
          poCancelPOMember.companyId = user.companyId
          poCancelPOMember.createdBy = user.id
          poCancelPOMember.employeeId = employeeCancelPo.id
          poCancelPOMember.poRoleCode = enumData.PORoleCode.Cancel.code
          poCancelPOMember.description = enumData.PORoleCode.Cancel.description
          poCancelPOMember.poId = poEntity.id
          await poMemberRepo.save(poCancelPOMember)
          // người hủy  PO
          let poCancelViewMember = new POMemberEntity()
          poCancelViewMember.companyId = user.companyId
          poCancelViewMember.createdBy = user.id
          poCancelViewMember.employeeId = employeeCancelPo.id
          poCancelViewMember.poRoleCode = enumData.PORoleCode.View.code
          poCancelViewMember.description = enumData.PORoleCode.View.description
          poCancelViewMember.poId = poEntity.id
          await poMemberRepo.save(poCancelViewMember)

          for (let detail of data.lstDataTable2) {
            if (detail.zenListId === item.zenId) {
              if (detail.price === 0 || detail.price < 0) {
                throw new Error('Giá tiền phải lớn hơn 0. Vui lòng kiểm tra lại')
              }

              if (detail.unit === null || detail.unit === undefined) {
                throw new Error('Đơn vị tính không được bỏ trống. Vui lòng kiểm tra lại')
              }

              if (detail.quantity === 0 || detail.quantity < 0) {
                throw new Error('Số lượng phải lớn hơn 0. Vui lòng kiểm tra lại')
              }

              if (detail.name === null || detail.name === undefined) {
                throw new Error('Tên hàng hóa	không được bỏ trống. Vui lòng kiểm tra lại')
              }

              const itemCode = coreHelper.codeDefaultItem()
              const poPurchaseProduct = new POProductEntity()
              poPurchaseProduct.companyId = user.companyId
              poPurchaseProduct.createdBy = user.id
              poPurchaseProduct.poId = poEntity.id
              poPurchaseProduct.description = detail.descriptionProduct
              poPurchaseProduct.name = detail.name
              poPurchaseProduct.note = detail.note
              poPurchaseProduct.unit = detail.unit
              poPurchaseProduct.quantity = detail.quantity
              poPurchaseProduct.price = detail.price
              poPurchaseProduct.money = detail.money
              poPurchaseProduct.serviceId = detail.serviceId
              poPurchaseProduct.itemCode = itemCode
              await poProductRepo.save(poPurchaseProduct)
            }
          }
          const paymentPlane = data.lstDataTable3.filter((x: { zenListDetailId: any }) => x.zenListDetailId == item.zenId)
          if (paymentPlane && paymentPlane.length > 0) {
            const sumTotal = paymentPlane.reduce((sum: any, current: { percent: any }) => +sum + +current.percent, 0)
            if (sumTotal > 100) {
              throw new Error('Số phần trăm không thể vượt quá 100')
            }
            for (let plant of paymentPlane) {
              const poPaymentPlan = new PaymentProgressEntity()
              poPaymentPlan.companyId = user.companyId
              poPaymentPlan.createdBy = user.id
              poPaymentPlan.poId = poEntity.id
              poPaymentPlan.percent = plant.percent
              poPaymentPlan.time = new Date(plant.time)
              poPaymentPlan.description = plant.note_percent
              poPaymentPlan.name = plant.namePayment
              poPaymentPlan.money = (plant.percent * +purchaseOrder.money) / 100
              await poPaymentProgressRepo.save(poPaymentPlan)
              await poRepo.update(poEntity.id, {
                paymentPlanType: enumData.ContractTypePo.NonContract.code,
                updatedBy: user.id,
              })
            }
          }
        }
      }

      return { message: 'PO Đã Được Tạo Excel Thành Công.', data: dataNof, success: true }
    })
  }

  /** Check quyền sửa thông tin PO */
  async checkPermissionPOEdit(user: UserDto, poId: string) {
    let result = false
    let message = ''
    const pId = await this.repo.findOne({ where: { id: poId, companyId: user.companyId } })
    if (pId) {
      const objCheck = await this.repo.manager.getRepository(POMemberEntity).findOne({
        where: {
          employeeId: user.employeeId,
          poId,
          poRoleCode: enumData.PORoleCode.Edit.code,
        },
      })
      if (!objCheck) {
        message = 'Bạn không có quyền chỉnh sửa thông tin PO.'
      } else result = true
    }
    return { hasPermission: result, message }
  }

  /** Check quyền duyệt PO */
  async checkPermissionPOApproved(user: UserDto, poId: string) {
    let result = false
    let message = ''
    const pId = await this.repo.findOne({ where: { id: poId, companyId: user.companyId } })
    if (pId) {
      const objCheck = await this.repo.manager.getRepository(POMemberEntity).findOne({
        where: {
          employeeId: user.employeeId,
          poId,
          companyId: user.companyId,
          poRoleCode: enumData.PORoleCode.Confirm.code,
        },
      })
      if (!objCheck) {
        message = 'Bạn không có quyền duyệt PO.'
      } else result = true
    }
    return { hasPermission: result, message }
  }

  /** Kiểm tra quyền sửa thanh toán po */
  async checkPermissionPOPayment(user: UserDto, poId: string) {
    let objCheck = null
    let message = ''
    const objPO = await this.repo.findOne({ where: { id: poId, companyId: user.companyId } })
    if (objPO) {
      objCheck = await this.repo.manager.getRepository(POMemberEntity).findOne({
        where: { employeeId: user.employeeId, poId, companyId: user.companyId, poRoleCode: enumData.PORoleCode.PurchaseOrderPayMent.code },
      })
      if (!objCheck) {
        message = 'Bạn không có quyền thanh toán PO.'
      }
    }

    return { hasPermission: !!objCheck, message }
  }

  /** Check quyền xem PO */
  async checkPermissionPOView(user: UserDto, poId: string) {
    let result = false
    let message = ''
    const pId = await this.repo.findOne({ where: { id: poId, companyId: user.companyId } })
    if (pId) {
      const objCheck = await this.repo.manager.getRepository(POMemberEntity).findOne({
        where: {
          employeeId: user.employeeId,
          poId,
          companyId: user.companyId,
          poRoleCode: enumData.PORoleCode.View.code,
        },
      })
      if (!objCheck) message = 'Bạn không có quyền xem PO.'
      else result = true
    }

    return { hasPermission: result, message }
  }

  /** Check quyền Hủy PO */
  async checkPermissionPOCancel(user: UserDto, poId: string) {
    let result = false
    let message = ''
    const pId = await this.repo.findOne({ where: { id: poId, companyId: user.companyId } })
    if (pId) {
      const objCheck = await this.repo.manager.getRepository(POMemberEntity).findOne({
        where: {
          employeeId: user.employeeId,
          poId,
          poRoleCode: enumData.PORoleCode.Cancel.code,
        },
      })
      if (!objCheck) {
        message = 'Bạn không có quyền Hủy PO.'
      } else result = true
    }
    return { hasPermission: result, message }
  }

  public async updateStatusConfirm(user: UserDto, data: { id: string }) {
    if (!user.supplierId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    return await this.repo.manager.transaction(async (manager) => {
      const poRepo = manager.getRepository(POEntity)
      const poHistoryRepo = manager.getRepository(POHistoryEntity)

      const entity = await poRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      if (entity.supplierId !== user.supplierId)
        throw new Error(`Bạn không phải là nhà cung cấp của PO này nên không thể xác nhận giao hàng. Vui lòng kiểm tra lại`)

      if (entity.status !== enumData.PurchaseOrderStatus.Approved.code)
        throw new Error(`Trạng thái hiện tại không thể xác nhận được PO từ nhà cung cấp. Vui lòng kiểm tra lại`)

      const historyNew = new POHistoryEntity()
      historyNew.companyId = user.companyId
      historyNew.createdBy = user.id
      historyNew.poId = entity.id
      let lstStatus = coreHelper.convertObjToArray(enumData.PurchaseOrderStatus)
      const statusCurrent = lstStatus.find((s) => s.code === entity.status)
      historyNew.statusCurrent = entity.status
      historyNew.statusConvert = enumData.PurchaseOrderStatus.Confirm.code
      const description = `Nhà cung cấp [${user.username}]  - vừa cập nhật [Xác Nhận] PO có mã [${entity.code}] - Trạng thái cũ [${statusCurrent.name}] - Trạng thái mới [${enumData.PurchaseOrderStatus.Confirm.name}]`
      historyNew.description = description
      historyNew.supplierId = user.supplierId
      await poHistoryRepo.save(historyNew)

      entity.status = enumData.PurchaseOrderStatus.Confirm.code
      await poRepo.save(entity)

      return { message: UPDATE_ACTIVE_SUCCESS, success: true }
    })
  }

  public async updateStatusDelivery(user: UserDto, data: { id: string }) {
    if (!user.supplierId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    return this.repo.manager.transaction(async (manager) => {
      const poRepo = manager.getRepository(POEntity)
      const poHistoryRepo = manager.getRepository(POHistoryEntity)

      const entity = await poRepo.findOne({ where: { id: data.id, companyId: user.companyId } })

      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      if (entity.status !== enumData.PurchaseOrderStatus.Confirm.code)
        throw new Error(`Trạng thái hiện tại không thể xác nhận giao hàng. Vui lòng kiểm tra lại`)

      if (entity.supplierId !== user.supplierId)
        throw new Error(`Bạn không phải là nhà cung cấp của PO này nên không thể xác nhận giao hàng. Vui lòng kiểm tra lại`)

      const historyNew = new POHistoryEntity()
      historyNew.companyId = user.companyId
      historyNew.createdBy = user.id
      historyNew.poId = entity.id
      let lstStatus = coreHelper.convertObjToArray(enumData.PurchaseOrderStatus)
      const statusCurrent = lstStatus.find((s) => s.code === entity.status)
      historyNew.statusCurrent = entity.status

      const description = `Nhà cung cấp [${user.username}] - cập nhật [Xác Nhận Giao Hàng] cho PO có mã [${entity.code}] - Trạng thái cũ [${statusCurrent.name}] - Trạng thái mới [${enumData.PurchaseOrderStatus.Delivery.name}]`
      historyNew.statusConvert = enumData.PurchaseOrderStatus.Delivery.code
      historyNew.description = description
      historyNew.supplierId = user.supplierId ? user.supplierId : ''
      await poHistoryRepo.save(historyNew)
      entity.status = enumData.PurchaseOrderStatus.Delivery.code
      await poRepo.save(entity)

      return { message: UPDATE_ACTIVE_SUCCESS, success: true }
    })
  }

  public async updateStatusApproved(user: UserDto, data: { id: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionPOApproved(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    return await this.repo.manager.transaction(async (manager) => {
      const poRepo = manager.getRepository(POEntity)
      const poHistoryRepo = manager.getRepository(POHistoryEntity)

      const entity = await poRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      if (entity.status !== enumData.PurchaseOrderStatus.Open.code) throw new Error(`Trạng thái hiện tại không thể Duyệt PO. Vui lòng kiểm tra lại`)

      //#region add POHistory
      const historyNew = new POHistoryEntity()
      historyNew.companyId = user.companyId
      historyNew.createdBy = user.id
      historyNew.poId = entity.id
      let lstStatus = coreHelper.convertObjToArray(enumData.PurchaseOrderStatus)
      const statusCurrent = lstStatus.find((s) => s.code === entity.status)
      historyNew.statusCurrent = entity.status
      const description = `Nhân viên [${user.username}] - cập nhật [Duyệt PO] cho PO có mã [${entity.code}] - Trạng thái cũ [${statusCurrent.name}] - Trạng thái mới [${enumData.PurchaseOrderStatus.Approved.name}]`

      historyNew.statusConvert = enumData.PurchaseOrderStatus.Approved.code
      historyNew.description = description
      historyNew.employeeId = user.employeeId
      await poHistoryRepo.save(historyNew)
      //#endregion

      entity.status = enumData.PurchaseOrderStatus.Approved.code
      await poRepo.save(entity)

      return { message: UPDATE_ACTIVE_SUCCESS, success: true }
    })
  }

  public async updateStatusComplete(user: UserDto, data: { id: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionPOApproved(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    return await this.repo.manager.transaction(async (manager) => {
      const poRepo = manager.getRepository(POEntity)
      const poHistoryRepo = manager.getRepository(POHistoryEntity)

      const entity = await poRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      if (entity.status !== enumData.PurchaseOrderStatus.Delivery.code)
        throw new Error(`Trạng thái hiện tại không thể xác nhận hoàn thành PO. Vui lòng kiểm tra lại`)

      const historyNew = new POHistoryEntity()
      historyNew.companyId = user.companyId
      historyNew.createdBy = user.id
      historyNew.poId = entity.id
      historyNew.statusCurrent = entity.status
      let lstStatus = coreHelper.convertObjToArray(enumData.PurchaseOrderStatus)
      const statusCurrent = lstStatus.find((s) => s.code === entity.status)

      const description = `Nhân viên [${user.username}] - cập nhật [Hoàn Thành] cho PO có mã [${entity.code}] - Trạng thái cũ [${statusCurrent.name}] - Trạng thái mới [${enumData.PurchaseOrderStatus.Complete.name}]`
      historyNew.statusConvert = enumData.PurchaseOrderStatus.Complete.code
      historyNew.description = description
      historyNew.employeeId = user.employeeId
      await poHistoryRepo.save(historyNew)

      entity.status = enumData.PurchaseOrderStatus.Complete.code
      entity.updatedBy = user.id
      await poRepo.save(entity)

      return { message: UPDATE_ACTIVE_SUCCESS, success: true }
    })
  }

  public async updateStatusRefuse(user: UserDto, data: { id: string; reason?: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionPOApproved(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    return this.repo.manager.transaction(async (manager) => {
      const poRepo = manager.getRepository(POEntity)
      const poHistoryRepo = manager.getRepository(POHistoryEntity)

      const entity = await poRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      if (entity.status !== enumData.PurchaseOrderStatus.Delivery.code)
        throw new Error(`Trạng thái hiện tại không thể xác nhận từ chối nhận hàng. Vui lòng kiểm tra lại`)

      // entity.reason = data.reason
      const historyNew = new POHistoryEntity()
      historyNew.companyId = user.companyId
      historyNew.createdBy = user.id
      historyNew.poId = entity.id

      let description = `Nhân viên [${user.username}] - cập nhật trạng thái [Từ Chối Nhận Hàng] Cho PO có mã [${entity.code}] - Lý do: ${data.reason} `
      if (data.reason) {
        description = `${description} - Lý do: ${data.reason}`
      }
      historyNew.statusConvert = entity.status
      historyNew.statusCurrent = entity.status
      historyNew.description = description
      historyNew.employeeId = user.employeeId
      await poHistoryRepo.save(historyNew)
      if (data.reason) {
        entity.reason = data.reason
      }
      entity.status = enumData.PurchaseOrderStatus.Cancel.code
      entity.orderStatus = enumData.PoOrder.WAIT_REPLY.code
      entity.updatedBy = user.id
      await poRepo.save(entity)

      return { message: UPDATE_ACTIVE_SUCCESS, success: true }
    })
  }

  public async updateStatusCancel(user: UserDto, data: { id: string; reason?: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    return this.repo.manager.transaction(async (manager) => {
      const poRepo = manager.getRepository(POEntity)
      const poHistoryRepo = manager.getRepository(POHistoryEntity)

      const entity = await poRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      if (entity.status !== enumData.PurchaseOrderStatus.Open.code && entity.status !== enumData.PurchaseOrderStatus.DeliveryRefuse.code)
        throw new Error(`Trạng thái hiện tại không thể [Hủy PO]. Vui lòng kiểm tra lại`)

      const historyNew = new POHistoryEntity()
      historyNew.companyId = user.companyId
      historyNew.createdBy = user.id
      historyNew.poId = entity.id
      let lstStatus = coreHelper.convertObjToArray(enumData.PurchaseOrderStatus)
      const statusCurrent = lstStatus.find((s) => s.code === entity.status)
      historyNew.statusConvert = enumData.PurchaseOrderStatus.Cancel.code
      historyNew.statusCurrent = entity.status

      let description = `Nhân viên [${user.username}] - cập nhật [Hủy PO] cho PO có mã [${entity.code}] - Trạng thái cũ [${statusCurrent.name}] - Trạng thái mới [${enumData.PurchaseOrderStatus.Cancel.name}]`
      if (data.reason) {
        description = `${description} - Lý do: ${data.reason}`
      }
      historyNew.description = description
      historyNew.employeeId = user.employeeId
      await poHistoryRepo.save(historyNew)
      entity.status = enumData.PurchaseOrderStatus.Cancel.code

      entity.updatedBy = user.id
      await poRepo.save(entity)

      return { message: UPDATE_ACTIVE_SUCCESS, success: true }
    })
  }

  /** Lịch sử PO */
  public async paginationHistory(user: UserDto, data: PaginationDto) {
    if (!data.where.poId) throw new Error('Vui lòng chọn PO trước!')

    const whereCon: any = { poId: data.where.poId, companyId: user.companyId }
    return await this.repo.manager.getRepository(POHistoryEntity).findAndCount({
      where: whereCon,
      relations: { po: true, employee: true },
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })
  }

  /** Danh sách PO theo nhà cung cấp */
  public async paginationSupplier(user: UserDto, data: PaginationDto) {
    if (!user.supplierId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const whereCon: any = { companyId: user.companyId }
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.title) whereCon.title = Like(`%${data.where.title}%`)
    whereCon.supplierId = user.supplierId
    if (data.where.status) whereCon.status = data.where.status
    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.createdAt = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    }

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
      relations: {
        bid: true,
        contract: true,
      },
    })

    const dicStatus: any = {}
    const dicStatusColor: any = {}
    {
      const lstPOStatus = coreHelper.convertObjToArray(enumData.PurchaseOrderStatus)
      lstPOStatus.forEach((c) => (dicStatus[c.code] = c.name))
      lstPOStatus.forEach((c) => (dicStatusColor[c.code] = c.color))
    }
    const dicStatusOrder: any = {}
    const dicStatusOrderColor: any = {}
    {
      const lstPOOrderStatus = coreHelper.convertObjToArray(enumData.PoOrder)
      lstPOOrderStatus.forEach((c) => (dicStatusOrder[c.code] = c.name))
      lstPOOrderStatus.forEach((c) => (dicStatusOrderColor[c.code] = c.color))
    }
    for (const item of res[0]) {
      if (item.__bid__) {
        item.bidName = item.__bid__.name
        delete item.__bid__
      }

      if (item.__contract__) {
        item.contractCode = item.__contract__.code
      }
      delete item.__contract__

      item.statusOrderName = dicStatusOrder[item.orderStatus]
      item.statusOrderColor = dicStatusOrderColor[item.orderStatus]
      item.statusName = dicStatus[item.status]
      item.statusColor = dicStatusColor[item.status]
    }

    return res
  }

  public async updateStatusSupplierRefuse(user: UserDto, data: { id: string; reason?: string }) {
    if (!user.supplierId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionPOApproved(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    return this.repo.manager.transaction(async (manager) => {
      const poRepo = manager.getRepository(POEntity)
      const poHistoryRepo = manager.getRepository(POHistoryEntity)

      const entity = await poRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      if (entity.status !== enumData.PurchaseOrderStatus.Approved.code && entity.status !== enumData.PurchaseOrderStatus.Confirm.code)
        throw new Error(`Trạng thái hiện tại không thể xác nhận Từ Chối Giao Hàng nhận hàng. Vui lòng kiểm tra lại`)

      if (entity.supplierId !== user.supplierId)
        throw new Error(`Bạn không phải là nhà cung cấp của PO này nên không thể xác nhận từ chối giao hàng. Vui lòng kiểm tra lại`)

      const historyNew = new POHistoryEntity()
      historyNew.companyId = user.companyId
      historyNew.createdBy = user.id
      historyNew.poId = entity.id
      historyNew.statusCurrent = entity.status

      let lstStatus = coreHelper.convertObjToArray(enumData.PurchaseOrderStatus)
      const statusCurrent = lstStatus.find((s) => s.code === entity.status)
      let description = `Nhà cung cấp [${user.username}] - cập nhật trạng thái [Từ Chối Giao Hàng] Cho PO có mã [${entity.code}] - Trạng thái cũ [${statusCurrent.name}] - Trạng thái mới [${enumData.PurchaseOrderStatus.DeliveryRefuse.name}]`
      if (data.reason) {
        description = `${description} - Lý do: ${data.reason}`
      }
      historyNew.statusConvert = enumData.PurchaseOrderStatus.DeliveryRefuse.code
      historyNew.description = description
      historyNew.supplierId = user.supplierId
      await poHistoryRepo.save(historyNew)

      entity.status = enumData.PurchaseOrderStatus.DeliveryRefuse.code
      if (data.reason) {
        entity.reason = data.reason
      }

      entity.updatedBy = user.id
      await poRepo.save(entity)

      return { message: UPDATE_ACTIVE_SUCCESS, success: true }
    })
  }

  public async findDetailPo(user: UserDto, data: { id: string }) {
    if (!user.supplierId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res: any = await this.repo.findOne({
      where: { id: data.id, companyId: user.companyId, supplierId: user.supplierId },
      relations: {
        bid: true,
        contract: true,
        poHistorys: { employee: true, supplier: true },
        paymentPlan: true,
        products: { service: { parent: true } },
        //
      },
    })
    let bid = await res.bid
    if (bid) {
      res.bidName = bid.name
      res.bidCode = bid.code
    }
    let contract = await res.contract
    if (contract) {
      res.contractName = contract.name
      res.contractCode = contract.code
    }

    let lstProducts = await res.products
    if (lstProducts.length > 0) {
      for (let item of lstProducts) {
        let service = await item.__service__
        if (service) {
          item.serviceName = service.name
          item.serviceCode = service.code
        }
        delete item.__service__
      }
    }

    res.lstProducts = lstProducts
    let lstPaymentPlan = await res.paymentPlan
    if (lstPaymentPlan && lstPaymentPlan.length > 0) {
      const lstPayment = coreHelper.convertObjToArray(enumData.PaymentProgressStatus)
      for (let item of lstPaymentPlan) {
        const payment = lstPayment.find((x) => x.code === item.paymentStatus)
        if (payment) {
          item.paymentName = payment.name
        }
      }
    }
    res.lstPaymentPlan = lstPaymentPlan

    const dicStatus: any = {}
    const dicStatusColor: any = {}
    {
      const lstPOStatus = coreHelper.convertObjToArray(enumData.PurchaseOrderStatus)
      lstPOStatus.forEach((c) => (dicStatus[c.code] = c.name))
      lstPOStatus.forEach((c) => (dicStatusColor[c.code] = c.color))
    }

    res.statusName = dicStatus[res.status]
    res.statusColor = dicStatusColor[res.status]

    const lstPayment = coreHelper.convertObjToArray(enumData.ContractTypePo)
    const payment = lstPayment.find((x) => x.code === res.paymentPlanType)
    if (payment) {
      res.paymentName = payment.name
    }

    let lstHistory = await res.poHistorys
    if (lstHistory.length > 0) {
      for (let item of lstHistory) {
        let employee = await item.__employee__
        if (employee) {
          item.createdByName = employee.name
        }
        let supplier = await item.__supplier__
        if (supplier) {
          item.createdByName = supplier.name
        }

        item.statusCurrentName = dicStatus[item.statusCurrent]
        item.statusConvertName = dicStatus[item.statusConvert]
      }
    }
    lstHistory.sort((a: any, b: any) => (a.createdAt > b.createdAt ? 1 : -1))
    res.lstHistory = lstHistory

    const company = await this.settingStringRepo.findOne({
      where: {
        code: res.company,
        isDeleted: false,
        type: enumData.SettingStringType.company,
      },
    })
    if (company) {
      res.companyName = company.name
    }

    delete res.__bid__
    delete res.__contract__
    delete res.__poHistorys__
    delete res.__products__
    delete res.__paymentPlan__
    return res
  }

  public async findContractPO(user: UserDto, data: { poId: string }) {
    if (!data.poId) throw new Error('Vui lòng chọn PO trước!')
    const res: any = await this.repo.findOne({
      where: { id: data.poId },
      relations: {
        contract: true,
      },
    })
    if (!res) throw new Error('PO không tồn tại!')
    let contractId: string
    if (res.__contract__) {
      contractId = res.__contract__.id
      return { contractId: contractId }
    } else {
      return null
    }
  }

  public async loadPoProduct(user: UserDto, data: { poId: string }) {
    const whereCon: any = {
      poId: data.poId,
      isDeleted: false,
    }
    const res: any = await this.poProductRepo.find({
      where: whereCon,
    })

    res.forEach((item: any) => {
      if (item?.__material__) {
        const materialCode = item?.__material__.code
        item.materialCode = materialCode
      }
    })

    return res
  }

  public async loadListPoContract(user: UserDto, data: { contractId: string }) {
    // if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const whereCon: any = {
      contractId: data.contractId,
      isDeleted: false,
      // status: enumData.PurchaseOrderStatus.Approved.code,
    }
    const res: any = await this.repo.find({
      where: whereCon,
    })
    return res
  }

  public async loadPoBySupplier(user: UserDto, data: { status?: any }) {
    if (!user.supplierId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    // let whereCon: any = { isDeleted: false, supplierId: user.supplierId }
    let whereCon: any = { isDeleted: false }

    const res: any[] = await this.repo.find({
      where: whereCon,
    })

    return res
  }
}
