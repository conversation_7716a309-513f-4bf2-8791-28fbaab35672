import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm'
import { BidEntity } from './bid.entity'
import { BidSupplierTechValueEntity } from './bidSupplierTechValue.entity'
import { BidTechListDetailEntity } from './bidTechListDetail.entity'
import { MaterialEntity } from './material.entity'

@Entity('bid_tech')
export class BidTechEntity extends BaseEntity {
  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** <PERSON>ó bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  /** Cách tính điểm theo loại càng cao càng tốt */
  @Column({
    nullable: false,
    default: true,
  })
  isCalUp: boolean

  /** Kiểu dữ liệu: string - number - cal. Nếu cal thì cho phép tạo công thức con*/
  @Column({
    nullable: false,
    default: 'string',
  })
  type: string

  /** % tỉ trọng */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  percent: number

  /** % điều kiện đạt tỉ trọng */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  percentRule: number

  /** % điều kiện liệt tỉ trọng khi tính theo chiều giảm dần */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  percentDownRule: number

  /** Cấp độ */
  @Column({
    nullable: false,
    default: 1,
  })
  level: number

  /** Mô tả */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Id của công thức cha */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  parentId: string
  /** Cha */
  @ManyToOne(() => BidTechEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: BidTechEntity

  /** Con - 1 công thức sẽ có thể có nhiều con */
  @OneToMany(() => BidTechEntity, (p) => p.parent)
  childs: Promise<BidTechEntity[]>

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @Column({
    nullable: true,
  })
  scoreDLC: number

  /** Giá trị nhỏ nhất */
  @Column({
    nullable: true,
  })
  requiredMin: number

  /** Thuộc tính của tiêu chí thể hệ Doanh nghiệp sẽ được highlight màu xanh nếu đạt giá trị X khi xếp hạng năng lực */
  @Column({
    nullable: false,
    default: false,
  })
  isHighlight: boolean

  @Column({
    nullable: true,
  })
  hightlightValue: number

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.techs)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  /** Con - 1 công thức sẽ có thể có nhiều detail list */
  @OneToMany(() => BidTechListDetailEntity, (p) => p.bidTech)
  bidTechListDetails: Promise<BidTechListDetailEntity[]>

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => BidSupplierTechValueEntity, (p) => p.bidTech)
  bidSupplierTechValue: Promise<BidSupplierTechValueEntity[]>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  itemId: string

  @ManyToOne(() => MaterialEntity, (p) => p.techs)
  @JoinColumn({ name: 'itemId', referencedColumnName: 'id' })
  item: Promise<MaterialEntity>
}
