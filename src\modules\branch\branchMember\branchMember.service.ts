import { ConflictException, Injectable } from '@nestjs/common'
import { PaginationDto, UserDto } from '../../../dto'
import { BranchMemberRepository } from '../../../repositories'
import { CREATE_SUCCESS, enumData, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../../constants'
import { BranchMemberEntity } from '../../../entities'
import { BranchMemberCreateDto, BranchMemberUpdateDto } from './dto'
import { coreHelper } from '../../../helpers'

@Injectable()
export class BranchMemberService {
  constructor(private readonly repo: BranchMemberRepository) {}

  public async createData(user: UserDto, data: BranchMemberCreateDto) {
    const dicTypeName: any = {}
    {
      const lstType = coreHelper.convertObjToArray(enumData.BranchMember)
      lstType.forEach((c) => (dicTypeName[c.code] = c.name))
    }

    const objCheck = await this.repo.findOne({
      where: { branchId: data.branchId, companyId: user.companyId, employeeId: data.employeeId },
      select: { id: true, type: true },
    })
    if (objCheck) throw new ConflictException(`Nhân viên đang là ${dicTypeName[objCheck.type]} của chi nhánh, không thể thêm vai trò mới!`)

    const newEntity = new BranchMemberEntity()
    newEntity.companyId = user.companyId
    newEntity.createdBy = user.id
    newEntity.branchId = data.branchId
    newEntity.employeeId = data.employeeId
    newEntity.type = data.type
    await newEntity.save()

    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: BranchMemberUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    const dicTypeName: any = {}
    {
      const lstType = coreHelper.convertObjToArray(enumData.BranchMember)
      lstType.forEach((c) => (dicTypeName[c.code] = c.name))
    }

    if (entity.employeeId != data.employeeId) {
      const objCheck: any = await this.repo.findOne({
        where: { branchId: entity.branchId, companyId: user.companyId, employeeId: data.employeeId },
        select: { id: true, type: true, employee: { name: true } },
      })
      if (objCheck) {
        throw new ConflictException(
          `Nhân viên ${objCheck.__employee__.name} đang là ${dicTypeName[objCheck.type]} của chi nhánh, không thể chỉnh sửa!`,
        )
      }
    }

    entity.employeeId = data.employeeId
    entity.type = data.type
    entity.updatedBy = user.id
    await entity.save()

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    if (!data.where.branchId) throw new Error('Không thể xác định được chi nhánh!')
    const whereCon: any = { branchId: data.where.branchId, companyId: user.companyId }
    if (data.where.employeeId) whereCon.employeeId = data.where.employeeId
    if (data.where.type) whereCon.type = data.where.type
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { employee: true },
      order: { type: 'ASC', employee: { name: 'ASC' } },
    })
    if (res[0].length == 0) return res

    const dicTypeName: any = {}
    {
      const lstType = coreHelper.convertObjToArray(enumData.BranchMember)
      lstType.forEach((c) => (dicTypeName[c.code] = c.name))
    }

    for (const item of res[0]) {
      item.employeeCode = item.__employee__.code
      item.employeeName = item.__employee__.name
      delete item.__employee__

      item.typeName = dicTypeName[item.type]
    }

    return res
  }

  public async updateIsDelete(data: { id: string }, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }
}
