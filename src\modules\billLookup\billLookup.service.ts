import { Injectable } from '@nestjs/common'
import { v4 as uuidv4 } from 'uuid'
import { CREATE_SUCCESS, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { Like } from 'typeorm'
import { PaginationDto, UserDto } from '../../dto'
import { BillLookupEntity } from '../../entities'
import { BillLookupCreateDto, BillLookupUpdateDto } from './dto'
import { BillLookupRepository } from '../../repositories'

@Injectable()
export class BillLookupService {
  constructor(private readonly repo: BillLookupRepository) {}

  public async find(user: UserDto) {
    const whereCon: any = { isDeleted: false }
    return await this.repo.find({ where: whereCon, order: { code: 'ASC' } })
  }

  async codeDefault() {
    const code = `TC`
    const objData = await this.repo.findOne({
      where: { code: Like(`%${code}%`) },
      order: { code: 'DESC' },
    })
    let sortString = '0000'
    if (objData) {
      sortString = objData.code.substring(code.length, code.length + 4)
    }
    const lastSort = parseInt(sortString, 10)
    sortString = ('0000' + (lastSort + 1)).slice(-4)

    return code + sortString
  }

  public async createData(user: UserDto, data: BillLookupCreateDto) {
    const newCode = await this.codeDefault()
    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BillLookupEntity)
      const newBillLookup = new BillLookupEntity()
      newBillLookup.id = uuidv4()
      newBillLookup.companyId = user.companyId
      newBillLookup.createdBy = user.id
      newBillLookup.code = newCode
      newBillLookup.name = data.name
      newBillLookup.link = data.link
      newBillLookup.abbreviation = data.abbreviation

      newBillLookup.description = data.description
      newBillLookup.createdAt = new Date()
      await repo.insert(newBillLookup)

      return { message: CREATE_SUCCESS }
    })
  }

  public async updateData(user: UserDto, data: BillLookupUpdateDto) {
    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BillLookupEntity)

      const entity = await repo.findOne({ where: { id: data.id } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      entity.name = data.name
      entity.link = data.link
      entity.abbreviation = data.abbreviation
      entity.description = data.description
      entity.updatedBy = user.id
      entity.companyId = user.companyId
      entity.updatedAt = new Date()
      await repo.save(entity)
      return { message: UPDATE_SUCCESS }
    })
  }

  /** Hàm phân trang */
  public async pagination(user: UserDto, data: PaginationDto, lan?: string) {
    const whereCon: any = {}

    if (data.where?.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where?.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where?.code) whereCon.code = Like(`%${data.where.code}%`)

    const res: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    })

    return res
  }

  public async updateIsDelete(user: UserDto, data: { id: string }) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id, updatedAt: new Date() })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async findDetail(user: UserDto, data: { id: string }) {
    const res: any = await this.repo.findOne({
      where: { id: data.id },
    })
    return res
  }

  /** Hàm cập nhật trạng thái isDeleted */
  public async updateActiveStatus(user: UserDto, data: { id: string }) {
    const found = await this.repo.findOne({ where: { id: data.id } })
    if (!found) throw new Error(ERROR_NOT_FOUND_DATA)

    return this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BillLookupEntity)
      found.isDeleted = !found.isDeleted
      found.updatedAt = new Date()
      found.updatedBy = user.id
      await repo.save(found)
      return { message: UPDATE_SUCCESS }
    })
  }
}
