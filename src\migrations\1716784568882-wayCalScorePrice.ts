import { MigrationInterface, QueryRunner } from 'typeorm'

export class wayCalScorePrice1716784568882 implements MigrationInterface {
  name = 'wayCalScorePrice1716784568882'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`service\` ADD \`wayCalScorePrice\` varchar(50) NULL DEFAULT 'SumScore'`)
    await queryRunner.query(`ALTER TABLE \`bid\` ADD \`wayCalScorePrice\` varchar(50) NULL DEFAULT 'SumScore'`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`bid\` DROP COLUMN \`wayCalScorePrice\``)
    await queryRunner.query(`ALTER TABLE \`service\` DROP COLUMN \`wayCalScorePrice\``)
  }
}
