import { Injectable, NotAcceptableException } from '@nestjs/common'
import * as moment from 'moment'
import { In, Like } from 'typeorm'
import { CREATE_SUCCESS, DELETE_SUCCESS, enumData, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION, UPDATE_SUCCESS } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import {
  ContractEntity,
  ContractMemberEntity,
  EmployeeEntity,
  InvoiceEntity,
  InvoiceFileEntity,
  InvoiceSuggestEntity,
  InvoiceSuggestFileEntity,
  InvoiceSuggestHistoryEntity,
  PaymentProgressEntity,
  POEntity,
  POMemberEntity,
} from '../../entities'
import { coreHelper } from '../../helpers'
import { InvoiceSuggestRepository } from '../../repositories'
import { InvoiceSuggestCreate, InvoiceSuggestUpdate } from './dto'

@Injectable()
export class InvoiceSuggestService {
  constructor(private repo: InvoiceSuggestRepository) {}

  public async findDetail(data: { id: string }, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res: any = await this.repo.findOne({
      where: { id: data.id, companyId: user.companyId },
      relations: { po: true, contract: true, files: true, paymentPlan: true, invoices: { files: true }, histories: { employee: true } },
    })
    if (res.__contract__) {
      res.contractName = res.__contract__.name
      res.contractCode = res.__contract__.code
      res.isPaymentPO = await this.repo.manager.getRepository(ContractMemberEntity).findOne({
        where: {
          contractId: res.contractId,
          employeeId: user.employeeId,
          companyId: user.companyId,
          contractRoleCode: enumData.ContractRoleCode.Management.code,
        },
        select: { id: true },
      })
      res.poMoney = res.__contract__.value
      res.suggestPaid = 0
    }
    delete res.__contract__

    if (res.__po__) {
      res.poCode = res.__po__.code
      res.isPaymentPO = await this.repo.manager.getRepository(POMemberEntity).findOne({
        where: {
          employeeId: user.employeeId,
          poId: res.poId,
          companyId: user.companyId,
          poRoleCode: enumData.PORoleCode.PurchaseOrderPayMent.code,
        },
        select: { id: true },
      })
      res.poMoney = res.__po__.money
      res.suggestPaid = res.__po__.suggestPaid
    }
    delete res.__po__

    if (res.__paymentPlan__) {
      res.paymentPlanName = res.__paymentPlan__.name
    }
    delete res.__paymentPlan__

    {
      const lstFile = res.__files__ || []
      res.fileList = lstFile.map((c: any) => {
        return {
          uid: c.id,
          name: c.fileName,
          status: 'done',
          url: c.fileUrl,
        }
      })
      delete res.__files__
    }

    res.lstInvoice = res.__invoices__ || []
    for (const invoice of res.lstInvoice) {
      invoice.lstFile = invoice.__files__ || []
      invoice.numFile = invoice.__files__.length
      delete invoice.__files__
    }
    delete res.__invoices__
    const lstInvoiceSuggestStatus = coreHelper.convertObjToArray(enumData.InvoiceSuggestStatus)
    res.lstHistory = res.__histories__ || []
    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.InvoiceSuggestStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }
    for (const history of res.lstHistory) {
      history.employeeName = history.__employee__?.name || ''
      history.statusName = dicStatus[history.status]
      delete history.__employee__
      const objStatus = lstInvoiceSuggestStatus.find((c) => c.code == res.status)
      if (objStatus) history.statusName = objStatus.name
    }
    delete res.__histories__
    const objStatus = lstInvoiceSuggestStatus.find((c) => c.code == res.status)
    if (objStatus) res.statusName = objStatus.name

    return res
  }

  public async findClient(data: { id: string }, user: UserDto) {
    const lstData: any = await this.repo.find({ where: { poId: data.id }, relations: { files: true, paymentPlan: true } })
    const rs = []
    for (const item of lstData) {
      if (item.__files__ && item.__files__.length > 0)
        for (const file of item.__files__) {
          file.progressName = item.__paymentPlan__?.name
          rs.push(file)
        }
    }
    return rs
  }
  /** Danh sách ĐNTT có phân trang */
  public async pagination(data: PaginationDto, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const whereCon: any = { companyId: user.companyId }
    if (data.where.contractId) whereCon.contractId = data.where.contractId
    if (data.where.poId) whereCon.poId = data.where.poId
    if (data.where.status) whereCon.status = data.where.status

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      relations: { po: true, contract: true, files: true, paymentPlan: true },
    })

    const dicStatusName: any = {}
    {
      const lstInvoiceSuggestStatus = coreHelper.convertObjToArray(enumData.InvoiceSuggestStatus)
      lstInvoiceSuggestStatus.forEach((c) => (dicStatusName[c.code] = c.name))
    }

    const temp: any[] = []
    for (const item of res[0]) {
      item.statusName = dicStatusName[item.status]

      if (item.contractId) {
        item.contractName = item.__contract__.name
        item.contractCode = item.__contract__.code
        item.isPaymentPO = await this.repo.manager.getRepository(ContractMemberEntity).findOne({
          where: {
            contractId: item.contractId,
            employeeId: user.employeeId,
            companyId: user.companyId,
            contractRoleCode: enumData.ContractRoleCode.Management.code,
          },
          select: { id: true },
        })
      }

      if (item.poId) {
        item.poCode = item.__po__.code
        item.isPaymentPO = await this.repo.manager.getRepository(POMemberEntity).findOne({
          where: {
            employeeId: user.employeeId,
            poId: item.poId,
            companyId: user.companyId,
            poRoleCode: enumData.PORoleCode.PurchaseOrderPayMent.code,
          },
          select: { id: true },
        })
      }

      if (item.paymentPlanId) {
        item.poMoney = item.__paymentPlan__.money
        item.suggestPaid = item.__paymentPlan__.suggestPaid
        item.paymentPlanName = item.__paymentPlan__.name
      }

      temp.push(item)
    }

    return [temp, res[1]]
  }

  /** Tạo ĐNTT */
  public async createData(data: InvoiceSuggestCreate, user: UserDto) {
    return this.repo.manager.transaction('READ COMMITTED', async (manager) => {
      const poRepo = manager.getRepository(POEntity)
      const contractRepo = manager.getRepository(ContractEntity)
      const invoiceSuggestRepo = manager.getRepository(InvoiceSuggestEntity)
      const employeeRepo = manager.getRepository(EmployeeEntity)
      const invoiceSuggestHistoryRepo = manager.getRepository(InvoiceSuggestHistoryEntity)
      const invoiceSuggestFileRepo = manager.getRepository(InvoiceSuggestFileEntity)
      const paymentProgressRepo = manager.getRepository(PaymentProgressEntity)
      const poMemberRepo = manager.getRepository(POMemberEntity)
      const contractMemberRepo = manager.getRepository(ContractMemberEntity)

      let branch: any = {}
      if (!user.supplierId) {
        if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
        const employee = await employeeRepo.findOne({ where: { id: user.employeeId, companyId: user.companyId } })
        if (!employee) throw new Error(ERROR_NOT_FOUND_DATA)
        branch = await employee.branch
      } else {
        //   const employee = await employeeRepo.findOne({ where: { id: user.employeeId, companyId: user.companyId } })
        // branch = await user.branch
      }

      if (!data.fileList || data.fileList.length == 0) throw new Error('Vui lòng chọn File liên quan')
      if (!data.paymentPlanId) throw new Error('Vui lòng chọn tiến độ thanh toán trước')

      let userPaymentId = null
      if (data.contractId) {
        const contract = await contractRepo.findOne({ where: { id: data.contractId, companyId: user.companyId, isDeleted: false } })
        if (!contract) throw new Error('Hợp đồng không tồn tại')

        const contractMemberPayment: any = await contractMemberRepo.findOne({
          where: { contractId: data.contractId, companyId: user.companyId, contractRoleCode: enumData.ContractRoleCode.Management.code },
          relations: { employee: true },
        })
        if (contractMemberPayment) userPaymentId = contractMemberPayment.__employee__.userId
      }

      if (data.poId) {
        const po = await poRepo.findOne({ where: { id: data.poId, companyId: user.companyId, isDeleted: false } })
        if (!po) throw new Error('PO không tồn tại')
        if (po.status === enumData.PurchaseOrderStatus.Cancel.code) {
          throw new Error(`PO [${po.code}] đã xóa, không thể sửa thông tin ĐNTT`)
        } else if (po.status === enumData.PurchaseOrderStatus.DeliveryRefuse.code) {
          throw new Error(`PO [${po.code}] đã từ chối giao hàng, không thể sửa thông tin ĐNTT`)
        }
        const poMemberPayMent: any = await poMemberRepo.findOne({
          where: { poId: data.poId, companyId: user.companyId, poRoleCode: enumData.PORoleCode.PurchaseOrderPayMent.code },
          relations: { employee: true },
        })
        if (poMemberPayMent) {
          userPaymentId = poMemberPayMent.__employee__.userId
        }
      }

      const paymentPlan = await paymentProgressRepo.findOne({ where: { id: data.paymentPlanId, companyId: user.companyId, isDeleted: false } })
      if (!paymentPlan) throw new Error('Tiến độ thanh toán không tồn tại')

      if (+data.suggestPaid !== +paymentPlan.suggestPaid)
        throw new Error(`Tiến độ thanh toán vừa được thay đổi số tiền đã ĐNTT, vui lòng kiểm tra lại`)
      if (+data.money > +paymentPlan.money - +paymentPlan.suggestPaid)
        throw new Error(`Số tiền ĐNTT không được lớn hơn số tiền cần ĐNTT còn lại, vui lòng kiểm tra lại`)

      const newInvoiceSuggest = new InvoiceSuggestEntity()
      newInvoiceSuggest.companyId = user.companyId
      newInvoiceSuggest.createdBy = user.id
      newInvoiceSuggest.type = data.type
      if (data.contractId) newInvoiceSuggest.contractId = data.contractId
      if (data.poId) newInvoiceSuggest.poId = data.poId
      newInvoiceSuggest.paymentPlanId = data.paymentPlanId
      newInvoiceSuggest.money = data.money
      newInvoiceSuggest.invoiceNo = data.invoiceNo
      newInvoiceSuggest.discount = data.discount
      newInvoiceSuggest.invoiceDate = data.invoiceDate
      newInvoiceSuggest.description = data.description || ''
      newInvoiceSuggest.supplierId = data.supplierId
      newInvoiceSuggest.beneficiaryUnit = data.beneficiaryUnit
      newInvoiceSuggest.bankName = data.bankName
      newInvoiceSuggest.bankAccountNo = data.bankAccountNo
      newInvoiceSuggest.status = enumData.InvoiceSuggestStatus.Unpaid.code

      let suggestPaid = +paymentPlan.suggestPaid + +data.money
      let paymentStatus = enumData.PaymentProgressStatus.Partial.code
      // Kiểm tra nếu số tiền ĐNTT = số tiền cần ĐNTT thì hoàn tất ĐNTT của tiến độ thanh toán
      if (suggestPaid == +paymentPlan.money) {
        paymentStatus = enumData.PaymentProgressStatus.Paid.code
      }
      await paymentProgressRepo.update(data.paymentPlanId, { paymentStatus, suggestPaid, updatedBy: user.id })

      const codeSrc = `/${moment(new Date()).format('YY')}/${branch?.code || 'Admin'}-TT`
      const objLast = await invoiceSuggestRepo.findOne({
        where: {
          code: Like(`%${codeSrc}%`),
          companyId: user.companyId,
        },
        order: { code: 'DESC' },
      })
      let sort = 1
      if (objLast) {
        const lastSort = +objLast.code.substring(0, 4)
        sort = lastSort + 1
      }
      newInvoiceSuggest.code = `000${sort}`.slice(-4) + codeSrc
      const invoiceSuggest = await invoiceSuggestRepo.save(newInvoiceSuggest)

      const historyNew = new InvoiceSuggestHistoryEntity()
      historyNew.companyId = user.companyId
      historyNew.createdBy = user.id
      historyNew.invoiceSuggestId = invoiceSuggest.id
      historyNew.employeeId = user.employeeId
      historyNew.status = invoiceSuggest.status
      historyNew.description = `${user.username} tạo đề nghị thanh toán`
      await invoiceSuggestHistoryRepo.save(historyNew)

      for (const file of data.fileList) {
        const newFile = new InvoiceSuggestFileEntity()
        newFile.companyId = user.companyId
        if (data.url) newFile.url = data.url
        newFile.invoiceNo = data.invoiceNo
        newFile.createdBy = user.id
        newFile.invoiceSuggestId = invoiceSuggest.id
        newFile.fileName = file.name
        newFile.fileUrl = file.url
        await invoiceSuggestFileRepo.save(newFile)
      }

      return { message: CREATE_SUCCESS }
    })
  }

  /** Sửa thông tin ĐNTT */
  public async updateData(data: InvoiceSuggestUpdate, user: UserDto) {
    return this.repo.manager.transaction('READ COMMITTED', async (manager) => {
      const poRepo = manager.getRepository(POEntity)
      const contractRepo = manager.getRepository(ContractEntity)
      const invoiceSuggestRepo = manager.getRepository(InvoiceSuggestEntity)
      const employeeRepo = manager.getRepository(EmployeeEntity)
      const invoiceSuggestHistoryRepo = manager.getRepository(InvoiceSuggestHistoryEntity)
      const invoiceSuggestFileRepo = manager.getRepository(InvoiceSuggestFileEntity)
      const paymentProgressRepo = manager.getRepository(PaymentProgressEntity)
      const poMemberRepo = manager.getRepository(POMemberEntity)
      const contractMemberRepo = manager.getRepository(ContractMemberEntity)

      if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
      const employee = await employeeRepo.findOne({ where: { id: user.employeeId, companyId: user.companyId } })
      if (!employee) throw new Error(ERROR_NOT_FOUND_DATA)

      const invoiceSuggestEdit = await invoiceSuggestRepo.findOne({ where: { id: data.id, companyId: user.companyId, isDeleted: false } })
      if (!invoiceSuggestEdit) throw new Error('Đề nghị thanh toán không tồn tại')

      if (!data.fileList || data.fileList.length == 0) throw new Error('Vui lòng chọn File liên quan')

      let userPaymentId = null
      if (data.contractId) {
        const contract = await contractRepo.findOne({ where: { id: data.contractId, companyId: user.companyId, isDeleted: false } })
        if (!contract) throw new Error('Hợp đồng không tồn tại')

        const contractMemberPayment: any = await contractMemberRepo.findOne({
          where: { contractId: data.contractId, companyId: user.companyId, contractRoleCode: enumData.ContractRoleCode.Management.code },
          relations: { employee: true },
        })
        if (contractMemberPayment) userPaymentId = contractMemberPayment.__employee__.userId
      }
      if (data.poId) {
        const po = await poRepo.findOne({ where: { id: data.poId, companyId: user.companyId, isDeleted: false } })
        if (!po) throw new Error('PO không tồn tại')
        if (po.status === enumData.PurchaseOrderStatus.Cancel.code) {
          throw new Error(`PO [${po.code}] đã xóa, không thể sửa thông tin ĐNTT`)
        } else if (po.status === enumData.PurchaseOrderStatus.DeliveryRefuse.code) {
          throw new Error(`PO [${po.code}] đã từ chối giao hàng, không thể sửa thông tin ĐNTT`)
        }

        const poMemberPayMent: any = await poMemberRepo.findOne({
          where: { poId: data.poId, companyId: user.companyId, poRoleCode: enumData.PORoleCode.PurchaseOrderPayMent.code },
          relations: { employee: true },
        })
        if (poMemberPayMent) userPaymentId = poMemberPayMent.__employee__.userId
      }

      const paymentPlan = await paymentProgressRepo.findOne({ where: { id: data.paymentPlanId, companyId: user.companyId, isDeleted: false } })
      if (!paymentPlan) throw new Error('Tiến độ thanh toán không tồn tại')

      if (+data.suggestPaid !== +paymentPlan.suggestPaid)
        throw new Error(`Tiến độ thanh toán vừa được thay đổi số tiền đã ĐNTT, vui lòng kiểm tra lại`)
      let suggestPaid = +paymentPlan.suggestPaid - +invoiceSuggestEdit.money + +data.money
      if (+suggestPaid > +paymentPlan.money) throw new Error(`Số tiền ĐNTT không được lớn hơn số tiền cần ĐNTT còn lại, vui lòng kiểm tra lại`)

      invoiceSuggestEdit.money = data.money
      invoiceSuggestEdit.invoiceNo = data.invoiceNo
      invoiceSuggestEdit.discount = data.discount
      invoiceSuggestEdit.invoiceDate = data.invoiceDate
      invoiceSuggestEdit.description = data.description || ''
      invoiceSuggestEdit.bankName = data.bankName
      invoiceSuggestEdit.bankAccountNo = data.bankAccountNo
      invoiceSuggestEdit.updatedBy = user.id

      // Kiểm tra nếu số tiền ĐNTT = số tiền cần ĐNTT thì hoàn tất ĐNTT của PO
      let paymentStatus = enumData.PaymentProgressStatus.Partial.code
      if (+suggestPaid == +paymentPlan.money) {
        paymentStatus = enumData.PaymentProgressStatus.Paid.code
      }
      await paymentProgressRepo.update(data.paymentPlanId, { paymentStatus, suggestPaid, updatedBy: user.id })

      await invoiceSuggestRepo.save(invoiceSuggestEdit)

      const historyNew = new InvoiceSuggestHistoryEntity()
      historyNew.companyId = user.companyId
      historyNew.createdBy = user.id
      historyNew.invoiceSuggestId = invoiceSuggestEdit.id
      historyNew.employeeId = user.employeeId
      historyNew.status = invoiceSuggestEdit.status
      historyNew.description = `${user.username} sửa thông tin đề nghị thanh toán`
      await invoiceSuggestHistoryRepo.save(historyNew)

      // xóa các file liên quan cũ
      await invoiceSuggestFileRepo.delete({ invoiceSuggestId: invoiceSuggestEdit.id })
      // thêm lại các file liên quan
      for (const file of data.fileList) {
        const newFile = new InvoiceSuggestFileEntity()
        newFile.companyId = user.companyId
        newFile.createdBy = user.id
        newFile.invoiceSuggestId = invoiceSuggestEdit.id
        newFile.fileName = file.name
        newFile.fileUrl = file.url
        await invoiceSuggestFileRepo.save(newFile)
      }

      return { message: UPDATE_SUCCESS }
    })
  }

  /** Xóa ĐNTT */
  public async deleteData(data: { id: string; reason?: string }, user: UserDto) {
    return this.repo.manager.transaction('READ COMMITTED', async (manager) => {
      const invoiceSuggestRepo = manager.getRepository(InvoiceSuggestEntity)
      const invoiceSuggestHistoryRepo = manager.getRepository(InvoiceSuggestHistoryEntity)
      const invoiceSuggestFileRepo = manager.getRepository(InvoiceSuggestFileEntity)
      const invoiceRepo = manager.getRepository(InvoiceEntity)
      const invoiceFileRepo = manager.getRepository(InvoiceFileEntity)
      const paymentProgressRepo = manager.getRepository(PaymentProgressEntity)

      if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

      const invoiceSuggestDelete: any = await invoiceSuggestRepo.findOne({
        where: { id: data.id, companyId: user.companyId, isDeleted: false },
        relations: { invoices: true, paymentPlan: true },
        select: { id: true, status: true, money: true, paymentPlan: { id: true, suggestPaid: true }, invoices: { id: true } },
      })
      if (!invoiceSuggestDelete) throw new Error('Đề nghị thanh toán không tồn tại')
      if (invoiceSuggestDelete.status !== enumData.InvoiceSuggestStatus.Unpaid.code)
        throw new Error('Chỉ được xóa ĐNTT ở trạng thái [Chưa thanh toán]')

      const paymentPlan = invoiceSuggestDelete.__paymentPlan__
      if (!paymentPlan) throw new Error('Không xác định được ký thanh toán của Đề nghị thanh toán!')

      // Kiểm tra nếu số tiền ĐNTT = số tiền cần ĐNTT thì hoàn tất ĐNTT của PO
      let suggestPaid = +paymentPlan.suggestPaid - +invoiceSuggestDelete.money
      let paymentStatus = enumData.PaymentProgressStatus.Partial.code
      if (+suggestPaid == 0) {
        paymentStatus = enumData.PaymentProgressStatus.Unpaid.code
      }
      await paymentProgressRepo.update(paymentPlan.id, { paymentStatus, suggestPaid, updatedBy: user.id })

      // xóa lịch sử
      await invoiceSuggestHistoryRepo.delete({ invoiceSuggestId: data.id })

      // xóa các File liên quan
      await invoiceSuggestFileRepo.delete({ invoiceSuggestId: data.id })

      // xóa các thanh toán
      const lstInvoice = invoiceSuggestDelete.__invoices__ || []
      if (lstInvoice.length > 0) {
        const lstInvoiceId = lstInvoice.map((c) => c.id)
        // xóa các File liên quan Invoice
        await invoiceFileRepo.delete({ invoiceId: In(lstInvoiceId) })
        await invoiceRepo.delete({ invoiceSuggestId: data.id })
      }

      // xóa ĐNTT
      await invoiceSuggestRepo.delete(data.id)

      return { message: DELETE_SUCCESS }
    })
  }
}
