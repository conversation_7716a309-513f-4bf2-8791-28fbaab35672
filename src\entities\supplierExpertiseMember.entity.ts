import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, Join<PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { EmployeeEntity } from './employee.entity'
import { SupplierExpertiseEntity } from './supplierExpertise.entity'

/** <PERSON><PERSON><PERSON> thành viên trong ban thẩm định */
@Entity('supplier_expertise_member')
export class SupplierExpertiseMemberEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.supplierExpertiseMembers)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  supplierExpertiseId: string
  @ManyToOne(() => SupplierExpertiseEntity, (p) => p.members)
  @JoinColumn({ name: 'supplierExpertiseId', referencedColumnName: 'id' })
  supplierExpertise: Promise<SupplierExpertiseEntity>
}
