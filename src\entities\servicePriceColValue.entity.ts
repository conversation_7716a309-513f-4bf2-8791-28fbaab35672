import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, ManyToOne, JoinC<PERSON>umn } from 'typeorm'
import { ServicePriceEntity } from './servicePrice.entity'
import { ServicePriceColEntity } from './servicePriceCol.entity'

@Entity('service_price_col_value')
export class ServicePriceColValueEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  value: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  servicePriceId: string
  @ManyToOne(() => ServicePriceEntity, (p) => p.servicePriceColValues)
  @JoinColumn({ name: 'servicePriceId', referencedColumnName: 'id' })
  servicePrice: Promise<ServicePriceEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  servicePriceColId: string
  @ManyToOne(() => ServicePriceColEntity, (p) => p.servicePriceColValues)
  @JoinColumn({ name: 'servicePriceColId', referencedColumnName: 'id' })
  servicePriceCol: Promise<ServicePriceColEntity>
}
