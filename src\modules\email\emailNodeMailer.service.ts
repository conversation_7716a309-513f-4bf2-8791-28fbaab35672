import { Injectable } from '@nestjs/common'

require('dotenv').config()
const nodemailer = require('nodemailer')
const { google } = require('googleapis')
const OAuth2 = google.auth.OAuth2

@Injectable()
export class EmailNodeService {
  async createTransporter() {
    try {
      const oauth2Client = new OAuth2(process.env.CLIENT_ID, process.env.CLIENT_SECRET, process.env.OAUTH_PLAYGROUND)

      oauth2Client.setCredentials({
        refresh_token: process.env.REFRESH_TOKEN,
      })

      const accessToken = await new Promise((resolve, reject) => {
        oauth2Client.getAccessToken((err: any, token: any) => {
          if (err) {
            console.error('Error retrieving access token:', err)
            return reject(err)
          }
          resolve(token)
        })
      })

      const transporter = nodemailer.createTransport({
        service: process.env.SERVICE,
        auth: {
          type: process.env.TYPE,
          user: process.env.USER_MAIL,
          accessToken,
          clientId: process.env.CLIENT_ID,
          clientSecret: process.env.CLIENT_SECRET,
          refreshToken: process.env.REFRESH_TOKEN,
        },
      })
      return transporter
    } catch (err) {
      console.error('Error creating transporter:', err)
      throw err // Rethrow the error for further handling
    }
  }

  async sendMail(mailOptions: any) {
    try {
      // const mailOptions = {
      //   from: 'PMSLite',
      //   to: '<EMAIL>',
      //   subject: 'Test HTML Email',
      //   html: '<h1>Hi!</h1><p>This is a test email with <strong>HTML</strong> content.</p>',
      // }
      const mailOptionsC: any = mailOptions
      let emailTransporter = await this.createTransporter()
      // console.log('Email sent successfully')
      return await emailTransporter.sendMail(mailOptionsC)
    } catch (err) {
      console.error('Error sending email:', err)
    }
  }
}
