import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { InvoiceSuggestRepository } from '../../repositories'
import { InvoiceSuggestController } from './invoiceSuggest.controller'
import { InvoiceSuggestService } from './invoiceSuggest.service'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([InvoiceSuggestRepository])],
  controllers: [InvoiceSuggestController],
  providers: [InvoiceSuggestService],
  exports: [InvoiceSuggestService],
})
export class InvoiceSuggestModule {}
