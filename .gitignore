# compiled output
/dist
/node_modules


.env
ormconfig.json
yarn-error.log
ormconfig2.example.json
package-lock.json
ormconfig.json.dist

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

.dccache

deloyment-prod.yaml
deloyment-p-prod.yaml