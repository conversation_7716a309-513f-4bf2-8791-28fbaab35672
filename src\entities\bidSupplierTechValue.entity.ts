import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { BidSupplierEntity } from './bidSupplier.entity'
import { BidTechEntity } from './bidTech.entity'

@Entity('bid_supplier_tech_value')
export class BidSupplierTechValueEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: true,
  })
  score: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  value: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidSupplierId: string
  @ManyToOne(() => BidSupplierEntity, (p) => p.bidSupplierTechValue)
  @JoinColumn({ name: 'bidSupplierId', referencedColumnName: 'id' })
  bidSupplier: Promise<BidSupplierEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidTechId: string
  @ManyToOne(() => BidTechEntity, (p) => p.bidSupplierTechValue)
  @JoinColumn({ name: 'bidTechId', referencedColumnName: 'id' })
  bidTech: Promise<BidTechEntity>
}
