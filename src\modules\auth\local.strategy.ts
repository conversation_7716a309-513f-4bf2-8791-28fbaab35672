import { Strategy } from 'passport-local'
import { PassportStrategy } from '@nestjs/passport'
import { Injectable } from '@nestjs/common'
import { AuthService } from './auth.service'
import { apeAuthApiHelper } from '../../helpers'

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy, 'local') {
  constructor(private readonly authService: AuthService) {
    super({
      usernameField: 'username',
      passwordField: 'password',
      passReqToCallback: true,
    })
  }

  async validate(req: Request, username: string, password: string) {
    const isProduct = process.env.IS_PRODUCT == 'true'

    if (isProduct) {
      const data: any = await apeAuthApiHelper.login(req, { username, password })

      // AUTH: Product (SAAS)
      return await this.authService.validateUserProduct(data.userType, data.companyId, username, data.accessToken)
    }

    // AUTH: On-premise
    return await this.authService.validateUserOnPremise(username, password)
  }
}
