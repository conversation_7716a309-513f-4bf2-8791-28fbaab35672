import { IsString, <PERSON>Optional, IsN<PERSON>ber, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'

export class LegalDetailDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  code?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  dealName?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  address?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  dealAddress?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  fileMST?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  represen?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  chief?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  bankNumber?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  bankname?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  bankBrand?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  fileAccount?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  contactName?: string

  @ApiPropertyOptional()
  @IsOptional()
  email?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  phone?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  createYear?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  capital?: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  assets?: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  fileBill?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  fileInfoBill?: string

  /** Không có yêu cầu điều chỉnh */
  @ApiPropertyOptional()
  isNotRequireEdit: boolean
}

export class SaveLegalDetailBodyDto {
  @ApiProperty()
  @IsString()
  expertiseId: string

  @ApiPropertyOptional()
  @IsOptional()
  @ValidateNested()
  @Type(() => LegalDetailDto)
  detail?: LegalDetailDto

  @ApiPropertyOptional()
  comment: string
}
