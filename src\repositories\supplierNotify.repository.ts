import { Repository } from 'typeorm'
import { SupplierNotifyEntity } from '../entities'
import { enumData } from '../constants'
import { CustomRepository } from '../typeorm'

@CustomRepository(SupplierNotifyEntity)
export class SupplierNotifyRepository extends Repository<SupplierNotifyEntity> {
  async createSuplierNotify(companyId: string, supplierId: string, message: string, messageFull: string, url: string) {
    const newNotify = new SupplierNotifyEntity()
    newNotify.companyId = companyId
    newNotify.message = message
    newNotify.messageFull = messageFull
    newNotify.url = url
    newNotify.supplierId = supplierId
    newNotify.status = enumData.NotifyStatus.ChuaDoc.code
    await this.insert(newNotify)
  }
}
