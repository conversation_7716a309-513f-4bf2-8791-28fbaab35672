import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import {
  BidRepository,
  BidTechRepository,
  BidSupplierRepository,
  BidTradeRepository,
  BidPriceRepository,
  BidAuctionRepository,
  BidDealRepository,
  BidPriceColRepository,
  SupplierServiceRepository,
} from '../../repositories'
import { BidDetailController } from './bidDetail.controller'
import { BidDetailService } from './bidDetail.service'
import { BidRateModule } from '../bid/bidRate/bidRate.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      SupplierServiceRepository,
      BidRepository,
      BidDealRepository,
      BidAuctionRepository,
      BidTechRepository,
      BidTradeRepository,
      BidPriceRepository,
      BidPriceColRepository,
      BidSupplierRepository,
    ]),

    BidRateModule,
  ],
  controllers: [BidDetailController],
  providers: [BidDetailService],
  exports: [BidDetailService],
})
export class BidDetailModule {}
