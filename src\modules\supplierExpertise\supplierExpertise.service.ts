import { Injectable, NotFoundException, ConflictException, NotAcceptableException } from '@nestjs/common'
import { In, IsNull, Like, Raw } from 'typeorm'
import { SupplierExpertiseRepository, ServiceRepository, SupplierServiceRepository, SupplierRepository } from '../../repositories'
import { PaginationDto, UserDto } from '../../dto'
import { SaveCapacityDetailDto, SaveLegalDetailBodyDto } from './dto'
import { enumData, UPDATE_SUCCESS, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION } from '../../constants'
import { EmailService } from '../email/email.service'
import {
  ServiceAccessEntity,
  SupplierCapacityEntity,
  SupplierEntity,
  SupplierExpertiseDetailEntity,
  SupplierExpertiseEntity,
  SupplierExpertiseLawDetailEntity,
  SupplierExpertiseMemberEntity,
  SupplierExpertiseYearDetailEntity,
} from '../../entities'
import * as moment from 'moment'

@Injectable()
export class SupplierExpertiseService {
  constructor(
    private readonly repo: SupplierExpertiseRepository,
    private readonly supplierServiceRepo: SupplierServiceRepository,
    private readonly serviceRepo: ServiceRepository,
    private readonly supplierRepo: SupplierRepository,
    private readonly emailService: EmailService,
  ) {}

  private async getEvaluableServices(user: UserDto, parentId?: string, serviceId?: string) {
    const whereCon: any = { companyId: user.companyId }
    if (typeof parentId === 'undefined') {
      whereCon.isLast = true
      whereCon.isDeleted = false
    }
    if (typeof serviceId !== 'undefined') {
      whereCon.id = serviceId
    }

    const lstService = await this.serviceRepo.find({
      where: whereCon,
      select: { id: true },
    })
    const lstServiceId = lstService.map((c) => c.id)

    return lstServiceId
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const services = await this.getEvaluableServices(user, data.where.parentId, data.where.serviceId)
    if (services.length === 0) return [[], 0]

    const whereCon: any = { serviceId: In(services), companyId: user.companyId }
    if (data.where.supplierName) {
      whereCon.supplier = [{ code: Like(`%${data.where.supplierName}%`) }, { name: Like(`%${data.where.supplierName}%`) }]
    }
    if (data.where.expertiseStatus?.length > 0) whereCon.status = In(data.where.expertiseStatus)
    if (data.where.lawStatus) whereCon.statusLaw = data.where.lawStatus
    if (data.where.capacityStatus) whereCon.statusCapacity = data.where.capacityStatus

    if (data.where.fromDate && data.where.toDate) {
      whereCon.changeDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.fromDate).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.toDate).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.fromDate && !data.where.toDate) {
      whereCon.changeDate = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.fromDate).format('YYYY-MM-DD')}")`)
    } else if (data.where.toDate && !data.where.fromDate) {
      whereCon.changeDate = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.toDate).format('YYYY-MM-DD')}")`)
    }

    const res: any[] = await this.repo.findAndCount({
      where: [
        { ...whereCon, approvedLawId: user.employeeId },
        { ...whereCon, service: { serviceAccess: { employeeId: user.employeeId } } },
      ],
      relations: { supplier: true, service: { serviceAccess: { employee: true } }, approvedLaw: true, members: { employee: true } },
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    })

    for (let item of res[0]) {
      item.supplierName = item.__supplier__.name
      item.statusLawName = enumData.SupplierExpertiseLawStatus[item.statusLaw]?.name || ''
      item.statusCapacityName = enumData.SupplierExpertiseCapacityStatus[item.statusCapacity]?.name || ''
      delete item.__supplier__
      item.itemName = item.__service__.code + ' - ' + item.__service__.name
      const lstAccess = item.__service__.__serviceAccess__
      delete item.__service__
      item.finish = true
      item.isApproved = false
      if (item.isCheckLaw && item.statusLaw !== enumData.SupplierExpertiseLawStatus.DaThamDinh.code) {
        item.finish = false
      }
      if (item.isCheckCapacity && item.statusCapacity !== enumData.SupplierExpertiseCapacityStatus.DaThamDinh.code) {
        item.finish = false
      }
      if (lstAccess.some((c) => c.employeeId === user.employeeId)) {
        item.isApproved = true
      }

      if (item.approvedLawId) {
        item.approvedLawName = item.__approvedLaw__.name
      }
      delete item.__approvedLaw__

      item.approvedCapacityName = lstAccess.map((c) => c.__employee__.name).join(', ')

      item.memberName = item.__members__.map((c) => c.__employee__.name).join(', ')
      delete item.__members__
    }
    return res
  }

  /** Lấy thông tin thẩm định pháp lý */
  public async getLaw(user: UserDto, expertiseId: string) {
    if (!user.employeeId) throw new Error(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const whereCommon: any = { id: expertiseId, companyId: user.companyId }
    const supplierExpertise = await this.repo.findOne({
      where: [
        { ...whereCommon, approvedLawId: user.employeeId },
        { ...whereCommon, service: { serviceAccess: { employeeId: user.employeeId } } },
      ],
      relations: { supplierExpertiseLawDetails: true, supplier: true },
    })
    if (!supplierExpertise) throw new NotFoundException('Bạn không có quyền xem thông tin pháp lý!')

    return {
      supplierExpertise,
      legal: supplierExpertise.approvedLawId === user.employeeId,
    }
  }

  /** Lấy thông tin thẩm định năng lực */
  public async getCapacity(user: UserDto, expertiseId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const whereCommon: any = { id: expertiseId, companyId: user.companyId }
    const supplierExpertise = await this.repo.findOne({
      where: [
        { ...whereCommon, approvedLawId: user.employeeId },
        { ...whereCommon, service: { serviceAccess: { employeeId: user.employeeId } } },
      ],
      relations: { supplierExpertiseDetails: { supplierExpertiseYearDetails: true } },
    })
    if (!supplierExpertise) throw new NotFoundException('Bạn không có quyền xem thông tin năng lực!')

    const supplierCapacity = await this.repo.manager.getRepository(SupplierCapacityEntity).find({
      where: { supplierServiceId: supplierExpertise.supplierServiceId, parentId: IsNull(), companyId: user.companyId, isDeleted: false },
      relations: {
        supplierCapacityListDetails: true,
        supplierCapacityYearValues: true,
        childs: { supplierCapacityListDetails: true, supplierCapacityYearValues: true },
      },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })

    const lstAccess = await this.repo.manager.getRepository(ServiceAccessEntity).find({
      where: { serviceId: supplierExpertise.serviceId, isDeleted: false },
      select: { id: true, employeeId: true },
    })
    return { supplierCapacity, supplierExpertise, capacity: lstAccess.some((c) => c.employeeId == user.employeeId) }
  }

  /** Lấy quyền thẩm định */
  public async getExpertisePermissions(user: UserDto, expertiseId: string) {
    const whereCommon: any = { id: expertiseId, companyId: user.companyId }
    const supplierExpertise = await this.repo.findOne({
      where: [
        { ...whereCommon, approvedLawId: user.employeeId },
        { ...whereCommon, service: { serviceAccess: { employeeId: user.employeeId } } },
      ],
      select: {
        id: true,
        serviceId: true,
        approvedLawId: true,
      },
    })
    if (!supplierExpertise) throw new NotFoundException('Bạn không có quyền xem thông tin pháp lý!')

    const lstAccess = await this.repo.manager.getRepository(ServiceAccessEntity).find({
      where: { serviceId: supplierExpertise.serviceId, isDeleted: false },
      select: { id: true, employeeId: true },
    })

    return {
      legal: supplierExpertise.approvedLawId === user.employeeId,
      capacity: lstAccess.some((c) => c.employeeId == user.employeeId),
    }
  }

  /** Lưu thẩm định thông tin pháp lý */
  public async saveLaw(user: UserDto, data: SaveLegalDetailBodyDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    await this.repo.manager.transaction(async (manager) => {
      const supplierExpertiseLawDetailRepo = manager.getRepository(SupplierExpertiseLawDetailEntity)
      const supplierExpertiseRepo = manager.getRepository(SupplierExpertiseEntity)
      const supplierRepo = manager.getRepository(SupplierEntity)

      const expertiseEntity: any = await supplierExpertiseRepo.findOne({
        where: {
          id: data.expertiseId,
          approvedLawId: user.employeeId,
          status: enumData.SupplierExpertiseStatus.DangThamDinh.code,
          companyId: user.companyId,
        },
        relations: { supplier: true },
        select: { id: true, supplier: { code: true } },
      })
      if (!expertiseEntity) throw new NotFoundException('Không tìm thấy thông tin thẩm định!')

      // Xóa dữ liệu cũ
      await supplierExpertiseLawDetailRepo.delete({ supplierExpertiseId: data.expertiseId })
      // Cập nhật trạng thái
      await supplierExpertiseRepo.update(data.expertiseId, {
        statusLaw: enumData.SupplierExpertiseLawStatus.DaThamDinh.code,
        comment: data.comment,
        updatedBy: user.id,
      })

      if (!data.detail || data.detail.isNotRequireEdit) return

      if (data.detail.code?.length > 0 && data.detail.code != expertiseEntity.__supplier__.code) {
        const objCheckCode = await supplierRepo.findOne({
          where: { code: data.detail.code, companyId: user.companyId },
          select: { id: true },
        })
        if (objCheckCode) throw new ConflictException(`Mã nhà cung cấp [${data.detail.code}] đã được sử dụng, không thể yêu cầu điều chỉnh!`)
      }

      const expertiseLegalDetailEntity = supplierExpertiseLawDetailRepo.create({
        supplierExpertiseId: data.expertiseId,
        companyId: user.companyId,
        ...data.detail,
        createdBy: user.id,
      })

      await supplierExpertiseLawDetailRepo.save(expertiseLegalDetailEntity)
    })

    if (!data.detail || data.detail.isNotRequireEdit) this.emailService.GuiKetQuaThamDinhPhapLyNcc(data.expertiseId)
    else this.emailService.GuiNccBoSungPhapLy(data.expertiseId)

    return { message: UPDATE_SUCCESS }
  }

  /** Lưu thẩm định thông tin năng lực */
  public async saveCapacity(user: UserDto, data: SaveCapacityDetailDto) {
    await this.repo.manager.transaction(async (manager) => {
      const supplierExpertiseRepo = new SupplierExpertiseRepository(SupplierExpertiseEntity, manager)
      const supplierExpertiseDetailRepo = manager.getRepository(SupplierExpertiseDetailEntity)
      const supplierExpertiseYearDetailRepo = manager.getRepository(SupplierExpertiseYearDetailEntity)

      //#region Check thẩm định
      if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

      const objCheckExpertise = await supplierExpertiseRepo.findOne({
        where: {
          id: data.expertiseId,
          service: { serviceAccess: { employeeId: user.employeeId } },
          status: enumData.SupplierExpertiseStatus.DangThamDinh.code,
          companyId: user.companyId,
        },
        select: { id: true },
      })
      if (!objCheckExpertise) throw new NotFoundException('Không tìm thấy thông tin thẩm định!')
      //#endregion

      //#region Xóa dữ liệu cũ
      const listOld = await supplierExpertiseDetailRepo.find({
        where: {
          supplierExpertiseId: data.expertiseId,
          companyId: user.companyId,
        },
        select: { id: true },
      })

      for (let item of listOld) {
        await supplierExpertiseYearDetailRepo.delete({ supplierExpertiseDetailId: item.id })
      }

      await supplierExpertiseDetailRepo.delete({ supplierExpertiseId: data.expertiseId })
      //#endregion

      // update => đã thẩm định
      await this.updateCapacityStatusToEvaluated(supplierExpertiseRepo, data.expertiseId, user, data.comment)

      if (!data.detail || data.isNotRequireEdit) return

      for (let i = 0, length = data.detail.length; i < length; i++) {
        let item = data.detail[i]

        // Nếu có gợi ý. hoặc kiểu dữ liệu tăng theo năm
        if ((item.value != null && item.value != undefined) || item.listDetailYear?.length > 0) {
          const supplierExpertiseDetailEntity = supplierExpertiseDetailRepo.create({
            ...item,
            value: item.value.toString(),
            comment: item.comment ? item.comment.toString() : '',
            supplierExpertiseId: data.expertiseId,
            companyId: user.companyId,
            createdBy: user.id,
          })
          const supplierExpertiseDetailSave = await supplierExpertiseDetailRepo.save(supplierExpertiseDetailEntity)

          // Dữ liệu theo năm
          if (item.listDetailYear?.length > 0) {
            for (let y = 0, yearLength = item.listDetailYear.length; y < yearLength; y++) {
              const itemYear = item.listDetailYear[y]
              const newEntity = supplierExpertiseYearDetailRepo.create({
                year: itemYear.year,
                value: itemYear.value.toString(),
                supplierExpertiseDetailId: supplierExpertiseDetailSave.id,
                companyId: user.companyId,
                createdBy: user.id,
              })
              await supplierExpertiseYearDetailRepo.save(newEntity)
            }
          }
        }

        if (item.childs?.length > 0) {
          for (let j = 0, childLength = item.childs.length; j < childLength; j++) {
            let itemC = item.childs[j]
            if ((itemC.value != null && itemC.value != undefined) || itemC.listDetailYear?.length > 0) {
              const supplierExpertiseCDetailEntity = supplierExpertiseDetailRepo.create({
                ...itemC,
                value: itemC.value.toString(),
                // comment: itemC.comment.toString(),
                supplierExpertiseId: data.expertiseId,
                companyId: user.companyId,
                createdBy: user.id,
              })
              const supplierExpertiseDetailCSave = await supplierExpertiseDetailRepo.save(supplierExpertiseCDetailEntity)

              // Dữ liệu theo năm
              if (itemC.listDetailYear?.length > 0) {
                for (let y = 0, yearLength = itemC.listDetailYear.length; y < yearLength; y++) {
                  const itemYear = itemC.listDetailYear[y]
                  const newEntity = supplierExpertiseYearDetailRepo.create({
                    year: itemYear.year,
                    value: itemYear.value.toString(),
                    supplierExpertiseDetailId: supplierExpertiseDetailCSave.id,
                    companyId: user.companyId,
                    createdBy: user.id,
                  })

                  await supplierExpertiseYearDetailRepo.save(newEntity)
                }
              }
            }
          }
        }
      }
    })

    if (!data.detail || data.isNotRequireEdit) this.emailService.GuiKetQuaThamDinhNangLucNcc(data.expertiseId)
    else this.emailService.GuiNccBoSungNangLuc(data.expertiseId)

    return { message: UPDATE_SUCCESS }
  }

  /** Hoàn tất thẩm định */
  public async finishExpertise(user: UserDto, data: { expertiseId: string }) {
    const whereCommon: any = { id: data.expertiseId, companyId: user.companyId, status: enumData.SupplierExpertiseStatus.DangThamDinh.code }
    const supplierExpertise = await this.repo.findOne({
      where: [
        { ...whereCommon, approvedLawId: user.employeeId },
        { ...whereCommon, service: { serviceAccess: { employeeId: user.employeeId } } },
      ],
    })
    if (!supplierExpertise) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    let statusExper = enumData.SupplierExpertiseStatus.DaThamDinh.code
    let statusExpertiseSupplierService = enumData.SupplierServiceExpertiseStatus.DaThamDinh.code
    const law = await supplierExpertise.supplierExpertiseLawDetails
    const capacity = await supplierExpertise.supplierExpertiseDetails
    const supplier = await supplierExpertise.supplier
    if (law?.length > 0) {
      const check = !this.compareObject(law[0], supplier)
      if (check) statusExper = enumData.SupplierExpertiseStatus.KhongDuyetQT2.code
    }

    if (capacity.length > 0) {
      statusExper = enumData.SupplierExpertiseStatus.KhongDuyetQT2.code
      // statusSupplierService = enumData.SupplierServiceStatus.KhongDuyetQT2.code
    }

    // Cập nhật trạng thái supplierService
    if (statusExper !== enumData.SupplierExpertiseStatus.KhongDuyetQT2.code) {
      await this.supplierServiceRepo.update(supplierExpertise.supplierServiceId, {
        statusExpertise: statusExpertiseSupplierService,
        updatedBy: user.id,
      })
    }

    // Cập nhật trạng thái thẩm định
    await this.repo.update(data.expertiseId, {
      status: statusExper,
      updatedBy: user.id,
    })

    await this.emailService.finishEvaluation(supplierExpertise.supplierId, data.expertiseId)

    return { message: 'Hoàn tất thẩm định' }
  }

  /** Huỷ yêu cầu thẩm định */
  public async cancelExpertise(user: UserDto, data: { expertiseId: string }) {
    const whereCommon: any = { id: data.expertiseId, companyId: user.companyId, status: enumData.SupplierExpertiseStatus.DangThamDinh.code }
    const supplierExpertise = await this.repo.findOne({
      where: [
        { ...whereCommon, approvedLawId: user.employeeId },
        { ...whereCommon, service: { serviceAccess: { employeeId: user.employeeId } } },
      ],
    })
    if (!supplierExpertise) throw new NotFoundException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    return await this.repo.manager.transaction(async (manager) => {
      const supplierExpertiseRepo = manager.getRepository(SupplierExpertiseEntity)
      const supplierExpertiseYearDetailRepo = manager.getRepository(SupplierExpertiseYearDetailEntity)
      const supplierExpertiseMemberRepo = manager.getRepository(SupplierExpertiseMemberEntity)
      const supplierExpertiseDetailRepo = manager.getRepository(SupplierExpertiseDetailEntity)
      const supplierExpertiseLawDetailRepo = manager.getRepository(SupplierExpertiseLawDetailEntity)

      const supplierExpertiseDetail = await supplierExpertiseDetailRepo.find({
        where: { supplierExpertiseId: data.expertiseId, companyId: user.companyId },
      })

      if (supplierExpertiseDetail && supplierExpertiseDetail.length > 0) {
        const map = supplierExpertiseDetail.map((p) => p.id)
        await supplierExpertiseYearDetailRepo.delete({ supplierExpertiseDetailId: In(map) })
      }
      await supplierExpertiseLawDetailRepo.delete({ supplierExpertiseId: data.expertiseId })
      await supplierExpertiseDetailRepo.delete({ supplierExpertiseId: data.expertiseId })
      await supplierExpertiseMemberRepo.delete({ supplierExpertiseId: data.expertiseId })
      await supplierExpertiseRepo.delete({ id: data.expertiseId })

      return { message: 'Đã huỷ yêu cầu thẩm định' }
    })
  }

  /** So sánh các giá trị 2 obj */
  public compareObject(obj1: any, obj2: any) {
    for (const key in obj1) {
      if (obj1[key]) {
        if (obj1[key] !== obj2[key]) {
          return false
        }
      }
    }
    return true
  }

  /** Cập nhật trạng thái đã thẩm định năng lực */
  private async updateCapacityStatusToEvaluated(repo: SupplierExpertiseRepository, expertiseId: string, user: UserDto, comment?: string) {
    const find = await repo.findOne({
      where: {
        id: expertiseId,
        service: { serviceAccess: { employeeId: user.employeeId } },
        status: enumData.SupplierExpertiseStatus.DangThamDinh.code,
        companyId: user.companyId,
      },
      select: { id: true },
    })
    if (!find) throw new Error('Không tìm thấy thông tin thẩm định.')

    await repo.update(expertiseId, {
      commentCapacity: comment || '',
      statusCapacity: enumData.SupplierExpertiseCapacityStatus.DaThamDinh.code,
      updatedBy: user.id,
    })
  }
}
