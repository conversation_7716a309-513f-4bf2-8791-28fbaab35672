import { <PERSON>ti<PERSON>, Column, <PERSON>T<PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON> } from 'typeorm'
import { BidAuctionEntity } from './bidAuction.entity'
import { BidPriceEntity } from './bidPrice.entity'
import { BaseEntity } from './base.entity'

@Entity('bid_auction_price')
export class BidAuctionPriceEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: true,
  })
  maxPrice: number

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidAuctionId: string
  @ManyToOne(() => BidAuctionEntity, (p) => p.bidAuctionPrice)
  @JoinColumn({ name: 'bidAuctionId', referencedColumnName: 'id' })
  bidAuction: Promise<BidAuctionEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidPriceId: string
  @ManyToOne(() => BidPriceEntity, (p) => p.bidAuctionPrices)
  @JoinColumn({ name: 'bidPriceId', referencedColumnName: 'id' })
  bidPrice: Promise<BidPriceEntity>
}
