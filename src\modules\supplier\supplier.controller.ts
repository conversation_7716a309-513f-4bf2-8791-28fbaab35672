import { Controller, Post, Body, UseGuards, Get, Query, Param, Req } from '@nestjs/common'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { CurrentUser, Roles } from '../common/decorators'
import { PaginationDto, UserDto } from '../../dto'
import { SupplierService } from './supplier.service'
import { CreateExpertiseDto, SupplierCreateDto, SupplierUpdateLawDto } from './dto'
import { enumProject } from '../../constants'
import { Request as IRequest } from 'express'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Supplier')
@Controller('suppliers')
export class SupplierController {
  constructor(private readonly service: SupplierService) {}

  //#region Quản lý NCC

  @ApiOperation({ summary: 'Lấy ds NCC' })
  @UseGuards(ApeAuthGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: { bidId?: string; isSuccessBid?: boolean }) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds NCC' })
  @Post('find_offer')
  public async loadDataSelectOffer(@CurrentUser() user: UserDto, @Body() data: { bidId?: string; isSuccessBid?: boolean }) {
    return await this.service.loadDataSelectOffer(user, data)
  }

  @ApiOperation({ summary: 'Thông tin chi tiết NCC' })
  @Roles(enumProject.Features.SUPPLIER_001.code, enumProject.Features.SUPPLIER_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetail(user, data)
  }

  @ApiOperation({ summary: 'Danh sách NCC phân trang' })
  @Roles(enumProject.Features.SUPPLIER_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('supplier_pagination')
  public async supplierPagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.supplierPagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo NCC' })
  @Roles(enumProject.Features.SUPPLIER_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_supplier')
  public async createSupplier(@CurrentUser() user: UserDto, @Body() data: SupplierCreateDto, @Req() req: IRequest) {
    return await this.service.createSupplier(user, data, req)
  }

  @ApiOperation({ summary: 'Cập nhật thông tin NCC' })
  @Roles(enumProject.Features.SUPPLIER_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_supplier')
  public async updateSupplier(@CurrentUser() user: UserDto, @Body() data: SupplierUpdateLawDto) {
    return await this.service.updateSupplier(user, data)
  }

  @ApiOperation({ summary: 'Lấy thông tin năng lực Item của NCC' })
  @Roles(enumProject.Features.SUPPLIER_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_capacity')
  public async loadCapacity(@CurrentUser() user: UserDto, @Body() data: { supplierServiceId: string }) {
    return await this.service.loadCapacity(user, data)
  }

  @ApiOperation({ summary: 'Admin Lưu thông tin năng lực item NCC' })
  @Roles(enumProject.Features.SUPPLIER_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('save_supplier_service_capacity')
  public async saveSupplierServiceCapacity(@CurrentUser() user: UserDto, @Body() data: { supplierServiceId; lstCapacity: any[] }) {
    return await this.service.saveSupplierServiceCapacity(user, data)
  }

  @ApiOperation({ summary: 'Tải lại template năng lực Item của NCC' })
  @Roles(enumProject.Features.SUPPLIER_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('delete_all_capacity')
  public async deleteAllCapacity(@CurrentUser() user: UserDto, @Body() data: { supplierServiceId: string }) {
    return await this.service.deleteAllCapacity(user, data)
  }

  @ApiOperation({ summary: 'Import NCC' })
  @Roles(enumProject.Features.SUPPLIER_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('import')
  public async importSuppliers(@CurrentUser() user: UserDto, @Body() data: { lstData: any[] }, @Req() req: IRequest) {
    return await this.service.importSuppliers(user, data, req)
  }

  @ApiOperation({ summary: 'Lưu thông tin supplier service' })
  @Roles(enumProject.Features.SUPPLIER_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('save_supplier_service')
  public async saveSupplierService(@CurrentUser() user: UserDto, @Body() data: { id: string; supplierType: string }) {
    return await this.service.saveSupplierService(user, data)
  }

  //#endregion

  @ApiOperation({ summary: 'Lấy thông tin năng lực Item của NCC' })
  @Roles(enumProject.Features.SUPPLIER_001.code, enumProject.Features.SUPPLIER_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_supplier_service_capacity/:id')
  public async getSupplierCapacity(@CurrentUser() user: UserDto, @Param('id') supplierServiceId: string) {
    return await this.service.getSupplierCapacity(user, supplierServiceId)
  }

  @ApiOperation({ summary: 'Lấy ds supplierService để tạo YC thẩm định' })
  @Roles(enumProject.Features.EXPERTISE_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('supplier_service_pagination')
  public async supplierServicePagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.supplierServicePagination(user, data)
  }

  @ApiOperation({ summary: 'Lấy service mà NCC có thể thêm' })
  @Post('get_services_can_add')
  @Roles(enumProject.Features.SUPPLIER_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  public async getServicesCanAdd(@CurrentUser() user: UserDto, @Body() data: { supplierId: string }) {
    return await this.service.getServicesCanAdd(user, data)
  }

  @ApiOperation({ summary: 'Lấy service mà NCC có thể thêm' })
  @Post('add_supplier_service')
  @Roles(enumProject.Features.SUPPLIER_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  public async addSupplierService(@CurrentUser() user: UserDto, @Body() data: { supplierId: string; serviceId: string }) {
    return await this.service.addSupplierService(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds supplierService của supplier' })
  @Roles(enumProject.Features.SUPPLIER_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_supplier_service')
  public async loadSupplierService(@CurrentUser() user: UserDto, @Body() data: { supplierId: string }) {
    return await this.service.loadSupplierService(user, data)
  }

  @ApiOperation({ summary: 'Tạo yêu cầu thẩm định' })
  @Roles(enumProject.Features.EXPERTISE_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('supplier_service_to_expertise')
  public async supplierServiceToExpertise(@CurrentUser() user: UserDto, @Body() data: CreateExpertiseDto) {
    return await this.service.supplierServiceToExpertise(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật mật khẩu' })
  @Roles(enumProject.Features.EXPERTISE_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_password')
  public async updatePassword(@CurrentUser() user: UserDto, @Body() data: { id: string; newPassword: string }) {
    return await this.service.updatePassword(user, data)
  }

  @ApiOperation({ summary: 'Xoá NCC (chức năng bảo mật)' })
  @Roles(enumProject.Features.EXPERTISE_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('delete')
  public async deleteSuppliers(@CurrentUser() user: UserDto, @Body() data: { confirmCode: string; lstSupplierId: string[] }) {
    return await this.service.deleteSuppliers(user, data)
  }

  @ApiOperation({ summary: 'Lịch sử đấu thầu NCC' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination_bid_history')
  public async paginationBidHistory(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.paginationBidHistory(user, data)
  }
}
