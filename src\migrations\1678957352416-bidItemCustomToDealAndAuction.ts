import { MigrationInterface, QueryRunner } from 'typeorm'

export class bidItemCustomToDealAndAuction1678957352416 implements MigrationInterface {
  name = 'bidItemCustomToDealAndAuction1678957352416'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`bid_deal\` ADD \`parentId\` varchar(36) NULL`)
    await queryRunner.query(`ALTER TABLE \`bid_auction\` ADD \`parentId\` varchar(36) NULL`)
    await queryRunner.query(
      `ALTER TABLE \`bid_deal\` ADD CONSTRAINT \`FK_0fa84e5f3dc0a7ba411603f50d3\` FOREIGN KEY (\`parentId\`) REFERENCES \`bid_deal\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_auction\` ADD CONSTRAINT \`FK_e702054a4806f40ed5a61982af2\` FOREIGN KEY (\`parentId\`) REFERENCES \`bid_auction\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`bid_auction\` DROP FOREIGN KEY \`FK_e702054a4806f40ed5a61982af2\``)
    await queryRunner.query(`ALTER TABLE \`bid_deal\` DROP FOREIGN KEY \`FK_0fa84e5f3dc0a7ba411603f50d3\``)
    await queryRunner.query(`ALTER TABLE \`bid_auction\` DROP COLUMN \`parentId\``)
    await queryRunner.query(`ALTER TABLE \`bid_deal\` DROP COLUMN \`parentId\``)
  }
}
