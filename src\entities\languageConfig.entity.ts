import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { LanguageEntity } from './language.entity'

@Entity('language_config')
export class LanguageConfigEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  languageId: string
  @ManyToOne(() => LanguageEntity, (p) => p.configs)
  @JoinColumn({ name: 'languageId', referencedColumnName: 'id' })
  language: Promise<LanguageEntity>

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  sourceType: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: false,
  })
  component: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: false,
  })
  key: string

  @Column({
    type: 'text',
    nullable: false,
  })
  value: string

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string
}
