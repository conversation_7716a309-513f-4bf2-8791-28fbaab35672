import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty } from 'class-validator'

export class AuctionCreate {
  @ApiProperty()
  @IsNotEmpty()
  title: string

  /** Gi<PERSON> khởi điểm */
  @ApiProperty()
  @IsNotEmpty()
  price: number

  @ApiProperty()
  @IsNotEmpty()
  dateStart: Date

  @ApiProperty()
  @IsNotEmpty()
  dateEnd: Date

  /** G<PERSON><PERSON> thầu */
  @ApiPropertyOptional()
  bidId: string

  @ApiPropertyOptional()
  description: string

  @ApiProperty()
  @IsNotEmpty()
  lstSupplierId: string[]
}
