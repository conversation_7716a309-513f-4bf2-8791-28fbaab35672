import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsBoolean, IsNumber } from 'class-validator'

export class ServiceCapacityCreateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiPropertyOptional()
  sort: number

  @ApiPropertyOptional()
  parentId: string
  @ApiPropertyOptional()
  percent: number
  @ApiPropertyOptional()
  percentRule: number

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isRequired: boolean

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isChangeByYear: boolean

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  type: string

  @ApiPropertyOptional()
  description: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  serviceId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isCalUp: boolean

  @ApiPropertyOptional()
  percentDownRule: number
}
