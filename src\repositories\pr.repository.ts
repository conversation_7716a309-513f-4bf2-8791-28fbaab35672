import { Repository } from 'typeorm'
import { PrApproverEntity, PrEntity, PrItemEntity } from '../entities'
import { CustomRepository } from '../typeorm'

@CustomRepository(PrEntity)
export class PrRepository extends Repository<PrEntity> {}

@CustomRepository(PrApproverEntity)
export class PrApproverRepository extends Repository<PrApproverEntity> {}

@CustomRepository(PrItemEntity)
export class PrItemRepository extends Repository<PrItemEntity> {}
