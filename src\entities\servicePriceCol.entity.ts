import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { ServicePriceColValueEntity } from './servicePriceColValue.entity'
import { ServiceEntity } from './service.entity'

@Entity('service_price_col')
export class ServicePriceColEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'text',
    nullable: true,
  })
  fomular: string

  /** C<PERSON> bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  @Column({
    length: 50,
    nullable: false,
  })
  type: string

  @Column({
    length: 50,
    nullable: false,
  })
  colType: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  serviceId: string
  @ManyToOne(() => ServiceEntity, (p) => p.servicePriceCols)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  @OneToMany(() => ServicePriceColValueEntity, (p) => p.servicePriceCol)
  servicePriceColValues: Promise<ServicePriceColValueEntity[]>
}
