import { BaseEntity } from './base.entity'
import { Entity, Column, JoinColumn, ManyToOne, OneToMany, Index } from 'typeorm'
import { POEntity, EmployeeEntity, InboundContainerEntity } from '.'

/** Thông tin inbound*/
@Entity('inbound')
export class InboundEntity extends BaseEntity {
  /**Trạng thái  */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Mã inbound */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  /** Tên inbound */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  name: string

  /** <PERSON><PERSON><PERSON> giao hàng */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  deliveryDate: Date

  /** Thời gian về kho dự kiến */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  dateArrivalWarehouse: Date

  /** <PERSON><PERSON><PERSON> viên chịu trách nhiệm */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeInchargeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.inbounds)
  @JoinColumn({ name: 'employeeInchargeId', referencedColumnName: 'id' })
  employeeIncharge: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.inbounds)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  /**Inbound được tạo từ nhà cung cấp hay là từ trang admin: true ? Nhà cung cấp : trang Admin */
  @Column({
    nullable: true,
    default: false,
  })
  isSupplierCreate: boolean

  /** Thời gian dự kiến về cảng */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  dateArrivalPort: Date

  @OneToMany(() => InboundContainerEntity, (p) => p.inbound)
  inboundContainers: Promise<InboundContainerEntity[]>
}
