import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { OfferSupplierEntity } from './offerSupplier.entity'
import { OfferTradeEntity } from './offerTrade.entity'

@Entity('offer_supplier_trade_value')
export class OfferSupplierTradeValueEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: true,
  })
  score: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerSupplierId: string
  @ManyToOne(() => OfferSupplierEntity, (p) => p.offerSupplierTradeValue)
  @JoinColumn({ name: 'offerSupplierId', referencedColumnName: 'id' })
  offerSupplier: Promise<OfferSupplierEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerTradeId: string
  @ManyToOne(() => OfferTradeEntity, (p) => p.offerSupplierTradeValue)
  @JoinColumn({ name: 'offerTradeId', referencedColumnName: 'id' })
  offerTrade: Promise<OfferTradeEntity>
}
