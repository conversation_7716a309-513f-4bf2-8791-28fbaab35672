import { In, <PERSON><PERSON><PERSON>, Not, Repository } from 'typeorm'
import { CustomRepository } from '../typeorm'
import { OfferEntity } from '../entities/offer.entity'
import { OfferServiceEntity } from '../entities/offerService.entity'
import { OfferPriceEntity } from '../entities/offerPrice.entity'
import { OfferSupplierEntity } from '../entities/offerSupplier.entity'
import { OfferTradeEntity } from '../entities/offerTrade.entity'
import { enumData } from '../constants'
import { EmployeeEntity, ServiceEntity } from '../entities'
import { UserDto } from '../dto'
import { OfferPriceColEntity } from '../entities/offerPriceCol.entity'
import { OfferCustomPriceEntity } from '../entities/offerCustomPrice.entity'
import { OfferSupplierPriceEntity } from '../entities/offerSupplierPrice.entity'
import { OfferDealEntity } from '../entities/offerDeal.entity'
import { OfferDealSupplierEntity } from '../entities/offerDealSupplier.entity'
import { OfferDealSupplierPriceValueEntity } from '../entities/offerDealSupplierPriceValue.entity'
import { OfferSupplierShipmentValueEntity } from '../entities/offerSupplierShipmentValue.entity'
import { OfferSupplierServiceEntity } from '../entities/offerSupplierService.entity'

@CustomRepository(OfferEntity)
export class OfferRepository extends Repository<OfferEntity> {
  async getBid2(user: UserDto, bidId: string) {
    const res: any = await this.findOne({
      where: { id: bidId },
      relations: { offerService: { service: true } },
    })
    if (!res) throw new Error('Gói thầu không còn tồn tại')
    if (res.isDeleted) throw new Error('Gói thầu đã ngưng hoạt động')

    res.listItem = res.__offerService__ || []
    for (const item of res.listItem) {
      item.itemName = item.__service__?.code + ' - ' + item.__service__?.name
      delete item.__service__
    }
    delete res.__offerService__

    return res
  }

  async getBid3(user: UserDto, bidId: string) {
    const res: any = await this.findOne({
      where: { id: bidId },
      relations: { offerService: { service: true } },
    })
    if (!res) throw new Error('Gói thầu không còn tồn tại')
    if (res.isDeleted) throw new Error('Gói thầu đã ngưng hoạt động')

    const lstItem = []
    for (const item of res.__offerService__) {
      if (item.isExGr) lstItem.push(item)
    }
    res.listItem = lstItem
    for (const item of res.listItem) {
      item.itemName = item.__service__?.code + ' - ' + item.__service__?.name
      delete item.__service__
    }
    delete res.__offerService__

    return res
  }

  async getBid1(user: UserDto, bidId: string, isGetListAccess = false, isTakeExMat?: false) {
    const res: any = await this.findOne({
      where: { id: bidId },
      relations: { offerService: true },
    })
    if (!res) throw new Error('Gói thầu không còn tồn tại')
    if (res.isDeleted) throw new Error('Gói thầu đã ngưng hoạt động')

    //#endregion

    // tìm service

    if (res.prId) res.isLoadFromPr = true
    res.listItem = res.__offerService__ || []

    for (const item of res.listItem) {
      // item.itemName = res.__service__?.code + ' - ' + res.__service__?.name
      item.itemName = item.shortText
      // delete item.__service__
    }
    delete res.__offerService__

    return res
  }
}

@CustomRepository(OfferServiceEntity)
export class OfferServiceRepository extends Repository<OfferServiceEntity> {}

@CustomRepository(OfferPriceEntity)
export class OfferPriceRepository extends Repository<OfferPriceEntity> {
  async getPrice(user: UserDto, bidId: string) {
    return await this.find({
      // where: { bidItemId: bidId, companyId: user?.companyId, parentId: IsNull() },
      where: [{ offerServiceId: bidId }, { offerId: bidId }],
      relations: {
        offerPriceListDetails: true,
        offerPriceColValue: true,
        childs: { offerPriceListDetails: true, offerPriceColValue: true, childs: { offerPriceListDetails: true, offerPriceColValue: true } },
      },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } } },
    })
  }
}

@CustomRepository(OfferDealSupplierEntity)
export class OfferDealSupplierRepository extends Repository<OfferDealSupplierEntity> {}
@CustomRepository(OfferDealSupplierPriceValueEntity)
export class OfferDealSupplierPriceValueRepository extends Repository<OfferDealSupplierPriceValueEntity> {}

@CustomRepository(OfferSupplierServiceEntity)
export class OfferSupplierServiceRepository extends Repository<OfferSupplierServiceEntity> {}

@CustomRepository(OfferSupplierEntity)
export class OfferSupplierRepository extends Repository<OfferSupplierEntity> {}
@CustomRepository(OfferPriceColEntity)
export class OfferPriceColRepository extends Repository<OfferPriceColEntity> {
  async getBidPriceColMPO(user: UserDto, bidId: string) {
    return await this.find({
      // where: { bidItemId: bidId, companyId: user?.companyId, isDeleted: false, colType: enumData.ColType.MPO.code },
      where: [
        { offerServiceId: bidId, isDeleted: false },
        { offerId: bidId, isDeleted: false },
      ],
      order: { sort: 'ASC', createdAt: 'ASC' },
    })
  }

  /** Lấy tất cả cột động gói thầu */
  async getBidPriceColAll(user: UserDto, bidId: string) {
    return await this.find({
      // where: { offerServiceId: bidId, companyId: user?.companyId, isDeleted: false },
      where: [
        { offerServiceId: bidId, isDeleted: false },
        { offerId: bidId, isDeleted: false },
      ],

      order: { sort: 'ASC', createdAt: 'ASC' },
    })
  }
}

@CustomRepository(OfferCustomPriceEntity)
export class OfferCustomPriceRepository extends Repository<OfferCustomPriceEntity> {}

@CustomRepository(OfferServiceEntity)
export class OfferPrItemRepository extends Repository<OfferServiceEntity> {}

@CustomRepository(OfferTradeEntity)
export class OfferTradeRepository extends Repository<OfferTradeEntity> {
  async getTrade(user: UserDto, bidId: string) {
    return await this.find({
      where: [
        { offerServiceId: bidId, isDeleted: false, parentId: IsNull() },
        { offerId: bidId, isDeleted: false, parentId: IsNull() },
      ],
      relations: { offerTradeListDetails: true, childs: { offerTradeListDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })
  }
}

@CustomRepository(OfferSupplierPriceEntity)
export class OfferSupplierPriceRepository extends Repository<OfferSupplierPriceEntity> {
  async saveBidSupplierPrice(user: UserDto, bidSupplier: OfferSupplierEntity, item: any) {
    const bidSupplierPrice = new OfferSupplierPriceEntity()
    bidSupplierPrice.companyId = user?.companyId
    bidSupplierPrice.createdBy = user?.id
    bidSupplierPrice.offerSupplierId = bidSupplier.id
    bidSupplierPrice.offerPriceId = item.id || item.bidPriceId
    bidSupplierPrice.offerPriceName = item.name
    bidSupplierPrice.offerPriceLevel = item.level

    bidSupplierPrice.unitPrice = 0
    const unitPrice = +item.value
    if (!isNaN(unitPrice) && isFinite(unitPrice)) {
      bidSupplierPrice.unitPrice = unitPrice
    }
    bidSupplierPrice.number = item.number
    bidSupplierPrice.price = bidSupplierPrice.unitPrice * bidSupplierPrice.number
    bidSupplierPrice.offerId = bidSupplier.offerId
    // bidSupplierPrice.serviceId = bidSupplier.serviceId || (await bidSupplier.bid).serviceId
    bidSupplierPrice.supplierId = bidSupplier.supplierId
    bidSupplierPrice.submitDate = item.submitDate || new Date()
    bidSupplierPrice.submitType = item.submitType || 0
    await this.save(bidSupplierPrice)
  }
}

@CustomRepository(OfferDealEntity)
export class OfferDealRepository extends Repository<OfferDealEntity> {
  /** Lấy giá mới nhất các Doanh nghiệp chào (cùng số lượng) */
  async getPriceValueNewest(user: UserDto, bidId: string) {
    const bidDealSupplierRepo = this.manager.getRepository(OfferDealSupplierEntity)
    const bidSupplierRepo = this.manager.getRepository(OfferSupplierEntity)
    const bidServiceRepo = this.manager.getRepository(OfferServiceEntity)

    const bidPriceRepo = this.manager.getRepository(OfferPriceEntity)

    const offerService = await bidServiceRepo.findOne({ where: { id: bidId } })

    const lstResult: any[] = []
    const lstBidDeal = await this.find({ where: { offerId: offerService.offerId }, order: { createdAt: 'DESC' } })

    // TH: Có đàm phán
    if (lstBidDeal && lstBidDeal.length > 0) {
      const bidDealLast = lstBidDeal[0]
      const lstDealLastPrice = await bidDealLast.offerDealPrices

      // biến kiểm tra có thay đổi số lượng khi đàm phán
      let isChangeNumber = false
      // lấy các lần đàm phán gần nhất cùng số lượng
      const lstBidDealId = []
      lstBidDealId.push(bidDealLast.id)
      // quét ngược các lần đàm phán
      for (let i = 1; i < lstBidDeal.length; i++) {
        const bidDealCurrent = lstBidDeal[i]
        const lstDealCurrentPrice = await bidDealCurrent.offerDealPrices
        // Kiểm tra lần đàm phán có thay đổi số lượng hay không
        for (const price of lstDealLastPrice) {
          const priceCurrent = lstDealCurrentPrice.find((c) => c.offerPriceId == price.offerPriceId)
          if (!priceCurrent || priceCurrent.number != price.number) {
            isChangeNumber = true
            break
          }
        }
        if (isChangeNumber) break
        else lstBidDealId.push(bidDealCurrent.id)
      }

      // template bảng giá
      const bidPrices = await bidPriceRepo.find({ where: { offerServiceId: bidId, isDeleted: false } })
      if (!isChangeNumber) {
        // Kiểm tra lần có thay đổi số lượng với template bảng giá hay không
        for (const price of lstDealLastPrice) {
          const priceCurrent = bidPrices.find((c) => c.id == price.offerPriceId)
          if (!priceCurrent || priceCurrent.number != price.number) {
            isChangeNumber = true
            break
          }
        }
      }

      // Lấy những Doanh nghiệp đã nộp giá mới
      const lstBidDealSupplier = await bidDealSupplierRepo.find({
        where: {
          offerDealId: In(lstBidDealId),

          status: enumData.BidDealSupplierStatus.DaGuiGiaMoi.code,
        },
        order: { createdAt: 'DESC' },
      })
      // Khi isChangeNumber = false thì kiểm tra thêm hồ sơ chào giá các Doanh nghiệp không tham gia đàm phán

      // Danh sách các Doanh nghiệp tham gia đấu thầu
      const lstBidSupplier = await bidSupplierRepo.find({ where: { offerId: offerService.offerId, parentId: Not(IsNull()) } })

      // Lấy dữ liệu giá cho mỗi Doanh nghiệp tham gia đấu thầu
      for (const bidSupplier of lstBidSupplier) {
        // Kiểm tra Doanh nghiệp có đàm phán k
        const bidDealSupplier = lstBidDealSupplier.find((c) => c.supplierId == bidSupplier.supplierId)
        if (bidDealSupplier) {
          const supplier = await bidDealSupplier.supplier
          const lstValue = await bidDealSupplier.offerDealSupplierPriceValue
          for (const item of lstValue) {
            const itemRes: any = {}
            const bidDealPrice = lstDealLastPrice.find((c) => c.offerPriceId === item.offerPriceId)

            itemRes.bidPriceId = item.offerPriceId

            if (bidDealPrice) {
              itemRes.number = bidDealPrice.number
              itemRes.suggestPrice = bidDealPrice.suggestPrice
              itemRes.maxPrice = bidDealPrice.maxPrice
            }
            if (itemRes.number == null || itemRes.number <= 0) {
              const bidPrice = bidPrices.find((c) => c.id === item.offerPriceId)
              itemRes.number = bidPrice?.number
            }

            itemRes.value = item.value
            itemRes.supplierId = supplier.id
            itemRes.supplierName = supplier.name
            itemRes.supplierScorePrice = bidSupplier?.scorePrice
            lstResult.push(itemRes)
          }
        }
        // Nếu không thay đổi số lượng so với template thì lấy giá nộp chào giá
        else if (!isChangeNumber) {
          if (bidSupplier.statusFile == enumData.BidSupplierFileStatus.HopLe.code) {
            const supplier = await bidSupplier.supplier
            const lstBidSupplierPriceValue = await bidSupplier.offerSupplierPriceValue
            for (const item2 of lstBidSupplierPriceValue) {
              const itemRes: any = {}
              const bidPrice = bidPrices.find((c) => c.id === item2.offerPriceId)

              itemRes.bidPriceId = item2.offerPriceId
              itemRes.number = bidPrice?.number
              itemRes.value = item2.value
              itemRes.supplierId = supplier.id
              itemRes.supplierName = supplier.name
              itemRes.supplierScorePrice = bidSupplier.scorePrice
              lstResult.push(itemRes)
            }
          }
        }
      }
    }
    // TH: Không có đàm phán
    else {
      const statusFile = enumData.BidSupplierFileStatus.HopLe.code
      const lstBidSupplier = await bidSupplierRepo.find({ where: { offerId: bidId, statusFile } })
      if (lstBidSupplier.length == 0) return lstResult

      for (const bidSupplier of lstBidSupplier) {
        const supplier = await bidSupplier.supplier
        const lstBidSupplierPriceValue = await bidSupplier.offerSupplierPriceValue
        for (const item2 of lstBidSupplierPriceValue) {
          const itemRes: any = {}
          const bidPrice = await item2.offerPrice

          itemRes.bidPriceId = item2.offerPriceId
          itemRes.number = bidPrice.number
          itemRes.value = item2.value
          itemRes.supplierId = supplier.id
          itemRes.supplierName = supplier.name
          itemRes.supplierScorePrice = bidSupplier.scorePrice
          lstResult.push(itemRes)
        }
      }
    }

    return lstResult
  }
}

@CustomRepository(OfferSupplierShipmentValueEntity)
export class OfferSupplierShipmentValueRepository extends Repository<OfferSupplierShipmentValueEntity> {}
