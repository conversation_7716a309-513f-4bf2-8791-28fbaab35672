import { MigrationInterface, QueryRunner } from "typeorm";

export class addTogle1742008450595 implements MigrationInterface {
    name = 'addTogle1742008450595'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid\` ADD \`watchProfile\` tinyint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid\` DROP COLUMN \`watchProfile\``);
    }

}
