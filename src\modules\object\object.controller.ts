import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { ObjectService } from './object.service'
import { ObjectCreateDto } from './dto/objectCreate.dto'
import { ObjectUpdateDto } from './dto/objectUpdate.dto'
import { CurrentUser, Roles } from '../common/decorators'
import { PaginationDto, UserDto } from '../../dto'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { enumProject } from '../../constants'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Object')
@Controller('object')
export class ObjectController {
  constructor(private readonly service: ObjectService) {}

  @ApiOperation({ summary: 'Danh sách đối tượng' })
  @UseGuards(ApeAuthGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: {}) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Danh sách đối tượng phân trang' })
  @Roles(enumProject.Features.SETTING_017.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo đối tượng' })
  @Roles(enumProject.Features.SETTING_017.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: ObjectCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật đối tượng' })
  @Roles(enumProject.Features.SETTING_017.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: ObjectUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động đối tượng' })
  @Roles(enumProject.Features.SETTING_017.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateIsDelete(user, data.id)
  }
}
