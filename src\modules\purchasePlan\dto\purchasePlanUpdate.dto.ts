import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpty, <PERSON><PERSON>ptional, IsString } from 'class-validator'
import { PurchasePlanProgressDto } from './purchasePlanProgress.dto'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'

export class PurchasePlanUpdateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  departmentId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  serviceId: string

  @ApiPropertyOptional()
  @IsOptional()
  quantityInbound: number

  @ApiProperty()
  @IsNotEmpty()
  budget: number

  @ApiPropertyOptional()
  @IsOptional()
  description: string

  @ApiProperty()
  @IsArray()
  __progresss__: PurchasePlanProgressDto[]
}
