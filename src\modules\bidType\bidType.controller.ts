import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { BidTypeService } from './bidType.service'
import { BidTypeCreateDto, BidTypeUpdateDto } from './dto'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { PaginationDto, UserDto } from '../../dto'
import { enumProject } from '../../constants'
import { CurrentUser, Roles } from '../common/decorators'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Bid Type')
/** Hình thức đấu thầu */
@Controller('bidTypes')
export class BidTypeController {
  constructor(private readonly service: BidTypeService) {}

  @ApiOperation({ summary: 'L<PERSON>y danh sách hình thức đấu thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto) {
    return await this.service.find(user)
  }

  @ApiOperation({ summary: 'Danh sách hình thức đấu thầu phân trang' })
  @Roles(enumProject.Features.SETTING_015.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo hình thức đấu thầu' })
  @Roles(enumProject.Features.SETTING_015.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: BidTypeCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật hình thức đấu thầu' })
  @Roles(enumProject.Features.SETTING_015.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: BidTypeUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động hình thức đấu thầu' })
  @Roles(enumProject.Features.SETTING_015.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateIsDelete(user, data)
  }
}
