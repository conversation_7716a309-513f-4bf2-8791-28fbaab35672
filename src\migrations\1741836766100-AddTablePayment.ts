import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTablePayment1741836766100 implements MigrationInterface {
    name = 'AddTablePayment1741836766100'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`payment\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`status\` varchar(50) NULL, \`code\` varchar(50) NOT NULL, \`name\` varchar(250) NULL, \`fileAttach\` varchar(4000) NULL, \`filePaymentRequest\` varchar(4000) NULL, \`fileAcceptanceReport\` varchar(4000) NULL, \`note\` text NULL, \`supplierId\` varchar(255) NULL, \`settingStringId\` varchar(255) NULL, \`moneyAdvance\` bigint NULL DEFAULT '0', \`paymentType\` varchar(10) NULL, \`isSupplierCreate\` tinyint NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`payment_bill\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`billId\` varchar(255) NOT NULL, \`paymentId\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`payment_po\` ADD CONSTRAINT \`FK_3ad23ea16b4999989a500093da1\` FOREIGN KEY (\`poId\`) REFERENCES \`po\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`payment_po\` ADD CONSTRAINT \`FK_548e1e23c4bd4160b0d4229b3b8\` FOREIGN KEY (\`paymentId\`) REFERENCES \`payment\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`payment_contract\` ADD CONSTRAINT \`FK_c7312ab6067307abca093192227\` FOREIGN KEY (\`contractId\`) REFERENCES \`contract\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`payment_contract\` ADD CONSTRAINT \`FK_fd92000b542ff541c7e2001ad7d\` FOREIGN KEY (\`paymentId\`) REFERENCES \`payment\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`payment\` ADD CONSTRAINT \`FK_a3772a91be111b4b6a8a496fffa\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`payment\` ADD CONSTRAINT \`FK_57063ce74d3c74ed2def217e95e\` FOREIGN KEY (\`settingStringId\`) REFERENCES \`setting_string\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`payment_bill\` ADD CONSTRAINT \`FK_0a987673166bbd293ee1113c503\` FOREIGN KEY (\`billId\`) REFERENCES \`bill\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`payment_bill\` ADD CONSTRAINT \`FK_54038a0e8db8407df9426fb8ccd\` FOREIGN KEY (\`paymentId\`) REFERENCES \`payment\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`payment_bill\` DROP FOREIGN KEY \`FK_54038a0e8db8407df9426fb8ccd\``);
        await queryRunner.query(`ALTER TABLE \`payment_bill\` DROP FOREIGN KEY \`FK_0a987673166bbd293ee1113c503\``);
        await queryRunner.query(`ALTER TABLE \`payment\` DROP FOREIGN KEY \`FK_57063ce74d3c74ed2def217e95e\``);
        await queryRunner.query(`ALTER TABLE \`payment\` DROP FOREIGN KEY \`FK_a3772a91be111b4b6a8a496fffa\``);
        await queryRunner.query(`ALTER TABLE \`payment_contract\` DROP FOREIGN KEY \`FK_fd92000b542ff541c7e2001ad7d\``);
        await queryRunner.query(`ALTER TABLE \`payment_contract\` DROP FOREIGN KEY \`FK_c7312ab6067307abca093192227\``);
        await queryRunner.query(`ALTER TABLE \`payment_po\` DROP FOREIGN KEY \`FK_548e1e23c4bd4160b0d4229b3b8\``);
        await queryRunner.query(`ALTER TABLE \`payment_po\` DROP FOREIGN KEY \`FK_3ad23ea16b4999989a500093da1\``);
        await queryRunner.query(`DROP TABLE \`payment_bill\``);
        await queryRunner.query(`DROP TABLE \`payment\``);
    }

}
