import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { EmployeeNotifyService } from './employeeNotify.service'
import { ApeAuthGuard } from '../common/guards'
import { CurrentUser } from '../common/decorators'
import { UserDto } from '../../dto'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('EmployeeNotify')
@Controller('employeeNotify')
export class EmployeeNotifyController {
  constructor(private readonly service: EmployeeNotifyService) {}

  @ApiOperation({ summary: 'L<PERSON>y các thông báo' })
  @UseGuards(ApeAuthGuard)
  @Post('load')
  public async loadEmployeeNotify(@CurrentUser() user: UserDto, @Body() data: { take: number }) {
    return await this.service.loadEmployeeNotify(user, data)
  }

  @ApiOperation({ summary: '<PERSON>ậ<PERSON> nhật trạng thái đã đọc cho thông báo' })
  @UseGuards(ApeAuthGuard)
  @Post('read')
  public async readEmployeeNotify(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.readEmployeeNotify(user, data)
  }
}
