import { MigrationInterface, QueryRunner } from "typeorm";

export class migrationMaterial1730279634407 implements MigrationInterface {
    name = 'migrationMaterial1730279634407'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid_tech\` ADD \`itemId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`bid_tech\` ADD CONSTRAINT \`FK_e5653f883942ac21c5bc0e5f3f4\` FOREIGN KEY (\`itemId\`) REFERENCES \`material\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid_tech\` DROP FOREIGN KEY \`FK_e5653f883942ac21c5bc0e5f3f4\``);
        await queryRunner.query(`ALTER TABLE \`bid_tech\` DROP COLUMN \`itemId\``);
    }

}
