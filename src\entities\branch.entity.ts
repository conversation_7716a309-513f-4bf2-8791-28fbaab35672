import { <PERSON>ti<PERSON>, Column, OneToMany, ManyToOne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm'
import { AsnEntity } from './asn.entity'
import { BaseEntity } from './base.entity'
import { BranchMemberEntity } from './branchMember.entity'
import { ContractEntity } from './contract.entity'
import { DepartmentEntity } from './department.entity'
import { EmployeeEntity } from './employee.entity'
import { PrEntity } from './pr.entity'
import { POEntity } from './po.entity'

@Entity('branch')
export class BranchEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  code: string

  @Column({
    type: 'text',
  })
  description: string

  /** <PERSON>ạ<PERSON> chi nh<PERSON>h enum BranchType */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  type: string

  // Cấp bậc c<PERSON>a công ty (1-2-3)
  @Column({ nullable: false })
  level: number

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  parentId: string
  @ManyToOne(() => BranchEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: Promise<BranchEntity>

  @OneToMany(() => BranchEntity, (p) => p.parent)
  childs: Promise<BranchEntity[]>

  @OneToMany(() => DepartmentEntity, (p) => p.branch)
  departments: Promise<DepartmentEntity[]>

  @OneToMany(() => AsnEntity, (p) => p.branch)
  asn: Promise<AsnEntity[]>

  @OneToMany(() => BranchMemberEntity, (p) => p.branch)
  branchMembers: Promise<BranchMemberEntity[]>

  @OneToMany(() => POEntity, (p) => p.branch)
  pos: Promise<POEntity[]>

  @OneToMany(() => ContractEntity, (p) => p.branch)
  contracts: Promise<ContractEntity[]>

  /** Danh sách nhân viên */
  @OneToMany(() => EmployeeEntity, (p) => p.branch)
  employees: Promise<EmployeeEntity[]>

  @OneToMany(() => PrEntity, (p) => p.branch)
  prs: Promise<PrEntity[]>
}
