import { ApiProperty } from '@nestjs/swagger'
import { IsOptional } from 'class-validator'

/** Interface Id */
export class LanguageFilterDto {
  @ApiProperty({ description: 'key <PERSON>ô<PERSON>', required: false })
  @IsOptional()
  key?: string

  @ApiProperty({ description: '<PERSON><PERSON><PERSON>', required: false })
  @IsOptional()
  value?: string

  @ApiProperty({ description: 'Loại ngôn ng<PERSON> (ví dụ VI)', required: false })
  @IsOptional()
  languageType?: string

  @ApiProperty({ description: 'Trạng thái hoạt động', required: false })
  @IsOptional()
  isDeleted?: boolean
}
