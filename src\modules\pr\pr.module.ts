import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { PrService } from './pr.service'
import { PrController } from './pr.controller'
import { EmployeeRepository, PrApproverRepository, PrItemRepository, PrRepository, ServiceTechRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([PrRepository, PrItemRepository, ServiceTechRepository, PrApproverRepository, EmployeeRepository])],
  controllers: [PrController],
  providers: [PrService],
  exports: [PrService],
})
export class PrModule {}
