import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import {
  ServiceCustomPriceRepository,
  ServicePriceRepository,
  ServiceRepository,
  ServiceTradeRepository,
  SupplierRepository,
} from '../../repositories'
import {
  OfferCustomPriceRepository,
  OfferDealRepository,
  OfferPriceColRepository,
  OfferPriceRepository,
  OfferPrItemRepository,
  OfferRepository,
  OfferServiceRepository,
  // OfferSupplierPriceRepository,
  OfferSupplierRepository,
  OfferSupplierServiceRepository,
  OfferSupplierShipmentValueRepository,
  OfferTradeRepository,
} from '../../repositories/offer.repository'
import { OfferController } from './offer.controller'
import { OfferService } from './offer.service'
import { OfferTradeService } from './offerTrade.service'
import { OfferPriceService } from './offerPrice.service'
import { OfferClientController } from './offerClient.controller'
import { OfferClientService } from './offerClient.service'
import { OfferSupplerService } from './offerSupplier.service'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      OfferRepository,
      ServiceRepository,
      OfferTradeRepository,
      OfferPriceRepository,
      OfferPriceColRepository,
      OfferCustomPriceRepository,
      OfferSupplierRepository,
      OfferServiceRepository,
      OfferTradeRepository,
      ServiceTradeRepository,
      OfferServiceRepository,
      OfferPriceRepository,
      OfferDealRepository,
      OfferSupplierRepository,
      ServiceCustomPriceRepository,
      ServicePriceRepository,
      OfferTradeRepository,
      OfferPriceColRepository,
      OfferCustomPriceRepository,
      OfferPrItemRepository,
      SupplierRepository,
      SupplierRepository,
      OfferSupplierServiceRepository,
      // OfferSupplierPriceRepository,
      OfferSupplierShipmentValueRepository,
    ]),
  ],
  controllers: [OfferController, OfferClientController],
  providers: [OfferService, OfferTradeService, OfferPriceService, OfferClientService, OfferSupplerService],
  exports: [OfferClientService],
})
export class OfferModule {}
