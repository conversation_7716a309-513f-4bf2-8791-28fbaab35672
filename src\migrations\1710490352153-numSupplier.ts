import { MigrationInterface, QueryRunner } from 'typeorm'

export class numSupplier1710490352153 implements MigrationInterface {
  name = 'numSupplier1710490352153'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`auction\` ADD \`numSupplier\` int NOT NULL DEFAULT '0'`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`auction\` DROP COLUMN \`numSupplier\``)
  }
}
