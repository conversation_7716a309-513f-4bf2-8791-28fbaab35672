import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class PoProductDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  poId: string

  @ApiPropertyOptional()
  @IsOptional()
  description: string

  @ApiPropertyOptional()
  @IsOptional()
  type: string

  @ApiPropertyOptional()
  @IsOptional()
  group: string

  @ApiProperty()
  @IsNotEmpty()
  name: string

  @ApiProperty()
  @IsNotEmpty()
  serviceId: string

  @ApiPropertyOptional()
  @IsOptional()
  money: number

  @IsOptional()
  itemId: string

  @ApiPropertyOptional()
  @IsOptional()
  price: number

  @ApiPropertyOptional()
  @IsOptional()
  quantity: number

  @ApiPropertyOptional()
  @IsOptional()
  unit: string

  @ApiPropertyOptional()
  @IsOptional()
  note: string
}
