import { Injectable } from '@nestjs/common'
import { LanguageConfigRepository } from '../../repositories'
import { LanguageConfigCreateDto } from './dto/languageConfigCreate.dto'
import { LanguageConfigUpdateDto } from './dto/languageConfigUpdate.dto'
import { PaginationDto, UserDto } from '../../dto'
import { Like } from 'typeorm'
import { CREATE_SUCCESS, ERROR_DUPLICATE_DATA, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { LanguageConfigEntity, LanguageEntity } from '../../entities'

@Injectable()
export class LanguageConfigService {
  constructor(private readonly repo: LanguageConfigRepository) {}

  public async createData(data: LanguageConfigCreateDto, user: UserDto) {
    const objCheck = await this.repo.findOne({
      where: { languageId: data.languageId, key: data.key, component: data.component, companyId: user.companyId },
    })
    if (objCheck) throw new Error(ERROR_DUPLICATE_DATA)

    const entity = new LanguageConfigEntity()
    entity.languageId = data.languageId
    entity.sourceType = data.sourceType
    entity.component = data.component
    entity.key = data.key
    entity.value = data.value
    entity.companyId = user.companyId
    entity.description = data.description
    entity.createdBy = user.id
    await this.repo.save(entity)

    return { message: CREATE_SUCCESS }
  }

  public async updateData(data: LanguageConfigUpdateDto, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.languageId = data.languageId
    entity.sourceType = data.sourceType
    entity.component = data.component
    entity.key = data.key
    entity.value = data.value
    entity.description = data.description
    entity.updatedBy = user.id
    await this.repo.save(entity)

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { companyId: user.companyId }
    if (data.where.key) whereCon.key = Like(`%${data.where.key}%`)
    if (data.where.value) whereCon.value = Like(`%${data.where.value}%`)
    if (data.where.languageId) whereCon.languageId = data.where.languageId
    if (data.where.component) whereCon.component = data.where.component
    if (data.where.sourceType) whereCon.sourceType = data.where.sourceType
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    })
  }

  public async updateIsDelete(user: UserDto, id: string) {
    const entity = await this.repo.findOne({ where: { id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async findLanguageConfig(id: string) {
    const result = await this.repo.find({
      where: {
        isDeleted: false,
        languageId: id,
      },
      select: {
        key: true,
        value: true,
        component: true,
      },
    })

    return result
  }

  public async createDataExcel(data: LanguageConfigCreateDto[], user: UserDto) {
    return this.repo.manager.transaction(async (manager) => {
      const languageRepo = manager.getRepository(LanguageEntity)
      const languageConfigRepo = manager.getRepository(LanguageConfigEntity)

      for (let item of data) {
        const findLanguage = await languageRepo.findOne({ where: { code: item.languageCode, isDeleted: false, companyId: user.companyId } })
        if (!findLanguage) throw new Error('Mã ngôn ngữ không tồn tại trong hệ thống')

        const exist = await languageConfigRepo.findOne({ where: { languageId: findLanguage.id, key: item.key, companyId: user.companyId } })
        if (exist) {
          exist.value = item.value
          exist.updatedBy = user.id
          await languageConfigRepo.save(exist)
        } else {
          const entity = new LanguageConfigEntity()
          entity.languageId = findLanguage.id
          entity.key = item.key
          entity.value = item.value
          entity.companyId = user.companyId
          entity.createdBy = user.id
          await languageConfigRepo.save(entity)
        }
      }

      return { message: CREATE_SUCCESS }
    })
  }
}
