import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { AppController } from './app.controller'
import { AppService } from './app.service'
import { AuthModule } from './auth/auth.module'
import { DepartmentModule } from './department/department.module'
import { ServiceModule } from './service/service.module'
import { EmployeeModule } from './employee/employee.module'
import { LinkClientModule } from './linkClient/linkClient.module'
import { SettingStringClientModule } from './settingStringClient/settingStringClient.module'
import { BannerClientModule } from './bannerClient/bannerClient.module'
import { HomePageModule } from './homePage/homePage.module'
import { SupplierExpertiseModule } from './supplierExpertise/supplierExpertise.module'
import { SettingStringModule } from './settingString/settingString.module'
import { ServiceSettingModule } from './serviceSetting/serviceSetting.module'
import { SupplierRegistrationModule } from './supplierRegistration/supplierRegistration.module'
import { UploadFileModule } from './uploadFile/uploadFile.module'
import { BidTypeModule } from './bidType/bidType.module'
import { BidModule } from './bid/bid.module'
import { SupplierReviewalModule } from './supplierReviewal/supplierReviewal.module'
import { EmailModule } from './email/email.module'
import { SupplierModule } from './supplier/supplier.module'
import { TasksModule } from './common/tasks/tasks.module'
import { ScheduleModule } from '@nestjs/schedule'
import { SQSModule } from './common/sqs/sqs.module'
import { BidRateModule } from './bid/bidRate/bidRate.module'
import { SqsHandlerModule } from './common/sqs/sqsHandler.module'
import { BidDealModule } from './bidDeal/bidDeal.module'
import { BidAuctionModule } from './bidAuction/bidAuction.module'
import { BidDetailModule } from './bidDetail/bidDetail.module'
import { BidEvaluationModule } from './bid/bidEvaluation/bidEvaluation.module'
import { ReportModule } from './report/report.module'
import { FaqCategoryModule } from './faqCategory/faqCategory.module'
import { FaqModule } from './faq/faq.module'
import { ContractModule } from './contract/contract.module'
import { ObjectModule } from './object/object.module'
import { ContractAppendixModule } from './contractAppendix/contractAppendix.module'
import { InvoiceSuggestModule } from './invoiceSuggest/invoiceSuggest.module'
import { InvoiceModule } from './invoice/invoice.module'
import { POModule } from './po/po.module'
import { BranchModule } from './branch/branch.module'
import { PurchasePlanModule } from './purchasePlan/purchasePlan.module'
import { WarehouseModule } from './warehouse/warehouse.module'
import { AsnModule } from './asn/asn.module'
import { PrModule } from './pr/pr.module'
import { DashboardModule } from './dashboard/dashboard.module'
import { EmployeeNotifyModule } from './employeeNotify/employeeNotify.module'
import { EmployeeWarningModule } from './employeeWarning/employeeWarning.module'
import { LanguageModule } from './language/language.module'
import { LanguageConfigModule } from './languageConfig/languageConfig.module'
import { EmailTemplateModule } from './emailTemplate/emailTemplate.module'
import { ClientWebModule } from './clientWeb/clientWeb.module'
import { PaymentProgressModule } from './paymentProgress/paymentProgress.module'
import { BranchMemberModule } from './branch/branchMember/branchMember.module'
import { LanguageKeyModule } from './languageKey/languageKey.module'
import { AuctionModule } from './auction/auction.module'
import { HttpModule } from '@nestjs/axios'
import { OfferModule } from './offer/offer.module'
import { MaterialModule } from './material/material.module'
import { BillLookupModule } from './billLookup/billLookup.module'
import { BillModule } from './bill/bill.module'
import { AiModule } from './ai/ai.module'
import { OfferRateModule } from './offer/offerRate/offerRate.module'
import { OfferEvaluationModule } from './offer/offerEvaluation/offerEvaluation.module'
import { PaymentModule } from './payment/payment.module'
import { InboundModule } from './inbound/inbound.module'

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    HttpModule,
    ScheduleModule.forRoot(),
    TasksModule,
    SQSModule,
    SqsHandlerModule.forRoot(),
    AuthModule,
    ClientWebModule,
    DepartmentModule,
    ServiceModule,
    EmployeeModule,
    SettingStringModule,
    LinkClientModule,
    SettingStringClientModule,
    BannerClientModule,
    HomePageModule,
    SupplierExpertiseModule,
    SupplierRegistrationModule,
    ServiceSettingModule,
    UploadFileModule,
    BidTypeModule,
    BidModule,
    BidRateModule,
    MaterialModule,
    EmailModule,
    SupplierReviewalModule,
    SupplierModule,
    SQSModule,
    OfferModule,
    BidDealModule,
    BidEvaluationModule,
    BidAuctionModule,
    LanguageKeyModule,
    BidDetailModule,
    EmailTemplateModule,
    ReportModule,
    FaqCategoryModule,
    FaqModule,
    ContractModule,
    ObjectModule,
    ContractAppendixModule,
    InvoiceModule,
    InvoiceSuggestModule,
    POModule,
    BranchModule,
    BranchMemberModule,
    PurchasePlanModule,
    WarehouseModule,
    AsnModule,
    PrModule,
    DashboardModule,
    EmployeeNotifyModule,
    EmployeeWarningModule,
    LanguageModule,
    LanguageConfigModule,
    PaymentProgressModule,
    AuctionModule,
    BillLookupModule,
    BillModule,
    AiModule,
    OfferRateModule,
    OfferEvaluationModule,
    PaymentModule,
    InboundModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
