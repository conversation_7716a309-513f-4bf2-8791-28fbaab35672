{"name": "ape-bidding-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node ./node_modules/typeorm/cli -d ./src/typeorm/typeorm.config.ts", "typeorm:generate-migration": "yarn typeorm migration:generate ./src/migrations/migration_name", "typeorm:run-migrations": "yarn typeorm migration:run", "typeorm:revert-migration": "yarn typeorm migration:revert"}, "dependencies": {"@nestjs/axios": "^1.0.1", "@nestjs/common": "^9.2.1", "@nestjs/config": "^2.2.0", "@nestjs/core": "^9.2.1", "@nestjs/jwt": "^9.0.0", "@nestjs/passport": "^9.0.0", "@nestjs/platform-express": "^9.2.1", "@nestjs/schedule": "^2.1.0", "@nestjs/swagger": "^6.2.1", "@nestjs/typeorm": "^9.0.1", "@types/bcrypt": "^5.0.0", "@types/cron": "^2.0.0", "@types/passport-jwt": "^3.0.8", "@types/passport-local": "^1.0.34", "@types/validator": "^13.7.10", "aws-sdk": "^2.1282.0", "bcrypt": "^5.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "date-fns": "^2.29.3", "express-rate-limit": "^6.7.0", "googleapis": "^144.0.0", "mathjs": "^11.5.0", "moment": "^2.29.4", "mysql2": "^3.13.0", "nanoid": "^3.3.4", "nodemailer": "^6.8.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.8.0", "sqs-consumer": "^6.1.0", "typeorm": "^0.3.21"}, "devDependencies": {"@nestjs/cli": "^9.1.5", "@nestjs/schematics": "^9.0.3", "@nestjs/testing": "^9.2.1", "@types/express": "^4.17.15", "@types/jest": "29.2.4", "@types/node": "^18.11.18", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.47.1", "@typescript-eslint/parser": "^5.47.1", "eslint": "^8.30.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "jest": "29.3.1", "prettier": "^2.8.1", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "29.0.3", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "tsconfig-paths": "4.1.1", "typescript": "^4.9.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}