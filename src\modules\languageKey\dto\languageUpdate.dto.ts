import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsOptional } from 'class-validator'

class LanguageDto {
  @ApiProperty({ description: 'Key ngôn ngữ' })
  @IsNotEmpty()
  @IsString()
  key: string

  @ApiProperty({ description: 'Giá Trị ngôn ngữ' })
  @IsNotEmpty()
  @IsString()
  value: string

  @ApiProperty({ description: 'Loại ngôn ngữ' })
  @IsNotEmpty()
  @IsString()
  languageType: string

  @ApiProperty({ description: 'URL về ngôn ngữ', required: false })
  @IsOptional()
  @IsString()
  path: string

  @ApiProperty({ description: 'Ghi chú về ngôn ngữ', required: false })
  @IsOptional()
  @IsString()
  description: string
}

/** Interface Cập nhật ngôn ngữ. */
export class LanguageUpdateDto {
  @ApiProperty({ description: 'Loại ngôn ngữ' })
  languageType: string

  @ApiProperty({ description: '<PERSON>h sách thiết lập ngôn ngữ', type: [LanguageDto] })
  items: LanguageDto[]
}
