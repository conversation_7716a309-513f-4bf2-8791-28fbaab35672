import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsBoolean, IsNumber } from 'class-validator'

export class ServicePriceCreateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string
  @ApiPropertyOptional()
  sort: number

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isRequired: boolean

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isSetup: boolean

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isTemplate: boolean

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  type: string

  @ApiPropertyOptional()
  unit: string
  @ApiPropertyOptional()
  currency: string
  @ApiPropertyOptional()
  number: number

  @ApiPropertyOptional()
  percent: number

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  level: number

  @ApiPropertyOptional()
  description: string

  @ApiPropertyOptional()
  parentId: string

  @ApiPropertyOptional()
  scoreDLC: number
  @ApiPropertyOptional()
  requiredMin: number

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  serviceId: string
}
