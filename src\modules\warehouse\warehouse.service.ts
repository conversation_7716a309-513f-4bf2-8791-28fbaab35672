import { Injectable, ConflictException } from '@nestjs/common'
import { WarehouseCreateDto, WarehouseUpdateDto } from './dto'
import { ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { WarehouseRepository } from '../../repositories'
import { Like } from 'typeorm'

@Injectable()
export class WarehouseService {
  constructor(private readonly repo: WarehouseRepository) {}

  public async find(user: UserDto) {
    const whereCon: any = { isDeleted: false, companyId: user.companyId }
    return await this.repo.find({ where: whereCon, order: { code: 'ASC' } })
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { companyId: user.companyId }
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { name: 'ASC' },
    })
  }

  public async createData(user: UserDto, data: WarehouseCreateDto) {
    const objCheckCode = await this.repo.findOne({ where: { code: data.code, companyId: user.companyId }, select: { id: true } })
    if (objCheckCode) throw new ConflictException(ERROR_CODE_TAKEN)

    const newEntity = this.repo.create({ ...data })
    newEntity.createdBy = user.id
    newEntity.companyId = user.companyId
    await this.repo.save(newEntity)
    return { message: 'Tạo mới thành công' }
  }

  public async updateData(user: UserDto, data: WarehouseUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    entity.name = data.name
    entity.description = data.description
    entity.updatedBy = user.id
    await this.repo.save(entity)

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async updateIsDelete(user: UserDto, data: { id: string }) {
    if (!data.id) throw new Error(ERROR_NOT_FOUND_DATA)
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }
}
