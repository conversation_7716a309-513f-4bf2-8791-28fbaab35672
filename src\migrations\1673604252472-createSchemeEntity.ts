import { MigrationInterface, QueryRunner } from "typeorm";

export class createSchemeEntity1673604252472 implements MigrationInterface {
    name = 'createSchemeEntity1673604252472'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`scheme_detail\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`schemeId\` varchar(36) NOT NULL, \`serviceId\` varchar(36) NOT NULL, \`quantity\` float NOT NULL DEFAULT '0', \`money\` float NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`scheme\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(10) NOT NULL, \`name\` varchar(100) NOT NULL, \`totalMoney\` float NOT NULL, \`status\` varchar(50) NOT NULL DEFAULT 'MoiTao', \`purchasePlanId\` varchar(36) NULL, \`prId\` varchar(36) NULL, \`description\` varchar(250) NULL, UNIQUE INDEX \`REL_0977846fba96761c060fc5258e\` (\`purchasePlanId\`), UNIQUE INDEX \`REL_7c9eb25163968be8b50d92d0fe\` (\`prId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`po\` ADD \`schemeId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`pr\` ADD \`schemeId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`pr\` ADD UNIQUE INDEX \`IDX_c5ea8ff2353ce3a8adf1cb2caf\` (\`schemeId\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`REL_c5ea8ff2353ce3a8adf1cb2caf\` ON \`pr\` (\`schemeId\`)`);
        await queryRunner.query(`ALTER TABLE \`scheme_detail\` ADD CONSTRAINT \`FK_57f966ffacf32b79e90f2499c2f\` FOREIGN KEY (\`schemeId\`) REFERENCES \`scheme\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`scheme_detail\` ADD CONSTRAINT \`FK_93d9986ec3ae0554582cbb6f396\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`scheme\` ADD CONSTRAINT \`FK_0977846fba96761c060fc5258e6\` FOREIGN KEY (\`purchasePlanId\`) REFERENCES \`purchase_plan\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`scheme\` ADD CONSTRAINT \`FK_7c9eb25163968be8b50d92d0fe2\` FOREIGN KEY (\`prId\`) REFERENCES \`pr\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`po\` ADD CONSTRAINT \`FK_683006cef22b658ae6def587003\` FOREIGN KEY (\`schemeId\`) REFERENCES \`scheme\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`pr\` ADD CONSTRAINT \`FK_c5ea8ff2353ce3a8adf1cb2caf1\` FOREIGN KEY (\`schemeId\`) REFERENCES \`scheme\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`pr\` DROP FOREIGN KEY \`FK_c5ea8ff2353ce3a8adf1cb2caf1\``);
        await queryRunner.query(`ALTER TABLE \`po\` DROP FOREIGN KEY \`FK_683006cef22b658ae6def587003\``);
        await queryRunner.query(`ALTER TABLE \`scheme\` DROP FOREIGN KEY \`FK_7c9eb25163968be8b50d92d0fe2\``);
        await queryRunner.query(`ALTER TABLE \`scheme\` DROP FOREIGN KEY \`FK_0977846fba96761c060fc5258e6\``);
        await queryRunner.query(`ALTER TABLE \`scheme_detail\` DROP FOREIGN KEY \`FK_93d9986ec3ae0554582cbb6f396\``);
        await queryRunner.query(`ALTER TABLE \`scheme_detail\` DROP FOREIGN KEY \`FK_57f966ffacf32b79e90f2499c2f\``);
        await queryRunner.query(`DROP INDEX \`REL_c5ea8ff2353ce3a8adf1cb2caf\` ON \`pr\``);
        await queryRunner.query(`ALTER TABLE \`pr\` DROP INDEX \`IDX_c5ea8ff2353ce3a8adf1cb2caf\``);
        await queryRunner.query(`ALTER TABLE \`pr\` DROP COLUMN \`schemeId\``);
        await queryRunner.query(`ALTER TABLE \`po\` DROP COLUMN \`schemeId\``);
        await queryRunner.query(`DROP INDEX \`REL_7c9eb25163968be8b50d92d0fe\` ON \`scheme\``);
        await queryRunner.query(`DROP INDEX \`REL_0977846fba96761c060fc5258e\` ON \`scheme\``);
        await queryRunner.query(`DROP TABLE \`scheme\``);
        await queryRunner.query(`DROP TABLE \`scheme_detail\``);
    }

}
