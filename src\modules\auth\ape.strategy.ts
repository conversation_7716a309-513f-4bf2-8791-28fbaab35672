import { ExtractJwt, Strategy } from 'passport-jwt'
import { PassportStrategy } from '@nestjs/passport'
import { Injectable, UnauthorizedException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { UserRepository } from '../../repositories'
import { JwtPayloadDto } from './dto'
import { apeAuthApiHelper } from '../../helpers'

@Injectable()
export class ApeStrategy extends PassportStrategy(Strategy, 'ape') {
  constructor(protected configService: ConfigService, private userRepo: UserRepository) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
      passReqToCallback: true,
    })
  }

  async validate(req: Request, payload: JwtPayloadDto) {
    /** Không xét phân quyền theo PMS */
    let hasAllRoles = false
    const isProduct = process.env.IS_PRODUCT == 'true'

    if (isProduct) {
      const data: any = await apeAuthApiHelper.validateToken(req)
      if (!data) throw new UnauthorizedException('Không có quyền truy cập! (code: APE_AUTH_TOKEN_ERROR)')

      const user = await this.userRepo.findOne({
        where: { username: data.username, isDeleted: false },
        select: {
          id: true,
          supplierId: true,
          employeeId: true,
          username: true,
          type: true,
          roles: true,
          companyId: true,
        },
      })
      if (!user) throw new UnauthorizedException('Không có quyền truy cập! (code: APE_PMS_USER_ERROR)')

      let lstRole: any[] = []
      if (user.roles) {
        try {
          lstRole = JSON.parse(user.roles)
        } catch (err) {
          console.log(err)
        }
      }
      // Nếu Doanh nghiệp thì k áp dụng phân quyền site, chỉ áp dụng phân quyền APE AUTH
      if (user.supplierId) {
        hasAllRoles = true
      }

      return { ...user, roles: lstRole, hasAllRoles }
    }
    // AUTH: On-premise
    else {
      if (!payload.uid) throw new UnauthorizedException('Không có quyền truy cập! (code: PMS_TOKEN_ERROR)')
      const user = await this.userRepo.findOne({
        where: { id: payload.uid },
        select: {
          id: true,
          supplierId: true,
          employeeId: true,
          username: true,
          type: true,
          roles: true,
        },
      })
      if (!user) throw new UnauthorizedException('Không có quyền truy cập! (code: PMS_USER_ERROR)')

      let lstRole: any[] = []
      if (user.roles) {
        try {
          lstRole = JSON.parse(user.roles)
        } catch (err) {
          console.log(err)
        }
      }

      return { ...user, roles: lstRole, hasAllRoles: !!user.supplierId }
    }
  }
}
