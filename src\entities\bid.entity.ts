import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { ServiceEntity } from './service.entity'
import { BidSupplierEntity } from './bidSupplier.entity'
import { BidEmployeeAccessEntity } from './bidEmployeeAccess.entity'
import { BidTechEntity } from './bidTech.entity'
import { BidTradeEntity } from './bidTrade.entity'
import { BidPriceEntity } from './bidPrice.entity'
import { BidTypeEntity } from './bidType.entity'
import { SettingStringEntity } from './settingString.entity'
import { BidHistoryEntity } from './bidHistory.entity'
import { BidDealEntity } from './bidDeal.entity'
import { BidAuctionEntity } from './bidAuction.entity'
import { BidCustomPriceEntity } from './bidCustomPrice.entity'
import { BidPriceColEntity } from './bidPriceCol.entity'
import { ContractEntity } from './contract.entity'
import { POEntity } from './po.entity'
import { PrItemEntity } from './prItem.entity'
import { PrEntity } from './pr.entity'
import { AuctionEntity } from './auction.entity'
import { enumData } from '../constants'
import { BidItemEntity } from './bidItem.entity'

@Entity('bid')
export class BidEntity extends BaseEntity {
  /** Có tự động đấu thầu không? */
  @Column({
    nullable: false,
    default: false,
  })
  isAutoBid: boolean

  /** Có hiển thị ở trang chủ không */
  @Column({
    nullable: false,
    default: false,
  })
  isShowHomePage: boolean

  /** Có gửi thông báo mời thầu Doanh nghiệp qua email không */
  @Column({
    nullable: false,
    default: true,
  })
  isSendEmailInviteBid: boolean

  /** Đã gửi email mời thầu Doanh nghiệp chưa, nếu chưa thì cho phép sửa isSendEmailInviteBid và gửi mail nếu bật isSendEmailInviteBid = true */
  @Column({
    nullable: false,
    default: false,
  })
  hasSendEmailInviteBid: boolean

  @Column({
    nullable: false,
    default: false,
  })
  isSkipEnd: boolean

  @Column({
    nullable: false,
    default: false,
  })
  isNotImportFromAdmin: boolean

  @Column({
    nullable: false,
    default: false,
  })
  watchProfile: boolean

  /** Tên gói thầu */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** Mã hệ thống tự sinh */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Mô tả nội dung mời thầu */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  serviceInvite: string

  /** Ngày hết hạn xác nhận tham gia đấu thầu */
  @Column({
    nullable: false,
  })
  acceptEndDate: Date

  /** Ngày hết hạn nộp hồ sơ thầu */
  @Column({
    nullable: false,
  })
  submitEndDate: Date

  /** Địa chỉ nộp hồ sơ thầu */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  addressSubmit: string

  /** Công ty mời thầu */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  companyInvite: string

  /** Các địa điểm thực hiện gói thầu */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  listAddress: string

  /** Thời gian đăng tải */
  @Column({
    nullable: false,
  })
  publicDate: Date

  /** Hình thức đấu thầu */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidTypeId: string
  /** Hình thức đấu thầu */
  @ManyToOne(() => BidTypeEntity, (p) => p.bids)
  @JoinColumn({ name: 'bidTypeId', referencedColumnName: 'id' })
  bidType: Promise<BidTypeEntity>

  /** Hiệu lực hợp đồng (tháng) */
  @Column({ type: 'float', nullable: false, default: 0 })
  timeserving: number

  /** Thời điểm mở thầu */
  @Column({ nullable: false })
  startBidDate: Date

  /** Ngày xác nhận mở thầu */
  @Column({
    nullable: true,
  })
  bidOpenDate: Date

  /** Số tiền bảo lãnh dự thầu (VNĐ) */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  moneyGuarantee: number

  /** Thời hạn bảo lãnh dự thầu (tháng) */
  @Column({ type: 'float', nullable: true })
  timeGuarantee: number

  /** Hình thức bảo lãnh dự thầu */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  masterBidGuaranteeId: string
  /** Hình thức bảo lãnh dự thầu */
  @ManyToOne(() => SettingStringEntity, (p) => p.masterBidGuarantee)
  @JoinColumn({ name: 'masterBidGuaranteeId', referencedColumnName: 'id' })
  masterBidGuarantee: Promise<SettingStringEntity>

  /** Thời hạn thiết lập yêu cầu kỹ thuật, năng lực */
  @Column({
    nullable: false,
  })
  timeTechDate: Date

  /** Thời hạn thiết lập các hạng mục báo giá, cơ cấu giá và điều kiện thương mại */
  @Column({
    nullable: false,
  })
  timePriceDate: Date

  /** Thời hạn đánh giá yêu cầu kỹ thuật, năng lực */
  @Column({
    nullable: false,
  })
  timeCheckTechDate: Date

  /** Thời hạn đánh giá các hạng mục báo giá, cơ cấu giá và điều kiện thương mại */
  @Column({
    nullable: false,
  })
  timeCheckPriceDate: Date

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /** Trạng thái thiết lập kỹ thuật */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  statusTech: string

  /** Trạng thái  thiết lập thương mại */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  statusTrade: string

  /** Trạng thái thiết lập giá */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  statusPrice: string

  /** Trạng thái chọn Doanh nghiệp */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
    default: 'ChuaChon',
  })
  statusChooseSupplier: string

  /** Trạng thái đánh giá kỹ thuật */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  statusRateTech: string

  /** Trạng thái đánh giá thương mại */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  statusRateTrade: string

  /** Trạng thái đánh giá giá */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  statusRatePrice: string

  /** Trạng thái cấu hình lại bảng giá enum BidResetPriceStatus */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
    default: 'ChuaTao',
  })
  statusResetPrice: string

  /** Ngày kết thúc nộp chào giá hiệu chỉnh */
  @Column({
    nullable: true,
  })
  resetPriceEndDate: Date

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @Column({
    nullable: true,
  })
  scoreDLC: number

  /** Ghi chú của người tạo khi tạo kỹ thuật */
  @Column({
    type: 'text',
    nullable: true,
  })
  noteTech: string

  /** Ghi chú của người tạo khi tạo ĐKTM */
  @Column({
    type: 'text',
    nullable: true,
  })
  noteTrade: string

  /** Ghi chú của người tạo khi tạo giá */
  @Column({
    type: 'text',
    nullable: true,
  })
  notePrice: string

  /** Ghi chú của người duyệt khi duyệt thông tin kỹ thuật */
  @Column({
    type: 'text',
    nullable: true,
  })
  noteTechLeader: string

  /** Ghi chú của người duyệt khi duyệt thông tin ĐKTM, giá, Doanh nghiệp */
  @Column({
    type: 'text',
    nullable: true,
  })
  noteMPOLeader: string

  /** Người phụ trách đánh giá khi chọn Doanh nghiệp trúng thầu */
  @Column({
    type: 'text',
    nullable: true,
  })
  noteCloseBidMPO: string

  /** Người duyệt đánh giá khi chọn Doanh nghiệp trúng thầu */
  @Column({
    type: 'text',
    nullable: true,
  })
  noteCloseBidMPOLeader: string

  /** Lĩnh vực mời thầu */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  serviceId?: string
  /** Lĩnh vực mời thầu */
  @ManyToOne(() => ServiceEntity, (p) => p.bids)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  /** Id của pr ở gói thầu tổng/gói thầu chi tiết khi tạo gói thầu từ việc chọn pr */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  prId?: string
  /** Pr */
  @ManyToOne(() => PrEntity, (p) => p.bids)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>

  /** Id của prItem ở gói thầu chi tiết khi tạo gói thầu từ việc chọn pr */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  prItemId?: string
  /** Pr */
  @ManyToOne(() => PrItemEntity, (p) => p.bids)
  @JoinColumn({ name: 'prItemId', referencedColumnName: 'id' })
  prItem: Promise<PrItemEntity>

  /** Số lượng của Item trong gói thầu chi tiết */
  @Column({
    nullable: true,
    default: 0,
  })
  quantityItem: number

  /** Yêu cầu hủy gói thầu */
  @Column({
    nullable: false,
    default: false,
  })
  isRequestDelete: boolean

  /** Lý do yêu cầu hủy gói thầu */
  @Column({
    type: 'text',
    nullable: true,
  })
  noteRequestDelete: string

  /** Công thức tính cột đơn giá */
  @Column({
    type: 'text',
    nullable: true,
  })
  fomular: string

  /** Cách tính điểm giá */
  @Column({ type: 'varchar', length: 50, nullable: true, default: enumData.PriceScoreCalculateWay.SumScore.code })
  wayCalScorePrice: string

  /** Tỉ trọng % kỹ thuật */
  @Column({
    nullable: false,
    default: 0,
  })
  percentTech: number

  /** Tỉ trọng % DKTM */
  @Column({
    nullable: false,
    default: 0,
  })
  percentTrade: number

  /** Tỉ trọng % giá */
  @Column({
    nullable: false,
    default: 0,
  })
  percentPrice: number

  /** Bản vẽ kỹ thuật hoặc hình ảnh minh hoạ */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileDrawing: string

  /** Phạm vi công việc */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileJD: string

  /** Tiêu chuẩn đánh giá KPI */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileKPI: string

  /** Các quy định về nội quy gói thầu: an toàn, an ninh, VSTP,... */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileRule: string

  /** Tài liệu mẫu (mẫu báo giá, mẫu hợp đồng,...) */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileDocument: string

  /** Khác */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileAnother: string

  /** Có bắt buộc File chi tiết giá */
  @Column({
    nullable: true,
    default: false,
  })
  isRequireFilePriceDetail: boolean

  /** Có bắt buộc File chi tiết kỹ thuật */
  @Column({
    nullable: true,
    default: false,
  })
  isRequireFileTechDetail: boolean

  @Column({
    nullable: true,
    default: false,
  })
  hiddenScore: boolean

  /** Ngày duyệt chọn Doanh nghiệp thắng thầu */
  @Column({
    nullable: true,
  })
  approveChooseSupplierWinDate: Date

  /** MPO ghi chú khi gửi yêu cầu phê duyệt kết thúc thầu */
  @Column({
    type: 'text',
    nullable: true,
  })
  noteFinishBidMPO: string

  /** File scan kết quả gói thầu do MPO upload khi gửi yêu cầu phê duyệt kết thúc thầu */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileScan: string

  /** Ngày đóng thầu (ngày mpoLead duyệt kết thúc thầu) */
  @Column({
    nullable: true,
  })
  bidCloseDate: Date

  @OneToMany(() => ContractEntity, (p) => p.bid)
  contracts: Promise<ContractEntity[]>

  @OneToMany(() => POEntity, (p) => p.bid)
  pos: Promise<POEntity[]>

  /** Id của gói thầu cha */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  parentId?: string
  /** Cha */
  @ManyToOne(() => BidEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: Promise<BidEntity>

  /** Con - 1 gói thầu sẽ có thể có nhiều gói thầu con */
  @OneToMany(() => BidEntity, (p) => p.parent)
  childs: Promise<BidEntity[]>

  /** Danh sách nhà cung cấp đấu thầu */
  @OneToMany(() => BidSupplierEntity, (p) => p.bid)
  bidSuppliers: Promise<BidSupplierEntity[]>

  /** Danh sách nhân viên có quyền */
  @OneToMany(() => BidEmployeeAccessEntity, (p) => p.bid)
  employeeAccess: Promise<BidEmployeeAccessEntity[]>

  /** 1 dịch vụ sẽ có nhiều cấu hình kỹ thuật */
  @OneToMany(() => BidTechEntity, (p) => p.bid)
  techs: Promise<BidTechEntity[]>

  /** 1 dịch vụ sẽ có nhiều cấu hình thương mại */
  @OneToMany(() => BidTradeEntity, (p) => p.bid)
  trades: Promise<BidTradeEntity[]>

  /** 1 dịch vụ sẽ có nhiều cấu hình giá */
  @OneToMany(() => BidPriceEntity, (p) => p.bid)
  prices: Promise<BidPriceEntity[]>

  /** 1 dịch vụ sẽ có nhiều cấu hình cơ cấu giá */
  @OneToMany(() => BidCustomPriceEntity, (p) => p.bid)
  customPrices: Promise<BidCustomPriceEntity[]>

  /** Lịch sử bid */
  @OneToMany(() => BidHistoryEntity, (p) => p.bid)
  bidHistorys: Promise<BidHistoryEntity[]>

  @OneToMany(() => BidDealEntity, (p) => p.bid)
  bidDeals: Promise<BidDealEntity[]>

  @OneToMany(() => BidAuctionEntity, (p) => p.bid)
  bidAuctions: Promise<BidAuctionEntity[]>

  /** Danh sách các cột bổ sung thêm để Doanh nghiệp nhập dữ liệu */
  @OneToMany(() => BidPriceColEntity, (p) => p.bid)
  bidPriceCols: Promise<BidPriceColEntity[]>

  /** Các lần đấu thầu nhanh */
  @OneToMany(() => AuctionEntity, (p) => p.bid)
  auctions: Promise<AuctionEntity[]>

  @OneToMany(() => BidItemEntity, (p) => p.bid)
  bidItems: Promise<BidItemEntity[]>
}
