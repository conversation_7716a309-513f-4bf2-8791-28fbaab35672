import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsString } from 'class-validator'

export class SupplierCreatePriceItemDto {
  @ApiPropertyOptional()
  bidPriceId: string

  @ApiProperty()
  @IsString()
  value: string

  @ApiPropertyOptional()
  unit: string
  @ApiPropertyOptional()
  currency: string

  @ApiPropertyOptional()
  name: string

  @ApiPropertyOptional()
  number: number

  @ApiPropertyOptional()
  bidPriceParentId: string

  @ApiPropertyOptional()
  __childs__: SupplierCreatePriceItemDto[]
}
