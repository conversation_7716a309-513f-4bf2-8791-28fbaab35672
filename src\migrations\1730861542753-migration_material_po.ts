import { MigrationInterface, QueryRunner } from 'typeorm'

export class migrationMaterialPo1730861542753 implements MigrationInterface {
  name = 'migrationMaterialPo1730861542753'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`bid_custom_price\` ADD \`itemId\` varchar(36) NULL`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`bid_custom_price\` DROP COLUMN \`itemId\``)
  }
}
