import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinC<PERSON>umn } from 'typeorm'
import { BidSupplierEntity } from './bidSupplier.entity'
import { BidTradeEntity } from './bidTrade.entity'

@Entity('bid_supplier_trade_value')
export class BidSupplierTradeValueEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: true,
  })
  score: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  value: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidSupplierId: string
  @ManyToOne(() => BidSupplierEntity, (p) => p.bidSupplierTradeValue)
  @JoinColumn({ name: 'bidSupplierId', referencedColumnName: 'id' })
  bidSupplier: Promise<BidSupplierEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidTradeId: string
  @ManyToOne(() => BidTradeEntity, (p) => p.bidSupplierTradeValue)
  @JoinColumn({ name: 'bidTradeId', referencedColumnName: 'id' })
  bidTrade: Promise<BidTradeEntity>
}
