import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { SupplierService } from './supplier.service'
import { SupplierController } from './supplier.controller'
import {
  SupplierServiceRepository,
  ServiceRepository,
  UserRepository,
  SupplierExpertiseRepository,
  ServiceAccessRepository,
  EmployeeRepository,
  ServiceCapacityRepository,
  SupplierRepository,
  BidSupplierRepository,
} from '../../repositories'
import { EmailModule } from '../email/email.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      SupplierRepository,
      BidSupplierRepository,
      SupplierServiceRepository,
      SupplierExpertiseRepository,
      ServiceRepository,
      ServiceCapacityRepository,
      ServiceAccessRepository,
      UserRepository,
      EmployeeRepository,
    ]),

    EmailModule,
  ],
  controllers: [SupplierController],
  providers: [SupplierService],
  exports: [SupplierService],
})
export class SupplierModule {}
