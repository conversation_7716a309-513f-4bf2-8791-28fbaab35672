import { Controller, UseGuards, Post, Body, Get, Param, Put, Req } from '@nestjs/common'
import { BidService } from './bid.service'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { CurrentUser, Roles } from '../common/decorators'
import { PaginationDto, UserDto } from '../../dto'
import {
  SupplierCreateTechItemDto,
  SupplierCreateTradeItemDto,
  SupplierCreatePriceItemDto,
  SupplierCreateCustomPriceItemDto,
  BidUpdateSettingDto,
  BidCreateDto,
  BidUpdateDto,
} from './dto'
import { enumProject } from '../../constants'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import {
  BidCustomPriceCreateDto,
  BidCustomPriceUpdateDto,
  BidPriceColCreateDto,
  BidPriceColUpdateDto,
  BidPriceCreateDto,
  BidPriceUpdateDto,
  BidTechCreateDto,
  BidTechUpdateDto,
  BidTradeCreateDto,
  BidTradeUpdateDto,
} from './dto2'

@ApiBearerAuth()
@ApiTags('Bid')
@Controller('bids')
export class BidController {
  constructor(private readonly service: BidService) {}

  //#region get data

  @ApiOperation({ summary: 'Lấy ds gói thầu' })
  @Roles(enumProject.Features.CONTRACT_001.code, enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: { status?: string }) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Lấy chi tiết gói thầu khi chỉnh sửa' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_detail_edit')
  public async findDetailEdit(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetailEdit(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds Doanh nghiệp được mời thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_bid_supplier')
  public async findBidSupplier(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.findBidSupplier(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds Doanh nghiệp khi nộp thầu trang admin' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_bid_supplier')
  public async loadBidSupplier(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadBidSupplier(user, data)
  }

  @ApiOperation({ summary: 'Gửi email nội bộ và NCC được mời tham gia thầu' })
  @Roles(enumProject.Features.BID_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('send_email_bid')
  public async sendEmailBid(
    @CurrentUser() user: UserDto,
    @Body() data: { bidId: string; lstEmployeeId: string[]; lstSupplierId: string[]; emailContent: string },
  ) {
    return await this.service.sendEmailBid(user, data)
  }

  @ApiOperation({ summary: 'DS gói thầu phân trang' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Lấy trạng thái gói thầu' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_bid_status/:id')
  public async getBidStatus(@CurrentUser() user: UserDto, @Param('id') id: string) {
    return await this.service.getBidStatus(user, id)
  }

  //#endregion

  //#region createBid

  @ApiOperation({ summary: 'Tạo gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_bid')
  public async createBid(@CurrentUser() user: UserDto, @Body() data: BidCreateDto) {
    return await this.service.createBid(user, data)
  }

  @ApiOperation({ summary: 'Copy gói thầu' })
  @Roles(enumProject.Features.BID_004.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('copy_bid')
  public async copyBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.copyBid(user, data.bidId)
  }

  @ApiOperation({ summary: 'Chỉnh sửa thông tin gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_bid')
  public async updateBid(@CurrentUser() user: UserDto, @Body() data: BidUpdateDto) {
    return await this.service.updateBid(user, data)
  }

  @ApiOperation({ summary: 'Tạo gói thầu bằng excel' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('import_bid')
  public async importBids(@CurrentUser() user: UserDto, @Body() data: { lstData: any[] }) {
    return await this.service.importBids(user, data)
  }

  @ApiOperation({ summary: 'Yêu cầu hủy gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Put('request_delete_bid/:id')
  public async requestDeleteBid(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.requestDeleteBid(user, bidId)
  }

  @ApiOperation({ summary: 'Xác nhận hủy gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Put('delete_bid/:id')
  public async deleteBid(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.deleteBid(user, bidId)
  }

  @ApiOperation({ summary: 'Gửi yêu cầu phê duyệt gói thầu tạm' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Put('send_mpoleader_check_bid/:id')
  public async sendMpoleaderCheckBid(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.sendMpoleaderCheckBid(user, bidId)
  }

  @ApiOperation({ summary: 'Duyệt gói thầu tạm' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Put('mpoleader_accept_bid/:id')
  public async mpoleaderAcceptBid(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.mpoleaderAcceptBid(user, bidId)
  }

  @ApiOperation({ summary: 'Chào giá cạnh tranh cho gói thầu tạm' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Put('mpoleader_accept_bid_quick/:id')
  public async mpoleaderAcceptBidQuick(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.mpoleaderAcceptBidQuick(user, bidId)
  }

  @ApiOperation({ summary: 'Từ chối gói thầu tạm' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Put('mpoleader_reject_bid/:id')
  public async mpoleaderRejectBid(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.mpoleaderRejectBid(user, bidId)
  }

  @ApiOperation({ summary: 'Chỉnh sửa thông tin khác của gói thầu ' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_setting_rate')
  public async updateSettingRate(@CurrentUser() user: UserDto, @Body() data: BidUpdateSettingDto) {
    return await this.service.updateSettingRate(user, data)
  }

  //#endregion

  //#region bidTech

  @ApiOperation({ summary: 'Tải template kỹ thuật từ Item' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('load_tech/:id')
  public async loadTech(@CurrentUser() user: UserDto, @Param('id') bidItemId: string) {
    return await this.service.loadTech(user, bidItemId)
  }

  @ApiOperation({ summary: 'Lưu template kỹ thuật cho gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_tech/:id')
  public async createTech(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { noteTech?: string }) {
    return await this.service.createTech(user, bidId, data)
  }

  @ApiOperation({ summary: 'Lấy ds các tiêu chí kỹ thuật cho gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_tech/:id')
  public async getTech(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.getTech(user, bidId)
  }

  @ApiOperation({ summary: 'Duyệt thiết lập yêu cầu kỹ thuật của gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_accept/:id')
  public async techAccept(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { noteTechLeader?: string }) {
    return await this.service.techAccept(user, bidId, data)
  }

  @ApiOperation({ summary: 'Từ chối thiết lập yêu cầu kỹ thuật của gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_reject/:id')
  public async techReject(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { noteTechLeader?: string }) {
    return await this.service.techReject(user, bidId, data)
  }

  @ApiOperation({ summary: 'Lấy data cbb tiêu chí cấp 1' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('tech_get_data/:bidid')
  public async techGetData(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.service.techGetData(user, bidId)
  }

  @ApiOperation({ summary: 'Lấy data item' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('item_get_data/:bidid')
  public async getItem(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.service.getItem(user, bidId)
  }

  @ApiOperation({ summary: 'Tạo mới tiêu chí kỹ thuật' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_create_data')
  public async techCreateData(@CurrentUser() user: UserDto, @Body() data: BidTechCreateDto) {
    return await this.service.techCreateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật tiêu chí kỹ thuật' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_update_data')
  public async techUpdateData(@CurrentUser() user: UserDto, @Body() data: BidTechUpdateDto) {
    return await this.service.techUpdateData(user, data)
  }

  @ApiOperation({ summary: 'Xóa tiêu chí kỹ thuật' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_delete_data')
  public async techDeleteData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.techDeleteData(user, data.id)
  }

  @ApiOperation({ summary: 'Xoá tất cả tiêu chí kỹ thuật' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_deleteall_data')
  public async techDeleteAllData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.techDeleteAllData(user, data.id)
  }

  @ApiOperation({ summary: 'Import excel kỹ thuật' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_import/:bidId')
  public async tech_import(
    @CurrentUser() user: UserDto,
    @Param('bidId') bidId: string,
    @Body() data: { lstDataTable1: any[]; lstDataTable2: any[] },
  ) {
    return await this.service.tech_import(user, bidId, data)
  }

  @ApiOperation({ summary: 'Danh sách lựa chọn cho tiêu chí kiểu List' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('tech_listdetail_list/:id')
  public async bidTechListDetail_list(@CurrentUser() user: UserDto, @Param('id') id: any) {
    return await this.service.bidTechListDetail_list(user, id)
  }

  @ApiOperation({ summary: 'Tạo lựa chọn cho tiêu chí kiểu List' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_listdetail_create_data')
  public async bidTechListDetail_create_data(@CurrentUser() user: UserDto, @Body() data: { bidTechId: string; name: string; value: number }) {
    return await this.service.bidTechListDetail_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật lựa chọn cho tiêu chí kiểu List' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_listdetail_update_data')
  public async bidTechListDetail_update_data(
    @CurrentUser() user: UserDto,
    @Body() data: { id: string; bidTechId: string; name: string; value: number },
  ) {
    return await this.service.bidTechListDetail_update_data(user, data)
  }

  @ApiOperation({ summary: 'Xóa lựa chọn cho tiêu chí kiểu List' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_listdetail_delete_data')
  public async bidTechListDetail_delete_data(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.bidTechListDetail_delete_data(user, data.id)
  }

  //#endregion

  //#region bidTrade

  @ApiOperation({ summary: 'Tải template ĐKTM từ Item' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('load_trade/:id')
  public async loadTrade(@CurrentUser() user: UserDto, @Param('id') bidItemId: string) {
    return await this.service.loadTrade(user, bidItemId)
  }

  @ApiOperation({ summary: 'Lưu template ĐKTM cho gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_trade/:id')
  public async createTrade(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { noteTrade: string }) {
    return await this.service.createTrade(user, bidId, data)
  }

  @ApiOperation({ summary: 'Lấy thiết lập ĐKTM của gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_trade/:id')
  public async getTrade(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.getTrade(user, bidId)
  }

  @ApiOperation({ summary: 'Lấy data cbb tiêu chí cấp 1' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('trade_get_data/:bidid')
  public async tradeGetData(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.service.tradeGetData(user, bidId)
  }

  @ApiOperation({ summary: 'Tạo thêm ĐKTM' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('trade_create_data')
  public async tradeCreateData(@CurrentUser() user: UserDto, @Body() data: BidTradeCreateDto) {
    return await this.service.tradeCreateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật ĐKTM' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('trade_update_data')
  public async tradeUpdateData(@CurrentUser() user: UserDto, @Body() data: BidTradeUpdateDto) {
    return await this.service.tradeUpdateData(user, data)
  }

  @ApiOperation({ summary: 'Xoá ĐKTM' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('trade_delete_data')
  public async tradeDeleteData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.tradeDeleteData(user, data.id)
  }

  @ApiOperation({ summary: 'Xoá tất cả ĐKTM' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('trade_deleteall_data')
  public async tradeDeleteAllData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.tradeDeleteAllData(user, data.id)
  }

  @ApiOperation({ summary: 'Import excel ĐKTM' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('trade_import/:bidId')
  public async trade_import(
    @CurrentUser() user: UserDto,
    @Param('bidId') bidId: string,
    @Body() data: { lstDataTable1: any[]; lstDataTable2: any[] },
  ) {
    return await this.service.trade_import(user, bidId, data)
  }

  @ApiOperation({ summary: 'Danh sách lựa chọn cho tiêu chí kiểu List' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('trade_listdetail_list/:id')
  public async bidTradeListDetail_list(@CurrentUser() user: UserDto, @Param('id') id: any) {
    return await this.service.bidTradeListDetail_list(user, id)
  }

  @ApiOperation({ summary: 'Tạo lựa chọn cho tiêu chí kiểu List' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('trade_listdetail_create_data')
  public async bidTradeListDetail_create_data(
    @CurrentUser() user: UserDto,
    @Body() data: { id: string; bidTradeId: string; name: string; value: number },
  ) {
    return await this.service.bidTradeListDetail_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật lựa chọn cho tiêu chí kiểu List' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('trade_listdetail_update_data')
  public async bidTradeListDetail_update_data(
    @CurrentUser() user: UserDto,
    @Body() data: { id: string; bidTradeId: string; name: string; value: number },
  ) {
    return await this.service.bidTradeListDetail_update_data(user, data)
  }

  @ApiOperation({ summary: 'Xóa lựa chọn cho tiêu chí kiểu List' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('trade_listdetail_delete_data')
  public async bidTradeListDetail_delete_data(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.bidTradeListDetail_delete_data(user, data.id)
  }

  //#endregion

  //#region bidPrice

  @ApiOperation({ summary: 'Lấy các hạng mục giá của gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_find')
  public async price_find(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.price_find(user, data)
  }

  @ApiOperation({ summary: 'Tải các hạng mục chào giá từ template Item' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('load_price/:id')
  public async loadPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.loadPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Tải các hạng mục cơ cấu giá từ template Item' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('load_customprice/:id')
  public async loadCustomPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.loadCustomPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Lưu template chào giá cho gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_price/:id')
  public async createPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: any) {
    return await this.service.createPrice(user, bidId, data)
  }

  @ApiOperation({ summary: 'Lấy template chào giá cho gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_price/:id')
  public async getPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.getPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Xác nhận cấu hình lại bảng giá' })
  @Roles(enumProject.Features.BID_008.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Put('reset_price/:id')
  public async resetPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.resetPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Lưu cấu hình bảng giá' })
  @Roles(enumProject.Features.BID_008.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('save_reset_price/:id')
  public async saveResetPrice(
    @CurrentUser() user: UserDto,
    @Param('id') bidId: string,
    @Body()
    data: {
      lstSupplierId: string[]
      resetPriceEndDate: Date
      isRequireFilePriceDetail: boolean
      isRequireFileTechDetail: boolean
    },
  ) {
    return await this.service.saveResetPrice(user, bidId, data)
  }

  @ApiOperation({ summary: 'Danh sách ncc đã nộp chào giá hiệu chỉnh' })
  @Roles(enumProject.Features.BID_008.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('bid_supplier_join_reset_price/:bidid')
  public async bidSupplierJoinResetPrice(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.service.bidSupplierJoinResetPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Kết thúc nộp chào giá hiệu chỉnh' })
  @Roles(enumProject.Features.BID_008.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Put('end_reset_price/:id')
  public async endResetPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.endResetPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Lấy template cơ cấu giá của gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_customprice/:id')
  public async getCustomPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.getCustomPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Tạo hạng mục giá mới' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_create_data')
  public async priceCreateData(@CurrentUser() user: UserDto, @Body() data: BidPriceCreateDto) {
    return await this.service.priceCreateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật hạng mục giá' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_update_data')
  public async priceUpdateData(@CurrentUser() user: UserDto, @Body() data: BidPriceUpdateDto) {
    return await this.service.priceUpdateData(user, data)
  }

  @ApiOperation({ summary: 'Xóa hạng mục giá' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_delete_data')
  public async priceDeleteData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.priceDeleteData(user, data.id)
  }

  @ApiOperation({ summary: 'Xóa tất cả hạng mục giá của gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_deleteall_data')
  public async priceDeleteAllData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.priceDeleteAllData(user, data.id)
  }

  @ApiOperation({ summary: 'Lưu công thức tính đơn giá' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('setting_fomular')
  public async setting_fomular(@CurrentUser() user: UserDto, @Body() data: { id: string; fomular: string }) {
    return await this.service.setting_fomular(user, data)
  }

  @ApiOperation({ summary: 'Setup cách tính điểm giá của Item gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('setting_way_cal_score_price')
  public async saveSettingPriceCalWay(@CurrentUser() user: UserDto, @Body() data: { id: string; wayCalScorePrice: string }) {
    return await this.service.saveSettingPriceCalWay(user, data)
  }

  @ApiOperation({ summary: 'Import excel chào giá' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_import/:bidId')
  public async price_import(
    @CurrentUser() user: UserDto,
    @Param('bidId') bidId: string,
    @Body() data: { lstDataTable1: any[]; lstDataTable2: any[] },
  ) {
    return await this.service.price_import(user, bidId, data)
  }

  @ApiOperation({ summary: 'Tạo hạng mục cơ cấu giá mới' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('customprice_create_data')
  public async customPriceCreateData(@CurrentUser() user: UserDto, @Body() data: BidCustomPriceCreateDto) {
    return await this.service.customPriceCreateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật hạng mục cơ cấu giá' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('customprice_update_data')
  public async customPriceUpdateData(@CurrentUser() user: UserDto, @Body() data: BidCustomPriceUpdateDto) {
    return await this.service.customPriceUpdateData(user, data)
  }

  @ApiOperation({ summary: 'Xóa hạng mục cơ cấu giá' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('customprice_delete_data')
  public async customPriceDeleteData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.customPriceDeleteData(user, data.id)
  }

  @ApiOperation({ summary: 'Xóa tất cả hạng mục cơ cấu giá của Item gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('customprice_deleteall_data')
  public async customPriceDeleteAllData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.customPriceDeleteAllData(user, data.id)
  }

  @ApiOperation({ summary: 'Import template cơ cấu giá Item gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('customprice_import/:bidId')
  public async custompriceImport(@CurrentUser() user: UserDto, @Param('bidId') bidId: string, @Body() data: { lstData: any[] }) {
    return await this.service.custompriceImport(user, bidId, data)
  }

  @ApiOperation({ summary: 'Danh sách cột động Item' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('price_col_list/:id')
  public async bidPriceCol_list(@CurrentUser() user: UserDto, @Param('id') id: any) {
    return await this.service.bidPriceCol_list(user, id)
  }

  @ApiOperation({ summary: 'Tạo cột động Item' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_col_create_data')
  public async bidPriceCol_create_data(@CurrentUser() user: UserDto, @Body() data: BidPriceColCreateDto) {
    return await this.service.bidPriceCol_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật cột động Item' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_col_update_data')
  public async bidPriceCol_update_data(@CurrentUser() user: UserDto, @Body() data: BidPriceColUpdateDto) {
    return await this.service.bidPriceCol_update_data(user, data)
  }

  @ApiOperation({ summary: 'Xóa cột động Item' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_col_delete_data')
  public async bidPriceCol_delete_data(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.bidPriceCol_delete_data(user, data.id)
  }

  @ApiOperation({ summary: 'Xóa tất cả cột động Item gói thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_col_delete_all_data')
  public async bidPriceCol_delete_all_data(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.bidPriceCol_delete_all_data(user, data.bidId)
  }

  @ApiOperation({ summary: 'Danh sách thông tin mở rộng của hạng mục chào giá' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('price_listdetail_list/:id')
  public async bidPriceListDetail_list(@CurrentUser() user: UserDto, @Param('id') id: any) {
    return await this.service.bidPriceListDetail_list(user, id)
  }

  @ApiOperation({ summary: 'Tạo thông tin mở rộng của hạng mục chào giá' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_listdetail_create_data')
  public async bidPriceListDetail_create_data(
    @CurrentUser() user: UserDto,
    @Body() data: { bidPriceId: string; name: string; type: string; value: string },
  ) {
    return await this.service.bidPriceListDetail_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật thông tin mở rộng của hạng mục chào giá' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_listdetail_update_data')
  public async bidPriceListDetail_update_data(
    @CurrentUser() user: UserDto,
    @Body() data: { id: string; bidPriceId: string; name: string; type: string; value: string },
  ) {
    return await this.service.bidPriceListDetail_update_data(user, data)
  }

  @ApiOperation({ summary: 'Xóa thông tin mở rộng của hạng mục chào giá' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_listdetail_delete_data')
  public async bidPriceListDetail_delete_data(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.bidPriceListDetail_delete_data(user, data.id)
  }

  //#endregion

  //#region bidChooseSupplier

  @ApiOperation({ summary: 'Lấy danh sách NCC mời tham gia thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_supplier_invite')
  public async loadSupplierInvite(
    @CurrentUser() user: UserDto,
    @Body() data: { bidId: string; supplierName?: string; lstStatus?: string[]; typeGetData: number },
  ) {
    return await this.service.loadSupplierInvite(user, data)
  }

  @ApiOperation({ summary: 'Chọn NCC mời tham gia thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('bid_choose_supplier')
  public async bidChooseSupplier(@CurrentUser() user: UserDto, @Body() data: { bidId: string; lstData: any[] }) {
    return await this.service.bidChooseSupplier(user, data)
  }

  @ApiOperation({ summary: 'Chọn lại NCC mời tham gia thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Put('bid_rechoose_supplier/:id')
  public async bidRechooseSupplier(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.bidRechooseSupplier(user, bidId)
  }

  @ApiOperation({ summary: 'Gửi yêu cầu phê duyệt bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Put('bid_send_mpoleader_check/:id')
  public async bidSendMpoleaderCheck(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.bidSendMpoleaderCheck(user, bidId)
  }
  //#endregion

  //#region accept all

  @ApiOperation({ summary: 'Duyệt tất cả (bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu)' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('accept_all')
  public async acceptAll(@CurrentUser() user: UserDto, @Body() data: { id: string; noteMPOLeader: string }) {
    return await this.service.acceptAll(user, data)
  }

  @ApiOperation({ summary: 'Từ chối tất cả (bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu)' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('reject_all')
  public async rejectAll(@CurrentUser() user: UserDto, @Body() data: { id: string; noteMPOLeader: string }) {
    return await this.service.rejectAll(user, data)
  }

  //#endregion

  //#region open bid

  @ApiOperation({ summary: 'Load ds Doanh nghiệp khi mở thầu' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_bid_supplier_open_bid')
  public async loadBidSupplierOpenBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadBidSupplierOpenBid(user, data)
  }

  @ApiOperation({ summary: 'Xác nhận mở thầu' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('open_bid')
  public async openBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string; lstData: any[]; lstEmployeeId: string[] }) {
    return await this.service.openBid(user, data)
  }

  //#endregion

  //#region Chức năng admin nộp hồ sơ cho Doanh nghiệp

  @ApiOperation({ summary: 'Admin nộp hồ sơ thầu Item cho NCC' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('bidding_from_admin_createBidSupplier')
  public async biddingFromAdmin_createBidSupplier(
    @CurrentUser() user: UserDto,
    @Body()
    data: {
      bidId: string
      supplierId: string
      techInfo: SupplierCreateTechItemDto[]
      tradeInfo: SupplierCreateTradeItemDto[]
      priceInfo: SupplierCreatePriceItemDto[]
      customPriceInfo: SupplierCreateCustomPriceItemDto[]
    },
  ) {
    return await this.service.createBidSupplierFromAdmin(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds hồ sơ kỹ thuật Item của NCC' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('bidding_from_admin_loadDataBidTech')
  public async biddingFromAdmin_loadDataBidTech(@CurrentUser() user: UserDto, @Body() data: { bidId: string; supplierId?: string }) {
    return await this.service.loadDataBidTech(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds hồ sơ ĐKTM Item của NCC' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('bidding_from_admin_loadDataBidTrade')
  public async biddingFromAdmin_loadDataBidTrade(@CurrentUser() user: UserDto, @Body() data: { bidId: string; supplierId?: string }) {
    return await this.service.loadDataBidTrade(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds bảng chào giá Item của NCC' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('bidding_from_admin_loadDataBidPrice')
  public async biddingFromAdmin_loadDataBidPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string; supplierId?: string }) {
    return await this.service.loadDataBidPrice(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds cơ cấu giá Item của NCC' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('bidding_from_admin_loadDataBidCustomPrice')
  public async biddingFromAdmin_loadDataBidCustomPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string; supplierId?: string }) {
    return await this.service.loadDataBidCustomPrice(user, data)
  }

  //#endregion
}
