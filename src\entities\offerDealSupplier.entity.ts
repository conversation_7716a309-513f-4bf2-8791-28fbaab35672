import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { SupplierEntity } from './supplier.entity'
import { OfferDealEntity } from './offerDeal.entity'
import { OfferDealSupplierPriceValueEntity } from './offerDealSupplierPriceValue.entity'

@Entity('offer_deal_supplier')
export class OfferDealSupplierEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  offerDealId: string
  @ManyToOne(() => OfferDealEntity, (p) => p.offerDealSupplier)
  @JoinColumn({ name: 'offerDealId', referencedColumnName: 'id' })
  offerDeal: Promise<OfferDealEntity>

  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  score: number

  @Column({
    type: 'varchar',
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.offerDealSupplier)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /** File chi tiết giá (Nếu có) */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  filePriceDetail: string

  /** File chi tiết kỹ thuật (Nếu có) */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileTechDetail: string

  /** Link driver các file bổ sung (Nếu có) */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  linkDriver: string

  /** Ngày nộp đàm phán giá */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  submitDate: Date

  /** Danh sách giá trị dữ liệu nhà cung cấp đàm phán */
  @OneToMany(() => OfferDealSupplierPriceValueEntity, (p) => p.offerDealSupplier)
  offerDealSupplierPriceValue: Promise<OfferDealSupplierPriceValueEntity[]>
}
