import { MigrationInterface, QueryRunner } from 'typeorm'

export class auction1710118795926 implements MigrationInterface {
  name = 'auction1710118795926'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`auction_supplier\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`auctionId\` varchar(36) NOT NULL, \`supplierId\` varchar(36) NOT NULL, \`submitPriceOld\` bigint NULL, \`submitPrice\` bigint NULL, \`submitDate\` datetime NULL, \`rank\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`auction\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`title\` varchar(500) NOT NULL, \`price\` bigint NOT NULL, \`minPrice\` bigint NULL, \`dateStart\` datetime NOT NULL, \`dateEnd\` datetime NOT NULL, \`description\` text NULL, \`bidId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`auction_history\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`auctionId\` varchar(36) NOT NULL, \`description\` varchar(250) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `ALTER TABLE \`auction_supplier\` ADD CONSTRAINT \`FK_b2ee2caf4c7757c53bda116c932\` FOREIGN KEY (\`auctionId\`) REFERENCES \`auction\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`auction_supplier\` ADD CONSTRAINT \`FK_08ab6c1ec05a27b032a5a59647c\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`auction\` ADD CONSTRAINT \`FK_a35536b1e6e1c61dfac69d80dbf\` FOREIGN KEY (\`bidId\`) REFERENCES \`bid\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`auction_history\` ADD CONSTRAINT \`FK_a9b75b1dbd49d90df8afc7d2481\` FOREIGN KEY (\`auctionId\`) REFERENCES \`auction\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`auction_history\` DROP FOREIGN KEY \`FK_a9b75b1dbd49d90df8afc7d2481\``)
    await queryRunner.query(`ALTER TABLE \`auction\` DROP FOREIGN KEY \`FK_a35536b1e6e1c61dfac69d80dbf\``)
    await queryRunner.query(`ALTER TABLE \`auction_supplier\` DROP FOREIGN KEY \`FK_08ab6c1ec05a27b032a5a59647c\``)
    await queryRunner.query(`ALTER TABLE \`auction_supplier\` DROP FOREIGN KEY \`FK_b2ee2caf4c7757c53bda116c932\``)
    await queryRunner.query(`DROP TABLE \`auction_history\``)
    await queryRunner.query(`DROP TABLE \`auction\``)
    await queryRunner.query(`DROP TABLE \`auction_supplier\``)
  }
}
