import { Module } from '@nestjs/common'

import { EmailModule } from '../../email/email.module'
import { TypeOrmExModule } from '../../../typeorm'
import {
  OfferDealRepository,
  OfferPriceColRepository,
  OfferPriceRepository,
  OfferPrItemRepository,
  OfferRepository,
  OfferSupplierRepository,
  OfferTradeRepository,
} from '../../../repositories/offer.repository'
import { OfferRateController } from './offerRate.controller'
import { OfferRateService } from './offerRate.service'
import { OfferRateService2 } from './offerRate2.service'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      OfferRepository,
      OfferPrItemRepository,
      OfferDealRepository,
      OfferTradeRepository,
      OfferPriceRepository,
      OfferPriceColRepository,
      OfferSupplierRepository,
    ]),
    EmailModule,
  ],
  controllers: [OfferRateController],
  providers: [OfferRateService2, OfferRateService],
  exports: [OfferRateService2, OfferRateService],
})
export class OfferRateModule {}
