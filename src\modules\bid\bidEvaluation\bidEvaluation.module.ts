import { Module } from '@nestjs/common'
import { BidEvaluationController } from './bidEvaluation.controller'
import { BidEvaluationService } from './bidEvaluation.service'
import { EmailModule } from '../../email/email.module'
import { BidRepository, BidEmployeeAccessRepository, BidSupplierRepository } from '../../../repositories'
import { TypeOrmExModule } from '../../../typeorm'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BidRepository, BidEmployeeAccessRepository, BidSupplierRepository]), EmailModule],
  controllers: [BidEvaluationController],
  providers: [BidEvaluationService],
})
export class BidEvaluationModule {}
