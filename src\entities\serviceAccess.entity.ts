import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, Join<PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { EmployeeEntity } from './employee.entity'
import { ServiceEntity } from './service.entity'

@Entity('service_access')
export class ServiceAccessEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.serviceAccess)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  serviceId: string
  @ManyToOne(() => ServiceEntity, (p) => p.serviceAccess)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>
}
