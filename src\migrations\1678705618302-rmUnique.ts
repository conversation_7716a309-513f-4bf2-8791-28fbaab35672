import { MigrationInterface, QueryRunner } from 'typeorm'

export class rmUnique1678705618302 implements MigrationInterface {
  name = 'rmUnique1678705618302'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX \`IDX_2ebff7c458ed09689a53242fec\` ON \`ticket\``)
    await queryRunner.query(`DROP INDEX \`IDX_78a916df40e02a9deb1c4b75ed\` ON \`user\``)
    await queryRunner.query(`DROP INDEX \`IDX_d12ca650871335dade46b2052d\` ON \`error\``)
    await queryRunner.query(`DROP INDEX \`IDX_dcbf22551ec3827f234e532a08\` ON \`warehouse\``)
    await queryRunner.query(`DROP INDEX \`IDX_eaef0b45a49ddfb89435f91e74\` ON \`contract_appendix\``)
    await queryRunner.query(`DROP INDEX \`IDX_a167b5ec6a7dd9cd577bd622d8\` ON \`contract\``)
    await queryRunner.query(`DROP INDEX \`IDX_e1183babf2fed1bb440905b1e5\` ON \`supplier\``)
    await queryRunner.query(`DROP INDEX \`IDX_4cb3cf237c83885cc504634829\` ON \`service\``)
    await queryRunner.query(`DROP INDEX \`IDX_5961204089e6486be6b43a9e6c\` ON \`bid_type\``)
    await queryRunner.query(`DROP INDEX \`IDX_2c96dc3c8343f236e00485aef1\` ON \`bid\``)
    await queryRunner.query(`DROP INDEX \`IDX_62690f4fe31da9eb824d909285\` ON \`department\``)
    await queryRunner.query(`DROP INDEX \`IDX_348a4a9894eef0760bfe0a2632\` ON \`employee\``)
    await queryRunner.query(`DROP INDEX \`IDX_fcbdf6b1d748b3fe0f43acaf7d\` ON \`asn\``)
    await queryRunner.query(`DROP INDEX \`IDX_75bd34e96cde646bb118a6c267\` ON \`email_template\``)
    await queryRunner.query(`DROP INDEX \`IDX_89b26e4ed3db8895b86c8df55e\` ON \`holiday\``)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_89b26e4ed3db8895b86c8df55e\` ON \`holiday\` (\`date\`)`)
    await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_75bd34e96cde646bb118a6c267\` ON \`email_template\` (\`code\`)`)
    await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_fcbdf6b1d748b3fe0f43acaf7d\` ON \`asn\` (\`code\`)`)
    await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_348a4a9894eef0760bfe0a2632\` ON \`employee\` (\`code\`)`)
    await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_62690f4fe31da9eb824d909285\` ON \`department\` (\`code\`)`)
    await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_2c96dc3c8343f236e00485aef1\` ON \`bid\` (\`code\`)`)
    await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_5961204089e6486be6b43a9e6c\` ON \`bid_type\` (\`code\`)`)
    await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_4cb3cf237c83885cc504634829\` ON \`service\` (\`code\`)`)
    await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_e1183babf2fed1bb440905b1e5\` ON \`supplier\` (\`code\`)`)
    await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_a167b5ec6a7dd9cd577bd622d8\` ON \`contract\` (\`code\`)`)
    await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_eaef0b45a49ddfb89435f91e74\` ON \`contract_appendix\` (\`code\`)`)
    await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_dcbf22551ec3827f234e532a08\` ON \`warehouse\` (\`code\`)`)
    await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_d12ca650871335dade46b2052d\` ON \`error\` (\`code\`)`)
    await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_78a916df40e02a9deb1c4b75ed\` ON \`user\` (\`username\`)`)
    await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_2ebff7c458ed09689a53242fec\` ON \`ticket\` (\`code\`)`)
  }
}
