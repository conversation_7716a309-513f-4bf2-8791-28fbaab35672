import { MigrationInterface, QueryRunner } from 'typeorm'

export class changeColumnNmae1673515575395 implements MigrationInterface {
  name = 'changeColumnNmae1673515575395'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`supplier_capacity\` CHANGE \`dataType\` \`type\` varchar(255) NOT NULL DEFAULT 'string'`)
    await queryRunner.query(`ALTER TABLE \`service_capacity\` CHANGE \`dataType\` \`type\` varchar(255) NOT NULL DEFAULT 'string'`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`service_capacity\` CHANGE \`type\` \`dataType\` varchar(255) NOT NULL DEFAULT 'string'`)
    await queryRunner.query(`ALTER TABLE \`supplier_capacity\` <PERSON>AN<PERSON> \`type\` \`dataType\` varchar(255) NOT NULL DEFAULT 'string'`)
  }
}
