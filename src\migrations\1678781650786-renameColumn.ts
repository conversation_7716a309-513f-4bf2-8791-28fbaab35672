import { MigrationInterface, QueryRunner } from 'typeorm'

export class renameColumn1678781650786 implements MigrationInterface {
  name = 'renameColumn1678781650786'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`branch_member\` CHANGE \`branchdId\` \`branchId\` varchar(36) NOT NULL`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`branch_member\` CHANGE \`branchId\` \`branchdId\` varchar(36) NOT NULL`)
  }
}
