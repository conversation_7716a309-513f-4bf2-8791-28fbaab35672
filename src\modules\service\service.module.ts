import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { EmployeeRepository, ServiceAccessRepository, ServiceRepository } from '../../repositories'
import { ServiceController } from './service.controller'
import { ServiceService } from './service.service'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([ServiceRepository, ServiceAccessRepository, EmployeeRepository])],
  controllers: [ServiceController],
  providers: [ServiceService],
})
export class ServiceModule {}
