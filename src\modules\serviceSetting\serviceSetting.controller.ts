import { Controller, UseGuards, Post, Body, Param } from '@nestjs/common'
import { ServiceSettingService } from './serviceSetting.service'
import {
  ServiceCapacityCreateDto,
  ServiceCapacityUpdateDto,
  ServiceCapacityListDetailCreateDto,
  ServiceCapacityListDetailUpdateDto,
  ServiceTechCreateDto,
  ServiceTechUpdateDto,
  ServiceTechListDetailCreateDto,
  ServiceTechListDetailUpdateDto,
  ServiceTradeCreateDto,
  ServiceTradeUpdateDto,
  ServiceTradeListDetailCreateDto,
  ServiceTradeListDetailUpdateDto,
  ServicePriceCreateDto,
  ServicePriceUpdateDto,
  ServicePriceColCreateDto,
  ServicePriceColUpdateDto,
  ServicePriceListDetailCreateDto,
  ServicePriceListDetailUpdateDto,
  ServiceCustomPriceCreateDto,
  ServiceCustomPriceUpdateDto,
} from './dto'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { enumProject } from '../../constants'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Service')
@Controller('serviceSettings')
export class ServiceSettingController {
  constructor(private readonly service: ServiceSettingService) {}

  //#region capacity

  @ApiOperation({ summary: 'Lấy tổng tỉ trọng các tiêu chí cấp 1 của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('capacity_getsumpercent')
  public async capacity_getSumpercent(@CurrentUser() user: UserDto, @Body() data: { serviceId: string }) {
    return await this.service.capacity_getSumPercent(user, data)
  }

  @ApiOperation({ summary: 'Lấy các tiêu chí cấp 1 của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('capacity_find')
  public async capacity_find(@CurrentUser() user: UserDto, @Body() data: { serviceId: string }) {
    return await this.service.capacity_find(user, data)
  }

  @ApiOperation({ summary: 'Danh sách tiêu chí năng lực Item phân trang' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('capacity_pagination')
  public async capacity_pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.capacity_pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới tiêu chí năng lực Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('capacity_create_data')
  public async capacity_create_data(@CurrentUser() user: UserDto, @Body() data: ServiceCapacityCreateDto) {
    return await this.service.capacity_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật tiêu chí năng lực Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('capacity_update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: ServiceCapacityUpdateDto) {
    return await this.service.capacity_update_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động tiêu chí năng lực Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('capacity_update_active')
  public async capacity_update_active(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.capacity_update_active(user, data.id)
  }

  @ApiOperation({ summary: 'Xóa tất cả tiêu chí năng lực Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('capacity_deleteall')
  public async capacity_deleteall(@CurrentUser() user: UserDto, @Body() data: { serviceId: string }) {
    return await this.service.capacity_deleteall(user, data.serviceId)
  }

  @ApiOperation({ summary: 'Import tiêu chí năng lực Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('capacity_import/:serviceId')
  public async capacity_import(
    @Param('serviceId') serviceId: string,
    @CurrentUser() user: UserDto,
    @Body() data: { lstDataTable1: any[]; lstDataTable2: any[] },
  ) {
    return await this.service.capacity_import(user, serviceId, data)
  }

  @ApiOperation({ summary: 'Gửi duyệt template năng lực Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('capacity_send_approve')
  public async capacity_send_approve(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.capacity_send_approve(user, data.id)
  }

  @ApiOperation({ summary: 'Duyệt template năng lực Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('capacity_approve')
  public async capacity_approve(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.capacity_approve(user, data.id)
  }

  @ApiOperation({ summary: 'Yêu cầu kiểm tra lại template năng lực Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('capacity_recheck')
  public async capacity_recheck(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.capacity_recheck(user, data.id)
  }

  //#region capacitylistdetail

  @ApiOperation({ summary: 'Danh sách các lựa chọn của tiêu chí kiểu List' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('capacitylistdetail_pagination')
  public async capacitylistdetail_pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.capacitylistdetail_pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới lựa chọn cho tiêu chí kiểu List' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('capacitylistdetail_create_data')
  public async capacitylistdetail_create_data(@CurrentUser() user: UserDto, @Body() data: ServiceCapacityListDetailCreateDto) {
    return await this.service.capacitylistdetail_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật lựa chọn cho tiêu chí kiểu List' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('capacitylistdetail_update_data')
  public async capacitylistdetail_update_data(@CurrentUser() user: UserDto, @Body() data: ServiceCapacityListDetailUpdateDto) {
    return await this.service.capacitylistdetail_update_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động lựa chọn cho tiêu chí kiểu List' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('capacitylistdetail_update_active')
  public async capacitylistdetail_update_active(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.capacitylistdetail_update_active(user, data.id)
  }
  //#endregion

  //#endregion

  //#region tech
  @ApiOperation({ summary: 'Lấy tổng tỉ trọng các tiêu chí cấp 1 của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_getsumpercent')
  public async tech_getSumpercent(@CurrentUser() user: UserDto, @Body() data: { serviceId: string }) {
    return await this.service.tech_getSumPercent(user, data)
  }

  @ApiOperation({ summary: 'Lấy các tiêu chí cấp 1 của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_find')
  public async tech_find(@CurrentUser() user: UserDto, @Body() data: { serviceId: string; isGetRelation: boolean }) {
    return await this.service.tech_find(user, data)
  }

  @ApiOperation({ summary: 'Danh sách tiêu chí kỹ thuật Item phân trang' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_pagination')
  public async tech_pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.tech_pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới tiêu chí kỹ thuật Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_create_data')
  public async tech_create_data(@CurrentUser() user: UserDto, @Body() data: ServiceTechCreateDto) {
    return await this.service.tech_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật tiêu chí kỹ thuật Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_update_data')
  public async tech_update_data(@CurrentUser() user: UserDto, @Body() data: ServiceTechUpdateDto) {
    return await this.service.tech_update_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động tiêu chí kỹ thuật Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_update_active')
  public async tech_update_active(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.tech_update_active(user, data.id)
  }

  @ApiOperation({ summary: 'Xóa tất cả tiêu chí kỹ thuật Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_deleteall')
  public async tech_deleteall(@CurrentUser() user: UserDto, @Body() data: { serviceId: string }) {
    return await this.service.tech_deleteall(user, data.serviceId)
  }

  @ApiOperation({ summary: 'Import tiêu chí kỹ thuật Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tech_import/:serviceId')
  public async tech_import(
    @Param('serviceId') serviceId: string,
    @CurrentUser() user: UserDto,
    @Body() data: { lstDataTable1: any[]; lstDataTable2: any[] },
  ) {
    return await this.service.tech_import(user, serviceId, data)
  }

  //#region techlistdetail

  @ApiOperation({ summary: 'Danh sách các lựa chọn của tiêu chí kiểu List' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('techlistdetail_pagination')
  public async techlistdetail_pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.techlistdetail_pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới lựa chọn cho tiêu chí kiểu List' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('techlistdetail_create_data')
  public async techlistdetail_create_data(@CurrentUser() user: UserDto, @Body() data: ServiceTechListDetailCreateDto) {
    return await this.service.techlistdetail_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật lựa chọn cho tiêu chí kiểu List' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('techlistdetail_update_data')
  public async techlistdetail_update_data(@CurrentUser() user: UserDto, @Body() data: ServiceTechListDetailUpdateDto) {
    return await this.service.techlistdetail_update_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động lựa chọn cho tiêu chí kiểu List' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('techlistdetail_update_active')
  public async techlistdetail_update_active(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.techlistdetail_update_active(user, data.id)
  }
  //#endregion

  //#endregion

  //#region trade
  @ApiOperation({ summary: 'Lấy tổng tỉ trọng các tiêu chí cấp 1 của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('trade_getsumpercent')
  public async trade_getSumpercent(@CurrentUser() user: UserDto, @Body() data: { serviceId: string }) {
    return await this.service.trade_getSumPercent(user, data)
  }

  @ApiOperation({ summary: 'Lấy các tiêu chí cấp 1 của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('trade_find')
  public async trade_find(@CurrentUser() user: UserDto, @Body() data: { serviceId: string }) {
    return await this.service.trade_find(user, data)
  }

  @ApiOperation({ summary: 'Danh sách ĐKTM của Item phân trang' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('trade_pagination')
  public async trade_pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.trade_pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới ĐKTM của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('trade_create_data')
  public async trade_create_data(@CurrentUser() user: UserDto, @Body() data: ServiceTradeCreateDto) {
    return await this.service.trade_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật ĐKTM của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('trade_update_data')
  public async trade_update_data(@CurrentUser() user: UserDto, @Body() data: ServiceTradeUpdateDto) {
    return await this.service.trade_update_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động ĐKTM của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('trade_update_active')
  public async trade_update_active(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.trade_update_active(user, data.id)
  }

  @ApiOperation({ summary: 'Xóa tất cả ĐKTM của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('trade_deleteall')
  public async trade_deleteall(@CurrentUser() user: UserDto, @Body() data: { serviceId: string }) {
    return await this.service.trade_deleteall(user, data.serviceId)
  }

  @ApiOperation({ summary: 'Import ĐKTM của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('trade_import/:serviceId')
  public async trade_import(
    @Param('serviceId') serviceId: string,
    @CurrentUser() user: UserDto,
    @Body() data: { lstDataTable1: any[]; lstDataTable2: any[] },
  ) {
    return await this.service.trade_import(user, serviceId, data)
  }

  //#region tradelistdetail

  @ApiOperation({ summary: 'Danh sách các lựa chọn của tiêu chí kiểu List' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tradelistdetail_pagination')
  public async tradelistdetail_pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.tradelistdetail_pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới lựa chọn cho tiêu chí' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tradelistdetail_create_data')
  public async tradelistdetail_create_data(@CurrentUser() user: UserDto, @Body() data: ServiceTradeListDetailCreateDto) {
    return await this.service.tradelistdetail_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật lựa chọn cho tiêu chí' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tradelistdetail_update_data')
  public async tradelistdetail_update_data(@CurrentUser() user: UserDto, @Body() data: ServiceTradeListDetailUpdateDto) {
    return await this.service.tradelistdetail_update_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động lựa chọn cho tiêu chí' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('tradelistdetail_update_active')
  public async tradelistdetail_update_active(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.tradelistdetail_update_active(user, data.id)
  }
  //#endregion

  //#endregion

  //#region price

  @ApiOperation({ summary: 'Lấy các hạng mục cấp 1 của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_find')
  public async price_find(@CurrentUser() user: UserDto, @Body() data: { serviceId: string }) {
    return await this.service.price_find(user, data)
  }

  @ApiOperation({ summary: 'Danh sách hạng mục chào giá Item phân trang' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_pagination')
  public async price_pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.price_pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới hạng mục chào giá Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_create_data')
  public async price_create_data(@CurrentUser() user: UserDto, @Body() data: ServicePriceCreateDto) {
    return await this.service.price_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật hạng mục chào giá Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_update_data')
  public async price_update_data(@CurrentUser() user: UserDto, @Body() data: ServicePriceUpdateDto) {
    return await this.service.price_update_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động hạng mục chào giá Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_update_active')
  public async price_update_active(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.price_update_active(user, data.id)
  }

  @ApiOperation({ summary: 'Xóa tất cả hạng mục chào giá Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_deleteall')
  public async price_deleteall(@CurrentUser() user: UserDto, @Body() data: { serviceId: string }) {
    return await this.service.price_deleteall(user, data.serviceId)
  }

  @ApiOperation({ summary: 'Setup công thức tính đơn giá của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('setting_fomular')
  public async setting_fomular(@CurrentUser() user: UserDto, @Body() data: { id: string; fomular: string }) {
    return await this.service.setting_fomular(user, data)
  }

  @ApiOperation({ summary: 'Setup cách tính điểm giá của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('setting_way_cal_score_price')
  public async saveSettingPriceCalWay(@CurrentUser() user: UserDto, @Body() data: { id: string; wayCalScorePrice: string }) {
    return await this.service.saveSettingPriceCalWay(user, data)
  }

  @ApiOperation({ summary: 'Import hạng mục chào giá Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('price_import/:serviceId')
  public async price_import(
    @Param('serviceId') serviceId: string,
    @CurrentUser() user: UserDto,
    @Body() data: { lstDataTable1: any[]; lstDataTable2: any[] },
  ) {
    return await this.service.price_import(user, serviceId, data)
  }

  //#region pricelistdetail

  @ApiOperation({ summary: 'Danh sách thông tin mở rộng của hạng mục chào giá Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pricelistdetail_pagination')
  public async pricelistdetail_pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pricelistdetail_pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới thông tin mở rộng cho hạng mục chào giá Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pricelistdetail_create_data')
  public async pricelistdetail_create_data(@CurrentUser() user: UserDto, @Body() data: ServicePriceListDetailCreateDto) {
    return await this.service.pricelistdetail_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật thông tin mở rộng cho hạng mục chào giá Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pricelistdetail_update_data')
  public async pricelistdetail_update_data(@CurrentUser() user: UserDto, @Body() data: ServicePriceListDetailUpdateDto) {
    return await this.service.pricelistdetail_update_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động thông tin mở rộng cho hạng mục chào giá Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pricelistdetail_update_active')
  public async pricelistdetail_update_active(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.pricelistdetail_update_active(user, data.id)
  }
  //#endregion

  //#region pricecol

  @ApiOperation({ summary: 'Danh sách cột động template chào giá của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pricecol_pagination')
  public async pricecol_pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pricecol_pagination(user, data)
  }

  @ApiOperation({ summary: 'Thêm mới cột động trong template chào giá của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pricecol_create_data')
  public async pricecol_create_data(@CurrentUser() user: UserDto, @Body() data: ServicePriceColCreateDto) {
    return await this.service.pricecol_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật cột động trong template chào giá của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pricecol_update_data')
  public async pricecol_update_data(@CurrentUser() user: UserDto, @Body() data: ServicePriceColUpdateDto) {
    return await this.service.pricecol_update_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động cột động trong template chào giá của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pricecol_update_active')
  public async pricecol_update_active(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.pricecol_update_active(user, data.id)
  }
  //#endregion

  //#endregion

  //#region customprice

  @ApiOperation({ summary: 'Danh sách hạng mục cơ cấu giá của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('customprice_pagination')
  public async customprice_pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.customprice_pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới hạng mục cơ cấu giá của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('customprice_create_data')
  public async customprice_create_data(@CurrentUser() user: UserDto, @Body() data: ServiceCustomPriceCreateDto) {
    return await this.service.customprice_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật hạng mục cơ cấu giá của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('customprice_update_data')
  public async customprice_update_data(@CurrentUser() user: UserDto, @Body() data: ServiceCustomPriceUpdateDto) {
    return await this.service.customprice_update_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động hạng mục cơ cấu giá của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('customprice_update_active')
  public async customprice_update_active(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.customprice_update_active(user, data.id)
  }

  @ApiOperation({ summary: 'Import hạng mục cơ cấu giá của Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('customprice_import/:serviceId')
  public async customprice_import(@Param('serviceId') serviceId: string, @CurrentUser() user: UserDto, @Body() data: { lstData: any[] }) {
    return await this.service.customprice_import(user, serviceId, data)
  }

  //#endregion

  //#region Rate

  @ApiOperation({ summary: 'Cập nhật tỉ lệ tính tổng điểm đánh giá Item' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('rate_update')
  public async rateUpdate(
    @CurrentUser() user: UserDto,
    @Body() data: { id: string; percentTech: number; percentPrice: number; percentTrade: number },
  ) {
    return await this.service.rateUpdate(user, data)
  }
  //#endregion
}
