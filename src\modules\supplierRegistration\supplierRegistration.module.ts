import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { SupplierRegistrationController } from './supplierRegistration.controller'
import { SupplierRegistrationService } from './supplierRegistration.service'
import { EmailModule } from '../email/email.module'
import { ServiceCapacityRepository, ServiceRepository, SupplierRepository, SupplierServiceRepository, UserRepository } from '../../repositories'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      ServiceRepository,
      ServiceCapacityRepository,
      SupplierRepository,
      UserRepository,
      SupplierServiceRepository,
    ]),

    EmailModule,
  ],
  controllers: [SupplierRegistrationController],
  providers: [SupplierRegistrationService],
})
export class SupplierRegistrationModule {}
