import { MigrationInterface, QueryRunner } from 'typeorm'

export class modifyInvoinceFile1714992626436 implements MigrationInterface {
  name = 'modifyInvoinceFile1714992626436'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`invoice_suggest_file\` ADD \`url\` varchar(250) NOT NULL`)
    await queryRunner.query(`ALTER TABLE \`invoice_suggest_file\` ADD \`invoiceNo\` varchar(50) NOT NULL`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`invoice_suggest_file\` DROP COLUMN \`invoiceNo\``)
    await queryRunner.query(`ALTER TABLE \`invoice_suggest_file\` DROP COLUMN \`url\``)
  }
}
