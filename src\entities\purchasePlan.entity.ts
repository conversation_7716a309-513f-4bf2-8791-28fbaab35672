import { <PERSON>ti<PERSON>, Column, Jo<PERSON><PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm'
import { AsnEntity } from './asn.entity'
import { BaseEntity } from './base.entity'
import { DepartmentEntity } from './department.entity'
import { PrItemEntity } from './prItem.entity'
import { PurchasePlanHistoryEntity } from './purchasePlanHistory.entity'
import { PurchasePlanProgressEntity } from './purchasePlanProgress.entity'
import { ServiceEntity } from './service.entity'

/** Kế hoạch mua hàng */
@Entity('purchase_plan')
export class PurchasePlanEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 1000,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  code: string

  /** Phòng ban */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  departmentId: string
  @ManyToOne(() => DepartmentEntity, (p) => p.purchasePlans)
  @JoinColumn({ name: 'departmentId', referencedColumnName: 'id' })
  department: Promise<DepartmentEntity>

  /** Vật tư */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  serviceId: string
  @ManyToOne(() => ServiceEntity, (p) => p.purchasePlans)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  @Column({
    nullable: false,
    default: 0,
  })
  quantity: number

  @Column({
    nullable: false,
    default: 0,
  })
  quantityInbound: number

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  /** Ngân sách */
  @Column({
    nullable: true,
    default: 0,
    type: 'float',
  })
  budget: number

  /** Ngân sách còn lại */
  @Column({
    nullable: true,
    default: 0,
    type: 'float',
  })
  budgetRemaining: number

  /** Tiến độ mua hàng */
  @OneToMany(() => PurchasePlanProgressEntity, (p) => p.purchasePlan)
  progresss: Promise<PurchasePlanProgressEntity[]>

  /** Danh mục Item của YCMH */
  @OneToMany(() => PrItemEntity, (p) => p.purchasePlan)
  prItems: Promise<PrItemEntity[]>

  /** DS Nhập kho */
  @OneToMany(() => AsnEntity, (p) => p.purchasePlan)
  asns: Promise<AsnEntity[]>

  /** Lịch sử của YCMH */
  @OneToMany(() => PurchasePlanHistoryEntity, (p) => p.purchasePlan)
  histories: Promise<PurchasePlanHistoryEntity[]>
}
