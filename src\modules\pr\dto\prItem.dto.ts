import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class PrItemDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  prId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  serviceId: string

  @ApiProperty()
  itemId: string

  @ApiPropertyOptional()
  @IsOptional()
  purchasePlanId: string

  @ApiProperty()
  @IsNotEmpty()
  quantity: number

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  productName: string

  @ApiPropertyOptional()
  description: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  suggestReason: string

  @ApiPropertyOptional()
  code: string

  @ApiPropertyOptional()
  unit: string
}
