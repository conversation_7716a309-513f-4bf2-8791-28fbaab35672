import { MigrationInterface, QueryRunner } from "typeorm";

export class update1741663364094 implements MigrationInterface {
    name = 'update1741663364094'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid\` ADD \`hiddenScore\` tinyint NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid\` DROP COLUMN \`hiddenScore\``);
    }

}
