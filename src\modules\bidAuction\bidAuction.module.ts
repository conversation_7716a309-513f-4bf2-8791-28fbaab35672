import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import {
  BidAuctionRepository,
  BidSupplierRepository,
  BidPriceColRepository,
  BidRepository,
  BidDealRepository,
  BidSupplierPriceRepository,
} from '../../repositories'
import { BidAuctionController } from './bidAuction.controller'
import { BidAuctionService } from './bidAuction.service'
import { EmailModule } from '../email/email.module'
import { BidRateModule } from '../bid/bidRate/bidRate.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      BidAuctionRepository,
      BidSupplierRepository,
      BidPriceColRepository,
      BidRepository,
      BidDealRepository,
      BidSupplierPriceRepository,
    ]),

    EmailModule,
    BidRateModule,
  ],
  controllers: [BidAuctionController],
  providers: [BidAuctionService],
  exports: [BidAuctionService],
})
export class BidAuctionModule {}
