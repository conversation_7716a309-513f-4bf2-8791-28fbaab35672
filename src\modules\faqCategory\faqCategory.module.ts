import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { FaqCategoryService } from './faqCategory.service'
import { FaqCategoryController } from './faqCategory.controller'
import { FaqCategoryRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([FaqCategoryRepository])],
  controllers: [FaqCategoryController],
  providers: [FaqCategoryService],
  exports: [FaqCategoryService],
})
export class FaqCategoryModule {}
