import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { BidController } from './bid.controller'
import { BidService } from './bid.service'
import { EmailModule } from '../email/email.module'
import {
  BidRepository,
  ServiceRepository,
  EmployeeRepository,
  BidTypeRepository,
  SettingStringRepository,
  BidEmployeeAccessRepository,
  ServiceTechRepository,
  ServiceTradeRepository,
  ServicePriceRepository,
  SupplierServiceRepository,
  ServiceCustomPriceRepository,
  BidCustomPriceRepository,
  BidTechRepository,
  BidTradeRepository,
  BidPriceRepository,
  BidPriceColRepository,
  BidSupplierRepository,
  SupplierRepository,
  BidItemRepository,
  MaterialRepository,
} from '../../repositories'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      BidEmployeeAccessRepository,
      BidRepository,
      BidItemRepository,
      ServiceRepository,
      EmployeeRepository,
      BidTypeRepository,
      SettingStringRepository,
      ServiceTechRepository,
      BidTechRepository,
      ServiceTradeRepository,
      BidTradeRepository,
      ServicePriceRepository,
      ServiceCustomPriceRepository,
      BidPriceRepository,
      BidCustomPriceRepository,
      BidPriceColRepository,
      SupplierServiceRepository,
      BidSupplierRepository,
      SupplierRepository,
      MaterialRepository,
    ]),
    EmailModule,
  ],
  controllers: [BidController],
  providers: [BidService],
  exports: [BidService],
})
export class BidModule {}
