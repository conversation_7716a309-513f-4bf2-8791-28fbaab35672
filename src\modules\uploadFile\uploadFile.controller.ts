import { Controller, Post, UseInterceptors, UploadedFile } from '@nestjs/common'
import { UploadFileService } from './uploadFile.service'
import { FileInterceptor } from '@nestjs/platform-express'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('UploadFile')
@Controller('uploadFiles')
export class UploadFileController {
  constructor(private readonly service: UploadFileService) {}

  @ApiOperation({ summary: 'Upload 1 file' })
  @Post('upload_single')
  @UseInterceptors(FileInterceptor('file'))
  async uploadSingle(@UploadedFile() file: any) {
    return await this.service.uploadSingle(file)
  }
}
