import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { EmployeeNotifyRepository } from '../../repositories'
import { EmployeeNotifyService } from './employeeNotify.service'
import { EmployeeNotifyController } from './employeeNotify.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([EmployeeNotifyRepository])],
  controllers: [EmployeeNotifyController],
  providers: [EmployeeNotifyService],
})
export class EmployeeNotifyModule {}
