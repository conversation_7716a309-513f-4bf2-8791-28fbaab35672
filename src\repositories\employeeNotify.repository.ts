import { Repository } from 'typeorm'
import { EmployeeNotifyEntity } from '../entities'
import { CustomRepository } from '../typeorm'

@CustomRepository(EmployeeNotifyEntity)
export class EmployeeNotifyRepository extends Repository<EmployeeNotifyEntity> {
  async createEmployeeNotify(companyId: string, employeeId: string, message: string, messageFull: string) {
    const newNotify = new EmployeeNotifyEntity()
    newNotify.companyId = companyId
    newNotify.message = message
    newNotify.messageFull = messageFull
    newNotify.employeeId = employeeId
    await this.save(newNotify)
  }
}
