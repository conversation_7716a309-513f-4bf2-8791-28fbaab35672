import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsArray, IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { PoProductDto } from './poProduct.dto'
import { PoProgressDto } from './poProgress.dto'

export class POUpdateStatusDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty()
  @IsNotEmpty()
  orderStatus: string

  @ApiProperty()
  @IsNotEmpty()
  isSupplier: boolean
}
