import { MigrationInterface, QueryRunner } from 'typeorm'

export class initDB1669891301764 implements MigrationInterface {
  name = 'initDB1669891301764'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`item_tech_list_detail\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`value\` int NOT NULL, \`itemTechId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`item_tech\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NOT NULL, \`isRequired\` tinyint NOT NULL DEFAULT 0, \`isCalUp\` tinyint NOT NULL DEFAULT 1, \`type\` varchar(255) NOT NULL DEFAULT 'string', \`dataCalType\` varchar(255) NULL, \`percent\` float NULL DEFAULT '0', \`percentRule\` bigint NULL, \`percentDownRule\` bigint NULL, \`level\` int NOT NULL DEFAULT '1', \`description\` varchar(250) NULL, \`parentId\` varchar(36) NULL, \`minTX\` int NULL, \`maxTX\` int NULL, \`scoreDLC\` int NULL, \`requiredMin\` int NULL, \`isHighlight\` tinyint NOT NULL DEFAULT 0, \`hightlightValue\` int NULL, \`prItemId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`service_capacity_list_detail\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`value\` int NOT NULL, \`serviceCapacityId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`supplier_expertise_year_detail\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`value\` varchar(255) NOT NULL, \`year\` varchar(255) NOT NULL, \`supplierExpertiseDetailId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`supplier_expertise_detail\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`comment\` text NULL, \`sort\` int NOT NULL DEFAULT '0', \`type\` varchar(255) NOT NULL DEFAULT 'string', \`value\` varchar(250) NULL, \`supplierCapacityId\` varchar(36) NOT NULL, \`supplierExpertiseId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`supplier_service\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`score\` float NOT NULL DEFAULT '0', \`approveDate\` datetime NULL, \`status\` varchar(50) NOT NULL, \`statusExpertise\` varchar(50) NOT NULL DEFAULT 'ChuaThamDinh', \`comment\` text NULL, \`approverComment\` text NULL, \`serviceId\` varchar(36) NOT NULL, \`supplierId\` varchar(36) NOT NULL, \`totalPrice\` bigint NULL, \`supplierType\` varchar(50) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`supplier_expertise_law_detail\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(50) NOT NULL, \`name\` varchar(250) NULL, \`dealName\` varchar(250) NULL, \`address\` varchar(250) NULL, \`dealAddress\` varchar(250) NULL, \`fileMST\` varchar(250) NULL, \`represen\` varchar(50) NULL, \`chief\` varchar(50) NULL, \`bankNumber\` varchar(50) NULL, \`bankname\` varchar(250) NULL, \`bankBrand\` varchar(250) NULL, \`fileAccount\` varchar(250) NULL, \`contactName\` varchar(250) NULL, \`email\` varchar(50) NULL, \`phone\` varchar(50) NULL, \`createYear\` varchar(50) NULL, \`capital\` int NULL, \`assets\` int NULL, \`fileBill\` varchar(250) NULL, \`fileInfoBill\` varchar(250) NULL, \`comment\` text NULL, \`supplierExpertiseId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`supplier_expertise_member\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`employeeId\` varchar(36) NOT NULL, \`supplierExpertiseId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`supplier_expertise\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`comment\` text NULL, \`commentCapacity\` text NULL, \`note\` text NULL, \`changeDate\` datetime NOT NULL, \`isCheckLaw\` tinyint NOT NULL DEFAULT 0, \`isCheckCapacity\` tinyint NOT NULL DEFAULT 0, \`approvedLawId\` varchar(36) NULL, \`approvedCapacityId\` varchar(36) NULL, \`status\` varchar(50) NOT NULL, \`statusLaw\` varchar(50) NOT NULL, \`statusCapacity\` varchar(50) NOT NULL, \`serviceId\` varchar(36) NOT NULL, \`supplierId\` varchar(36) NOT NULL, \`supplierServiceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`user_confirm_code\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`userId\` varchar(36) NOT NULL, \`code\` varchar(36) NOT NULL, \`exDate\` datetime NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`user\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`username\` varchar(50) NOT NULL, \`password\` text NOT NULL, \`type\` varchar(50) NOT NULL, \`supplierId\` varchar(36) NULL, \`employeeId\` varchar(36) NULL, \`roles\` longtext NULL, UNIQUE INDEX \`IDX_78a916df40e02a9deb1c4b75ed\` (\`username\`), UNIQUE INDEX \`REL_031cdc2c9c5eb56d48b5bdb4e5\` (\`supplierId\`), UNIQUE INDEX \`REL_ab4a80281f1e8d524714e00f38\` (\`employeeId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_tech_list_detail\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`value\` int NOT NULL, \`bidTechId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_tech\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NOT NULL, \`isRequired\` tinyint NOT NULL DEFAULT 0, \`isCalUp\` tinyint NOT NULL DEFAULT 1, \`type\` varchar(255) NOT NULL DEFAULT 'string', \`dataCalType\` varchar(255) NULL, \`percent\` float NULL DEFAULT '0', \`percentRule\` bigint NULL, \`percentDownRule\` bigint NULL, \`level\` int NOT NULL DEFAULT '1', \`description\` varchar(250) NULL, \`parentId\` varchar(36) NULL, \`minTX\` int NULL, \`maxTX\` int NULL, \`scoreDLC\` int NULL, \`requiredMin\` int NULL, \`isHighlight\` tinyint NOT NULL DEFAULT 0, \`hightlightValue\` int NULL, \`bidId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_supplier_tech_value\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`score\` float NULL, \`value\` varchar(250) NULL, \`bidSupplierId\` varchar(36) NOT NULL, \`bidTechId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_price_list_detail\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`description\` text NULL, \`type\` varchar(50) NOT NULL, \`value\` varchar(250) NULL, \`bidPriceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_deal_supplier_price_value\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`score\` float NULL, \`value\` varchar(250) NULL, \`bidDealSupplierId\` varchar(36) NOT NULL, \`bidPriceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_deal_supplier\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`bidDealId\` varchar(36) NOT NULL, \`score\` float NOT NULL DEFAULT '0', \`supplierId\` varchar(36) NOT NULL, \`status\` varchar(50) NOT NULL, \`filePriceDetail\` varchar(500) NULL, \`fileTechDetail\` varchar(500) NULL, \`linkDriver\` varchar(500) NULL, \`submitDate\` datetime NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_deal\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`status\` varchar(50) NOT NULL, \`endDate\` datetime NOT NULL, \`isSendDealPrice\` tinyint NOT NULL DEFAULT 0, \`bidId\` varchar(36) NOT NULL, \`isRequireFilePriceDetail\` tinyint NULL DEFAULT 0, \`isRequireFileTechDetail\` tinyint NULL DEFAULT 0, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_deal_price\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`sort\` int NOT NULL DEFAULT '0', \`number\` int NOT NULL DEFAULT '0', \`bestPrice\` float NULL, \`bestPriceHistory\` float NULL, \`bestPriceCurrent\` float NULL, \`suggestPrice\` float NULL, \`maxPrice\` float NULL, \`bidDealId\` varchar(36) NOT NULL, \`bidPriceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`service_price_list_detail\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`description\` text NULL, \`type\` varchar(50) NOT NULL, \`value\` varchar(250) NULL, \`servicePriceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`service_price_col\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(50) NOT NULL, \`fomular\` text NULL, \`isRequired\` tinyint NOT NULL DEFAULT 0, \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NOT NULL, \`description\` text NULL, \`type\` varchar(50) NOT NULL, \`colType\` varchar(50) NOT NULL, \`serviceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`service_price_col_value\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`value\` varchar(250) NOT NULL, \`servicePriceId\` varchar(36) NOT NULL, \`servicePriceColId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`service_price\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`number\` int NOT NULL DEFAULT '0', \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NOT NULL, \`isRequired\` tinyint NOT NULL DEFAULT 0, \`isSetup\` tinyint NOT NULL DEFAULT 0, \`isTemplate\` tinyint NOT NULL DEFAULT 1, \`type\` varchar(255) NOT NULL DEFAULT 'string', \`unit\` varchar(36) NULL, \`currency\` varchar(36) NULL, \`dataCalType\` varchar(255) NULL, \`percent\` float NULL DEFAULT '0', \`level\` int NOT NULL DEFAULT '1', \`description\` varchar(250) NULL, \`parentId\` varchar(36) NULL, \`minTX\` int NULL, \`maxTX\` int NULL, \`scoreDLC\` int NULL, \`requiredMin\` int NULL, \`serviceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_auction_price\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`maxPrice\` float NULL, \`bidAuctionId\` varchar(36) NOT NULL, \`bidPriceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_auction\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`status\` varchar(50) NOT NULL, \`endDate\` datetime NOT NULL, \`bidId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_auction_supplier\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`score\` float NOT NULL DEFAULT '0', \`bidAuctionId\` varchar(36) NOT NULL, \`supplierId\` varchar(36) NOT NULL, \`status\` varchar(50) NOT NULL, \`submitDate\` datetime NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_auction_supplier_price_value\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`score\` float NULL, \`value\` varchar(250) NULL, \`bidAuctionSupplierId\` varchar(36) NOT NULL, \`bidPriceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_price_col_value\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`value\` varchar(250) NOT NULL, \`bidPriceId\` varchar(36) NOT NULL, \`bidPriceColId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_price_col\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(50) NOT NULL, \`fomular\` text NULL, \`isRequired\` tinyint NOT NULL DEFAULT 0, \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NOT NULL, \`description\` text NULL, \`type\` varchar(50) NOT NULL, \`colType\` varchar(50) NOT NULL, \`bidId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_supplier_price_col_value\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`value\` varchar(250) NULL, \`bidSupplierId\` varchar(36) NOT NULL, \`bidPriceId\` varchar(36) NULL, \`bidPriceColId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_supplier_price\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`bidSupplierId\` varchar(36) NOT NULL, \`bidPriceId\` varchar(36) NOT NULL, \`bidPriceName\` varchar(250) NOT NULL, \`bidPriceLevel\` int NOT NULL DEFAULT '1', \`bidId\` varchar(36) NOT NULL, \`serviceId\` varchar(36) NOT NULL, \`supplierId\` varchar(36) NOT NULL, \`submitDate\` datetime NOT NULL, \`submitType\` int NOT NULL, \`number\` int NOT NULL, \`unitPrice\` bigint NOT NULL, \`price\` bigint NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_price\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`number\` int NOT NULL DEFAULT '0', \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NOT NULL, \`isRequired\` tinyint NOT NULL DEFAULT 0, \`isSetup\` tinyint NOT NULL DEFAULT 0, \`isTemplate\` tinyint NOT NULL DEFAULT 1, \`value\` varchar(250) NULL, \`type\` varchar(255) NOT NULL DEFAULT 'string', \`unit\` varchar(36) NULL, \`currency\` varchar(36) NULL, \`dataCalType\` varchar(255) NULL, \`percent\` float NULL DEFAULT '100', \`level\` int NOT NULL DEFAULT '1', \`description\` varchar(250) NULL, \`parentId\` varchar(36) NULL, \`minTX\` int NULL, \`maxTX\` int NULL, \`scoreDLC\` int NULL, \`requiredMin\` int NULL, \`bidId\` varchar(36) NOT NULL, \`servicePriceId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_supplier_price_value\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`unit\` varchar(36) NULL, \`currency\` varchar(36) NULL, \`number\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NULL, \`score\` float NULL, \`value\` varchar(250) NULL, \`bidSupplierId\` varchar(36) NOT NULL, \`bidPriceId\` varchar(36) NULL, \`bidPriceParentId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_trade_list_detail\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`value\` int NOT NULL, \`bidTradeId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_trade\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NOT NULL, \`isRequired\` tinyint NOT NULL DEFAULT 0, \`isCalUp\` tinyint NOT NULL DEFAULT 1, \`type\` varchar(255) NOT NULL DEFAULT 'string', \`dataCalType\` varchar(255) NULL, \`percent\` float NULL DEFAULT '0', \`percentRule\` bigint NULL, \`percentDownRule\` bigint NULL, \`level\` int NOT NULL DEFAULT '1', \`description\` varchar(250) NULL, \`parentId\` varchar(36) NULL, \`minTX\` int NULL, \`maxTX\` int NULL, \`scoreDLC\` int NULL, \`requiredMin\` int NULL, \`bidId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_supplier_trade_value\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`score\` float NULL, \`value\` varchar(250) NULL, \`bidSupplierId\` varchar(36) NOT NULL, \`bidTradeId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_supplier_custom_price_value\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`unit\` varchar(36) NULL, \`currency\` varchar(36) NULL, \`number\` int NOT NULL DEFAULT '0', \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NULL, \`value\` varchar(250) NULL, \`bidSupplierId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_supplier\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`bidId\` varchar(36) NOT NULL, \`note\` text NULL, \`noteTrade\` text NULL, \`noteTech\` text NULL, \`notePrice\` text NULL, \`noteMPOLeader\` text NULL, \`noteTechLeader\` text NULL, \`scoreTech\` float NOT NULL DEFAULT '0', \`scorePrice\` float NOT NULL DEFAULT '0', \`scoreTrade\` float NOT NULL DEFAULT '0', \`scoreManualTech\` float NOT NULL DEFAULT '0', \`scoreManualPrice\` float NOT NULL DEFAULT '0', \`scoreManualTrade\` float NOT NULL DEFAULT '0', \`supplierId\` varchar(36) NOT NULL, \`status\` varchar(50) NOT NULL, \`statusFile\` varchar(50) NOT NULL, \`statusTech\` varchar(50) NOT NULL, \`statusTrade\` varchar(50) NOT NULL, \`statusPrice\` varchar(50) NOT NULL, \`isHighlight\` tinyint NOT NULL DEFAULT 0, \`isNotHaveMinValue\` tinyint NOT NULL DEFAULT 0, \`isSuccessBid\` tinyint NOT NULL DEFAULT 0, \`noteSuccessBid\` text NULL, \`isTechValid\` tinyint NOT NULL DEFAULT 1, \`isTradeValid\` tinyint NOT NULL DEFAULT 1, \`isPriceValid\` tinyint NOT NULL DEFAULT 1, \`statusResetPrice\` varchar(50) NOT NULL DEFAULT 'KhongYeuCau', \`dataJson\` text NULL, \`filePriceDetail\` varchar(500) NULL, \`fileTechDetail\` varchar(500) NULL, \`submitDate\` datetime NULL, \`totalPrice\` bigint NULL, \`serviceId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`supplier_notify\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`message\` text NULL, \`messageFull\` text NULL, \`url\` text NULL, \`status\` varchar(50) NOT NULL, \`supplierId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`invoice_file\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`fileUrl\` varchar(250) NOT NULL, \`fileName\` varchar(250) NULL, \`data\` text NULL, \`invoiceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`invoice\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`invoiceSuggestId\` varchar(36) NOT NULL, \`money\` bigint NOT NULL DEFAULT '0', \`invoiceDate\` datetime NOT NULL, \`description\` varchar(250) NULL, \`employeeName\` varchar(250) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`invoice_suggest_history\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`status\` varchar(50) NOT NULL, \`description\` varchar(250) NULL, \`invoiceSuggestId\` varchar(36) NOT NULL, \`employeeId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`invoice_suggest_file\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`fileUrl\` varchar(250) NOT NULL, \`fileName\` varchar(250) NULL, \`data\` text NULL, \`invoiceSuggestId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`payment_progress\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`poId\` varchar(36) NULL, \`contractId\` varchar(36) NULL, \`name\` varchar(250) NULL, \`percent\` int NOT NULL DEFAULT '0', \`time\` datetime NOT NULL, \`description\` varchar(250) NULL, \`money\` bigint NOT NULL DEFAULT '0', \`suggestPaid\` bigint NOT NULL DEFAULT '0', \`paymentStatus\` varchar(50) NOT NULL DEFAULT 'UNPAIND', \`historyNote\` longtext NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`invoice_suggest\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(50) NOT NULL, \`type\` varchar(50) NOT NULL, \`contractId\` varchar(36) NULL, \`poId\` varchar(36) NULL, \`paymentPlanId\` varchar(36) NOT NULL, \`money\` bigint NOT NULL DEFAULT '0', \`moneyPaid\` bigint NOT NULL DEFAULT '0', \`invoiceNo\` varchar(50) NOT NULL, \`discount\` int NULL, \`invoiceDate\` datetime NOT NULL, \`supplierId\` varchar(36) NOT NULL DEFAULT '', \`beneficiaryUnit\` varchar(250) NOT NULL, \`bankName\` varchar(250) NOT NULL, \`bankAccountNo\` varchar(250) NOT NULL, \`description\` varchar(250) NULL, \`status\` varchar(250) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`po_history\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`statusCurrent\` varchar(150) NULL, \`statusConvert\` varchar(150) NULL, \`poId\` varchar(36) NOT NULL, \`employeeId\` varchar(36) NULL, \`supplierId\` varchar(36) NULL, \`description\` varchar(500) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`po_member\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`employeeId\` varchar(36) NOT NULL, \`poId\` varchar(36) NOT NULL, \`poRoleCode\` varchar(50) NOT NULL, \`description\` varchar(250) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`po_product\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`poId\` varchar(36) NOT NULL, \`type\` varchar(50) NULL, \`name\` varchar(250) NOT NULL, \`group\` varchar(50) NULL, \`note\` varchar(250) NULL, \`unit\` varchar(50) NOT NULL, \`quantity\` int NOT NULL, \`price\` int NOT NULL, \`money\` float NOT NULL, \`description\` varchar(250) NULL, \`itemCode\` varchar(250) NULL, \`serviceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`po\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(250) NOT NULL, \`title\` varchar(250) NOT NULL, \`supplierId\` varchar(36) NOT NULL, \`bidId\` varchar(36) NULL, \`prId\` varchar(36) NULL, \`contractId\` varchar(36) NULL, \`contractPaymentPlanId\` varchar(36) NULL, \`status\` varchar(50) NOT NULL, \`operator\` varchar(250) NULL, \`type\` varchar(250) NULL, \`region\` varchar(250) NULL, \`currency\` varchar(50) NOT NULL, \`money\` bigint NOT NULL DEFAULT '0', \`company\` varchar(50) NOT NULL, \`email\` varchar(50) NOT NULL, \`phone\` varchar(50) NOT NULL, \`description\` varchar(250) NULL, \`paymentPlanType\` varchar(250) NULL, \`branchId\` varchar(36) NULL, \`objectId\` varchar(36) NULL, \`reason\` varchar(1000) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`supplier\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`status\` varchar(50) NOT NULL, \`description\` varchar(250) NOT NULL, \`code\` varchar(50) NOT NULL, \`name\` varchar(250) NOT NULL, \`dealName\` varchar(250) NOT NULL, \`address\` varchar(250) NOT NULL, \`dealAddress\` varchar(250) NOT NULL, \`fileMST\` varchar(250) NOT NULL, \`represen\` varchar(50) NOT NULL, \`chief\` varchar(50) NOT NULL, \`bankNumber\` varchar(50) NOT NULL, \`bankname\` varchar(250) NOT NULL, \`bankBrand\` varchar(250) NOT NULL, \`fileAccount\` varchar(250) NULL, \`contactName\` varchar(250) NOT NULL, \`email\` varchar(50) NOT NULL, \`phone\` varchar(50) NOT NULL, \`createYear\` varchar(50) NOT NULL, \`capital\` float NOT NULL, \`assets\` float NOT NULL, \`fileBill\` varchar(250) NOT NULL, \`fileInfoBill\` varchar(250) NULL, \`userId\` varchar(36) NULL, \`introducerId\` varchar(36) NULL, UNIQUE INDEX \`IDX_e1183babf2fed1bb440905b1e5\` (\`code\`), UNIQUE INDEX \`REL_e8902c50550ff82dd0143913c0\` (\`userId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`supplier_capacity_list_detail\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`value\` int NOT NULL, \`isChosen\` tinyint NOT NULL DEFAULT 0, \`supplierCapacityId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`supplier_capacity_year_value\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`value\` varchar(255) NOT NULL, \`year\` varchar(255) NOT NULL, \`supplierCapacityId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`supplier_capacity\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NOT NULL, \`dataType\` varchar(255) NOT NULL DEFAULT 'string', \`isRequired\` tinyint NOT NULL DEFAULT 0, \`isCalUp\` tinyint NOT NULL DEFAULT 1, \`isChangeByYear\` tinyint NOT NULL DEFAULT 0, \`value\` varchar(250) NULL, \`percent\` float NULL, \`percentRule\` bigint NULL, \`percentDownRule\` bigint NULL, \`description\` varchar(250) NULL, \`parentId\` varchar(36) NULL, \`serviceId\` varchar(36) NOT NULL, \`serviceCapacityId\` varchar(36) NULL, \`supplierId\` varchar(36) NOT NULL, \`supplierServiceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`service_capacity\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NOT NULL, \`isRequired\` tinyint NOT NULL DEFAULT 0, \`isCalUp\` tinyint NOT NULL DEFAULT 1, \`isChangeByYear\` tinyint NOT NULL DEFAULT 0, \`dataType\` varchar(255) NOT NULL DEFAULT 'string', \`percent\` float NULL, \`percentRule\` bigint NULL, \`percentDownRule\` bigint NULL, \`description\` varchar(250) NULL, \`parentId\` varchar(36) NULL, \`serviceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`service_tech_list_detail\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`value\` int NOT NULL, \`serviceTechId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`service_tech\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NOT NULL, \`isRequired\` tinyint NOT NULL DEFAULT 0, \`isCalUp\` tinyint NOT NULL DEFAULT 1, \`type\` varchar(255) NOT NULL DEFAULT 'string', \`dataCalType\` varchar(255) NULL, \`percent\` float NULL DEFAULT '0', \`percentRule\` bigint NULL, \`percentDownRule\` bigint NULL, \`level\` int NOT NULL DEFAULT '1', \`description\` varchar(250) NULL, \`parentId\` varchar(36) NULL, \`minTX\` int NULL, \`maxTX\` int NULL, \`scoreDLC\` int NULL, \`requiredMin\` int NULL, \`isHighlight\` tinyint NOT NULL DEFAULT 0, \`hightlightValue\` int NULL, \`serviceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`service_trade_list_detail\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`value\` int NOT NULL, \`serviceTradeId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`service_trade\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NOT NULL, \`isRequired\` tinyint NOT NULL DEFAULT 0, \`isCalUp\` tinyint NOT NULL DEFAULT 1, \`type\` varchar(255) NOT NULL DEFAULT 'string', \`dataCalType\` varchar(255) NULL, \`percent\` float NULL DEFAULT '0', \`percentRule\` bigint NULL, \`percentDownRule\` bigint NULL, \`level\` int NOT NULL DEFAULT '1', \`description\` varchar(250) NULL, \`parentId\` varchar(36) NULL, \`minTX\` int NULL, \`maxTX\` int NULL, \`scoreDLC\` int NULL, \`requiredMin\` int NULL, \`serviceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`service_access\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`employeeId\` varchar(36) NOT NULL, \`serviceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`service_custom_price\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`number\` int NOT NULL DEFAULT '0', \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NOT NULL, \`isRequired\` tinyint NOT NULL DEFAULT 0, \`type\` varchar(255) NOT NULL DEFAULT 'Number', \`unit\` varchar(36) NULL, \`currency\` varchar(36) NULL, \`serviceId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`asn_item\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`asnId\` varchar(36) NOT NULL, \`poId\` varchar(36) NULL, \`serviceId\` varchar(36) NULL, \`quantity\` int NOT NULL DEFAULT '0', \`itemCode\` varchar(36) NOT NULL, \`description\` varchar(250) NULL, \`branchId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`service\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`percentTech\` int NOT NULL DEFAULT '0', \`percentTrade\` int NOT NULL DEFAULT '0', \`percentPrice\` int NOT NULL DEFAULT '0', \`name\` varchar(50) NOT NULL, \`code\` varchar(50) NOT NULL, \`level\` int NOT NULL, \`isLast\` tinyint NOT NULL DEFAULT 0, \`description\` varchar(250) NULL, \`parentId\` varchar(36) NULL, \`approveById\` varchar(36) NOT NULL, \`fomular\` text NULL, \`statusCapacity\` varchar(100) NULL DEFAULT 'ChuaDuyet', UNIQUE INDEX \`IDX_4cb3cf237c83885cc504634829\` (\`code\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_employee_access\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`type\` varchar(50) NOT NULL, \`employeeId\` varchar(36) NOT NULL, \`bidId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_type\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`code\` varchar(50) NOT NULL, \`description\` varchar(250) NULL, UNIQUE INDEX \`IDX_5961204089e6486be6b43a9e6c\` (\`code\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`setting_string\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(50) NOT NULL, \`name\` varchar(250) NULL, \`type\` varchar(50) NOT NULL, \`description\` varchar(250) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_history\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`status\` varchar(50) NOT NULL, \`description\` varchar(250) NULL, \`bidId\` varchar(36) NOT NULL, \`employeeId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid_custom_price\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`number\` int NOT NULL DEFAULT '0', \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NOT NULL, \`isRequired\` tinyint NOT NULL DEFAULT 0, \`type\` varchar(255) NOT NULL DEFAULT 'Number', \`unit\` varchar(36) NULL, \`currency\` varchar(36) NULL, \`bidId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`bid\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`isShowHomePage\` tinyint NOT NULL DEFAULT 0, \`isSendEmailInviteBid\` tinyint NOT NULL DEFAULT 1, \`hasSendEmailInviteBid\` tinyint NOT NULL DEFAULT 0, \`name\` varchar(250) NOT NULL, \`code\` varchar(50) NOT NULL, \`serviceInvite\` varchar(250) NOT NULL, \`acceptEndDate\` datetime NOT NULL, \`submitEndDate\` datetime NOT NULL, \`addressSubmit\` varchar(250) NULL, \`companyInvite\` varchar(250) NOT NULL, \`listAddress\` varchar(250) NULL, \`publicDate\` datetime NOT NULL, \`bidTypeId\` varchar(36) NOT NULL, \`timeserving\` int NOT NULL DEFAULT '0', \`startBidDate\` datetime NOT NULL, \`bidOpenDate\` datetime NULL, \`moneyGuarantee\` bigint NULL, \`timeGuarantee\` int NULL, \`masterBidGuaranteeId\` varchar(36) NULL, \`timeTechDate\` datetime NOT NULL, \`timePriceDate\` datetime NOT NULL, \`timeCheckTechDate\` datetime NOT NULL, \`timeCheckPriceDate\` datetime NOT NULL, \`status\` varchar(50) NOT NULL, \`statusTech\` varchar(50) NOT NULL, \`statusTrade\` varchar(50) NOT NULL, \`statusPrice\` varchar(50) NOT NULL, \`statusChooseSupplier\` varchar(50) NOT NULL DEFAULT 'ChuaChon', \`statusRateTech\` varchar(50) NOT NULL, \`statusRateTrade\` varchar(50) NOT NULL, \`statusRatePrice\` varchar(50) NOT NULL, \`statusResetPrice\` varchar(50) NOT NULL DEFAULT 'ChuaTao', \`resetPriceEndDate\` datetime NULL, \`scoreDLC\` int NULL, \`noteTech\` text NULL, \`noteTrade\` text NULL, \`notePrice\` text NULL, \`noteTechLeader\` text NULL, \`noteMPOLeader\` text NULL, \`noteCloseBidMPO\` text NULL, \`noteCloseBidMPOLeader\` text NULL, \`serviceId\` varchar(36) NOT NULL, \`isRequestDelete\` tinyint NOT NULL DEFAULT 0, \`noteRequestDelete\` text NULL, \`fomular\` text NULL, \`percentTech\` int NOT NULL DEFAULT '0', \`percentTrade\` int NOT NULL DEFAULT '0', \`percentPrice\` int NOT NULL DEFAULT '0', \`fileDrawing\` varchar(500) NULL, \`fileJD\` varchar(500) NULL, \`fileKPI\` varchar(500) NULL, \`fileRule\` varchar(500) NULL, \`fileDocument\` varchar(500) NULL, \`fileAnother\` varchar(500) NULL, \`isRequireFilePriceDetail\` tinyint NULL DEFAULT 0, \`isRequireFileTechDetail\` tinyint NULL DEFAULT 0, \`approveChooseSupplierWinDate\` datetime NULL, \`noteFinishBidMPO\` text NULL, \`fileScan\` varchar(500) NULL, \`bidCloseDate\` datetime NULL, \`createdByName\` varchar(50) NULL, UNIQUE INDEX \`IDX_2c96dc3c8343f236e00485aef1\` (\`code\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`contract_appendix\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(50) NOT NULL, \`title\` varchar(250) NOT NULL, \`type\` varchar(50) NOT NULL, \`contractId\` varchar(36) NOT NULL, \`fileAttach\` varchar(250) NULL, \`effectiveDate\` datetime NOT NULL, \`expiredDate\` datetime NOT NULL, \`description\` varchar(250) NULL, \`objectId\` varchar(36) NULL, UNIQUE INDEX \`IDX_eaef0b45a49ddfb89435f91e74\` (\`code\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`contract_history\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`statusCurrent\` varchar(150) NULL, \`statusConvert\` varchar(150) NULL, \`contractId\` varchar(36) NOT NULL, \`employeeId\` varchar(36) NULL, \`description\` varchar(500) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`contract_member\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`employeeId\` varchar(36) NOT NULL, \`contractId\` varchar(36) NOT NULL, \`contractRoleCode\` varchar(50) NOT NULL, \`description\` varchar(250) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`contract\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(50) NOT NULL, \`name\` varchar(250) NOT NULL, \`supplierId\` varchar(36) NOT NULL, \`bidId\` varchar(36) NOT NULL, \`fileAttach\` varchar(250) NULL, \`effectiveDate\` datetime NOT NULL, \`expiredDate\` datetime NOT NULL, \`status\` varchar(50) NOT NULL, \`reason\` varchar(250) NULL, \`description\` varchar(500) NULL, \`branchId\` varchar(36) NULL, \`objectId\` varchar(36) NULL, \`value\` bigint NOT NULL DEFAULT '0', UNIQUE INDEX \`IDX_a167b5ec6a7dd9cd577bd622d8\` (\`code\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`qc_approver\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`qcId\` varchar(36) NULL, \`level\` int NOT NULL DEFAULT '1', \`employeeId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`error\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`code\` varchar(50) NOT NULL, \`description\` text NULL, UNIQUE INDEX \`IDX_d12ca650871335dade46b2052d\` (\`code\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`qc_detail_error\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`errorId\` varchar(36) NOT NULL, \`qcDetailId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`qc_detail_tech_list_detail\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`value\` int NOT NULL, \`qcDetailTechValueId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`qc_detail_tech_value\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`score\` float NULL, \`value\` varchar(250) NULL, \`qcDetailId\` varchar(36) NOT NULL, \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NOT NULL, \`isRequired\` tinyint NOT NULL DEFAULT 0, \`isCalUp\` tinyint NOT NULL DEFAULT 1, \`type\` varchar(255) NOT NULL DEFAULT 'string', \`dataCalType\` varchar(255) NULL, \`percent\` float NULL DEFAULT '0', \`percentRule\` bigint NULL, \`percentDownRule\` bigint NULL, \`level\` int NOT NULL DEFAULT '1', \`description\` text NULL, \`parentId\` varchar(36) NULL, \`minTX\` int NULL, \`maxTX\` int NULL, \`scoreDLC\` int NULL, \`requiredMin\` int NULL, \`isHighlight\` tinyint NOT NULL DEFAULT 0, \`hightlightValue\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`qc_detail\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`qcId\` varchar(36) NULL, \`quantity\` float NULL DEFAULT '0', \`quantityQC\` float NULL DEFAULT '0', \`quantityPass\` float NULL DEFAULT '0', \`quantityFail\` float NULL DEFAULT '0', \`point\` float NULL DEFAULT '0', \`manualPoint\` float NULL DEFAULT '0', \`note\` text NULL, \`codeQcItem\` varchar(250) NULL, \`asnId\` varchar(36) NULL, \`serviceId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`qc_history\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`createdByName\` varchar(250) NOT NULL, \`statusCurrent\` varchar(150) NULL, \`statusConvert\` varchar(150) NULL, \`description\` text NOT NULL, \`qcId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`warehouse\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(50) NOT NULL, \`code\` varchar(50) NOT NULL, \`description\` varchar(250) NULL, UNIQUE INDEX \`IDX_dcbf22551ec3827f234e532a08\` (\`code\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`qc\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(100) NOT NULL, \`status\` varchar(100) NOT NULL, \`employeeDeleteId\` varchar(36) NULL, \`asnId\` varchar(36) NULL, \`warehouseId\` varchar(36) NULL, \`asnCode\` varchar(100) NULL, \`objectId\` varchar(36) NULL, \`branchId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`object\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(1000) NOT NULL, \`code\` varchar(250) NOT NULL, \`description\` text NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`pr_approve\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`prId\` varchar(36) NOT NULL, \`level\` int NOT NULL DEFAULT '1', \`employeeId\` varchar(36) NULL, \`status\` varchar(36) NOT NULL, \`description\` text NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`pr_approver\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`prId\` varchar(36) NULL, \`level\` int NOT NULL DEFAULT '1', \`employeeId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`pr_history\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`createdByName\` varchar(250) NOT NULL, \`prId\` varchar(36) NOT NULL, \`statusCurrent\` varchar(150) NULL, \`statusConvert\` varchar(150) NULL, \`description\` varchar(500) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`pr\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`branchId\` varchar(36) NULL, \`name\` varchar(1000) NOT NULL, \`code\` varchar(250) NOT NULL, \`empProposerId\` varchar(36) NOT NULL, \`empInChargeId\` varchar(36) NOT NULL, \`deliveryDate\` datetime NOT NULL, \`deliveryAddress\` varchar(1000) NOT NULL, \`quantity\` int NULL, \`status\` varchar(50) NOT NULL, \`prType\` varchar(50) NOT NULL, \`objectId\` varchar(36) NULL, \`reason\` varchar(1000) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`pr_item\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`prId\` varchar(36) NOT NULL, \`serviceId\` varchar(36) NOT NULL, \`purchasePlanId\` varchar(36) NULL, \`quantity\` int NOT NULL, \`productName\` varchar(500) NOT NULL, \`suggestReason\` text NOT NULL, \`description\` text NULL, \`code\` varchar(36) NULL, \`unit\` varchar(500) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`purchase_plan_history\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`createdByName\` varchar(250) NOT NULL, \`purchasePlanId\` varchar(36) NOT NULL, \`statusCurrent\` varchar(150) NULL, \`statusConvert\` varchar(150) NULL, \`description\` varchar(500) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`purchase_plan_progress\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`purchasePlanId\` varchar(36) NOT NULL, \`sort\` int NOT NULL DEFAULT '1', \`progressDate\` datetime NOT NULL, \`quantity\` int NOT NULL DEFAULT '0', \`description\` text NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`purchase_plan\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(1000) NOT NULL, \`code\` varchar(250) NOT NULL, \`departmentId\` varchar(36) NOT NULL, \`serviceId\` varchar(36) NOT NULL, \`quantity\` int NOT NULL DEFAULT '0', \`quantityInbound\` int NOT NULL DEFAULT '0', \`description\` text NULL, \`budget\` float NULL DEFAULT '0', \`budgetRemaining\` float NULL DEFAULT '0', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`department\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(50) NOT NULL, \`code\` varchar(50) NOT NULL, \`description\` varchar(250) NULL, \`branchId\` varchar(36) NULL, UNIQUE INDEX \`IDX_62690f4fe31da9eb824d909285\` (\`code\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`employee_notify\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`message\` text NULL, \`messageFull\` text NULL, \`isNew\` tinyint NOT NULL DEFAULT 1, \`employeeId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`employee_warning\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`message\` text NULL, \`messageFull\` text NULL, \`isNew\` tinyint NOT NULL DEFAULT 1, \`employeeId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`employee\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(50) NOT NULL, \`name\` varchar(50) NOT NULL, \`email\` varchar(50) NOT NULL, \`description\` varchar(250) NULL, \`departmentId\` varchar(36) NOT NULL, \`userId\` varchar(36) NOT NULL, \`branchId\` varchar(36) NULL, UNIQUE INDEX \`IDX_348a4a9894eef0760bfe0a2632\` (\`code\`), UNIQUE INDEX \`REL_f4b0d329c4a3cf79ffe9d56504\` (\`userId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`branch_member\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`employeeId\` varchar(36) NOT NULL, \`branchdId\` varchar(36) NOT NULL, \`type\` varchar(50) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`branch\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`code\` varchar(250) NULL, \`description\` text NOT NULL, \`type\` varchar(50) NULL, \`level\` int NOT NULL, \`parentId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`asn\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(50) NOT NULL, \`branchId\` varchar(36) NULL, \`warehouseId\` varchar(36) NOT NULL, \`asnDate\` datetime NOT NULL, \`quantity\` int NOT NULL DEFAULT '0', \`purchasePlanId\` varchar(36) NULL, \`poId\` varchar(36) NOT NULL, \`description\` varchar(250) NULL, \`objectId\` varchar(36) NULL, \`quantityQC\` float NULL DEFAULT '0', \`quantityPass\` float NULL DEFAULT '0', \`quantityFail\` float NULL DEFAULT '0', UNIQUE INDEX \`IDX_fcbdf6b1d748b3fe0f43acaf7d\` (\`code\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`banner_client\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(50) NOT NULL, \`atr\` varchar(250) NOT NULL, \`type\` varchar(50) NOT NULL, \`position\` varchar(50) NOT NULL, \`url\` varchar(250) NULL, \`description\` varchar(250) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`ward\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`code\` varchar(250) NULL, \`districtId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`district\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`code\` varchar(250) NULL, \`cityId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`city\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`area\` varchar(50) NOT NULL DEFAULT '', \`region\` varchar(50) NOT NULL DEFAULT '', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`email_history\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`status\` varchar(50) NOT NULL, \`type\` varchar(50) NOT NULL, \`count\` int NOT NULL DEFAULT '0', \`toAddresses\` varchar(250) NOT NULL DEFAULT '', \`ccAddresses\` varchar(250) NULL, \`bccAddresses\` varchar(250) NULL, \`subject\` varchar(250) NOT NULL DEFAULT '', \`body_text\` text NOT NULL, \`body_html\` text NOT NULL, \`result\` json NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`data_history\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`tableName\` varchar(250) NOT NULL, \`relationId\` varchar(36) NOT NULL, \`description\` varchar(250) NULL, \`dataJson\` json NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`email_template\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`code\` varchar(150) NOT NULL, \`description\` text NULL, UNIQUE INDEX \`IDX_75bd34e96cde646bb118a6c267\` (\`code\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`faq\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`title\` varchar(500) NOT NULL, \`description\` text NULL, \`categoryId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`faq_category\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(500) NOT NULL, \`description\` text NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`holiday\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`date\` datetime NOT NULL, UNIQUE INDEX \`IDX_89b26e4ed3db8895b86c8df55e\` (\`date\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`language\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(500) NOT NULL, \`code\` varchar(50) NOT NULL, \`avatarUrl\` text NULL, \`description\` text NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`language_config\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`languageId\` varchar(36) NOT NULL, \`sourceType\` varchar(50) NOT NULL, \`component\` varchar(500) NOT NULL, \`key\` varchar(500) NOT NULL, \`value\` text NOT NULL, \`description\` text NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`link_client\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(50) NOT NULL, \`url\` varchar(250) NOT NULL, \`description\` varchar(250) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`setting_string_client\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NULL, \`type\` varchar(50) NOT NULL, \`description\` varchar(250) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `ALTER TABLE \`item_tech_list_detail\` ADD CONSTRAINT \`FK_6d5e197e96ef20c03594d262ec2\` FOREIGN KEY (\`itemTechId\`) REFERENCES \`item_tech\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`item_tech\` ADD CONSTRAINT \`FK_48a9e3f00c9fd54618dcc23c5a6\` FOREIGN KEY (\`parentId\`) REFERENCES \`item_tech\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`item_tech\` ADD CONSTRAINT \`FK_5964da7a8a0a76663eb696a6789\` FOREIGN KEY (\`prItemId\`) REFERENCES \`pr_item\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_capacity_list_detail\` ADD CONSTRAINT \`FK_ceba55fc1b9eb39b36c43a9644c\` FOREIGN KEY (\`serviceCapacityId\`) REFERENCES \`service_capacity\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_expertise_year_detail\` ADD CONSTRAINT \`FK_b13f447aa60bdfc21ea9b307c3d\` FOREIGN KEY (\`supplierExpertiseDetailId\`) REFERENCES \`supplier_expertise_detail\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_expertise_detail\` ADD CONSTRAINT \`FK_897a53586bd0c3ae35db7822c9d\` FOREIGN KEY (\`supplierCapacityId\`) REFERENCES \`supplier_capacity\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_expertise_detail\` ADD CONSTRAINT \`FK_4b70099f1d6be7f37b245384b0a\` FOREIGN KEY (\`supplierExpertiseId\`) REFERENCES \`supplier_expertise\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_service\` ADD CONSTRAINT \`FK_6b05b7e468e9a3017dc6d3f166d\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_service\` ADD CONSTRAINT \`FK_f5c9f4bef5827bfb11d397889c5\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_expertise_law_detail\` ADD CONSTRAINT \`FK_61a4e2682fbbaf81ccad6ef2c1e\` FOREIGN KEY (\`supplierExpertiseId\`) REFERENCES \`supplier_expertise\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_expertise_member\` ADD CONSTRAINT \`FK_25ab41887eb8ff9c33d22870a26\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_expertise_member\` ADD CONSTRAINT \`FK_14c6ca8022f36caccb2c0d08b57\` FOREIGN KEY (\`supplierExpertiseId\`) REFERENCES \`supplier_expertise\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_expertise\` ADD CONSTRAINT \`FK_12f94c1dacf89108d7f46c63614\` FOREIGN KEY (\`approvedLawId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_expertise\` ADD CONSTRAINT \`FK_337bed433a9811ccf8dcd6b704f\` FOREIGN KEY (\`approvedCapacityId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_expertise\` ADD CONSTRAINT \`FK_10cffdbce6d3a175451316e8028\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_expertise\` ADD CONSTRAINT \`FK_d096408a337c2f47410e8c9d1bb\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_expertise\` ADD CONSTRAINT \`FK_7a85deff050ee041cbdac9191a0\` FOREIGN KEY (\`supplierServiceId\`) REFERENCES \`supplier_service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`user_confirm_code\` ADD CONSTRAINT \`FK_1d3f2a4a0693ef7a44e57284ea9\` FOREIGN KEY (\`userId\`) REFERENCES \`user\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`user\` ADD CONSTRAINT \`FK_031cdc2c9c5eb56d48b5bdb4e54\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`user\` ADD CONSTRAINT \`FK_ab4a80281f1e8d524714e00f38f\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_tech_list_detail\` ADD CONSTRAINT \`FK_4dd593368143571aceb5c285278\` FOREIGN KEY (\`bidTechId\`) REFERENCES \`bid_tech\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_tech\` ADD CONSTRAINT \`FK_2fe4a2fbbde3f9c07132b807ad6\` FOREIGN KEY (\`parentId\`) REFERENCES \`bid_tech\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_tech\` ADD CONSTRAINT \`FK_c7b436b32d81e53e7d9834ea324\` FOREIGN KEY (\`bidId\`) REFERENCES \`bid\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_supplier_tech_value\` ADD CONSTRAINT \`FK_2a427d946d1e7c5e65df58021eb\` FOREIGN KEY (\`bidSupplierId\`) REFERENCES \`bid_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_supplier_tech_value\` ADD CONSTRAINT \`FK_47145f6233cbe2efec0ddfd400f\` FOREIGN KEY (\`bidTechId\`) REFERENCES \`bid_tech\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_price_list_detail\` ADD CONSTRAINT \`FK_70b3d2a07ee305270b90ebf58dc\` FOREIGN KEY (\`bidPriceId\`) REFERENCES \`bid_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_deal_supplier_price_value\` ADD CONSTRAINT \`FK_34b418fb00850329693a941c154\` FOREIGN KEY (\`bidDealSupplierId\`) REFERENCES \`bid_deal_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_deal_supplier_price_value\` ADD CONSTRAINT \`FK_c95d5eae41bffd589aada849724\` FOREIGN KEY (\`bidPriceId\`) REFERENCES \`bid_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_deal_supplier\` ADD CONSTRAINT \`FK_291ace50643326687ee885f12e3\` FOREIGN KEY (\`bidDealId\`) REFERENCES \`bid_deal\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_deal_supplier\` ADD CONSTRAINT \`FK_14acd799af1581375e56c8e9ed6\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_deal\` ADD CONSTRAINT \`FK_94930fed93b5d08bd97b336a20a\` FOREIGN KEY (\`bidId\`) REFERENCES \`bid\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_deal_price\` ADD CONSTRAINT \`FK_58e338315728b94e4d7dc64512b\` FOREIGN KEY (\`bidDealId\`) REFERENCES \`bid_deal\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_deal_price\` ADD CONSTRAINT \`FK_fa500f428373aa6dd5f183cc6fe\` FOREIGN KEY (\`bidPriceId\`) REFERENCES \`bid_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_price_list_detail\` ADD CONSTRAINT \`FK_c3c43ede69d2f56a6c805b7ce23\` FOREIGN KEY (\`servicePriceId\`) REFERENCES \`service_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_price_col\` ADD CONSTRAINT \`FK_b598b93058d3376e11f0a5d7ef0\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_price_col_value\` ADD CONSTRAINT \`FK_5f7d7b40cfb3d7ae2c8ee6555c5\` FOREIGN KEY (\`servicePriceId\`) REFERENCES \`service_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_price_col_value\` ADD CONSTRAINT \`FK_1b5ccab6379d4d58b3fa85e7b41\` FOREIGN KEY (\`servicePriceColId\`) REFERENCES \`service_price_col\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_price\` ADD CONSTRAINT \`FK_d4520a0074d2b259db1b40149cd\` FOREIGN KEY (\`parentId\`) REFERENCES \`service_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_price\` ADD CONSTRAINT \`FK_4c807146960feb2faad0031f145\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_auction_price\` ADD CONSTRAINT \`FK_d4c542ae290d14e5afae2d9e33e\` FOREIGN KEY (\`bidAuctionId\`) REFERENCES \`bid_auction\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_auction_price\` ADD CONSTRAINT \`FK_02a5453262e93dbdb2c88945407\` FOREIGN KEY (\`bidPriceId\`) REFERENCES \`bid_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_auction\` ADD CONSTRAINT \`FK_98fb3b554a2cbb46e250fdf9321\` FOREIGN KEY (\`bidId\`) REFERENCES \`bid\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_auction_supplier\` ADD CONSTRAINT \`FK_0ffe9b806010eebbba16db32b77\` FOREIGN KEY (\`bidAuctionId\`) REFERENCES \`bid_auction\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_auction_supplier\` ADD CONSTRAINT \`FK_4f8b58f91c29f00e1a7d285de54\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_auction_supplier_price_value\` ADD CONSTRAINT \`FK_2fe1c9d4d0e650eab1eb90e2191\` FOREIGN KEY (\`bidAuctionSupplierId\`) REFERENCES \`bid_auction_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_auction_supplier_price_value\` ADD CONSTRAINT \`FK_da27cb5423990fdcd1aa1a18a9d\` FOREIGN KEY (\`bidPriceId\`) REFERENCES \`bid_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_price_col_value\` ADD CONSTRAINT \`FK_5736a119be595711e5d6abe52c5\` FOREIGN KEY (\`bidPriceId\`) REFERENCES \`bid_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_price_col_value\` ADD CONSTRAINT \`FK_cec4a85e38a248f39dad6030c45\` FOREIGN KEY (\`bidPriceColId\`) REFERENCES \`bid_price_col\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_price_col\` ADD CONSTRAINT \`FK_6dc4cb70fa85c3fda253011ede2\` FOREIGN KEY (\`bidId\`) REFERENCES \`bid\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_supplier_price_col_value\` ADD CONSTRAINT \`FK_037371a1b92304d1c0f2407e59e\` FOREIGN KEY (\`bidSupplierId\`) REFERENCES \`bid_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_supplier_price_col_value\` ADD CONSTRAINT \`FK_656f153e808288f954929c26fd0\` FOREIGN KEY (\`bidPriceId\`) REFERENCES \`bid_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_supplier_price_col_value\` ADD CONSTRAINT \`FK_a54de3eebd756601dade529646b\` FOREIGN KEY (\`bidPriceColId\`) REFERENCES \`bid_price_col\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_supplier_price\` ADD CONSTRAINT \`FK_1a034db1ac5166a91766a3320ae\` FOREIGN KEY (\`bidSupplierId\`) REFERENCES \`bid_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_supplier_price\` ADD CONSTRAINT \`FK_c2ca28dc7586fbdd2ca2704aa35\` FOREIGN KEY (\`bidPriceId\`) REFERENCES \`bid_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_price\` ADD CONSTRAINT \`FK_a440790cd408efa2af7dfc1a579\` FOREIGN KEY (\`parentId\`) REFERENCES \`bid_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_price\` ADD CONSTRAINT \`FK_867db5c177deae3fa9b9708dc2d\` FOREIGN KEY (\`bidId\`) REFERENCES \`bid\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_price\` ADD CONSTRAINT \`FK_4b18d8d606a19e22f05eda2aa9d\` FOREIGN KEY (\`servicePriceId\`) REFERENCES \`service_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_supplier_price_value\` ADD CONSTRAINT \`FK_16d1ee6e94f781d84aabd66c194\` FOREIGN KEY (\`bidSupplierId\`) REFERENCES \`bid_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_supplier_price_value\` ADD CONSTRAINT \`FK_b3c0325be1f8f2b8e9ac36f940c\` FOREIGN KEY (\`bidPriceId\`) REFERENCES \`bid_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_trade_list_detail\` ADD CONSTRAINT \`FK_1f7c17f8cf412189406f422713a\` FOREIGN KEY (\`bidTradeId\`) REFERENCES \`bid_trade\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_trade\` ADD CONSTRAINT \`FK_3b62791f9ea7005f5e904d03b1e\` FOREIGN KEY (\`parentId\`) REFERENCES \`bid_trade\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_trade\` ADD CONSTRAINT \`FK_be91681664c1ac732c62a9bf9af\` FOREIGN KEY (\`bidId\`) REFERENCES \`bid\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_supplier_trade_value\` ADD CONSTRAINT \`FK_9aac8e343250eb68472ac29586d\` FOREIGN KEY (\`bidSupplierId\`) REFERENCES \`bid_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_supplier_trade_value\` ADD CONSTRAINT \`FK_cbeb3468d0b6a2e95cee6278ea6\` FOREIGN KEY (\`bidTradeId\`) REFERENCES \`bid_trade\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_supplier_custom_price_value\` ADD CONSTRAINT \`FK_4ac6991b9fc4be2e43b62066736\` FOREIGN KEY (\`bidSupplierId\`) REFERENCES \`bid_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_supplier\` ADD CONSTRAINT \`FK_f0256ba2eb660b86de5838ccc76\` FOREIGN KEY (\`bidId\`) REFERENCES \`bid\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_supplier\` ADD CONSTRAINT \`FK_94afaf0e41c3cc6a62b05333f69\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_notify\` ADD CONSTRAINT \`FK_1c604e9c16b9d840aa0b73065db\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`invoice_file\` ADD CONSTRAINT \`FK_1eefed5c372953d7d8df4fb8346\` FOREIGN KEY (\`invoiceId\`) REFERENCES \`invoice\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`invoice\` ADD CONSTRAINT \`FK_b04287ad09b2e05e7cad221f23b\` FOREIGN KEY (\`invoiceSuggestId\`) REFERENCES \`invoice_suggest\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`invoice_suggest_history\` ADD CONSTRAINT \`FK_77fd3adce89fc31801cf99f897b\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`invoice_suggest_history\` ADD CONSTRAINT \`FK_e7321a1fad6a951d50320c34ced\` FOREIGN KEY (\`invoiceSuggestId\`) REFERENCES \`invoice_suggest\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`invoice_suggest_file\` ADD CONSTRAINT \`FK_d10942b8523a906cecb5782bd69\` FOREIGN KEY (\`invoiceSuggestId\`) REFERENCES \`invoice_suggest\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`payment_progress\` ADD CONSTRAINT \`FK_ca21e5bc8d43455f8d543924951\` FOREIGN KEY (\`poId\`) REFERENCES \`po\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`payment_progress\` ADD CONSTRAINT \`FK_327c5af92cf9846e831ed369853\` FOREIGN KEY (\`contractId\`) REFERENCES \`contract\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`invoice_suggest\` ADD CONSTRAINT \`FK_11f90e5481556437334ab94980a\` FOREIGN KEY (\`contractId\`) REFERENCES \`contract\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`invoice_suggest\` ADD CONSTRAINT \`FK_cbebd9a92b5e775a972f999022f\` FOREIGN KEY (\`poId\`) REFERENCES \`po\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`invoice_suggest\` ADD CONSTRAINT \`FK_df66edc0ae0b3af4e8e219df4aa\` FOREIGN KEY (\`paymentPlanId\`) REFERENCES \`payment_progress\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`po_history\` ADD CONSTRAINT \`FK_00555eaae617d36cb019cc436ea\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`po_history\` ADD CONSTRAINT \`FK_64a00b17e837cf662894fef5e35\` FOREIGN KEY (\`poId\`) REFERENCES \`po\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`po_history\` ADD CONSTRAINT \`FK_18b2851fecdd41d92f1c1126c85\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`po_member\` ADD CONSTRAINT \`FK_f41f38ea6931f3fc5becb0d632c\` FOREIGN KEY (\`poId\`) REFERENCES \`po\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`po_member\` ADD CONSTRAINT \`FK_345131b5513984074aef49c14ef\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`po_product\` ADD CONSTRAINT \`FK_babde8a2029fbfc7e461716c732\` FOREIGN KEY (\`poId\`) REFERENCES \`po\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`po_product\` ADD CONSTRAINT \`FK_4988bdc3aeb03f2942dd547e8b7\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`po\` ADD CONSTRAINT \`FK_717ae013a85f97e8e5685225037\` FOREIGN KEY (\`contractId\`) REFERENCES \`contract\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`po\` ADD CONSTRAINT \`FK_6a9bd6d1b0c829ba79e9df5269b\` FOREIGN KEY (\`contractPaymentPlanId\`) REFERENCES \`payment_progress\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`po\` ADD CONSTRAINT \`FK_e5fb555ea198a0aec1530f5e46b\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`po\` ADD CONSTRAINT \`FK_e1e5f8634306efc40704a8a2628\` FOREIGN KEY (\`bidId\`) REFERENCES \`bid\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`po\` ADD CONSTRAINT \`FK_9ae35e3316a98f3a9d740df26c0\` FOREIGN KEY (\`prId\`) REFERENCES \`pr\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`po\` ADD CONSTRAINT \`FK_e5941648e03ac9752a0a648c66d\` FOREIGN KEY (\`branchId\`) REFERENCES \`branch\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`po\` ADD CONSTRAINT \`FK_e9d9b2e16b2b42c93a73f493872\` FOREIGN KEY (\`objectId\`) REFERENCES \`object\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier\` ADD CONSTRAINT \`FK_e8902c50550ff82dd0143913c0a\` FOREIGN KEY (\`userId\`) REFERENCES \`user\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_capacity_list_detail\` ADD CONSTRAINT \`FK_6ddada23ada70eb762bd1a23629\` FOREIGN KEY (\`supplierCapacityId\`) REFERENCES \`supplier_capacity\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_capacity_year_value\` ADD CONSTRAINT \`FK_91d2bd0882d25205d6caa30510c\` FOREIGN KEY (\`supplierCapacityId\`) REFERENCES \`supplier_capacity\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_capacity\` ADD CONSTRAINT \`FK_3c21723f51589880426ee09a38f\` FOREIGN KEY (\`parentId\`) REFERENCES \`supplier_capacity\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_capacity\` ADD CONSTRAINT \`FK_f4c199649f05d2e0d4b6b1d1821\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_capacity\` ADD CONSTRAINT \`FK_a233c4a4427a6f87cc7fafc94e0\` FOREIGN KEY (\`serviceCapacityId\`) REFERENCES \`service_capacity\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_capacity\` ADD CONSTRAINT \`FK_658a89d26d22b5889d2c8808343\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`supplier_capacity\` ADD CONSTRAINT \`FK_5224e0a2a8a3950a956ce919e92\` FOREIGN KEY (\`supplierServiceId\`) REFERENCES \`supplier_service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_capacity\` ADD CONSTRAINT \`FK_dfaf935c8daef047d0dcf2bcde4\` FOREIGN KEY (\`parentId\`) REFERENCES \`service_capacity\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_capacity\` ADD CONSTRAINT \`FK_e7b898103d3542a0127af5c146a\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_tech_list_detail\` ADD CONSTRAINT \`FK_146154f9f04aa7b69ae91e183c2\` FOREIGN KEY (\`serviceTechId\`) REFERENCES \`service_tech\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_tech\` ADD CONSTRAINT \`FK_3d46899c062ba4e9b5063c3992a\` FOREIGN KEY (\`parentId\`) REFERENCES \`service_tech\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_tech\` ADD CONSTRAINT \`FK_662fc4b072225f31c77b1e80a85\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_trade_list_detail\` ADD CONSTRAINT \`FK_24bb7ed260a75bba7c4bbde494b\` FOREIGN KEY (\`serviceTradeId\`) REFERENCES \`service_trade\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_trade\` ADD CONSTRAINT \`FK_e47ba7f64cf736318a65b1c9c5d\` FOREIGN KEY (\`parentId\`) REFERENCES \`service_trade\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_trade\` ADD CONSTRAINT \`FK_82da0abb34d4309aab022fa6cb2\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_access\` ADD CONSTRAINT \`FK_f5b81b0704c5b994467aa8120b3\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_access\` ADD CONSTRAINT \`FK_d70b3e485a2bb3b9df9d23520e1\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service_custom_price\` ADD CONSTRAINT \`FK_2092855ad0938615c9692bf66d5\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`asn_item\` ADD CONSTRAINT \`FK_94f94225540bd149b93a7edbc3d\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`asn_item\` ADD CONSTRAINT \`FK_a4e8cdf243ed885aede15a40ca2\` FOREIGN KEY (\`asnId\`) REFERENCES \`asn\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service\` ADD CONSTRAINT \`FK_c5906ffd2cd6fa558710901e4d0\` FOREIGN KEY (\`parentId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`service\` ADD CONSTRAINT \`FK_9228bad899a36c28a90bc34608a\` FOREIGN KEY (\`approveById\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_employee_access\` ADD CONSTRAINT \`FK_e1ab4ee56faa6b39f43ee3e8d72\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_employee_access\` ADD CONSTRAINT \`FK_3eb200381cf528ef3bb0328ddaf\` FOREIGN KEY (\`bidId\`) REFERENCES \`bid\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_history\` ADD CONSTRAINT \`FK_1add2777fbf78612fd65d33a930\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_history\` ADD CONSTRAINT \`FK_cedae6a0aa32c469442a9465bf5\` FOREIGN KEY (\`bidId\`) REFERENCES \`bid\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid_custom_price\` ADD CONSTRAINT \`FK_3640f4bfc140dc3cbc0f3135171\` FOREIGN KEY (\`bidId\`) REFERENCES \`bid\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid\` ADD CONSTRAINT \`FK_97c59f5857f8e386c213640b046\` FOREIGN KEY (\`bidTypeId\`) REFERENCES \`bid_type\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid\` ADD CONSTRAINT \`FK_e2aeadf35be1bbbbc7a86a828d5\` FOREIGN KEY (\`masterBidGuaranteeId\`) REFERENCES \`setting_string\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid\` ADD CONSTRAINT \`FK_1854c79454cd8bbbf31cac76304\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`contract_appendix\` ADD CONSTRAINT \`FK_eb779d50474b4b376465d77b3ea\` FOREIGN KEY (\`contractId\`) REFERENCES \`contract\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`contract_appendix\` ADD CONSTRAINT \`FK_556f3e22ab0398943b401e51825\` FOREIGN KEY (\`objectId\`) REFERENCES \`object\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`contract_history\` ADD CONSTRAINT \`FK_5e938fef02a12dc97fe6bd31dd4\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`contract_history\` ADD CONSTRAINT \`FK_b4081c0bb5e5ad9440295155b7a\` FOREIGN KEY (\`contractId\`) REFERENCES \`contract\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`contract_member\` ADD CONSTRAINT \`FK_515f927b988d26efbbdb0812039\` FOREIGN KEY (\`contractId\`) REFERENCES \`contract\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`contract_member\` ADD CONSTRAINT \`FK_23604fe008a588fdb81f16e8db7\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`contract\` ADD CONSTRAINT \`FK_5f9aba86ffb85928d9bda6e9244\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`contract\` ADD CONSTRAINT \`FK_94641949661345c81577cdf4f11\` FOREIGN KEY (\`bidId\`) REFERENCES \`bid\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`contract\` ADD CONSTRAINT \`FK_7e0acd2febb431736422963c5eb\` FOREIGN KEY (\`branchId\`) REFERENCES \`branch\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`contract\` ADD CONSTRAINT \`FK_5afd147c13c9f7350edcc1b4d00\` FOREIGN KEY (\`objectId\`) REFERENCES \`object\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`qc_approver\` ADD CONSTRAINT \`FK_72fdb87f13b6822a52331febba9\` FOREIGN KEY (\`qcId\`) REFERENCES \`qc\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`qc_approver\` ADD CONSTRAINT \`FK_2069cb9fdad46db7ade8d45b235\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`qc_detail_error\` ADD CONSTRAINT \`FK_17f33b71bda4eca3491164a1415\` FOREIGN KEY (\`errorId\`) REFERENCES \`error\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`qc_detail_error\` ADD CONSTRAINT \`FK_2285706e44bcf1195a6eed3d484\` FOREIGN KEY (\`qcDetailId\`) REFERENCES \`qc_detail\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`qc_detail_tech_list_detail\` ADD CONSTRAINT \`FK_b22362d6b67f171a0bc6cd7027c\` FOREIGN KEY (\`qcDetailTechValueId\`) REFERENCES \`qc_detail_tech_value\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`qc_detail_tech_value\` ADD CONSTRAINT \`FK_e1298fa8b0974aa33705c110499\` FOREIGN KEY (\`qcDetailId\`) REFERENCES \`qc_detail\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`qc_detail_tech_value\` ADD CONSTRAINT \`FK_a60d2e2dcd5f69db57ae3b03c36\` FOREIGN KEY (\`parentId\`) REFERENCES \`qc_detail_tech_value\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`qc_detail\` ADD CONSTRAINT \`FK_53c15878a478f873e46f0f0e3cf\` FOREIGN KEY (\`qcId\`) REFERENCES \`qc\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`qc_detail\` ADD CONSTRAINT \`FK_b6952e0c7ba3926cde159df6dfd\` FOREIGN KEY (\`asnId\`) REFERENCES \`asn\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`qc_history\` ADD CONSTRAINT \`FK_fd4c285a2f623b390819fc6b839\` FOREIGN KEY (\`qcId\`) REFERENCES \`qc\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`qc\` ADD CONSTRAINT \`FK_3fb895842faafc6c52e1380902a\` FOREIGN KEY (\`warehouseId\`) REFERENCES \`warehouse\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`qc\` ADD CONSTRAINT \`FK_3e20641b83dcfcedb05233d05c8\` FOREIGN KEY (\`employeeDeleteId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`qc\` ADD CONSTRAINT \`FK_ee6d38bbf6c06f17a6e792c6f12\` FOREIGN KEY (\`asnId\`) REFERENCES \`asn\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`qc\` ADD CONSTRAINT \`FK_646b3f9901048fe4f0646691cd4\` FOREIGN KEY (\`branchId\`) REFERENCES \`branch\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`qc\` ADD CONSTRAINT \`FK_e6ecfa5009a50089bf2444801a1\` FOREIGN KEY (\`objectId\`) REFERENCES \`object\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_approve\` ADD CONSTRAINT \`FK_9ee9e99d558a52c7c4b126045dd\` FOREIGN KEY (\`prId\`) REFERENCES \`pr\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_approve\` ADD CONSTRAINT \`FK_ef191141a93744337fa6a27832d\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_approver\` ADD CONSTRAINT \`FK_674cf3c692471a6d2a0125da1b9\` FOREIGN KEY (\`prId\`) REFERENCES \`pr\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_approver\` ADD CONSTRAINT \`FK_bac38338d39c6b8427345f58b68\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_history\` ADD CONSTRAINT \`FK_b3e89605faa3387382f647936e5\` FOREIGN KEY (\`prId\`) REFERENCES \`pr\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr\` ADD CONSTRAINT \`FK_8656b90e0b3b5e5d486219d3751\` FOREIGN KEY (\`empProposerId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr\` ADD CONSTRAINT \`FK_c129905cf9385e8e9b37f0a27a7\` FOREIGN KEY (\`empInChargeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr\` ADD CONSTRAINT \`FK_71c661eef16ccaf0573f7b4c125\` FOREIGN KEY (\`branchId\`) REFERENCES \`branch\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr\` ADD CONSTRAINT \`FK_3bff1f3c945e4297489a9d04955\` FOREIGN KEY (\`objectId\`) REFERENCES \`object\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_item\` ADD CONSTRAINT \`FK_f46b4eaadb685dc4462f80a51e2\` FOREIGN KEY (\`prId\`) REFERENCES \`pr\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_item\` ADD CONSTRAINT \`FK_59e8a9cc2e2435ef62ae7402a1a\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_item\` ADD CONSTRAINT \`FK_a7d1b8bd0a5547fd0ff1e3a9f1d\` FOREIGN KEY (\`purchasePlanId\`) REFERENCES \`purchase_plan\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`purchase_plan_history\` ADD CONSTRAINT \`FK_1bbf46da1f333e0465df3e9b4dc\` FOREIGN KEY (\`purchasePlanId\`) REFERENCES \`purchase_plan\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`purchase_plan_progress\` ADD CONSTRAINT \`FK_e4e190ca929cb8958fc52d215ef\` FOREIGN KEY (\`purchasePlanId\`) REFERENCES \`purchase_plan\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`purchase_plan\` ADD CONSTRAINT \`FK_e158f8375b20595097bc3a7f2ca\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`purchase_plan\` ADD CONSTRAINT \`FK_875c53db2a281018c3be10a01cf\` FOREIGN KEY (\`departmentId\`) REFERENCES \`department\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`department\` ADD CONSTRAINT \`FK_a44bcea55f11d1502fc11e39fe7\` FOREIGN KEY (\`branchId\`) REFERENCES \`branch\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`employee_notify\` ADD CONSTRAINT \`FK_829b874115967ba418739c0020d\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`employee_warning\` ADD CONSTRAINT \`FK_f8729a94462abe07155640c0bc6\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`employee\` ADD CONSTRAINT \`FK_9ad20e4029f9458b6eed0b0c454\` FOREIGN KEY (\`departmentId\`) REFERENCES \`department\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`employee\` ADD CONSTRAINT \`FK_f4b0d329c4a3cf79ffe9d565047\` FOREIGN KEY (\`userId\`) REFERENCES \`user\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`employee\` ADD CONSTRAINT \`FK_c36b6dc182259c56ee8c1cfecb3\` FOREIGN KEY (\`branchId\`) REFERENCES \`branch\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`branch_member\` ADD CONSTRAINT \`FK_b6ecf37f359e9e350741b3e47a1\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`branch_member\` ADD CONSTRAINT \`FK_ce789a3ab0a31e3dba2452ef278\` FOREIGN KEY (\`branchdId\`) REFERENCES \`branch\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`branch\` ADD CONSTRAINT \`FK_f36f272d6f21d49663f0bcf431f\` FOREIGN KEY (\`parentId\`) REFERENCES \`branch\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`asn\` ADD CONSTRAINT \`FK_2005232919f7729590046ad2928\` FOREIGN KEY (\`warehouseId\`) REFERENCES \`warehouse\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`asn\` ADD CONSTRAINT \`FK_db83385873e75c44e7e9cfc482e\` FOREIGN KEY (\`branchId\`) REFERENCES \`branch\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`asn\` ADD CONSTRAINT \`FK_8bed13c28ada5a824ca86fb00ef\` FOREIGN KEY (\`poId\`) REFERENCES \`po\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`asn\` ADD CONSTRAINT \`FK_062faf2ef7ebea085910510b74c\` FOREIGN KEY (\`purchasePlanId\`) REFERENCES \`purchase_plan\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`asn\` ADD CONSTRAINT \`FK_b6fd1bb9b3eee21b59a0e535316\` FOREIGN KEY (\`objectId\`) REFERENCES \`object\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`ward\` ADD CONSTRAINT \`FK_19a3bc9b3be291e8b9bc2bb623b\` FOREIGN KEY (\`districtId\`) REFERENCES \`district\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`district\` ADD CONSTRAINT \`FK_148f1c944d0fec4114a54984da1\` FOREIGN KEY (\`cityId\`) REFERENCES \`city\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`faq\` ADD CONSTRAINT \`FK_953d55203e245c23dc0882cf1d6\` FOREIGN KEY (\`categoryId\`) REFERENCES \`faq_category\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`language_config\` ADD CONSTRAINT \`FK_383c6c21a4121464e736df53053\` FOREIGN KEY (\`languageId\`) REFERENCES \`language\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )

    await queryRunner.query("INSERT INTO `department`(`id`,`name`,`code`) VALUES ('af1f9652-1ebe-4fbd-8ce0-4e2854940fe8','IT','IT')", undefined)
    await queryRunner.query(
      "INSERT INTO `user`(`id`,`username`, `password`, `type`) VALUES ('4503e885-11db-4dca-8a55-292f5f8166eb','admin', '$2b$12$zRX8Z8WRQGoX9Qu9kOolfugXhbky3vU7VPJB4FJ5XwODe4iYBPkO2', 'Admin')",
      undefined,
    )
    await queryRunner.query(
      "INSERT INTO `employee`(id,`name`,`code`,`email`,`userId`,`departmentId`) VALUES ('59d16def-de29-469a-bf89-658b34ec7cf6','admin','admin','<EMAIL>','4503e885-11db-4dca-8a55-292f5f8166eb','af1f9652-1ebe-4fbd-8ce0-4e2854940fe8')",
      undefined,
    )
    await queryRunner.query(
      "UPDATE `user` SET `employeeId` = '59d16def-de29-469a-bf89-658b34ec7cf6' WHERE id = '4503e885-11db-4dca-8a55-292f5f8166eb'",
      undefined,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`language_config\` DROP FOREIGN KEY \`FK_383c6c21a4121464e736df53053\``)
    await queryRunner.query(`ALTER TABLE \`faq\` DROP FOREIGN KEY \`FK_953d55203e245c23dc0882cf1d6\``)
    await queryRunner.query(`ALTER TABLE \`district\` DROP FOREIGN KEY \`FK_148f1c944d0fec4114a54984da1\``)
    await queryRunner.query(`ALTER TABLE \`ward\` DROP FOREIGN KEY \`FK_19a3bc9b3be291e8b9bc2bb623b\``)
    await queryRunner.query(`ALTER TABLE \`asn\` DROP FOREIGN KEY \`FK_b6fd1bb9b3eee21b59a0e535316\``)
    await queryRunner.query(`ALTER TABLE \`asn\` DROP FOREIGN KEY \`FK_062faf2ef7ebea085910510b74c\``)
    await queryRunner.query(`ALTER TABLE \`asn\` DROP FOREIGN KEY \`FK_8bed13c28ada5a824ca86fb00ef\``)
    await queryRunner.query(`ALTER TABLE \`asn\` DROP FOREIGN KEY \`FK_db83385873e75c44e7e9cfc482e\``)
    await queryRunner.query(`ALTER TABLE \`asn\` DROP FOREIGN KEY \`FK_2005232919f7729590046ad2928\``)
    await queryRunner.query(`ALTER TABLE \`branch\` DROP FOREIGN KEY \`FK_f36f272d6f21d49663f0bcf431f\``)
    await queryRunner.query(`ALTER TABLE \`branch_member\` DROP FOREIGN KEY \`FK_ce789a3ab0a31e3dba2452ef278\``)
    await queryRunner.query(`ALTER TABLE \`branch_member\` DROP FOREIGN KEY \`FK_b6ecf37f359e9e350741b3e47a1\``)
    await queryRunner.query(`ALTER TABLE \`employee\` DROP FOREIGN KEY \`FK_c36b6dc182259c56ee8c1cfecb3\``)
    await queryRunner.query(`ALTER TABLE \`employee\` DROP FOREIGN KEY \`FK_f4b0d329c4a3cf79ffe9d565047\``)
    await queryRunner.query(`ALTER TABLE \`employee\` DROP FOREIGN KEY \`FK_9ad20e4029f9458b6eed0b0c454\``)
    await queryRunner.query(`ALTER TABLE \`employee_warning\` DROP FOREIGN KEY \`FK_f8729a94462abe07155640c0bc6\``)
    await queryRunner.query(`ALTER TABLE \`employee_notify\` DROP FOREIGN KEY \`FK_829b874115967ba418739c0020d\``)
    await queryRunner.query(`ALTER TABLE \`department\` DROP FOREIGN KEY \`FK_a44bcea55f11d1502fc11e39fe7\``)
    await queryRunner.query(`ALTER TABLE \`purchase_plan\` DROP FOREIGN KEY \`FK_875c53db2a281018c3be10a01cf\``)
    await queryRunner.query(`ALTER TABLE \`purchase_plan\` DROP FOREIGN KEY \`FK_e158f8375b20595097bc3a7f2ca\``)
    await queryRunner.query(`ALTER TABLE \`purchase_plan_progress\` DROP FOREIGN KEY \`FK_e4e190ca929cb8958fc52d215ef\``)
    await queryRunner.query(`ALTER TABLE \`purchase_plan_history\` DROP FOREIGN KEY \`FK_1bbf46da1f333e0465df3e9b4dc\``)
    await queryRunner.query(`ALTER TABLE \`pr_item\` DROP FOREIGN KEY \`FK_a7d1b8bd0a5547fd0ff1e3a9f1d\``)
    await queryRunner.query(`ALTER TABLE \`pr_item\` DROP FOREIGN KEY \`FK_59e8a9cc2e2435ef62ae7402a1a\``)
    await queryRunner.query(`ALTER TABLE \`pr_item\` DROP FOREIGN KEY \`FK_f46b4eaadb685dc4462f80a51e2\``)
    await queryRunner.query(`ALTER TABLE \`pr\` DROP FOREIGN KEY \`FK_3bff1f3c945e4297489a9d04955\``)
    await queryRunner.query(`ALTER TABLE \`pr\` DROP FOREIGN KEY \`FK_71c661eef16ccaf0573f7b4c125\``)
    await queryRunner.query(`ALTER TABLE \`pr\` DROP FOREIGN KEY \`FK_c129905cf9385e8e9b37f0a27a7\``)
    await queryRunner.query(`ALTER TABLE \`pr\` DROP FOREIGN KEY \`FK_8656b90e0b3b5e5d486219d3751\``)
    await queryRunner.query(`ALTER TABLE \`pr_history\` DROP FOREIGN KEY \`FK_b3e89605faa3387382f647936e5\``)
    await queryRunner.query(`ALTER TABLE \`pr_approver\` DROP FOREIGN KEY \`FK_bac38338d39c6b8427345f58b68\``)
    await queryRunner.query(`ALTER TABLE \`pr_approver\` DROP FOREIGN KEY \`FK_674cf3c692471a6d2a0125da1b9\``)
    await queryRunner.query(`ALTER TABLE \`pr_approve\` DROP FOREIGN KEY \`FK_ef191141a93744337fa6a27832d\``)
    await queryRunner.query(`ALTER TABLE \`pr_approve\` DROP FOREIGN KEY \`FK_9ee9e99d558a52c7c4b126045dd\``)
    await queryRunner.query(`ALTER TABLE \`qc\` DROP FOREIGN KEY \`FK_e6ecfa5009a50089bf2444801a1\``)
    await queryRunner.query(`ALTER TABLE \`qc\` DROP FOREIGN KEY \`FK_646b3f9901048fe4f0646691cd4\``)
    await queryRunner.query(`ALTER TABLE \`qc\` DROP FOREIGN KEY \`FK_ee6d38bbf6c06f17a6e792c6f12\``)
    await queryRunner.query(`ALTER TABLE \`qc\` DROP FOREIGN KEY \`FK_3e20641b83dcfcedb05233d05c8\``)
    await queryRunner.query(`ALTER TABLE \`qc\` DROP FOREIGN KEY \`FK_3fb895842faafc6c52e1380902a\``)
    await queryRunner.query(`ALTER TABLE \`qc_history\` DROP FOREIGN KEY \`FK_fd4c285a2f623b390819fc6b839\``)
    await queryRunner.query(`ALTER TABLE \`qc_detail\` DROP FOREIGN KEY \`FK_b6952e0c7ba3926cde159df6dfd\``)
    await queryRunner.query(`ALTER TABLE \`qc_detail\` DROP FOREIGN KEY \`FK_53c15878a478f873e46f0f0e3cf\``)
    await queryRunner.query(`ALTER TABLE \`qc_detail_tech_value\` DROP FOREIGN KEY \`FK_a60d2e2dcd5f69db57ae3b03c36\``)
    await queryRunner.query(`ALTER TABLE \`qc_detail_tech_value\` DROP FOREIGN KEY \`FK_e1298fa8b0974aa33705c110499\``)
    await queryRunner.query(`ALTER TABLE \`qc_detail_tech_list_detail\` DROP FOREIGN KEY \`FK_b22362d6b67f171a0bc6cd7027c\``)
    await queryRunner.query(`ALTER TABLE \`qc_detail_error\` DROP FOREIGN KEY \`FK_2285706e44bcf1195a6eed3d484\``)
    await queryRunner.query(`ALTER TABLE \`qc_detail_error\` DROP FOREIGN KEY \`FK_17f33b71bda4eca3491164a1415\``)
    await queryRunner.query(`ALTER TABLE \`qc_approver\` DROP FOREIGN KEY \`FK_2069cb9fdad46db7ade8d45b235\``)
    await queryRunner.query(`ALTER TABLE \`qc_approver\` DROP FOREIGN KEY \`FK_72fdb87f13b6822a52331febba9\``)
    await queryRunner.query(`ALTER TABLE \`contract\` DROP FOREIGN KEY \`FK_5afd147c13c9f7350edcc1b4d00\``)
    await queryRunner.query(`ALTER TABLE \`contract\` DROP FOREIGN KEY \`FK_7e0acd2febb431736422963c5eb\``)
    await queryRunner.query(`ALTER TABLE \`contract\` DROP FOREIGN KEY \`FK_94641949661345c81577cdf4f11\``)
    await queryRunner.query(`ALTER TABLE \`contract\` DROP FOREIGN KEY \`FK_5f9aba86ffb85928d9bda6e9244\``)
    await queryRunner.query(`ALTER TABLE \`contract_member\` DROP FOREIGN KEY \`FK_23604fe008a588fdb81f16e8db7\``)
    await queryRunner.query(`ALTER TABLE \`contract_member\` DROP FOREIGN KEY \`FK_515f927b988d26efbbdb0812039\``)
    await queryRunner.query(`ALTER TABLE \`contract_history\` DROP FOREIGN KEY \`FK_b4081c0bb5e5ad9440295155b7a\``)
    await queryRunner.query(`ALTER TABLE \`contract_history\` DROP FOREIGN KEY \`FK_5e938fef02a12dc97fe6bd31dd4\``)
    await queryRunner.query(`ALTER TABLE \`contract_appendix\` DROP FOREIGN KEY \`FK_556f3e22ab0398943b401e51825\``)
    await queryRunner.query(`ALTER TABLE \`contract_appendix\` DROP FOREIGN KEY \`FK_eb779d50474b4b376465d77b3ea\``)
    await queryRunner.query(`ALTER TABLE \`bid\` DROP FOREIGN KEY \`FK_1854c79454cd8bbbf31cac76304\``)
    await queryRunner.query(`ALTER TABLE \`bid\` DROP FOREIGN KEY \`FK_e2aeadf35be1bbbbc7a86a828d5\``)
    await queryRunner.query(`ALTER TABLE \`bid\` DROP FOREIGN KEY \`FK_97c59f5857f8e386c213640b046\``)
    await queryRunner.query(`ALTER TABLE \`bid_custom_price\` DROP FOREIGN KEY \`FK_3640f4bfc140dc3cbc0f3135171\``)
    await queryRunner.query(`ALTER TABLE \`bid_history\` DROP FOREIGN KEY \`FK_cedae6a0aa32c469442a9465bf5\``)
    await queryRunner.query(`ALTER TABLE \`bid_history\` DROP FOREIGN KEY \`FK_1add2777fbf78612fd65d33a930\``)
    await queryRunner.query(`ALTER TABLE \`bid_employee_access\` DROP FOREIGN KEY \`FK_3eb200381cf528ef3bb0328ddaf\``)
    await queryRunner.query(`ALTER TABLE \`bid_employee_access\` DROP FOREIGN KEY \`FK_e1ab4ee56faa6b39f43ee3e8d72\``)
    await queryRunner.query(`ALTER TABLE \`service\` DROP FOREIGN KEY \`FK_9228bad899a36c28a90bc34608a\``)
    await queryRunner.query(`ALTER TABLE \`service\` DROP FOREIGN KEY \`FK_c5906ffd2cd6fa558710901e4d0\``)
    await queryRunner.query(`ALTER TABLE \`asn_item\` DROP FOREIGN KEY \`FK_a4e8cdf243ed885aede15a40ca2\``)
    await queryRunner.query(`ALTER TABLE \`asn_item\` DROP FOREIGN KEY \`FK_94f94225540bd149b93a7edbc3d\``)
    await queryRunner.query(`ALTER TABLE \`service_custom_price\` DROP FOREIGN KEY \`FK_2092855ad0938615c9692bf66d5\``)
    await queryRunner.query(`ALTER TABLE \`service_access\` DROP FOREIGN KEY \`FK_d70b3e485a2bb3b9df9d23520e1\``)
    await queryRunner.query(`ALTER TABLE \`service_access\` DROP FOREIGN KEY \`FK_f5b81b0704c5b994467aa8120b3\``)
    await queryRunner.query(`ALTER TABLE \`service_trade\` DROP FOREIGN KEY \`FK_82da0abb34d4309aab022fa6cb2\``)
    await queryRunner.query(`ALTER TABLE \`service_trade\` DROP FOREIGN KEY \`FK_e47ba7f64cf736318a65b1c9c5d\``)
    await queryRunner.query(`ALTER TABLE \`service_trade_list_detail\` DROP FOREIGN KEY \`FK_24bb7ed260a75bba7c4bbde494b\``)
    await queryRunner.query(`ALTER TABLE \`service_tech\` DROP FOREIGN KEY \`FK_662fc4b072225f31c77b1e80a85\``)
    await queryRunner.query(`ALTER TABLE \`service_tech\` DROP FOREIGN KEY \`FK_3d46899c062ba4e9b5063c3992a\``)
    await queryRunner.query(`ALTER TABLE \`service_tech_list_detail\` DROP FOREIGN KEY \`FK_146154f9f04aa7b69ae91e183c2\``)
    await queryRunner.query(`ALTER TABLE \`service_capacity\` DROP FOREIGN KEY \`FK_e7b898103d3542a0127af5c146a\``)
    await queryRunner.query(`ALTER TABLE \`service_capacity\` DROP FOREIGN KEY \`FK_dfaf935c8daef047d0dcf2bcde4\``)
    await queryRunner.query(`ALTER TABLE \`supplier_capacity\` DROP FOREIGN KEY \`FK_5224e0a2a8a3950a956ce919e92\``)
    await queryRunner.query(`ALTER TABLE \`supplier_capacity\` DROP FOREIGN KEY \`FK_658a89d26d22b5889d2c8808343\``)
    await queryRunner.query(`ALTER TABLE \`supplier_capacity\` DROP FOREIGN KEY \`FK_a233c4a4427a6f87cc7fafc94e0\``)
    await queryRunner.query(`ALTER TABLE \`supplier_capacity\` DROP FOREIGN KEY \`FK_f4c199649f05d2e0d4b6b1d1821\``)
    await queryRunner.query(`ALTER TABLE \`supplier_capacity\` DROP FOREIGN KEY \`FK_3c21723f51589880426ee09a38f\``)
    await queryRunner.query(`ALTER TABLE \`supplier_capacity_year_value\` DROP FOREIGN KEY \`FK_91d2bd0882d25205d6caa30510c\``)
    await queryRunner.query(`ALTER TABLE \`supplier_capacity_list_detail\` DROP FOREIGN KEY \`FK_6ddada23ada70eb762bd1a23629\``)
    await queryRunner.query(`ALTER TABLE \`supplier\` DROP FOREIGN KEY \`FK_e8902c50550ff82dd0143913c0a\``)
    await queryRunner.query(`ALTER TABLE \`po\` DROP FOREIGN KEY \`FK_e9d9b2e16b2b42c93a73f493872\``)
    await queryRunner.query(`ALTER TABLE \`po\` DROP FOREIGN KEY \`FK_e5941648e03ac9752a0a648c66d\``)
    await queryRunner.query(`ALTER TABLE \`po\` DROP FOREIGN KEY \`FK_9ae35e3316a98f3a9d740df26c0\``)
    await queryRunner.query(`ALTER TABLE \`po\` DROP FOREIGN KEY \`FK_e1e5f8634306efc40704a8a2628\``)
    await queryRunner.query(`ALTER TABLE \`po\` DROP FOREIGN KEY \`FK_e5fb555ea198a0aec1530f5e46b\``)
    await queryRunner.query(`ALTER TABLE \`po\` DROP FOREIGN KEY \`FK_6a9bd6d1b0c829ba79e9df5269b\``)
    await queryRunner.query(`ALTER TABLE \`po\` DROP FOREIGN KEY \`FK_717ae013a85f97e8e5685225037\``)
    await queryRunner.query(`ALTER TABLE \`po_product\` DROP FOREIGN KEY \`FK_4988bdc3aeb03f2942dd547e8b7\``)
    await queryRunner.query(`ALTER TABLE \`po_product\` DROP FOREIGN KEY \`FK_babde8a2029fbfc7e461716c732\``)
    await queryRunner.query(`ALTER TABLE \`po_member\` DROP FOREIGN KEY \`FK_345131b5513984074aef49c14ef\``)
    await queryRunner.query(`ALTER TABLE \`po_member\` DROP FOREIGN KEY \`FK_f41f38ea6931f3fc5becb0d632c\``)
    await queryRunner.query(`ALTER TABLE \`po_history\` DROP FOREIGN KEY \`FK_18b2851fecdd41d92f1c1126c85\``)
    await queryRunner.query(`ALTER TABLE \`po_history\` DROP FOREIGN KEY \`FK_64a00b17e837cf662894fef5e35\``)
    await queryRunner.query(`ALTER TABLE \`po_history\` DROP FOREIGN KEY \`FK_00555eaae617d36cb019cc436ea\``)
    await queryRunner.query(`ALTER TABLE \`invoice_suggest\` DROP FOREIGN KEY \`FK_df66edc0ae0b3af4e8e219df4aa\``)
    await queryRunner.query(`ALTER TABLE \`invoice_suggest\` DROP FOREIGN KEY \`FK_cbebd9a92b5e775a972f999022f\``)
    await queryRunner.query(`ALTER TABLE \`invoice_suggest\` DROP FOREIGN KEY \`FK_11f90e5481556437334ab94980a\``)
    await queryRunner.query(`ALTER TABLE \`payment_progress\` DROP FOREIGN KEY \`FK_327c5af92cf9846e831ed369853\``)
    await queryRunner.query(`ALTER TABLE \`payment_progress\` DROP FOREIGN KEY \`FK_ca21e5bc8d43455f8d543924951\``)
    await queryRunner.query(`ALTER TABLE \`invoice_suggest_file\` DROP FOREIGN KEY \`FK_d10942b8523a906cecb5782bd69\``)
    await queryRunner.query(`ALTER TABLE \`invoice_suggest_history\` DROP FOREIGN KEY \`FK_e7321a1fad6a951d50320c34ced\``)
    await queryRunner.query(`ALTER TABLE \`invoice_suggest_history\` DROP FOREIGN KEY \`FK_77fd3adce89fc31801cf99f897b\``)
    await queryRunner.query(`ALTER TABLE \`invoice\` DROP FOREIGN KEY \`FK_b04287ad09b2e05e7cad221f23b\``)
    await queryRunner.query(`ALTER TABLE \`invoice_file\` DROP FOREIGN KEY \`FK_1eefed5c372953d7d8df4fb8346\``)
    await queryRunner.query(`ALTER TABLE \`supplier_notify\` DROP FOREIGN KEY \`FK_1c604e9c16b9d840aa0b73065db\``)
    await queryRunner.query(`ALTER TABLE \`bid_supplier\` DROP FOREIGN KEY \`FK_94afaf0e41c3cc6a62b05333f69\``)
    await queryRunner.query(`ALTER TABLE \`bid_supplier\` DROP FOREIGN KEY \`FK_f0256ba2eb660b86de5838ccc76\``)
    await queryRunner.query(`ALTER TABLE \`bid_supplier_custom_price_value\` DROP FOREIGN KEY \`FK_4ac6991b9fc4be2e43b62066736\``)
    await queryRunner.query(`ALTER TABLE \`bid_supplier_trade_value\` DROP FOREIGN KEY \`FK_cbeb3468d0b6a2e95cee6278ea6\``)
    await queryRunner.query(`ALTER TABLE \`bid_supplier_trade_value\` DROP FOREIGN KEY \`FK_9aac8e343250eb68472ac29586d\``)
    await queryRunner.query(`ALTER TABLE \`bid_trade\` DROP FOREIGN KEY \`FK_be91681664c1ac732c62a9bf9af\``)
    await queryRunner.query(`ALTER TABLE \`bid_trade\` DROP FOREIGN KEY \`FK_3b62791f9ea7005f5e904d03b1e\``)
    await queryRunner.query(`ALTER TABLE \`bid_trade_list_detail\` DROP FOREIGN KEY \`FK_1f7c17f8cf412189406f422713a\``)
    await queryRunner.query(`ALTER TABLE \`bid_supplier_price_value\` DROP FOREIGN KEY \`FK_b3c0325be1f8f2b8e9ac36f940c\``)
    await queryRunner.query(`ALTER TABLE \`bid_supplier_price_value\` DROP FOREIGN KEY \`FK_16d1ee6e94f781d84aabd66c194\``)
    await queryRunner.query(`ALTER TABLE \`bid_price\` DROP FOREIGN KEY \`FK_4b18d8d606a19e22f05eda2aa9d\``)
    await queryRunner.query(`ALTER TABLE \`bid_price\` DROP FOREIGN KEY \`FK_867db5c177deae3fa9b9708dc2d\``)
    await queryRunner.query(`ALTER TABLE \`bid_price\` DROP FOREIGN KEY \`FK_a440790cd408efa2af7dfc1a579\``)
    await queryRunner.query(`ALTER TABLE \`bid_supplier_price\` DROP FOREIGN KEY \`FK_c2ca28dc7586fbdd2ca2704aa35\``)
    await queryRunner.query(`ALTER TABLE \`bid_supplier_price\` DROP FOREIGN KEY \`FK_1a034db1ac5166a91766a3320ae\``)
    await queryRunner.query(`ALTER TABLE \`bid_supplier_price_col_value\` DROP FOREIGN KEY \`FK_a54de3eebd756601dade529646b\``)
    await queryRunner.query(`ALTER TABLE \`bid_supplier_price_col_value\` DROP FOREIGN KEY \`FK_656f153e808288f954929c26fd0\``)
    await queryRunner.query(`ALTER TABLE \`bid_supplier_price_col_value\` DROP FOREIGN KEY \`FK_037371a1b92304d1c0f2407e59e\``)
    await queryRunner.query(`ALTER TABLE \`bid_price_col\` DROP FOREIGN KEY \`FK_6dc4cb70fa85c3fda253011ede2\``)
    await queryRunner.query(`ALTER TABLE \`bid_price_col_value\` DROP FOREIGN KEY \`FK_cec4a85e38a248f39dad6030c45\``)
    await queryRunner.query(`ALTER TABLE \`bid_price_col_value\` DROP FOREIGN KEY \`FK_5736a119be595711e5d6abe52c5\``)
    await queryRunner.query(`ALTER TABLE \`bid_auction_supplier_price_value\` DROP FOREIGN KEY \`FK_da27cb5423990fdcd1aa1a18a9d\``)
    await queryRunner.query(`ALTER TABLE \`bid_auction_supplier_price_value\` DROP FOREIGN KEY \`FK_2fe1c9d4d0e650eab1eb90e2191\``)
    await queryRunner.query(`ALTER TABLE \`bid_auction_supplier\` DROP FOREIGN KEY \`FK_4f8b58f91c29f00e1a7d285de54\``)
    await queryRunner.query(`ALTER TABLE \`bid_auction_supplier\` DROP FOREIGN KEY \`FK_0ffe9b806010eebbba16db32b77\``)
    await queryRunner.query(`ALTER TABLE \`bid_auction\` DROP FOREIGN KEY \`FK_98fb3b554a2cbb46e250fdf9321\``)
    await queryRunner.query(`ALTER TABLE \`bid_auction_price\` DROP FOREIGN KEY \`FK_02a5453262e93dbdb2c88945407\``)
    await queryRunner.query(`ALTER TABLE \`bid_auction_price\` DROP FOREIGN KEY \`FK_d4c542ae290d14e5afae2d9e33e\``)
    await queryRunner.query(`ALTER TABLE \`service_price\` DROP FOREIGN KEY \`FK_4c807146960feb2faad0031f145\``)
    await queryRunner.query(`ALTER TABLE \`service_price\` DROP FOREIGN KEY \`FK_d4520a0074d2b259db1b40149cd\``)
    await queryRunner.query(`ALTER TABLE \`service_price_col_value\` DROP FOREIGN KEY \`FK_1b5ccab6379d4d58b3fa85e7b41\``)
    await queryRunner.query(`ALTER TABLE \`service_price_col_value\` DROP FOREIGN KEY \`FK_5f7d7b40cfb3d7ae2c8ee6555c5\``)
    await queryRunner.query(`ALTER TABLE \`service_price_col\` DROP FOREIGN KEY \`FK_b598b93058d3376e11f0a5d7ef0\``)
    await queryRunner.query(`ALTER TABLE \`service_price_list_detail\` DROP FOREIGN KEY \`FK_c3c43ede69d2f56a6c805b7ce23\``)
    await queryRunner.query(`ALTER TABLE \`bid_deal_price\` DROP FOREIGN KEY \`FK_fa500f428373aa6dd5f183cc6fe\``)
    await queryRunner.query(`ALTER TABLE \`bid_deal_price\` DROP FOREIGN KEY \`FK_58e338315728b94e4d7dc64512b\``)
    await queryRunner.query(`ALTER TABLE \`bid_deal\` DROP FOREIGN KEY \`FK_94930fed93b5d08bd97b336a20a\``)
    await queryRunner.query(`ALTER TABLE \`bid_deal_supplier\` DROP FOREIGN KEY \`FK_14acd799af1581375e56c8e9ed6\``)
    await queryRunner.query(`ALTER TABLE \`bid_deal_supplier\` DROP FOREIGN KEY \`FK_291ace50643326687ee885f12e3\``)
    await queryRunner.query(`ALTER TABLE \`bid_deal_supplier_price_value\` DROP FOREIGN KEY \`FK_c95d5eae41bffd589aada849724\``)
    await queryRunner.query(`ALTER TABLE \`bid_deal_supplier_price_value\` DROP FOREIGN KEY \`FK_34b418fb00850329693a941c154\``)
    await queryRunner.query(`ALTER TABLE \`bid_price_list_detail\` DROP FOREIGN KEY \`FK_70b3d2a07ee305270b90ebf58dc\``)
    await queryRunner.query(`ALTER TABLE \`bid_supplier_tech_value\` DROP FOREIGN KEY \`FK_47145f6233cbe2efec0ddfd400f\``)
    await queryRunner.query(`ALTER TABLE \`bid_supplier_tech_value\` DROP FOREIGN KEY \`FK_2a427d946d1e7c5e65df58021eb\``)
    await queryRunner.query(`ALTER TABLE \`bid_tech\` DROP FOREIGN KEY \`FK_c7b436b32d81e53e7d9834ea324\``)
    await queryRunner.query(`ALTER TABLE \`bid_tech\` DROP FOREIGN KEY \`FK_2fe4a2fbbde3f9c07132b807ad6\``)
    await queryRunner.query(`ALTER TABLE \`bid_tech_list_detail\` DROP FOREIGN KEY \`FK_4dd593368143571aceb5c285278\``)
    await queryRunner.query(`ALTER TABLE \`user\` DROP FOREIGN KEY \`FK_ab4a80281f1e8d524714e00f38f\``)
    await queryRunner.query(`ALTER TABLE \`user\` DROP FOREIGN KEY \`FK_031cdc2c9c5eb56d48b5bdb4e54\``)
    await queryRunner.query(`ALTER TABLE \`user_confirm_code\` DROP FOREIGN KEY \`FK_1d3f2a4a0693ef7a44e57284ea9\``)
    await queryRunner.query(`ALTER TABLE \`supplier_expertise\` DROP FOREIGN KEY \`FK_7a85deff050ee041cbdac9191a0\``)
    await queryRunner.query(`ALTER TABLE \`supplier_expertise\` DROP FOREIGN KEY \`FK_d096408a337c2f47410e8c9d1bb\``)
    await queryRunner.query(`ALTER TABLE \`supplier_expertise\` DROP FOREIGN KEY \`FK_10cffdbce6d3a175451316e8028\``)
    await queryRunner.query(`ALTER TABLE \`supplier_expertise\` DROP FOREIGN KEY \`FK_337bed433a9811ccf8dcd6b704f\``)
    await queryRunner.query(`ALTER TABLE \`supplier_expertise\` DROP FOREIGN KEY \`FK_12f94c1dacf89108d7f46c63614\``)
    await queryRunner.query(`ALTER TABLE \`supplier_expertise_member\` DROP FOREIGN KEY \`FK_14c6ca8022f36caccb2c0d08b57\``)
    await queryRunner.query(`ALTER TABLE \`supplier_expertise_member\` DROP FOREIGN KEY \`FK_25ab41887eb8ff9c33d22870a26\``)
    await queryRunner.query(`ALTER TABLE \`supplier_expertise_law_detail\` DROP FOREIGN KEY \`FK_61a4e2682fbbaf81ccad6ef2c1e\``)
    await queryRunner.query(`ALTER TABLE \`supplier_service\` DROP FOREIGN KEY \`FK_f5c9f4bef5827bfb11d397889c5\``)
    await queryRunner.query(`ALTER TABLE \`supplier_service\` DROP FOREIGN KEY \`FK_6b05b7e468e9a3017dc6d3f166d\``)
    await queryRunner.query(`ALTER TABLE \`supplier_expertise_detail\` DROP FOREIGN KEY \`FK_4b70099f1d6be7f37b245384b0a\``)
    await queryRunner.query(`ALTER TABLE \`supplier_expertise_detail\` DROP FOREIGN KEY \`FK_897a53586bd0c3ae35db7822c9d\``)
    await queryRunner.query(`ALTER TABLE \`supplier_expertise_year_detail\` DROP FOREIGN KEY \`FK_b13f447aa60bdfc21ea9b307c3d\``)
    await queryRunner.query(`ALTER TABLE \`service_capacity_list_detail\` DROP FOREIGN KEY \`FK_ceba55fc1b9eb39b36c43a9644c\``)
    await queryRunner.query(`ALTER TABLE \`item_tech\` DROP FOREIGN KEY \`FK_5964da7a8a0a76663eb696a6789\``)
    await queryRunner.query(`ALTER TABLE \`item_tech\` DROP FOREIGN KEY \`FK_48a9e3f00c9fd54618dcc23c5a6\``)
    await queryRunner.query(`ALTER TABLE \`item_tech_list_detail\` DROP FOREIGN KEY \`FK_6d5e197e96ef20c03594d262ec2\``)
    await queryRunner.query(`DROP TABLE \`setting_string_client\``)
    await queryRunner.query(`DROP TABLE \`link_client\``)
    await queryRunner.query(`DROP TABLE \`language_config\``)
    await queryRunner.query(`DROP TABLE \`language\``)
    await queryRunner.query(`DROP INDEX \`IDX_89b26e4ed3db8895b86c8df55e\` ON \`holiday\``)
    await queryRunner.query(`DROP TABLE \`holiday\``)
    await queryRunner.query(`DROP TABLE \`faq_category\``)
    await queryRunner.query(`DROP TABLE \`faq\``)
    await queryRunner.query(`DROP INDEX \`IDX_75bd34e96cde646bb118a6c267\` ON \`email_template\``)
    await queryRunner.query(`DROP TABLE \`email_template\``)
    await queryRunner.query(`DROP TABLE \`data_history\``)
    await queryRunner.query(`DROP TABLE \`email_history\``)
    await queryRunner.query(`DROP TABLE \`city\``)
    await queryRunner.query(`DROP TABLE \`district\``)
    await queryRunner.query(`DROP TABLE \`ward\``)
    await queryRunner.query(`DROP TABLE \`banner_client\``)
    await queryRunner.query(`DROP INDEX \`IDX_fcbdf6b1d748b3fe0f43acaf7d\` ON \`asn\``)
    await queryRunner.query(`DROP TABLE \`asn\``)
    await queryRunner.query(`DROP TABLE \`branch\``)
    await queryRunner.query(`DROP TABLE \`branch_member\``)
    await queryRunner.query(`DROP INDEX \`REL_f4b0d329c4a3cf79ffe9d56504\` ON \`employee\``)
    await queryRunner.query(`DROP INDEX \`IDX_348a4a9894eef0760bfe0a2632\` ON \`employee\``)
    await queryRunner.query(`DROP TABLE \`employee\``)
    await queryRunner.query(`DROP TABLE \`employee_warning\``)
    await queryRunner.query(`DROP TABLE \`employee_notify\``)
    await queryRunner.query(`DROP INDEX \`IDX_62690f4fe31da9eb824d909285\` ON \`department\``)
    await queryRunner.query(`DROP TABLE \`department\``)
    await queryRunner.query(`DROP TABLE \`purchase_plan\``)
    await queryRunner.query(`DROP TABLE \`purchase_plan_progress\``)
    await queryRunner.query(`DROP TABLE \`purchase_plan_history\``)
    await queryRunner.query(`DROP TABLE \`pr_item\``)
    await queryRunner.query(`DROP TABLE \`pr\``)
    await queryRunner.query(`DROP TABLE \`pr_history\``)
    await queryRunner.query(`DROP TABLE \`pr_approver\``)
    await queryRunner.query(`DROP TABLE \`pr_approve\``)
    await queryRunner.query(`DROP TABLE \`object\``)
    await queryRunner.query(`DROP TABLE \`qc\``)
    await queryRunner.query(`DROP INDEX \`IDX_dcbf22551ec3827f234e532a08\` ON \`warehouse\``)
    await queryRunner.query(`DROP TABLE \`warehouse\``)
    await queryRunner.query(`DROP TABLE \`qc_history\``)
    await queryRunner.query(`DROP TABLE \`qc_detail\``)
    await queryRunner.query(`DROP TABLE \`qc_detail_tech_value\``)
    await queryRunner.query(`DROP TABLE \`qc_detail_tech_list_detail\``)
    await queryRunner.query(`DROP TABLE \`qc_detail_error\``)
    await queryRunner.query(`DROP INDEX \`IDX_d12ca650871335dade46b2052d\` ON \`error\``)
    await queryRunner.query(`DROP TABLE \`error\``)
    await queryRunner.query(`DROP TABLE \`qc_approver\``)
    await queryRunner.query(`DROP INDEX \`IDX_a167b5ec6a7dd9cd577bd622d8\` ON \`contract\``)
    await queryRunner.query(`DROP TABLE \`contract\``)
    await queryRunner.query(`DROP TABLE \`contract_member\``)
    await queryRunner.query(`DROP TABLE \`contract_history\``)
    await queryRunner.query(`DROP INDEX \`IDX_eaef0b45a49ddfb89435f91e74\` ON \`contract_appendix\``)
    await queryRunner.query(`DROP TABLE \`contract_appendix\``)
    await queryRunner.query(`DROP INDEX \`IDX_2c96dc3c8343f236e00485aef1\` ON \`bid\``)
    await queryRunner.query(`DROP TABLE \`bid\``)
    await queryRunner.query(`DROP TABLE \`bid_custom_price\``)
    await queryRunner.query(`DROP TABLE \`bid_history\``)
    await queryRunner.query(`DROP TABLE \`setting_string\``)
    await queryRunner.query(`DROP INDEX \`IDX_5961204089e6486be6b43a9e6c\` ON \`bid_type\``)
    await queryRunner.query(`DROP TABLE \`bid_type\``)
    await queryRunner.query(`DROP TABLE \`bid_employee_access\``)
    await queryRunner.query(`DROP INDEX \`IDX_4cb3cf237c83885cc504634829\` ON \`service\``)
    await queryRunner.query(`DROP TABLE \`service\``)
    await queryRunner.query(`DROP TABLE \`asn_item\``)
    await queryRunner.query(`DROP TABLE \`service_custom_price\``)
    await queryRunner.query(`DROP TABLE \`service_access\``)
    await queryRunner.query(`DROP TABLE \`service_trade\``)
    await queryRunner.query(`DROP TABLE \`service_trade_list_detail\``)
    await queryRunner.query(`DROP TABLE \`service_tech\``)
    await queryRunner.query(`DROP TABLE \`service_tech_list_detail\``)
    await queryRunner.query(`DROP TABLE \`service_capacity\``)
    await queryRunner.query(`DROP TABLE \`supplier_capacity\``)
    await queryRunner.query(`DROP TABLE \`supplier_capacity_year_value\``)
    await queryRunner.query(`DROP TABLE \`supplier_capacity_list_detail\``)
    await queryRunner.query(`DROP INDEX \`REL_e8902c50550ff82dd0143913c0\` ON \`supplier\``)
    await queryRunner.query(`DROP INDEX \`IDX_e1183babf2fed1bb440905b1e5\` ON \`supplier\``)
    await queryRunner.query(`DROP TABLE \`supplier\``)
    await queryRunner.query(`DROP TABLE \`po\``)
    await queryRunner.query(`DROP TABLE \`po_product\``)
    await queryRunner.query(`DROP TABLE \`po_member\``)
    await queryRunner.query(`DROP TABLE \`po_history\``)
    await queryRunner.query(`DROP TABLE \`invoice_suggest\``)
    await queryRunner.query(`DROP TABLE \`payment_progress\``)
    await queryRunner.query(`DROP TABLE \`invoice_suggest_file\``)
    await queryRunner.query(`DROP TABLE \`invoice_suggest_history\``)
    await queryRunner.query(`DROP TABLE \`invoice\``)
    await queryRunner.query(`DROP TABLE \`invoice_file\``)
    await queryRunner.query(`DROP TABLE \`supplier_notify\``)
    await queryRunner.query(`DROP TABLE \`bid_supplier\``)
    await queryRunner.query(`DROP TABLE \`bid_supplier_custom_price_value\``)
    await queryRunner.query(`DROP TABLE \`bid_supplier_trade_value\``)
    await queryRunner.query(`DROP TABLE \`bid_trade\``)
    await queryRunner.query(`DROP TABLE \`bid_trade_list_detail\``)
    await queryRunner.query(`DROP TABLE \`bid_supplier_price_value\``)
    await queryRunner.query(`DROP TABLE \`bid_price\``)
    await queryRunner.query(`DROP TABLE \`bid_supplier_price\``)
    await queryRunner.query(`DROP TABLE \`bid_supplier_price_col_value\``)
    await queryRunner.query(`DROP TABLE \`bid_price_col\``)
    await queryRunner.query(`DROP TABLE \`bid_price_col_value\``)
    await queryRunner.query(`DROP TABLE \`bid_auction_supplier_price_value\``)
    await queryRunner.query(`DROP TABLE \`bid_auction_supplier\``)
    await queryRunner.query(`DROP TABLE \`bid_auction\``)
    await queryRunner.query(`DROP TABLE \`bid_auction_price\``)
    await queryRunner.query(`DROP TABLE \`service_price\``)
    await queryRunner.query(`DROP TABLE \`service_price_col_value\``)
    await queryRunner.query(`DROP TABLE \`service_price_col\``)
    await queryRunner.query(`DROP TABLE \`service_price_list_detail\``)
    await queryRunner.query(`DROP TABLE \`bid_deal_price\``)
    await queryRunner.query(`DROP TABLE \`bid_deal\``)
    await queryRunner.query(`DROP TABLE \`bid_deal_supplier\``)
    await queryRunner.query(`DROP TABLE \`bid_deal_supplier_price_value\``)
    await queryRunner.query(`DROP TABLE \`bid_price_list_detail\``)
    await queryRunner.query(`DROP TABLE \`bid_supplier_tech_value\``)
    await queryRunner.query(`DROP TABLE \`bid_tech\``)
    await queryRunner.query(`DROP TABLE \`bid_tech_list_detail\``)
    await queryRunner.query(`DROP INDEX \`REL_ab4a80281f1e8d524714e00f38\` ON \`user\``)
    await queryRunner.query(`DROP INDEX \`REL_031cdc2c9c5eb56d48b5bdb4e5\` ON \`user\``)
    await queryRunner.query(`DROP INDEX \`IDX_78a916df40e02a9deb1c4b75ed\` ON \`user\``)
    await queryRunner.query(`DROP TABLE \`user\``)
    await queryRunner.query(`DROP TABLE \`user_confirm_code\``)
    await queryRunner.query(`DROP TABLE \`supplier_expertise\``)
    await queryRunner.query(`DROP TABLE \`supplier_expertise_member\``)
    await queryRunner.query(`DROP TABLE \`supplier_expertise_law_detail\``)
    await queryRunner.query(`DROP TABLE \`supplier_service\``)
    await queryRunner.query(`DROP TABLE \`supplier_expertise_detail\``)
    await queryRunner.query(`DROP TABLE \`supplier_expertise_year_detail\``)
    await queryRunner.query(`DROP TABLE \`service_capacity_list_detail\``)
    await queryRunner.query(`DROP TABLE \`item_tech\``)
    await queryRunner.query(`DROP TABLE \`item_tech_list_detail\``)
  }
}
