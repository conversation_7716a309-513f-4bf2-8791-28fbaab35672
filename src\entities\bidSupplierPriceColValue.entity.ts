import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, Join<PERSON><PERSON>umn, OneToMany } from 'typeorm'
import { BidSupplierEntity } from './bidSupplier.entity'
import { BidPriceEntity } from './bidPrice.entity'
import { BidPriceColEntity } from './bidPriceCol.entity'

@Entity('bid_supplier_price_col_value')
export class BidSupplierPriceColValueEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  value: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidSupplierId: string
  @ManyToOne(() => BidSupplierEntity, (p) => p.bidSupplierPriceColValue)
  @JoinColumn({ name: 'bidSupplierId', referencedColumnName: 'id' })
  bidSupplier: Promise<BidSupplierEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  bidPriceId: string
  @ManyToOne(() => BidPriceEntity, (p) => p.bidSupplierPriceColValue)
  @JoinColumn({ name: 'bidPriceId', referencedColumnName: 'id' })
  bidPrice: Promise<BidPriceEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  bidPriceColId: string
  @ManyToOne(() => BidPriceColEntity, (p) => p.bidSupplierPriceColValue)
  @JoinColumn({ name: 'bidPriceColId', referencedColumnName: 'id' })
  bidPriceCol: Promise<BidPriceColEntity>
}
