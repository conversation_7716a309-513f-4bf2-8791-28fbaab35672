import { Injectable, NotFoundException, BadRequestException, MethodNotAllowedException, NotAcceptableException } from '@nestjs/common'
import {
  ERROR_YOU_DO_NOT_HAVE_PERMISSION,
  ERROR_NOT_FOUND_DATA,
  ERROR_SUPPLIER_USED_TEMPLATE,
  ERROR_INVALID_FOMULAR,
  UPDATE_SUCCESS,
  CREATE_SUCCESS,
  DELETE_SUCCESS,
  IMPORT_SUCCESS,
} from '../../constants'
import { ServicePriceRepository, ServiceCustomPriceRepository } from '../../repositories'
import { coreHelper } from '../../helpers'
import { enumData } from '../../constants/enumData'
import { UserDto } from '../../dto'
import { In, IsNull } from 'typeorm'
import {
  BidCustomPriceCreateDto,
  BidCustomPriceUpdateDto,
  BidPriceColCreateDto,
  BidPriceColUpdateDto,
  Bid<PERSON>rice<PERSON>reateDto,
  BidPriceUpdateDto,
} from './dto2'
import {
  OfferCustomPriceRepository,
  OfferPriceColRepository,
  OfferPriceRepository,
  OfferPrItemRepository,
  OfferRepository,
  OfferSupplierRepository,
} from '../../repositories/offer.repository'
import { OfferPriceColEntity } from '../../entities/offerPriceCol.entity'
import { OfferEntity, OfferPriceEntity, OfferServiceEntity, OfferSupplierEntity, ServicePriceColEntity } from '../../entities'
import { OfferPriceColValueEntity } from '../../entities/offerPriceColValue.entity'
import { OfferPriceListDetailEntity } from '../../entities/offerPriceListDetail.entity'
import { OfferCustomPriceEntity } from '../../entities/offerCustomPrice.entity'

@Injectable()
export class OfferPriceService {
  constructor(
    private readonly repo: OfferRepository,
    private readonly servicePriceRepo: ServicePriceRepository,
    private readonly serviceCustomPriceRepo: ServiceCustomPriceRepository,
    private readonly bidPriceRepo: OfferPriceRepository,
    private readonly bidPriceColRepo: OfferPriceColRepository,
    private readonly bidCustomPriceRepo: OfferCustomPriceRepository,
    private readonly bidPrItemRepository: OfferPrItemRepository,

    private readonly bidSupplierRepo: OfferSupplierRepository,
  ) {}

  //#region bidPrice

  /** Lấy các hạng mục giá */
  public async price_find(user: UserDto, data: { bidId: string }) {
    return await this.bidPriceRepo.find({
      where: { offerServiceId: data.bidId, level: 1, isDeleted: false },
      relations: { childs: true },
    })
  }

  /** Check quyền tạo chào giá cho gói thầu */
  async checkPermissionPriceCreate(user: UserDto, bidId: string) {
    let result = true
    let message = 'Gói thầu không còn tồn tại'

    return { hasPermission: result, message }
  }

  /** Update statusPrice => DangTao */
  async creatingPrice(user: UserDto, bidId: string) {
    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (!bid) return

    await this.repo.update(bidId, {
      statusPrice: enumData.BidPriceStatus.DangTao.code,
      status: enumData.BidStatus.DangCauHinhGoiThau.code,
      updatedBy: user.id,
    })
  }

  /** Lấy bảng chào giá của lĩnh vực mời thầu */
  async loadPrice(user: UserDto, bidItemId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.bidPrItemRepository.findOne({ where: { id: bidItemId } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidItemId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    const lstServicePrice = await this.servicePriceRepo.find({
      where: { serviceId: bid.serviceId, parentId: IsNull(), isDeleted: false },
      order: { sort: 'ASC', createdAt: 'ASC' },
    })

    const lstServicePriceCol = await this.repo.manager.getRepository(ServicePriceColEntity).find({
      where: { serviceId: bid.serviceId, isDeleted: false },
      order: { sort: 'ASC', createdAt: 'ASC' },
    })

    // Tạo danh sách yêu cầu giá cho gói thầu
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(OfferPriceEntity)
      const bidPriceListDetailRepo = manager.getRepository(OfferPriceListDetailEntity)
      const bidPriceColRepo = manager.getRepository(OfferPriceColEntity)
      const bidPriceColValueRepo = manager.getRepository(OfferPriceColValueEntity)

      const lstCol = []
      for (const col of lstServicePriceCol) {
        const item = new OfferPriceColEntity()
        item.companyId = user.companyId
        item.createdBy = user.id

        item.offerServiceId = bid.id
        item.code = col.code
        item.name = col.name
        item.fomular = col.fomular
        item.type = col.type
        item.colType = col.colType
        item.isRequired = col.isRequired
        item.sort = col.sort
        const bidPriceColEntity = await bidPriceColRepo.save(item)
        const temp = { ...col, bidPriceColId: bidPriceColEntity.id }
        lstCol.push(temp)
      }

      for (const servicePrice1 of lstServicePrice) {
        //#region Tạo lv1
        const itemLv1 = new OfferPriceEntity()
        itemLv1.companyId = user.companyId
        itemLv1.createdBy = user.id
        itemLv1.offerServiceId = bid.id
        itemLv1.offerId = bid.offerId
        itemLv1.sort = servicePrice1.sort
        itemLv1.name = servicePrice1.name
        itemLv1.isRequired = servicePrice1.isRequired
        itemLv1.type = servicePrice1.type
        itemLv1.percent = servicePrice1.percent
        itemLv1.level = servicePrice1.level
        itemLv1.description = servicePrice1.description
        itemLv1.parentId = servicePrice1.parentId
        itemLv1.scoreDLC = servicePrice1.scoreDLC
        itemLv1.requiredMin = servicePrice1.requiredMin
        itemLv1.unit = servicePrice1.unit
        itemLv1.currency = servicePrice1.currency
        itemLv1.isSetup = servicePrice1.isSetup
        itemLv1.isTemplate = servicePrice1.isTemplate
        itemLv1.number = servicePrice1.number
        // itemLv1.servicePriceId = servicePrice1.id
        const bidPriceLv1 = await bidPriceRepo.save(itemLv1)
        //#endregion

        // Tạo thông tin bổ sung lv1
        const lstDetailLv1 = (await servicePrice1.servicePriceListDetails).filter((c) => !c.isDeleted)
        for (const detailLv1 of lstDetailLv1) {
          const itemDetail1 = new OfferPriceListDetailEntity()
          itemDetail1.companyId = user.companyId
          itemDetail1.createdBy = user.id
          itemDetail1.offerPriceId = bidPriceLv1.id
          itemDetail1.name = detailLv1.name
          itemDetail1.type = detailLv1.type
          itemDetail1.value = detailLv1.value
          await bidPriceListDetailRepo.save(itemDetail1)
        }

        // Tạo giá trị cho các cột động lv1
        const lstValueLv1 = await servicePrice1.servicePriceColValues
        for (const itemValueLv1 of lstValueLv1) {
          const objCol = lstCol.find((c) => c.id === itemValueLv1.servicePriceColId)
          if (objCol) {
            const itemValueNew1 = new OfferPriceColValueEntity()
            itemValueNew1.companyId = user.companyId
            itemValueNew1.createdBy = user.id
            itemValueNew1.offerPriceId = bidPriceLv1.id
            itemValueNew1.offerPriceColId = objCol.bidPriceColId
            itemValueNew1.value = itemValueLv1.value
            await bidPriceColValueRepo.save(itemValueNew1)
          }
        }

        // Tạo thông tin lv2
        const lstServicePrice2 = (await servicePrice1.childs).filter((c) => !c.isDeleted)
        if (lstServicePrice2.length > 0) {
          for (const servicePrice2 of lstServicePrice2) {
            //#region Tạo lv2
            const itemLv2 = new OfferPriceEntity()
            itemLv2.companyId = user.companyId
            itemLv2.createdBy = user.id

            itemLv2.offerServiceId = bid.id
            itemLv2.sort = servicePrice2.sort
            itemLv2.name = servicePrice2.name
            itemLv2.isRequired = servicePrice2.isRequired
            itemLv2.type = servicePrice2.type
            itemLv2.percent = servicePrice2.percent
            itemLv2.level = servicePrice2.level
            itemLv2.description = servicePrice2.description
            itemLv2.parentId = bidPriceLv1.id
            itemLv2.scoreDLC = servicePrice2.scoreDLC
            itemLv2.requiredMin = servicePrice2.requiredMin
            itemLv2.unit = servicePrice2.unit
            itemLv2.currency = servicePrice2.currency
            itemLv2.number = servicePrice2.number
            itemLv2.isSetup = servicePrice2.isSetup
            // itemLv2.servicePriceId = servicePrice2.id
            const bidPriceLv2 = await bidPriceRepo.save(itemLv2)
            //#endregion

            // Tạo thông tin bổ sung lv2
            const lstDetailLv2 = (await servicePrice2.servicePriceListDetails).filter((c) => !c.isDeleted)
            for (const detailLv2 of lstDetailLv2) {
              const itemDetail2 = new OfferPriceListDetailEntity()
              itemDetail2.companyId = user.companyId
              itemDetail2.createdBy = user.id
              itemDetail2.offerPriceId = bidPriceLv2.id
              itemDetail2.name = detailLv2.name
              itemDetail2.type = detailLv2.type
              itemDetail2.value = detailLv2.value
              await bidPriceListDetailRepo.save(itemDetail2)
            }

            // Tạo giá trị cho các cột động lv2
            const lstValueLv2 = await servicePrice2.servicePriceColValues
            for (const itemValueLv2 of lstValueLv2) {
              const objCol = lstCol.find((c) => c.id === itemValueLv2.servicePriceColId)
              if (objCol) {
                const itemValueNew2 = new OfferPriceColValueEntity()
                itemValueNew2.companyId = user.companyId
                itemValueNew2.createdBy = user.id
                itemValueNew2.offerPriceId = bidPriceLv2.id
                itemValueNew2.offerPriceColId = objCol.bidPriceColId
                itemValueNew2.value = itemValueLv2.value
                await bidPriceColValueRepo.save(itemValueNew2)
              }
            }

            // Tạo thông tin lv3
            const lstServicePrice3 = (await servicePrice2.childs).filter((c) => !c.isDeleted)
            if (lstServicePrice3.length > 0) {
              for (const servicePrice3 of lstServicePrice3) {
                //#region Tạo lv3
                const itemLv3 = new OfferPriceEntity()
                itemLv3.companyId = user.companyId
                itemLv3.createdBy = user.id

                itemLv3.offerServiceId = bid.id
                itemLv3.sort = servicePrice3.sort
                itemLv3.name = servicePrice3.name
                itemLv3.isRequired = servicePrice3.isRequired
                itemLv3.type = servicePrice3.type
                itemLv3.percent = servicePrice3.percent
                itemLv3.level = servicePrice3.level
                itemLv3.description = servicePrice3.description
                itemLv3.parentId = bidPriceLv2.id
                itemLv3.scoreDLC = servicePrice3.scoreDLC
                itemLv3.requiredMin = servicePrice3.requiredMin
                itemLv3.unit = servicePrice3.unit
                itemLv3.currency = servicePrice3.currency
                itemLv3.number = servicePrice3.number
                itemLv3.isSetup = servicePrice3.isSetup
                // itemLv3.servicePriceId = servicePrice3.id
                const bidPriceLv3 = await bidPriceRepo.save(itemLv3)
                //#endregion

                // Tạo thông tin bổ sung lv3
                const lstDetailLv3 = (await servicePrice3.servicePriceListDetails).filter((c) => !c.isDeleted)
                for (const detailLv3 of lstDetailLv3) {
                  const itemDetail3 = new OfferPriceListDetailEntity()
                  itemDetail3.companyId = user.companyId
                  itemDetail3.createdBy = user.id
                  itemDetail3.offerPriceId = bidPriceLv3.id
                  itemDetail3.name = detailLv3.name
                  itemDetail3.type = detailLv3.type
                  itemDetail3.value = detailLv3.value
                  await bidPriceListDetailRepo.save(itemDetail3)
                }

                // Tạo giá trị cho các cột động lv3
                const lstValueLv3 = await servicePrice3.servicePriceColValues
                for (const itemValueLv3 of lstValueLv3) {
                  const objCol = lstCol.find((c) => c.id === itemValueLv3.servicePriceColId)
                  if (objCol) {
                    const itemValueNew3 = new OfferPriceColValueEntity()
                    itemValueNew3.companyId = user.companyId
                    itemValueNew3.createdBy = user.id
                    itemValueNew3.offerPriceId = bidPriceLv3.id
                    itemValueNew3.offerPriceColId = objCol.bidPriceColId
                    itemValueNew3.value = itemValueLv3.value
                    await bidPriceColValueRepo.save(itemValueNew3)
                  }
                }
              }
            }
          }
        }
      }
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)
  }

  async loadPriceItem(user: UserDto, bidItemId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const array = bidItemId.split('.,.,.')
    const bid = await this.bidPrItemRepository.findOne({ where: { id: array[0] } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const flag = await this.checkPermissionMpoEditTemplate(user, array[0])
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    const lstServicePrice = await this.bidPrItemRepository.find({
      where: { offerId: bid.offerId, isDeleted: false, isExGr: false },
    })

    // Tạo danh sách yêu cầu giá cho gói thầu
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(OfferPriceEntity)
      const bidRepo = manager.getRepository(OfferEntity)

      const lstCol = []

      for (const servicePrice1 of lstServicePrice) {
        //#region Tạo lv1
        const itemLv1 = new OfferPriceEntity()
        itemLv1.companyId = user.companyId
        itemLv1.createdBy = user.id
        itemLv1.offerServiceId = bid.id
        itemLv1.offerItemId = servicePrice1.id
        itemLv1.offerId = bid.offerId
        if (array[1]) itemLv1.currency = array[1]
        itemLv1.name = servicePrice1.shortText || servicePrice1.serviceName
        itemLv1.type = enumData.DataType.String.code
        itemLv1.number = servicePrice1.quantity
        itemLv1.level = 1

        // itemLv1.servicePriceId = servicePrice1.id
        const bidPriceLv1 = await bidPriceRepo.save(itemLv1)
        await bidRepo.update(bid.offerId, { isLoadFromItem: true })
        //#endregion
      }
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)
  }

  /** Lấy thông tin cơ cấu giá của lĩnh vực mời thầu */
  async loadCustomPrice(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.bidPrItemRepository.findOne({ where: { id: bidId } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    const lstServiceCustomPrice = await this.serviceCustomPriceRepo.find({
      where: { serviceId: bid.serviceId, isDeleted: false },
      order: { sort: 'ASC', createdAt: 'ASC' },
    })

    await this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(OfferCustomPriceEntity)
      for (const a of lstServiceCustomPrice) {
        const item = new OfferCustomPriceEntity()
        item.companyId = user.companyId
        item.createdBy = user.id
        item.offerServiceId = bidId
        item.sort = a.sort
        item.name = a.name
        item.isRequired = a.isRequired
        item.type = a.type
        item.unit = a.unit
        item.currency = a.currency
        item.number = a.number
        await bidCustomPriceRepo.save(item)
      }
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)
  }

  /** Tạo chào giá cho gói thầu */
  async createPrice(user: UserDto, bidId: string, data: { notePrice: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionPriceCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    bid.statusPrice = enumData.BidPriceStatus.DaDuyet.code
    bid.notePrice = data.notePrice
    // if (
    //   bid.statusTech === enumData.BidTechStatus.DaDuyet.code &&
    //   (bid.statusTrade === enumData.BidTradeStatus.DaTao.code || bid.statusTrade === enumData.BidTradeStatus.DaDuyet.code)
    // ) {
    //   bid.status = enumData.BidStatus.DangChonNCC.code
    //   // chưa chọn => đang chọn, để chọn Doanh nghiệp
    //   if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.ChuaChon.code) {
    //     bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DangChon.code
    //   }
    //   // đã duyệt => đã chọn, để duyệt lại
    //   if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaDuyet.code) {
    //     bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DaChon.code
    //   }
    // }
    await this.repo.save(bid)

    if (bid.statusTrade === enumData.BidPriceStatus.DaDuyet.code && bid.statusChooseSupplier === enumData.BidTechStatus.DaDuyet.code) {
      await this.repo.update({ id: bid.id }, { status: enumData.OfferStatus.DaCongKhai.code })
      // bid.status = enumData.OfferStatus.DaCongKhai.code
    }

    // return { message: 'Gửi yêu cầu phê duyệt template chào giá cho gói thầu thành công.' }

    return { message: 'Cấu hình template chào giá cho gói thầu thành công.' }
  }

  async sendPrice(user: UserDto, bidId: string, data: { noteTrade: string; isSurvey?: boolean }) {
    const bid = await this.repo.findOne({ where: { id: bidId, isDeleted: false } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    return { message: 'Gửi duyệt thành công' }

    // return { message: 'Thiết lập điều kiện thương mại cho gói thầu thành công.' }
  }

  /** Duyệt thiết lập hạng mục cơ cấu, cơ cấu giá của gói thầu */
  async priceAccept(user: UserDto, bidId: string, data: { notePrice?: string }) {
    // const objPermission = await this.checkPermissionTechAccept(user, bidId)
    // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (bid) {
      bid.statusPrice = enumData.BidPriceStatus.DaDuyet.code
      bid.notePrice = data.notePrice
      if (bid.statusTrade === enumData.BidTradeStatus.DaTao.code || bid.statusTrade === enumData.BidTradeStatus.DaDuyet.code) {
        bid.status = enumData.OfferStatus.HoanTatCauHinh.code
        // chưa chọn => đang chọn, để chọn Doanh nghiệp
        if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.ChuaChon.code) {
          bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DangChon.code
        }
        // đã duyệt => đã chọn, để duyệt lại
        if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaDuyet.code) {
          bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DaChon.code
        }
      }
      bid.updatedBy = user.id
      await this.repo.save(bid)
    }

    return { message: 'Duyệt thiết lập hạng mục chào giá, cơ cấu giá của gói thầu thành công.' }
  }

  /** Lấy chào giá của gói thầu */
  async getPrice(user: UserDto, bidId: string) {
    // if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const res = await this.repo.getBid1(user, bidId)

    const lstDataItem = []
    for (const item of res.listItem) {
      const service = await item.service
      item.itemName = service?.code + ' - ' + service?.name
      lstDataItem.push(item)
    }

    res.listItem = lstDataItem

    const getDataCell = (row, col) => {
      row[col.id] = ''
      if (row.__bidPriceColValue__?.length > 0) {
        const cell = row.__bidPriceColValue__.find((c) => c.bidPriceColId === col.id)
        if (cell) row[col.id] = cell.value
      }
    }
    for (const item of res.listItem) {
      item.listPrice = await this.bidPriceRepo.getPrice(user, item.id)
      item.listPriceCol = await this.bidPriceColRepo.getBidPriceColMPO(user, item.id)
      item.listPriceColId = item.listPriceCol.map((c) => c.id)
      item.listCustomPrice = await this.bidCustomPriceRepo.find({
        where: { offerServiceId: item.id },
        order: { sort: 'ASC', createdAt: 'ASC' },
      })

      if (item.listPriceCol.length == 0) continue
      for (const data1 of item.listPrice) {
        for (const col of item.listPriceCol) getDataCell(data1, col)
        for (const data2 of data1.__childs__) {
          for (const col of item.listPriceCol) getDataCell(data2, col)
          for (const data3 of data2.__childs__) {
            for (const col of item.listPriceCol) getDataCell(data3, col)
          }
        }
      }
    }

    return res
  }

  /** Kiểm tra quyền cấu hình lại bảng giá cho gói thầu */
  async checkPermissionResetBidPrice(user: UserDto, bidId: string) {
    let result = true
    let message = 'Gói thầu không còn tồn tại'

    return { hasPermission: result, message }
  }

  /** MPO/MPOLeader cấu hình lại bảng giá cho gói thầu */
  // async resetPrice(user: UserDto, bidId: string) {
  //   /** MPO/MPOLeader cấu hình lại bảng giá cho gói thầu */
  //   await this.repo.manager.transaction(async (manager) => {
  //     const bidRepo = manager.getRepository(OfferEntity)
  //     const bidSupplierRepo = manager.getRepository(BidSupplierEntity)
  //     const bidPriceRepo = manager.getRepository(OfferPriceEntity)
  //     const bidPriceColRepo = manager.getRepository(OfferPriceColEntity)
  //     const priceValueRepo = manager.getRepository(BidSupplierPriceValueEntity)
  //     const priceColValueRepo = manager.getRepository(BidSupplierPriceColValueEntity)

  //     const bidPrItemRepository = manager.getRepository(BidPrEntity)
  //     const bidDealRepo = manager.getRepository(BidDealEntity)
  //     const bidDealPriceRepo = manager.getRepository(BidDealPriceEntity)
  //     const bidDealSupplierRepo = manager.getRepository(BidDealSupplierEntity)
  //     const bidDealSupplierPriceValueRepo = manager.getRepository(BidDealSupplierPriceValueEntity)

  //     const bidAuctionRepo = manager.getRepository(BidAuctionEntity)
  //     const bidAuctionPriceRepo = manager.getRepository(BidAuctionPriceEntity)
  //     const bidAuctionSupplierRepo = manager.getRepository(BidAuctionSupplierEntity)
  //     const bidAuctionSupplierPriceValueRepo = manager.getRepository(BidAuctionSupplierPriceValueEntity)

  //     if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
  //     const bid = await bidPrItemRepository.findOne({ where: { id: bidId } })
  //     if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
  //     const objPermission = await this.checkPermissionResetBidPrice(user, bid.offerId)
  //     if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

  //     // Lấy những Doanh nghiệp đã nộp hồ sơ thầu
  //     const lstStatus = [
  //       enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
  //       enumData.BidSupplierStatus.DangDanhGia.code,
  //       enumData.BidSupplierStatus.DaDanhGia.code,
  //     ]
  //     const lstBidSupplier = await bidSupplierRepo.find({ where: { bidId, status: In(lstStatus) } })

  //     const bidPrice = await bidPriceRepo.find({ where: { id: bidId } })
  //     const bidPriceCol = await bidPriceColRepo.find({ where: { id: bidId } })
  //     const bidDeal = await bidDealRepo.find({ where: { id: bidId } })
  //     const lstBidDealId = bidDeal.map((c) => c.id)
  //     const bidAuction = await bidAuctionRepo.find({ where: { id: bidId } })
  //     const lstBidAuctionId = bidAuction.map((c) => c.id)

  //     for (const bidSupplier of lstBidSupplier) {
  //       // Thông tin chào giá cũ của Doanh nghiệp
  //       const bidPriceValue = await bidSupplier.bidSupplierPriceValue
  //       const bidPriceColValue = await bidSupplier.bidSupplierPriceColValue

  //       // Thông tin các lần đàm phán của Doanh nghiệp
  //       let lstBidDealSupplier: BidDealSupplierEntity[] = []
  //       if (lstBidDealId.length > 0) {
  //         lstBidDealSupplier = await bidDealSupplierRepo.find({
  //           where: { bidDealId: In(lstBidDealId), supplierId: bidSupplier.supplierId },
  //           relations: { bidDealSupplierPriceValue: true, bidDeal: { bidDealPrices: true } },
  //         })
  //       }

  //       // Thông tin các lần đấ gái của Doanh nghiệp
  //       let lstBidAuctionSupplier: BidAuctionSupplierEntity[] = []
  //       if (lstBidAuctionId.length > 0) {
  //         lstBidAuctionSupplier = await bidAuctionSupplierRepo.find({
  //           where: { bidAuctionId: In(lstBidAuctionId), supplierId: bidSupplier.supplierId },
  //           relations: { bidAuctionSupplierPriceValue: true, bidAuction: { bidAuctionPrice: true } },
  //         })
  //       }

  //       // Data lưu log
  //       const dataSave = {
  //         bidPrice,
  //         bidPriceCol,
  //         bidPriceValue,
  //         bidPriceColValue,
  //         lstBidDealSupplier,
  //         lstBidAuctionSupplier,
  //       }

  //       bidSupplier.dataJson = JSON.stringify(dataSave)
  //       bidSupplier.updatedBy = user.id

  //       await bidSupplierRepo.save(bidSupplier)
  //     }

  //     await bidSupplierRepo.update(
  //       { bidId },
  //       {
  //         statusResetPrice: enumData.BidSupplierResetPriceStatus.KhongYeuCau.code,
  //         updatedBy: user.id,
  //       },
  //     )

  //     const lstBidPrice = await bidPriceRepo.find({ where: { offerServiceId: bidId } })
  //     // Nếu chưa có cấu hình bảng giá trước đó thì không có gì để xóa
  //     if (lstBidPrice.length == 0) return

  //     // Xóa chào giá cũ
  //     const lstBidPriceId = lstBidPrice.map((c) => c.id)
  //     await priceValueRepo.delete({ offerPriceId: In(lstBidPriceId) })
  //     await priceColValueRepo.delete({ offerPriceId: In(lstBidPriceId) })

  //     // Xóa đàm phán giá
  //     if (lstBidDealId.length > 0) {
  //       await bidDealPriceRepo.delete({ bidDealId: In(lstBidDealId) })

  //       const lstBidDealSupplier = await bidDealSupplierRepo.find({ where: { bidDealId: In(lstBidDealId) } })
  //       const lstBidDealSupplierId = lstBidDealSupplier.map((c) => c.id)
  //       if (lstBidDealSupplierId.length > 0) {
  //         await bidDealSupplierPriceValueRepo.delete({
  //           bidDealSupplierId: In(lstBidDealSupplierId),
  //         })
  //         await bidDealSupplierRepo.delete({
  //           bidDealId: In(lstBidDealId),
  //         })
  //       }

  //       await bidDealRepo.delete({ bidId })
  //     }

  //     // Xóa đấu giá
  //     if (lstBidAuctionId.length > 0) {
  //       await bidAuctionPriceRepo.delete({ bidAuctionId: In(lstBidAuctionId) })

  //       const lstBidAuctionSupplier = await bidAuctionSupplierRepo.find({ where: { bidAuctionId: In(lstBidAuctionId) } })
  //       const lstBidAuctionSupplierId = lstBidAuctionSupplier.map((c) => c.id)
  //       if (lstBidAuctionSupplierId.length > 0) {
  //         await bidAuctionSupplierPriceValueRepo.delete({
  //           bidAuctionSupplierId: In(lstBidAuctionSupplierId),
  //         })
  //         await bidAuctionSupplierRepo.delete({
  //           bidAuctionId: In(lstBidAuctionId),
  //         })
  //       }

  //       await bidAuctionRepo.delete({ bidId })
  //     }
  //   })

  //   await this.repo.update(bidId, {
  //     // status: enumData.BidStatus.DangDanhGia.code,
  //     // statusRatePrice: enumData.BidPriceRateStatus.ChuaTao.code,
  //     statusResetPrice: enumData.BidResetPriceStatus.DangTao.code,
  //     updatedBy: user.id,
  //   })

  //   // Bid History
  //   const bidHistory = new BidHistoryEntity()
  //   bidHistory.companyId = user.companyId
  //   bidHistory.createdBy = user.id
  //   bidHistory.bidId = bidId
  //   bidHistory.employeeId = user.employeeId
  //   bidHistory.status = enumData.BidHistoryStatus.HieuChinhBangGia.code
  //   bidHistory.save()

  //   return { message: 'Thao tác thành công! Gói thầu đang được cấu hình lại bảng giá.' }
  // }

  /** MPO/MPOLeader lưu cấu hình lại bảng giá cho gói thầu */
  async saveResetPrice(
    user: UserDto,
    bidId: string,
    data: {
      lstSupplierId: string[]
      resetPriceEndDate: Date
      isRequireFilePriceDetail: boolean
      isRequireFileTechDetail: boolean
    },
  ) {
    if (!data.resetPriceEndDate) throw new BadRequestException('Vui lòng chọn thời điểm kết thúc nộp chào giá hiệu chỉnh.')
    if (data.lstSupplierId == null || data.lstSupplierId.length == 0)
      throw new BadRequestException('Vui lòng chọn doanh nghiệp nộp bảng giá hiệu chỉnh.')

    const bid = await this.bidPrItemRepository.findOne({ where: { id: bidId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    await this.repo.manager.transaction(async (manager) => {
      const bidRepo = manager.getRepository(OfferEntity)
      const bidSupplierRepo = manager.getRepository(OfferSupplierEntity)

      await bidRepo.update(bidId, {
        resetPriceEndDate: data.resetPriceEndDate,
        isRequireFilePriceDetail: data.isRequireFilePriceDetail,
        isRequireFileTechDetail: data.isRequireFileTechDetail,
        updatedBy: user.id,
      })

      // update lại các thông tin giá của các Doanh nghiệp tham gia gói thầu
      await bidSupplierRepo.update(
        { offerId: bidId, supplierId: In(data.lstSupplierId) },
        {
          statusPrice: enumData.BidSupplierPriceStatus.DangBoSung.code,
          scoreManualPrice: 0,
          scorePrice: 0,
          notePrice: '',
          isPriceValid: true,
          statusResetPrice: enumData.BidSupplierResetPriceStatus.YeuCauBoSung.code,
          updatedBy: user.id,
        },
      )
    })

    await this.repo.update(bidId, {
      status: enumData.BidStatus.DangDanhGia.code,
      statusRatePrice: enumData.BidPriceRateStatus.ChuaTao.code,
      statusResetPrice: enumData.BidResetPriceStatus.DaTao.code,
      updatedBy: user.id,
    })

    // Gửi email các Doanh nghiệp yêu cầu nộp bảng giá hiệu chỉnh
    // this.emailService.GuiNCCNopLaiChaoGia(bidId)

    return { message: 'Gói thầu đã được cấu hình lại bảng giá thành công.' }
  }

  /** Kiểm tra quyền kết thúc nộp chào giá hiệu chỉnh cho gói thầu */
  async checkPermissionEndResetBidPrice(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const bid = await this.repo.findOne({
      where: [{ id: bidId }],
    })
    if (bid) {
      if (bid.status == enumData.BidStatus.DangDanhGia.code && bid.statusResetPrice == enumData.BidResetPriceStatus.DaTao.code) {
        result = true
        if (!result) {
          message = 'Bạn không có quyền kết thúc nộp chào giá hiệu chỉnh cho gói thầu.'
        }
      } else {
        result = false
        message = 'Gói thầu đã thay đổi trạng thái, vui lòng kiểm tra lại.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Danh sách Doanh nghiệp tham gia gói thầu */
  async bidSupplierJoinResetPrice(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const objPermission = await this.checkPermissionEndResetBidPrice(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    return await this.bidSupplierRepo.find({
      where: { offerId: bidId, statusResetPrice: enumData.BidSupplierResetPriceStatus.DaBoSung.code },
      relations: { supplier: true },
    })
  }

  /** MPO/MPOLeader kết thúc nộp chào giá hiệu chỉnh cho gói thầu */
  async endResetPrice(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionEndResetBidPrice(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(bidId, {
      // status: enumData.BidStatus.DangDanhGia.code,
      statusRatePrice: enumData.BidPriceRateStatus.DangTao.code,
      statusRateTrade: enumData.BidTradeRateStatus.DaTao.code, // update để hiện nút duyệt chung cho (chào giá & thương mại)
      statusResetPrice: enumData.BidResetPriceStatus.KetThuc.code,
      updatedBy: user.id,
    })

    return { message: 'Thao tác thành công! Gói thầu đã kết thúc nộp chào giá hiệu chỉnh.' }
  }

  /** Lấy cơ cấu giá của gói thầu */
  async getCustomPrice(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const res = await this.repo.getBid1(user, bidId)
    for (const item of res.listItem) {
      item.listCustomPrice = await this.bidCustomPriceRepo.find({
        where: { offerServiceId: item.id },
        order: { sort: 'ASC', createdAt: 'ASC' },
      })
    }

    return res
  }

  async priceCreateData(user: UserDto, data: BidPriceCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.bidPrItemRepository.findOne({ where: { id: data.bidId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Tạo thêm ngoài lấy từ template
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(OfferPriceEntity)
      const offerServiceRepo = manager.getRepository(OfferServiceEntity)

      /* tìm ra offerId để gắn  */
      const offer = await offerServiceRepo.findOne({ where: { id: bid.id } })

      const item = new OfferPriceEntity()
      item.companyId = user.companyId
      item.code = data.name
      item.createdBy = user.id
      item.offerServiceId = bid.id
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      if (offer) item.offerId = offer.offerId
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.level = data.level
      item.description = data.description
      item.parentId = data.parentId
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      item.isSetup = data.isSetup
      if (data.isSetup) {
        item.isTemplate = data.isTemplate
      }
      item.unit = data.unit
      item.currency = data.currency
      item.number = data.number

      await bidPriceRepo.save(item)
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)
  }

  async priceUpdateData(user: UserDto, data: BidPriceUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.bidPrItemRepository.findOne({ where: { id: data.bidId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, bid.offerId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Update
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(OfferPriceEntity)

      const item = await bidPriceRepo.findOne({ where: { id: data.id } })
      if (!item) throw new Error(ERROR_NOT_FOUND_DATA)
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.level = data.level
      item.description = data.description
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      item.isSetup = data.isSetup
      if (data.isSetup) {
        item.isTemplate = data.isTemplate
      }
      item.unit = data.unit
      item.currency = data.currency
      item.number = data.number
      item.updatedBy = user.id

      await bidPriceRepo.save(item)
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)
  }

  async priceDeleteData(user: UserDto, bidPriceId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bidPrice = await this.bidPriceRepo.findOne({ where: { id: bidPriceId } })
    if (!bidPrice) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidPrice.offerService
    const objPermission = await this.checkPermissionPriceCreate(user, bid.offerId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, bidPrice.bidId)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Xoá
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(OfferPriceEntity)
      const bidPriceListDetailRepo = manager.getRepository(OfferPriceListDetailEntity)
      const bidPriceColValueRepo = manager.getRepository(OfferPriceColValueEntity)

      const bidPrice = await bidPriceRepo.findOne({ where: { id: bidPriceId } })
      if (!bidPrice) throw new Error(ERROR_NOT_FOUND_DATA)

      // lv2
      const lstData2 = await bidPrice.childs
      if (lstData2.length > 0) {
        for (const data2 of lstData2) {
          const lstData3 = await data2.childs
          if (lstData3.length > 0) {
            for (const data3 of lstData3) {
              // xóa thông tin mở rộng lv3
              await bidPriceListDetailRepo.delete({ offerPriceId: data3.id })
              // xóa giá trị cột động lv3
              await bidPriceColValueRepo.delete({ offerPriceId: data3.id })
            }

            // xóa lv3
            await bidPriceRepo.delete({ parentId: data2.id })
          }

          // xóa thông tin mở rộng lv2
          await bidPriceListDetailRepo.delete({ offerPriceId: data2.id })
          // xóa giá trị cột động lv2
          await bidPriceColValueRepo.delete({ offerPriceId: data2.id })
        }

        // xóa lv2
        await bidPriceRepo.delete({ parentId: bidPriceId })
      }

      // xóa thông tin mở rộng lv1
      await bidPriceListDetailRepo.delete({ offerPriceId: bidPriceId })
      // xóa giá trị cột động lv1
      await bidPriceColValueRepo.delete({ offerPriceId: bidPriceId })

      // xóa lv1
      await bidPriceRepo.delete(bidPriceId)
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)
  }

  async priceDeleteAllData(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.bidPrItemRepository.findOne({ where: { id: bidId, isDeleted: false } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, bid.offerId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bid.offerId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Xóa tất cả
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(OfferPriceEntity)
      const bidPriceListDetailRepo = manager.getRepository(OfferPriceListDetailEntity)
      const bidPriceColValueRepo = manager.getRepository(OfferPriceColValueEntity)

      const level = 1
      const lstBidPriceLv1 = await bidPriceRepo.find({ where: { offerServiceId: bidId, level } })
      if (lstBidPriceLv1.length > 0) {
        for (const bidPriceLv1 of lstBidPriceLv1) {
          const lstBidPriceLv2 = await bidPriceLv1.childs
          if (lstBidPriceLv2.length > 0) {
            for (const bidPriceLv2 of lstBidPriceLv2) {
              const lstBidPriceLv3 = await bidPriceLv2.childs
              if (lstBidPriceLv3.length > 0) {
                for (const bidPriceLv3 of lstBidPriceLv3) {
                  // xóa thông tin mở rộng lv3
                  await bidPriceListDetailRepo.delete({ offerPriceId: bidPriceLv3.id })
                  // xóa giá trị cột động lv3
                  await bidPriceColValueRepo.delete({ offerPriceId: bidPriceLv3.id })
                }
                // xóa lv3
                await bidPriceRepo.delete({ parentId: bidPriceLv2.id })
              }

              // xoá thông tin mở rộng Lv2
              await bidPriceListDetailRepo.delete({ offerPriceId: bidPriceLv2.id })
              // xoá giá trị cột động Lv2
              await bidPriceColValueRepo.delete({ offerPriceId: bidPriceLv2.id })
            }
            // xoá Lv2
            await bidPriceRepo.delete({ parentId: bidPriceLv1.id })
          }

          // xoá thông tin mở rộng Lv1
          await bidPriceListDetailRepo.delete({ offerPriceId: bidPriceLv1.id })
          // xoá giá trị cột động Lv1
          await bidPriceColValueRepo.delete({ offerPriceId: bidPriceLv1.id })
        }
        // xoá Lv1
        const offerServiceId = bidId
        await bidPriceRepo.delete({ offerServiceId })
      }
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)
  }

  async setting_fomular(user: UserDto, data: { id: string; fomular: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.bidPrItemRepository.findOne({ where: { id: data.id, isDeleted: false } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, bid.offerId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.id)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    const lstField = await this.bidPriceColRepo.getBidPriceColAll(user, data.id)
    const isValidFomular = await coreHelper.checkFomular(data.fomular, lstField)
    if (!isValidFomular) {
      throw new NotAcceptableException(ERROR_INVALID_FOMULAR)
    }

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)

    bid.fomular = data.fomular
    bid.updatedBy = user.id
    const res = await this.repo.save(bid)
    return res
  }

  /** Setup cách tính điểm giá của Item gói thầu */
  async saveSettingPriceCalWay(user: UserDto, data: { id: string; wayCalScorePrice: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionPriceCreate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.id)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    await this.repo.update(data.id, { wayCalScorePrice: data.wayCalScorePrice, updatedBy: user.id })

    return { message: UPDATE_SUCCESS }
  }

  /** Import excel chào giá */
  public async price_import(user: UserDto, bidId: string, data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    await this.priceDeleteAllData(user, bidId)

    // Import excel chào giá
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceColValueRepo = manager.getRepository(OfferPriceColValueEntity)
      const bidPriceRepo = manager.getRepository(OfferPriceEntity)
      const bidPriceColRepo = manager.getRepository(OfferPriceColEntity)
      const bidPriceListDetailRepo = manager.getRepository(OfferPriceListDetailEntity)

      const lstBidPriceCol = await bidPriceColRepo.find({
        where: { offerServiceId: bidId, colType: enumData.ColType.MPO.code, isDeleted: false },
        order: { sort: 'ASC', createdAt: 'ASC' },
      })

      // add lv1
      var lstDataLv1 = data.lstDataTable1.filter((c: any) => c.level == 1)
      for (const item of lstDataLv1) {
        const objBidPriceNew = new OfferPriceEntity()
        objBidPriceNew.companyId = user.companyId
        objBidPriceNew.createdBy = user.id
        // objBidPriceNew.bidId = bidId
        objBidPriceNew.offerServiceId = bidId
        objBidPriceNew.name = item.name
        objBidPriceNew.sort = item.sort || 0
        objBidPriceNew.unit = item.unit
        objBidPriceNew.currency = item.currency
        objBidPriceNew.number = item.number
        objBidPriceNew.isRequired = item.isRequired
        objBidPriceNew.level = 1
        objBidPriceNew.type = enumData.DataType.Number.code
        objBidPriceNew.isTemplate = false
        objBidPriceNew.isSetup = false

        const objBidPrice = await bidPriceRepo.save(objBidPriceNew)
        item.id = objBidPrice.id

        for (const col of lstBidPriceCol) {
          if (item[col.id] != null) {
            const objBidPriceColValueNew = new OfferPriceColValueEntity()
            objBidPriceColValueNew.companyId = user.companyId
            objBidPriceColValueNew.createdBy = user.id
            objBidPriceColValueNew.offerPriceId = item.id
            objBidPriceColValueNew.offerPriceColId = col.id
            objBidPriceColValueNew.value = item[col.id]

            await bidPriceColValueRepo.save(objBidPriceColValueNew)
          }
        }

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new OfferPriceListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.offerPriceId = item.id
            detailNew.name = detail.nameList
            detailNew.type = detail.typeDataList
            detailNew.value = detail.valueList
            await bidPriceListDetailRepo.save(detailNew)
          }
        }
      }

      // add lv2
      var lstDataLv2 = data.lstDataTable1.filter((c: any) => c.level == 2)
      for (const item of lstDataLv2) {
        const objBidPriceNew = new OfferPriceEntity()
        objBidPriceNew.companyId = user.companyId
        objBidPriceNew.createdBy = user.id
        objBidPriceNew.offerServiceId = bidId
        objBidPriceNew.name = item.name
        objBidPriceNew.sort = item.sort || 0
        objBidPriceNew.unit = item.unit
        objBidPriceNew.currency = item.currency
        objBidPriceNew.number = item.number
        objBidPriceNew.isRequired = item.isRequired
        objBidPriceNew.level = 2
        objBidPriceNew.type = enumData.DataType.Number.code
        objBidPriceNew.isTemplate = false
        objBidPriceNew.isSetup = false
        const parent = lstDataLv1.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objBidPriceNew.parentId = parent.id

        const objBidPrice = await bidPriceRepo.save(objBidPriceNew)
        item.id = objBidPrice.id

        for (const col of lstBidPriceCol) {
          if (item[col.id] != null) {
            const objBidPriceColValueNew = new OfferPriceColValueEntity()
            objBidPriceColValueNew.companyId = user.companyId
            objBidPriceColValueNew.createdBy = user.id
            objBidPriceColValueNew.offerPriceId = item.id
            objBidPriceColValueNew.offerPriceColId = col.id
            objBidPriceColValueNew.value = item[col.id]

            await bidPriceColValueRepo.save(objBidPriceColValueNew)
          }
        }

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new OfferPriceListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.offerPriceId = item.id
            detailNew.name = detail.nameList
            detailNew.type = detail.typeDataList
            detailNew.value = detail.valueList
            await bidPriceListDetailRepo.save(detailNew)
          }
        }
      }

      // add lv3
      var lstDataLv3 = data.lstDataTable1.filter((c: any) => c.level == 3)
      for (const item of lstDataLv3) {
        const objBidPriceNew = new OfferPriceEntity()
        objBidPriceNew.companyId = user.companyId
        objBidPriceNew.createdBy = user.id
        objBidPriceNew.offerServiceId = bidId
        objBidPriceNew.name = item.name
        objBidPriceNew.sort = item.sort || 0
        objBidPriceNew.unit = item.unit
        objBidPriceNew.currency = item.currency
        objBidPriceNew.number = item.number
        objBidPriceNew.isRequired = item.isRequired
        objBidPriceNew.level = 3
        objBidPriceNew.type = enumData.DataType.Number.code
        objBidPriceNew.isTemplate = false
        objBidPriceNew.isSetup = false
        const parent = lstDataLv2.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objBidPriceNew.parentId = parent.id

        const objBidPrice = await bidPriceRepo.save(objBidPriceNew)
        item.id = objBidPrice.id

        for (const col of lstBidPriceCol) {
          if (item[col.id] != null) {
            const objBidPriceColValueNew = new OfferPriceColValueEntity()
            objBidPriceColValueNew.companyId = user.companyId
            objBidPriceColValueNew.createdBy = user.id
            objBidPriceColValueNew.offerPriceId = item.id
            objBidPriceColValueNew.offerPriceColId = col.id
            objBidPriceColValueNew.value = item[col.id]

            await bidPriceColValueRepo.save(objBidPriceColValueNew)
          }
        }

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new OfferPriceListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.offerPriceId = item.id
            detailNew.name = detail.nameList
            detailNew.type = detail.typeDataList
            detailNew.value = detail.valueList
            await bidPriceListDetailRepo.save(detailNew)
          }
        }
      }
    })

    return { message: IMPORT_SUCCESS }
  }

  async customPriceCreateData(user: UserDto, data: BidCustomPriceCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.bidPrItemRepository.findOne({ where: { id: data.bidId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, bid.offerId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    await this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(OfferCustomPriceEntity)
      const item = new OfferCustomPriceEntity()
      item.companyId = user.companyId
      item.createdBy = user.id
      item.offerServiceId = data.bidId
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.unit = data.unit
      item.currency = data.currency
      item.number = data.number
      await bidCustomPriceRepo.save(item)
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)
  }

  async customPriceUpdateData(user: UserDto, data: BidCustomPriceUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.bidPrItemRepository.findOne({ where: { id: data.bidId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, bid.offerId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    await this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(OfferCustomPriceEntity)
      const item = await bidCustomPriceRepo.findOne({ where: { id: data.id } })
      if (!item) throw new Error(ERROR_NOT_FOUND_DATA)
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.unit = data.unit
      item.currency = data.currency
      item.number = data.number
      item.updatedBy = user.id
      await bidCustomPriceRepo.save(item)
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)
  }

  async customPriceDeleteData(user: UserDto, bidCustomPriceId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bidCustomPrice = await this.bidCustomPriceRepo.findOne({ where: { id: bidCustomPriceId } })
    if (!bidCustomPrice) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidCustomPrice.offerService

    const objPermission = await this.checkPermissionPriceCreate(user, bid.offerId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    const flag = await this.checkPermissionMpoEditTemplate(user, bid.offerId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    await this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(OfferCustomPriceEntity)
      const bidCustomPrice = await bidCustomPriceRepo.findOne({ where: { id: bidCustomPriceId } })
      if (!bidCustomPrice) throw new Error(ERROR_NOT_FOUND_DATA)
      await bidCustomPriceRepo.delete(bidCustomPriceId)
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)
  }

  async customPriceDeleteAllData(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bid = await this.bidPrItemRepository.findOne({ where: { id: bidId } })
    const objPermission = await this.checkPermissionPriceCreate(user, bid.offerId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const flag = await this.checkPermissionMpoEditTemplate(user, bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    await this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(OfferCustomPriceEntity)
      const offerServiceId = bidId
      await bidCustomPriceRepo.delete({ offerServiceId })
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)
  }

  public async custompriceImport(user: UserDto, bidId: string, data: { lstData: any[] }) {
    await this.customPriceDeleteAllData(user, bidId)

    return this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(OfferCustomPriceEntity)

      for (const item of data.lstData) {
        const objBidCustomPriceNew = new OfferCustomPriceEntity()
        objBidCustomPriceNew.companyId = user.companyId
        objBidCustomPriceNew.createdBy = user.id
        // objBidCustomPriceNew.bidId = bidId
        objBidCustomPriceNew.offerServiceId = bidId

        objBidCustomPriceNew.name = item.name
        objBidCustomPriceNew.sort = item.sort || 0
        objBidCustomPriceNew.unit = item.unit
        objBidCustomPriceNew.currency = item.currency
        objBidCustomPriceNew.number = item.number
        objBidCustomPriceNew.isRequired = item.isRequired
        objBidCustomPriceNew.type = enumData.DataType.Number.code

        await bidCustomPriceRepo.save(objBidCustomPriceNew)
      }
    })
  }

  public async bidPriceCol_list(user: UserDto, bidId: string) {
    const res: any[] = await this.bidPriceColRepo.getBidPriceColAll(user, bidId)
    for (const item of res) {
      item.typeName = enumData.DataType[item.type]?.name || ''
      item.colTypeName = enumData.ColType[item.colType]?.name || ''
    }

    return res
  }

  public async bidPriceCol_create_data(user: UserDto, data: BidPriceColCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.bidPrItemRepository.findOne({ where: { id: data.bidId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, bid.offerId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    if (data.fomular?.length > 0) {
      const lstField = await this.bidPriceColRepo.getBidPriceColAll(user, data.bidId)
      const isValidFomular = await coreHelper.checkFomular(data.fomular, lstField)
      if (!isValidFomular) throw new NotAcceptableException(ERROR_INVALID_FOMULAR)
    }

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)

    const entity = new OfferPriceColEntity()
    entity.companyId = user.companyId
    entity.createdBy = user.id
    entity.code = data.code
    entity.name = data.name
    entity.fomular = data.fomular
    entity.type = data.type
    entity.colType = data.colType
    entity.isRequired = data.isRequired
    entity.sort = data.sort || 0
    // entity.bidId = data.bidId
    entity.offerServiceId = bid.id
    entity.offerServiceId = bid.id

    return await entity.save()
  }

  public async bidPriceCol_update_data(user: UserDto, data: BidPriceColUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.bidPrItemRepository.findOne({ where: { id: data.bidId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bid.offerId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)

    const entity = await this.bidPriceColRepo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (data.fomular !== entity.fomular) {
      // Nếu update cthuc thì kiểm tra lại
      if (data.fomular?.length > 0) {
        const lstField = await this.bidPriceColRepo.getBidPriceColAll(user, entity.offerServiceId)
        const isValidFomular = await coreHelper.checkFomular(data.fomular, lstField)
        if (!isValidFomular) throw new Error(ERROR_INVALID_FOMULAR)
      }

      entity.fomular = data.fomular
    }
    entity.code = data.code
    entity.name = data.name
    entity.type = data.type
    entity.colType = data.colType
    entity.isRequired = data.isRequired
    entity.sort = data.sort || 0
    entity.updatedBy = user.id
    return await entity.save()
  }

  public async bidPriceCol_delete_data(user: UserDto, id: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidPriceCol = await this.bidPriceColRepo.findOne({ where: { id } })
    if (!bidPriceCol) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bid = await bidPriceCol.offerService

    const objPermission = await this.checkPermissionPriceCreate(user, bidPriceCol.offerServiceId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, bidPriceCol.bidItemId)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)

    return this.repo.manager.transaction(async (manager) => {
      const bidPriceColValueRepo = manager.getRepository(OfferPriceColValueEntity)
      const bidPriceColRepo = manager.getRepository(OfferPriceColEntity)
      await bidPriceColValueRepo.delete({ offerPriceColId: id })
      await bidPriceColRepo.delete(id)
      return { message: DELETE_SUCCESS }
    })
  }

  public async bidPriceCol_delete_all_data(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const objPermission = await this.checkPermissionPriceCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    const bid = await this.bidPrItemRepository.findOne({ where: [{ id: bidId }] })
    bid.id = bid.offerId
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id || bid.id)

    return this.repo.manager.transaction(async (manager) => {
      const bidPriceColValueRepo = manager.getRepository(OfferPriceColValueEntity)
      const bidPriceColRepo = manager.getRepository(OfferPriceColEntity)
      const offerServiceId = bidId
      const lstBidPriceCol = await bidPriceColRepo.find({ where: { offerServiceId } })
      for (const bidPriceCol of lstBidPriceCol) {
        await bidPriceColValueRepo.delete({ offerPriceColId: bidPriceCol.id })
      }

      await bidPriceColRepo.delete({ offerServiceId })

      return { message: DELETE_SUCCESS }
    })
  }

  public async bidPriceListDetail_list(user: UserDto, bidPriceId: string) {
    return await this.repo.manager.getRepository(OfferPriceListDetailEntity).find({
      where: { offerPriceId: bidPriceId },
      order: { createdAt: 'ASC' },
    })
  }

  public async bidPriceListDetail_create_data(user: UserDto, data: { bidPriceId: string; name: string; type: string; value: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidPrice = await this.bidPriceRepo.findOne({ where: { id: data.bidPriceId } })
    if (!bidPrice) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bid = await bidPrice.offerService

    const objPermission = await this.checkPermissionPriceCreate(user, bidPrice.offerServiceId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidPrice.offerServiceId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)

    const entity = new OfferPriceListDetailEntity()
    entity.companyId = user.companyId
    entity.createdBy = user.id
    entity.name = data.name
    entity.type = data.type
    entity.value = data.value
    entity.offerPriceId = data.bidPriceId
    await entity.save()

    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async bidPriceListDetail_update_data(user: UserDto, data: { id: string; bidPriceId: string; name: string; type: string; value: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidPrice = await this.bidPriceRepo.findOne({ where: { id: data.bidPriceId } })
    if (!bidPrice) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bid = await bidPrice.offerService

    const objPermission = await this.checkPermissionPriceCreate(user, bidPrice.offerServiceId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidPrice.offerServiceId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)

    const entity = await this.repo.manager.getRepository(OfferPriceListDetailEntity).findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    entity.name = data.name
    entity.type = data.type
    entity.value = data.value
    entity.updatedBy = user.id
    await entity.save()

    return { id: entity.id, message: UPDATE_SUCCESS }
  }

  public async bidPriceListDetail_delete_data(user: UserDto, id: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidPriceListDetail = await this.repo.manager.getRepository(OfferPriceListDetailEntity).findOne({ where: { id } })
    if (!bidPriceListDetail) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bidPrice = await bidPriceListDetail.offerPrice
    const bid = await bidPrice.offerService

    const objPermission = await this.checkPermissionPriceCreate(user, bidPrice.offerServiceId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidPrice.offerServiceId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.offerId || bid.id)

    await this.repo.manager.getRepository(OfferPriceListDetailEntity).delete(id)

    return { message: DELETE_SUCCESS }
  }

  //#endregion

  /** Kiểm tra quyền duyệt tất cả */
  async checkPermissionMpoAcceptAll(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const bid = await this.repo.findOne({
      where: [{ id: bidId }],
    })
    if (bid) {
      if (bid.status === enumData.BidStatus.DangDuyetGoiThau.code) {
        result = true
        if (!result) {
          message = 'Bạn không có quyền xét duyệt bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu.'
        }
      } else {
        result = false
        message =
          'Gói thầu đã được xét duyệt bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu hoặc chưa khởi tạo xong yêu cầu kỹ thuật, bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Duyệt tất cả */
  async acceptAll(user: UserDto, data: { id: string; noteMPOLeader: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.repo.findOne({ where: { id: data.id } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    const objPermission = await this.checkPermissionMpoAcceptAll(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const status = enumData.BidSupplierStatus.DaThongBaoMoiThau.code
    await this.bidSupplierRepo.update({ offerId: data.id }, { status, updatedBy: user.id })

    await this.repo.update(data.id, {
      status: enumData.BidStatus.DangNhanBaoGia.code,
      statusTrade: enumData.BidTradeStatus.DaDuyet.code,
      statusPrice: enumData.BidPriceStatus.DaDuyet.code,
      statusChooseSupplier: enumData.BidChooseSupplierStatus.DaDuyet.code,
      noteMPOLeader: data.noteMPOLeader || '',
      updatedBy: user.id,
    })

    return { message: UPDATE_SUCCESS }
  }

  /** Từ chối tất cả */
  async rejectAll(user: UserDto, data: { id: string; noteMPOLeader: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionMpoAcceptAll(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(data.id, {
      status: enumData.BidStatus.TuChoiGoiThau.code,
      statusTrade: enumData.BidTradeStatus.TuChoi.code,
      statusPrice: enumData.BidPriceStatus.TuChoi.code,
      statusChooseSupplier: enumData.BidChooseSupplierStatus.TuChoi.code,
      noteMPOLeader: data.noteMPOLeader || '',
      updatedBy: user.id,
    })

    return { message: UPDATE_SUCCESS }
  }

  //#endregion
  async checkPermissionMpoEditTemplate(user: UserDto, bidId: string) {
    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (bid && bid.statusResetPrice == enumData.BidResetPriceStatus.DangTao.code) {
      return true
    }

    let result = true
    const lstBidSupplierStatus = [
      enumData.BidSupplierStatus.DaXacNhanThamGiaThau.code,
      enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
      enumData.BidSupplierStatus.DangDanhGia.code,
      enumData.BidSupplierStatus.DaDanhGia.code,
    ]

    const lstBidSupplier = await this.bidSupplierRepo.find({
      where: { offerId: bidId, status: In(lstBidSupplierStatus) },
      select: { id: true },
    })

    if (lstBidSupplier.length > 0) {
      result = false
    }

    return result
  }
}
