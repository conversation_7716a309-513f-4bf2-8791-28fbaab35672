import { Controller, UseGuards, Post, Body, Get } from '@nestjs/common'
import { ServiceService } from './service.service'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { PaginationDto, UserDto } from '../../dto'
import { ServiceCreateDto, ServiceUpdateDto } from './dto'
import { enumProject } from '../../constants'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Service')
@Controller('services')
export class ServiceController {
  constructor(private readonly service: ServiceService) {}

  @ApiOperation({ summary: 'L<PERSON>y danh sách Item' })
  @UseGuards(ApeAuthGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: { isLast?: boolean; level?: number; parentId?: string }) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> tất cả LVMH có quyền' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code, enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_all_service')
  public async getAllService(@CurrentUser() user: UserDto) {
    return await this.service.getAllService(user)
  }

  @ApiOperation({ summary: 'Danh sách Item phân quyền' })
  @Roles(enumProject.Features.SETTING_013.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Danh sách Item phân quyền (Thiết lập template)' })
  @Roles(enumProject.Features.SETTING_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('manager_pagination')
  public async managerPagination(@Body() data: PaginationDto, @CurrentUser() user: UserDto) {
    return await this.service.managerPagination(data, user)
  }

  @ApiOperation({ summary: 'Tạo mới Item' })
  @Roles(enumProject.Features.SETTING_013.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: ServiceCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật Item' })
  @Roles(enumProject.Features.SETTING_013.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: ServiceUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động Item' })
  @Roles(enumProject.Features.SETTING_013.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateIsDelete(user, data)
  }

  @ApiOperation({ summary: 'Import LVMH' })
  @Roles(enumProject.Features.SETTING_013.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('import')
  public async importServices(@CurrentUser() user: UserDto, @Body() data: { lstData: any[] }) {
    return await this.service.importServices(user, data)
  }
}
