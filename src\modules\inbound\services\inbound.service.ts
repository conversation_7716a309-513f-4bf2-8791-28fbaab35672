import { Injectable, NotAcceptableException } from '@nestjs/common'
import { PaginationDto, UserDto } from '../../../dto'
import { CREATE_SUCCESS, enumData, enumLanguage, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION, UPDATE_SUCCESS } from '../../../constants'
import { Between, In, Like, Not } from 'typeorm'
import {
  ContractRepository,
  EmployeeRepository,
  MaterialRepository,
  POProductRepository,
  PORepository,
  PrRepository,
  WarehouseRepository,
} from '../../../repositories'
import { InboundCreateDto, InboundLoadByMonthDto, InboundUpdateDto, InboundUpdateStatusDto } from '../dto'
import { InboundContainerEntity, InboundEntity } from '../../../entities'
import * as moment from 'moment'
import { v4 as uuidv4 } from 'uuid'
import { coreHelper } from '../../../helpers/coreHelper'
import { InboundContainerRepository, InboundRepository } from '../../../repositories/inbound.repository'

@Injectable()
export class InboundService {
  constructor(
    private readonly repo: InboundRepository,
    private readonly prRepo: PrRepository,
    private readonly inboundContainerRepo: InboundContainerRepository,
    private readonly employeeRepo: EmployeeRepository,
    private readonly poRepo: PORepository,
  ) {}

  /** FIND */
  public async find(user: UserDto, data: { listPoId?: string[] }) {
    const whereCon: any = { isDeleted: false }
    if (data?.listPoId) {
      whereCon.poId = In(data.listPoId)
    }
    const res: any = await this.repo.find({
      where: whereCon,
      relations: {
        po: true,
        employeeIncharge: true,
      },
    })

    return res
  }

  /** lấy chi tiết */
  public async loadDetail(user: UserDto, data: { id: string }) {
    const res: any = await this.repo.findOne({
      where: { id: data.id, isDeleted: false },
      relations: {
        po: {
          supplier: true,

          products: true,
        },
        employeeIncharge: true,

        inboundContainers: true,
      },
    })

    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    res.poNumber = res.__po__?.code
    res.expectWarehouseName = res.__expectWarehouse__?.name
    res.employeeInchargeName = res.__employeeIncharge__?.name
    res.statusName = enumData.InboundStatus[res.status]?.name
    res.dateArrivalPortFormat = moment(res.dateArrivalPort).format('DD/MM/YYYY')
    res.dateArrivalWarehouseFormat = moment(res.dateArrivalWarehouse).format('DD/MM/YYYY')
    res.dateExpiredFormat = moment(res.dateExpired).format('DD/MM/YYYY')
    res.listContainer = res.__inboundContainers__
    res.lstProduct = res?.__po__?.__products__

    if (res.__po__) {
      res.prCode = res.__po__?.__pr__?.code
    }

    delete res.__po__
    delete res.__inboundContainers__
    delete res.__employeeIncharge__

    return res
  }

  /** Lấy danh sách inbound theo tháng */
  public async loadDataByMonth(user: UserDto, data: InboundLoadByMonthDto) {
    const startMonth = moment(data.date).startOf('month').toDate()
    const endMonth = moment(data.date).endOf('month').toDate()

    const whereCon: any = {}
    whereCon.createdAt = Between(startMonth, endMonth)
    whereCon.isDeleted = false

    const res: any = await this.repo.find({
      where: whereCon,
    })

    const groupByDate = res.reduce((acc: any, curr: any) => {
      const deliveryDate = curr.deliveryDate
      const date = new Date(deliveryDate.getFullYear(), deliveryDate.getMonth(), deliveryDate.getDate(), 0, 0, 0, 0).toLocaleDateString()

      if (!acc[date]) {
        acc[date] = { date, total: 1, dateTime: deliveryDate }
      } else {
        acc[date].total++
      }
      return acc
    }, {})

    return groupByDate
  }

  public async loadListContainer(user: UserDto, data: { listInboundId: string[] }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.listInboundId) {
      throw new Error('Không tìm thấy mã shipment')
    }

    const whereCon: any = {
      inboundId: In(data.listInboundId),
      isDeleted: false,
    }

    const res: any[] = await this.inboundContainerRepo.find({
      where: whereCon,
    })

    return res
  }

  /** Hàm phân trang theo ngày */
  public async loadPaginationByDate(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}
    if (data.where?.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where?.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where?.code) whereCon.code = Like(`%${data.where.code}%`)

    const dateStart = new Date(data.where.date)
    dateStart.setHours(0, 0, 0, 0)

    const dateEnd = new Date(data.where.date)
    dateEnd.setHours(23, 59, 59, 999)
    whereCon.deliveryDate = Between(dateStart, dateEnd)

    const res: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      relations: {
        po: {
          supplier: true,
        },
        employeeIncharge: true,
      },
    })

    const resPR = await this.prRepo.find({
      where: {
        isDeleted: false,
      },
    })

    const dictPR = resPR.reduce((acc: any, curr: any) => {
      acc[curr.id] = curr
      return acc
    }, {})

    for (const item of res[0]) {
      item.contractName = item.__contract__?.name
      item.poNumber = item.__po__?.code
      item.employeeInchargeName = item.__employeeIncharge__?.name
      item.statusName = enumData.InboundStatus[item.status]?.name
      item.dateArrivalPortFormat = moment(item.dateArrivalPort).format('DD/MM/YYYY')
      item.dateArrivalWarehouseFormat = moment(item.dateArrivalWarehouse).format('DD/MM/YYYY')
      item.dateExpiredFormat = moment(item.dateExpired).format('DD/MM/YYYY')
      item.supplierName = item.__po__?.__supplier__?.name
      if (item.__po__?.prIds) {
        const listPrId = JSON.parse(item.__po__?.prIds) || []
        if (Array.isArray(listPrId) && listPrId.length > 0) {
          const listPr = listPrId?.map((id: string) => dictPR[id])
          item.listPrCode = listPr?.map((pr: any) => pr.code)?.join(', ')
        }
      }

      delete item.__contract__
      delete item.__po__
      delete item.__employeeIncharge__
    }

    return res
  }

  /** Code Default inbound*/
  async codeDefault() {
    const code = `IB${moment(new Date()).format('YYYYMM')}`
    const objData = await this.repo.findOne({
      where: { code: Like(`%${code}%`) },
      order: { code: 'DESC' },
    })
    let sortString = '0000'
    if (objData) {
      sortString = objData.code.substring(code.length, code.length + 4)
    }
    const lastSort = parseInt(sortString, 10)
    sortString = ('0000' + (lastSort + 1)).slice(-4)

    return code + sortString
  }

  /** Tạo mới inbound */
  public async createData(user: UserDto, data: InboundCreateDto) {
    const newCode = await this.codeDefault()
    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(InboundEntity)
      const inboundContainerRepo = trans.getRepository(InboundContainerEntity)
      const newInbound = repo.create({
        ...data,
        code: newCode,
        createdBy: user.id,
        createdAt: new Date(),
        status: enumData.InboundStatus.NEW.code,
      })

      const created = await repo.save(newInbound)

      if (data.listContainer) {
        const listContainerTask = []
        for (const itemContainer of data.listContainer) {
          const inboundContainer = inboundContainerRepo.create({
            ...itemContainer,
            id: uuidv4(),
            inboundId: created.id,
            createdBy: user.id,
            createdAt: new Date(),
          })
          listContainerTask.push(inboundContainer)
        }
        await inboundContainerRepo.insert(listContainerTask)
      }

      return { message: CREATE_SUCCESS }
    })
  }

  /** Hàm cập nhật */
  public async updateData(user: UserDto, data: InboundUpdateDto) {
    const found = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!found) throw new Error(ERROR_NOT_FOUND_DATA)

    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(InboundEntity)
      const inboundContainerRepo = trans.getRepository(InboundContainerEntity)

      const updated = await repo.save(data)
      await inboundContainerRepo.delete({ inboundId: updated.id })

      if (data.listContainer) {
        const listContainerTask = []
        for (const itemContainer of data.listContainer) {
          const inboundContainer = inboundContainerRepo.create({
            ...itemContainer,
            inboundId: updated.id,
            updatedAt: new Date(),
            updatedBy: user.id,
          })
          listContainerTask.push(inboundContainer)
        }
        await inboundContainerRepo.save(listContainerTask)
      }

      return { message: UPDATE_SUCCESS }
    })
  }

  /** Hàm cập nhật trạng thái */
  public async updateStatus(user: UserDto, data: InboundUpdateStatusDto) {
    const found = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!found) throw new Error(ERROR_NOT_FOUND_DATA)

    return this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(InboundEntity)

      if (!enumData.InboundStatus[data.status]) {
        throw new Error('Trạng thái không tồn tại')
      }

      found.status = data.status
      found.updatedAt = new Date()
      found.updatedBy = user.id
      await repo.save(found)
      return { message: UPDATE_SUCCESS }
    })
  }

  /** Hàm xoá */
  public async deleteData(user: UserDto, data: { id: string }) {
    const found = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!found) throw new Error(ERROR_NOT_FOUND_DATA)

    return this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(InboundEntity)

      await repo.delete({ id: data.id })

      return { message: UPDATE_SUCCESS }
    })
  }

  public convertStatus(
    data: any,
    enumStatus: any,
    statusStyleFieldName: string = 'statusStyle',
    dotStyleFieldName: string = 'dotStyle',
    tagColorFieldName: string = 'tagStatusColor',
  ) {
    // Style cho trạng thái
    data[statusStyleFieldName] = {
      color: enumStatus.color,
      borderColor: enumStatus.borderColor,
      width: '200px',
      fontWeight: '600',
      borderRadius: '30px',
    }
    data[dotStyleFieldName] = {
      width: '6px',
      height: '6px',
      borderRadius: '100%',
      backgroundColor: enumStatus.color,
    }
    data[tagColorFieldName] = enumStatus.bgColor
    return data
  }

  /** Hàm phân trang */
  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}

    if (data.where?.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where?.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where?.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where?.supplierId) whereCon.supplierId = data.where.supplierId
    if (data.where?.poId) whereCon.poId = data.where.poId
    if (data.where.status) whereCon.status = data.where.status

    if (!whereCon.po) whereCon.po = {}
    whereCon.po.code = data.where.poCode

    if (!whereCon.po.supplier) whereCon.po.supplier = {}
    whereCon.po.supplier.code = data.where.supplierCode

    if (data.where.createdAtFrom && data.where.createdAtTo) {
      whereCon.createdAt = Between(
        moment(data.where.createdAtFrom).format('YYYY-MM-DD 00:00:00'),
        moment(data.where.createdAtTo).format('YYYY-MM-DD 23:59:59'),
      )
    }

    if (data.where.deliveryDateFrom && data.where.deliveryDateTo) {
      whereCon.createdAt = Between(
        moment(data.where.deliveryDateFrom).format('YYYY-MM-DD 00:00:00'),
        moment(data.where.deliveryDateTo).format('YYYY-MM-DD 23:59:59'),
      )
    }

    const resEmployee = await this.employeeRepo.find({
      where: {
        isDeleted: false,
      },
    })

    const dictUser = resEmployee.reduce((acc: any, curr: any) => {
      acc[curr.userId] = curr
      return acc
    }, {})

    const res: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      relations: {
        po: {
          supplier: true,
        },
        employeeIncharge: true,
      },
    })

    for (const item of res[0]) {
      item.poNumber = item.__po__?.code
      item.employeeInchargeName = item.__employeeIncharge__?.name
      item.statusName = enumData.InboundStatus[item.status]?.name
      this.convertStatus(item, enumData.InboundStatus[item.status])

      item.dateArrivalPortFormat = moment(item.dateArrivalPort).format('DD/MM/YYYY')
      item.dateArrivalWarehouseFormat = moment(item.dateArrivalWarehouse).format('DD/MM/YYYY')
      item.supplierName = item.__po__?.__supplier__?.name
      item.createdByName = dictUser[item.createdBy]?.name

      delete item.__po__
      delete item.__expectWarehouse__
      delete item.__employeeIncharge__
    }

    return res
  }

  /** Hàm cập nhật trạng thái */
  public async updateCancel(user: UserDto, data: InboundUpdateStatusDto) {
    const found = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!found) throw new Error(ERROR_NOT_FOUND_DATA)

    return this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(InboundEntity)

      if (!enumData.InboundStatus[data.status]) {
        throw new Error('Trạng thái không tồn tại')
      }

      found.status = data.status
      found.updatedAt = new Date()
      found.updatedBy = user.id
      await repo.save(found)

      return { message: 'Đã hủy Inbound thành công' }
    })
  }
}
