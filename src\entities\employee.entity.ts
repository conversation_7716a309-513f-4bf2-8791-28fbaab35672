import { BaseEntity } from './base.entity'
import { En<PERSON><PERSON>, Column, ManyToOne, JoinColumn, OneToOne, OneToMany } from 'typeorm'
import { DepartmentEntity } from './department.entity'
import { UserEntity } from './user.entity'
import { ServiceAccessEntity } from './serviceAccess.entity'
import { BidEmployeeAccessEntity } from './bidEmployeeAccess.entity'
import { SupplierExpertiseEntity } from './supplierExpertise.entity'
import { ServiceEntity } from './service.entity'
import { SupplierExpertiseMemberEntity } from './supplierExpertiseMember.entity'
import { BidHistoryEntity } from './bidHistory.entity'
import { POHistoryEntity } from './poHistory.entity'
import { POMemberEntity } from './poMember.entity'
import { BranchEntity } from './branch.entity'
import { BranchMemberEntity } from './branchMember.entity'
import { PrApproveEntity } from './prApprove.entity'
import { PrApproverEntity } from './prApprover.entity'
import { ContractMemberEntity } from './contractMember.entity'
import { ContractHistoryEntity } from './contractHistory.entity'
import { PrEntity } from './pr.entity'
import { EmployeeNotifyEntity } from './employeeNotify.entity'
import { EmployeeWarningEntity } from './employeeWarning.entity'
import { InvoiceSuggestHistoryEntity } from './invoiceSuggestHistory.entity'
import { InboundEntity } from './inbound.entity'

@Entity('employee')
export class EmployeeEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  email: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  departmentId: string
  @ManyToOne(() => DepartmentEntity, (p) => p.employee)
  @JoinColumn({ name: 'departmentId', referencedColumnName: 'id' })
  department: Promise<DepartmentEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  userId: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  branchId: string
  @ManyToOne(() => BranchEntity, (p) => p.employees)
  @JoinColumn({ name: 'branchId', referencedColumnName: 'id' })
  branch: Promise<BranchEntity>

  @OneToOne(() => UserEntity, (p) => p.employee)
  @JoinColumn({ name: 'userId', referencedColumnName: 'id' })
  user: Promise<UserEntity>

  // Nhân viên duyệt năng lực
  // @OneToMany(
  //   () => SupplierServiceEntity,
  //   p => p.approveBy,
  // )
  // supplierServices: Promise<SupplierServiceEntity[]>

  // Nhân viên duyệt pháp lý
  @OneToMany(() => SupplierExpertiseEntity, (p) => p.approvedLaw)
  supplierExpertiseLaw: Promise<SupplierExpertiseEntity[]>

  // Nhân viên ban thẩm định
  @OneToMany(() => SupplierExpertiseMemberEntity, (p) => p.employee)
  supplierExpertiseMembers: Promise<SupplierExpertiseMemberEntity[]>

  @OneToMany(() => ServiceEntity, (p) => p.approveBy)
  serviceApprover: Promise<ServiceEntity[]>

  // Nhân viên có quyền truy cập vào loại hình dịch vụ này
  @OneToMany(() => ServiceAccessEntity, (p) => p.employee)
  serviceAccess: Promise<ServiceAccessEntity[]>

  // Nhân viên có quyền truy cập vào loại gói thầu
  @OneToMany(() => BidEmployeeAccessEntity, (p) => p.employee)
  bidAccess: Promise<BidEmployeeAccessEntity[]>

  /** Lịch sử bid */
  @OneToMany(() => BidHistoryEntity, (p) => p.employee)
  bidHistorys: Promise<BidHistoryEntity[]>

  // thành viên trong hd
  @OneToMany(() => ContractMemberEntity, (p) => p.employee)
  contractMembers: Promise<ContractMemberEntity[]>

  // Lịch sử hợp đồng
  @OneToMany(() => ContractHistoryEntity, (p) => p.employee)
  contractHistorys: Promise<ContractHistoryEntity[]>

  @OneToMany(() => POHistoryEntity, (p) => p.employee)
  poHistorys: Promise<POHistoryEntity[]>

  @OneToMany(() => POMemberEntity, (p) => p.employee)
  poMembers: Promise<POMemberEntity[]>

  @OneToMany(() => BranchMemberEntity, (p) => p.employee)
  branchMembers: Promise<BranchMemberEntity[]>

  @OneToMany(() => PrApproveEntity, (p) => p.employee)
  prApproves: Promise<PrApproveEntity[]>

  @OneToMany(() => PrApproverEntity, (p) => p.employee)
  prApprovers: Promise<PrApproverEntity[]>

  @OneToMany(() => PrEntity, (p) => p.empInCharge)
  proposerPRs: Promise<PrEntity[]>

  @OneToMany(() => PrEntity, (p) => p.empInCharge)
  inChargePRs: Promise<PrEntity[]>

  /** Thông báo hệ thống */
  @OneToMany(() => EmployeeNotifyEntity, (p) => p.employee)
  employeeNotify: Promise<EmployeeNotifyEntity[]>

  /** Cảnh báo hệ thống */
  @OneToMany(() => EmployeeWarningEntity, (p) => p.employee)
  employeeWarning: Promise<EmployeeWarningEntity[]>

  // Lịch sử ĐNTT
  @OneToMany(() => InvoiceSuggestHistoryEntity, (p) => p.employee)
  invoiceSuggestHistories: Promise<InvoiceSuggestHistoryEntity[]>

  // DS Inbounds
  @OneToMany(() => InboundEntity, (p) => p.employeeIncharge)
  inbounds: Promise<InboundEntity[]>
}
