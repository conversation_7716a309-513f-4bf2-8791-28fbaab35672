import { Injectable, NotFoundException, MethodNotAllowedException, NotAcceptableException, BadRequestException } from '@nestjs/common'
import { enumData, ERROR_YOU_DO_NOT_HAVE_PERMISSION, ERROR_NOT_FOUND_DATA } from '../../../constants'
import { EmailService } from '../../email/email.service'
import {
  BidRepository,
  BidEmployeeAccessRepository,
  BidSupplierRepository,
  BidDealRepository,
  BidPriceColRepository,
  BidTechRepository,
  BidTradeRepository,
  BidPriceRepository,
} from '../../../repositories'
import { PaginationDto, UserDto } from '../../../dto'
import {
  BidSupplierTechValueEntity,
  BidSupplierTradeValueEntity,
  BidSupplierPriceValueEntity,
  BidSupplierCustomPriceValueEntity,
  BidHistoryEntity,
  BidEntity,
  OfferSupplierPriceValueEntity,
  OfferEntity,
} from '../../../entities'
import { coreHelper } from '../../../helpers'
import { In, IsNull, Like, Not, Raw } from 'typeorm'
import * as moment from 'moment'
import {
  OfferDealRepository,
  OfferPriceColRepository,
  OfferPriceRepository,
  OfferRepository,
  OfferSupplierRepository,
  OfferTradeRepository,
} from '../../../repositories/offer.repository'
import { OfferSupplierTradeValueEntity } from '../../../entities/offerSupplierTradeValue.entity'

@Injectable()
export class OfferRateService {
  constructor(
    private readonly repo: OfferRepository,
    private readonly bidDealRepo: OfferDealRepository,
    private readonly bidTradeRepo: OfferTradeRepository,
    private readonly bidPriceRepo: OfferPriceRepository,
    private readonly bidPriceColRepo: OfferPriceColRepository,

    private readonly bidSupplierRepo: OfferSupplierRepository,
    private readonly emailService: EmailService,
  ) {}

  /** Hàm load gói thầu module đánh giá */
  async pagination(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    let whereCon: any = { parentId: IsNull(), isDeleted: false, isSurvey: false }
    // Lấy gói thầu mpoLead cần duyệt
    if (data.where.isGetBidNeedApprove) {
      const whereTemp = [
        {
          companyId: user.companyId,
          employeeAccess: { employeeId: user.employeeId, type: enumData.BidRuleType.MPOLeader.code },
          status: In([enumData.BidStatus.DangDanhGia.code, enumData.BidStatus.DangDuyetDanhGia.code]),
          statusRateTech: enumData.BidTechRateStatus.DaTao.code,
        },
        {
          companyId: user.companyId,
          employeeAccess: { employeeId: user.employeeId, type: enumData.BidRuleType.MPOLeader.code },
          status: In([enumData.BidStatus.DangDanhGia.code, enumData.BidStatus.DangDuyetDanhGia.code]),
          statusRateTrade: enumData.BidTradeRateStatus.DaTao.code,
          statusRatePrice: enumData.BidPriceRateStatus.DaTao.code,
        },
        {
          companyId: user.companyId,
          employeeAccess: { employeeId: user.employeeId, type: enumData.BidRuleType.MPOLeader.code },
          status: In([enumData.BidStatus.DongThau.code, enumData.BidStatus.DangDuyetKetThucThau.code]),
        },
      ]

      const lstBidTemp = await this.repo.find({ where: whereTemp, select: { id: true } })
      const lstBidId = lstBidTemp.map((c) => c.id)

      if (lstBidId.length == 0) return [[], 0]
      whereCon.id = In(lstBidId)
    }
    // Lấy gói thầu có quyền xem
    else whereCon.employeeAccess = { employeeId: user.employeeId }

    if (data.where.serviceId) whereCon.childs = { serviceId: data.where.serviceId }

    let lstStatus = [
      enumData.BidStatus.GoiThauTam.code,
      enumData.BidStatus.DangNhanBaoGia.code,
      enumData.BidStatus.DangDanhGia.code,
      enumData.BidStatus.DangDuyetDanhGia.code,
      enumData.BidStatus.HoanTatDanhGia.code,
      enumData.BidStatus.DongDauGia.code,
      enumData.BidStatus.DongDamPhanGia.code,
      enumData.BidStatus.DangDauGia.code,
      enumData.BidStatus.DangDamPhanGia.code,
      enumData.BidStatus.DongThau.code,
      enumData.BidStatus.DuyetNCCThangThau.code,
      enumData.BidStatus.DangDuyetKetThucThau.code,
    ]
    if (data.where.status?.length > 0) lstStatus = data.where.status
    whereCon.status = In(lstStatus)

    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.publicDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.dateFrom) {
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}")`)
    } else if (data.where.dateTo) {
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.dateTo).format('YYYY-MM-DD')}")`)
    }

    // Tìm theo mã số hoặc tên gói thầu
    if (data.where.name) {
      whereCon = [
        { ...whereCon, code: Like(`%${data.where.name}%`) },
        { ...whereCon, name: Like(`%${data.where.name}%`) },
      ]
    }

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      select: {
        id: true,
        createdAt: true,
        code: true,
        name: true,
        status: true,
        publicDate: true,
        statusRateTech: true,
        statusRateTrade: true,
        statusRatePrice: true,
        isRequestDelete: true,
        statusResetPrice: true,
        fileScan: true,
        noteFinishBidMPO: true,
      },
    })
    if (res[0].length == 0) return res

    const lstId = res[0].map((c) => c.id)
    // const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
    //   where: { bidId: In(lstId), isDeleted: false },
    //   relations: { employee: true },
    //   select: {
    //     id: true,
    //     bidId: true,
    //     type: true,
    //     employeeId: true,
    //     employee: {
    //       id: true,
    //       name: true,
    //     },
    //   },
    // })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c))
    }

    for (const item of res[0]) {
      // const lstAccess = lstEmployeeAccess.filter((c) => c.bidId == item.id)
      // const objTech = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
      // item.techName = objTech?.__employee__?.name || ''
      // const objMpo = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
      // item.mpoName = objMpo?.__employee__?.name || ''

      // const lstAccessUser = lstAccess.filter((c) => c.employeeId === user.employeeId)
      // item.isMPO = lstAccessUser.some((c) => c.type === enumData.BidRuleType.MPO.code)
      // item.isMPOLeader = lstAccessUser.some((c) => c.type === enumData.BidRuleType.MPOLeader.code)
      // item.isTech = lstAccessUser.some((c) => c.type === enumData.BidRuleType.Tech.code)
      // item.isTechLeader = lstAccessUser.some((c) => c.type === enumData.BidRuleType.TechLeader.code)
      // item.isMember = lstAccessUser.some((c) => c.type === enumData.BidRuleType.Memmber.code)
      // item.statusName = dicStatus[item.status].name
      item.objPermissionApprove = true
      item.statusColor = dicStatus[item.status].statusColor
      item.statusBorderColor = dicStatus[item.status].statusBorderColor
      item.statusBgColor = dicStatus[item.status].statusBgColor
      // mpo có quyền như tech
      item.isTech = item.isTech || item.isMPO
      // mpoLead có quyền như techLead
      item.isTechLeader = item.isTechLeader || item.isMPOLeader
      item.isShowCopy = item.isMPO || item.isMPOLeader
      item.isShowDelete = item.isMPO && !item.isRequestDelete
      item.isShowApproveDelete = item.isMPOLeader && item.isRequestDelete
      // mở thầu
      item.isShowOpenBid = item.status === enumData.BidStatus.DangNhanBaoGia.code && (item.isMPO || item.isMPOLeader)
      // biên bản mở thầu
      item.isShowProtocolOpenBid = item.status !== enumData.BidStatus.DangNhanBaoGia.code && (item.isMPO || item.isMPOLeader)
      // report: các thành viên trong hội đồng thầu, mpo, mpoLead
      item.isShowReport = (item.isMPO || item.isMPOLeader || item.isMember) && item.status !== enumData.BidStatus.DangNhanBaoGia.code
      // phân tích giá
      item.isShowAnalysis = (item.isMPO || item.isMPOLeader) && item.status !== enumData.BidStatus.DangNhanBaoGia.code
      // Kết thúc nộp chào giá hiệu chỉnh
      item.isShowEndResetPrice = item.statusResetPrice == enumData.BidResetPriceStatus.DaTao.code && (item.isMPO || item.isMPOLeader)
      // đánh giá
      if (
        item.statusResetPrice !== enumData.BidResetPriceStatus.DaTao.code &&
        (item.status === enumData.BidStatus.DangDanhGia.code || item.status === enumData.BidStatus.DangDuyetDanhGia.code)
      ) {
        // nút đánh giá kỹ thuật
        item.isShowTechRate = item.isTech || item.isTechLeader
        if (item.isShowTechRate) {
          if (item.statusRateTech === enumData.BidTechRateStatus.DangTao.code) {
            item.techType = 'dashed'
          }
          if (item.statusRateTech === enumData.BidTechRateStatus.TuChoi.code) {
            item.techType = 'danger'
          }
          if (item.statusRateTech === enumData.BidTechRateStatus.DaTao.code) {
            item.techType = 'warning'
          }
          if (item.statusRateTech === enumData.BidTechRateStatus.DaDuyet.code) {
            item.techType = 'success'
          }
        }

        // nút đánh giá thương mại
        item.isShowTradeRate = item.isMPO || item.isMPOLeader
        if (item.isShowTradeRate) {
          if (item.statusRateTrade === enumData.BidTradeRateStatus.DangTao.code) {
            item.tradeType = 'dashed'
          }
          if (item.statusRateTrade === enumData.BidTradeRateStatus.TuChoi.code) {
            item.tradeType = 'danger'
          }
          if (item.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code) {
            item.tradeType = 'warning'
          }
          if (item.statusRateTrade === enumData.BidTradeRateStatus.DaDuyet.code) {
            item.tradeType = 'success'
          }
        }

        // nút đánh giá chào giá
        item.isShowPriceRate = item.isMPO || item.isMPOLeader
        if (item.isShowPriceRate) {
          if (item.statusRatePrice === enumData.BidPriceRateStatus.DangTao.code) {
            item.priceType = 'dashed'
          }
          if (item.statusRatePrice === enumData.BidPriceRateStatus.TuChoi.code) {
            item.priceType = 'danger'
          }
          if (item.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code) {
            item.priceType = 'warning'
          }
          if (item.statusRatePrice === enumData.BidPriceRateStatus.DaDuyet.code) {
            item.priceType = 'success'
          }
        }
      }

      // Cấu hình lại bảng giá
      item.isShowResetPrice =
        (item.status === enumData.BidStatus.HoanTatDanhGia.code ||
          item.status === enumData.BidStatus.DongDamPhanGia.code ||
          item.status === enumData.BidStatus.DongDauGia.code) &&
        (item.isMPO || item.isMPOLeader)

      if (item.statusResetPrice == enumData.BidResetPriceStatus.ChuaTao.code || item.statusResetPrice == enumData.BidResetPriceStatus.KetThuc.code) {
        // Chọn ncc thắng thầu
        item.isShowEnd =
          item.status === enumData.BidStatus.HoanTatDanhGia.code ||
          item.status === enumData.BidStatus.DongDamPhanGia.code ||
          item.status === enumData.BidStatus.DongDauGia.code
        //    &&
        // item.isMPO

        // Duyệt chọn ncc thắng thầu
        // item.isShowAcceptEnd = item.status === enumData.BidStatus.DongThau.code && item.isMPOLeader
        item.isShowAcceptEnd = item.status === enumData.BidStatus.DongThau.code

        // Đàm phán giá
        item.isShowDeal =
          (item.status === enumData.BidStatus.HoanTatDanhGia.code || item.status === enumData.BidStatus.DongDamPhanGia.code) && item.isMPO

        // Đấu giá
        item.isShowAuction = item.status === enumData.BidStatus.HoanTatDanhGia.code && item.isMPO

        // Đàm phán giá & Đấu giá
        item.isShowDealAuction = item.status === enumData.BidStatus.HoanTatDanhGia.code && item.isMPO
      }

      // In hồ sơ thầu
      item.isShowPrint = item.status === enumData.BidStatus.DuyetNCCThangThau.code && (item.isMPO || item.isMPOLeader)

      // Gửi yêu cầu phê duyệt kết thúc thầu
      item.isShowSendRequestFinishBid = item.status === enumData.BidStatus.DuyetNCCThangThau.code && item.isMPO

      // Phê duyệt kết thúc thầu
      // item.isShowApproveFinishBid = item.status === enumData.BidStatus.DangDuyetKetThucThau.code && item.isMPOLeader
      item.isShowApproveFinishBid = item.status === enumData.BidStatus.DangDuyetKetThucThau.code
    }

    return res
  }

  //#region bidRateTrade

  /** Check quyền tạo đánh giá điều kiện thương mại cho gói thầu */
  async checkPermissionCreateBidTradeRate(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (bid) {
      if (bid.statusRateTrade === enumData.BidTradeRateStatus.DangTao.code || bid.statusRateTrade === enumData.BidTradeRateStatus.TuChoi.code) {
        // result = await this.bidEmployeeAccessRepo.isMPO(user, bidId)
        // if (!result) {
        //   message = 'Bạn không có quyền đánh giá điều kiện thương mại cho gói thầu.'
        // }
      } else {
        result = false
        message = 'Gói thầu đã được đánh giá điều kiện thương mại.'
      }
    }

    return { hasPermission: true, message }
  }

  /** Lấy ds NCC tham gia thầu và tính điểm */
  async loadTradeRate(user: UserDto, bidId: string) {
    const bidSupplierTradeValueRepo = this.repo.manager.getRepository(OfferSupplierTradeValueEntity)

    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res = await this.repo.getBid2(user, bidId)
    res.canApprove = true

    const dicStatusFile: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierFileStatus)
      lstStatus.forEach((c) => (dicStatusFile[c.code] = c.name))
    }
    const dicStatusTrade: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierTradeStatus)
      lstStatus.forEach((c) => (dicStatusTrade[c.code] = c.name))
    }
    const setType = new Set()
    setType.add(enumData.DataType.Number.code)
    setType.add(enumData.DataType.List.code)
    const lstItem = []
    for (const item of res.listItem) {
      if (item.isExGr) lstItem.push(item)
    }
    res.listItem = lstItem
    for (const item of res.listItem) {
      // Lấy template hồ sơ thương mại của gói thầu
      item.lstBidTrade = await this.bidTradeRepo.getTrade(user, item.id)
      // Lấy hồ sơ thương mại của các NCC tham gia
      item.lstBidSupplier = await this.bidSupplierRepo.find({
        where: { offerId: item.offerId, parentId: Not(IsNull()) },
        relations: { supplier: true, offerSupplierTradeValue: true },
        order: { createdAt: 'DESC' },
      })
      if (item.lstBidSupplier.length === 0) continue

      const dicSup = []
      for (const bidSupplier of item.lstBidSupplier) {
        dicSup[bidSupplier.supplierId] = bidSupplier
      }
      //Lấy tiêu chí tính điểm (cấp 1 & kiểu number hoặc list)
      var lstBidTradeCal = item.lstBidTrade.filter((c) => c.parentId === null && setType.has(c.type))

      // Tính cho từng NCC tham gia thầu
      for (const bidSupplier of Object.values(dicSup)) {
        bidSupplier.supplierCode = bidSupplier.__supplier__.code
        bidSupplier.supplierName = bidSupplier.__supplier__.name
        delete bidSupplier.__supplier__
        bidSupplier.statusFileName = dicStatusFile[bidSupplier.statusFile]
        bidSupplier.statusTechName = dicStatusTrade[bidSupplier.statusTech]

        if (bidSupplier.scoreTrade === 0) {
          const lstBidSupplierTradeValue = bidSupplier.__offerSupplierTradeValue__ || []
          delete bidSupplier.__offerSupplierTradeValue__
          let scoreTrade = 0
          // với từng tiêu chí cần tính điểm
          for (const bidTrade of lstBidTradeCal) {
            const itemChilds = bidTrade.__childs__ || []
            if (itemChilds.length > 0) {
              let scoreTradeChild = 0
              for (const itemChild of itemChilds) {
                const objValueChild = lstBidSupplierTradeValue.find((c) => c.bidTradeId === itemChild.id)
                if (objValueChild) {
                  const tem = this.calScoreTradeItem(itemChild, objValueChild.value)
                  objValueChild.score = tem
                  scoreTradeChild += tem
                  // Lưu điểm
                  await bidSupplierTradeValueRepo.update(objValueChild.id, { score: tem, updatedBy: user.id })
                }
              }
              const temp = (bidTrade.percent * scoreTradeChild) / 100
              scoreTrade += temp
              // Lưu điểm
              await bidSupplierTradeValueRepo.update(
                { offerTradeId: bidTrade.id, offerSupplierId: bidSupplier.id },
                { score: temp, updatedBy: user.id },
              )
            } else {
              const objValue = lstBidSupplierTradeValue.find((c) => c.bidTradeId === bidTrade.id)
              if (objValue) {
                const temp = this.calScoreTradeItem(bidTrade, objValue.value)
                objValue.score = temp
                scoreTrade += temp
                // Lưu điểm
                await bidSupplierTradeValueRepo.update(
                  { offerTradeId: bidTrade.id, offerSupplierId: bidSupplier.id },
                  { score: temp, updatedBy: user.id },
                )
              }
            }
          }
          if (isNaN(scoreTrade)) {
            bidSupplier.scoreTrade = 0
          } else if (!isFinite(scoreTrade)) {
            bidSupplier.scoreTrade = 0
          } else bidSupplier.scoreTrade = scoreTrade
          // Lưu điểm
          await this.bidSupplierRepo.update(bidSupplier.id, { scoreTrade: bidSupplier.scoreTrade, updatedBy: user.id })
        }

        bidSupplier.rankABCD = coreHelper.rankABCD(bidSupplier.scoreTrade)
      }

      // sort
      item.lstBidSupplier.sort((a, b) => b.scoreTrade - a.scoreTrade)
    }

    /* Tìm ra danh sách cấp duyệt và comment và người duyệt */
    res.haveProgress = false
    res.approvalProgress = true
    res.showComment = false
    /* nếu như nhân viên hiện tại chưa duyệt và nằm trong luồng duyệt thì toggle bật showComment = true */
    if (res.approvalProgress.length > 0) res.haveProgress = true

    return res
  }

  /**
   * Lấy danh sách bidTrade và điểm cao nhất tương ứng
   * @param data.bidId - Item trong gói thầu
   * @param data.bidSupplierId - NCC tham gia Item
   */
  async loadBestTradeValue(user: UserDto, data: { bidId: string; bidSupplierId: string }) {
    const lstBidSupplier = await this.bidSupplierRepo.find({ where: { offerId: data.bidId }, select: { id: true } })
    if (lstBidSupplier.length == 0) throw new Error('Không có hồ sơ thầu hợp lệ')
    const listBidSupplierId = lstBidSupplier.map((p) => p.id)

    // Danh sách bidTrade
    const bidTrades = await this.bidTradeRepo.find({
      where: { offerId: data.bidId, parentId: IsNull(), isDeleted: false },
    })

    // Danh sách tất cả giá trị các NCC đã nộp
    const bidTradeValue = await this.repo.manager.getRepository(BidSupplierTradeValueEntity).find({
      where: { bidSupplierId: In(listBidSupplierId) },
    })

    let result = []
    const length = bidTrades.length

    const setType = new Set()
    setType.add(enumData.DataType.Number.code)
    setType.add(enumData.DataType.List.code)

    // Lọc qua danh sách bidTrade
    for (let i = 0; i < length; i++) {
      let itemResult: any = {}
      const item = bidTrades[i]
      itemResult.id = item.id
      itemResult.name = item.name
      itemResult.percent = item.percent
      itemResult.percentRule = item.percentRule
      itemResult.type = item.type
      itemResult.sort = item.sort

      // Giá trị mà NCC tương ứng đã nộp hồ sơ
      const supplierTraveValue = bidTradeValue.find((p) => p.bidTradeId === item.id && p.bidSupplierId === data.bidSupplierId)

      itemResult.value = supplierTraveValue?.value

      // Nếu kiểu list thì lọc để lấy name
      if (itemResult.type === enumData.DataType.List.code) {
        const listValue = await item.offerTradeListDetails
        const find = listValue.find((p) => p.id === itemResult.value)
        itemResult.value = find?.name
      }
      itemResult.score = supplierTraveValue?.score

      // Danh sách giá trị tất cả các NCC nộp theo tiêu chí này
      const listBidSupplierTraveValue = bidTradeValue.filter((p) => p.bidTradeId === item.id)

      // Nêu có và là loại list hoặc number thì bắt đầu tính điểm max và rank
      if (listBidSupplierTraveValue.length > 0 && setType.has(itemResult.type)) {
        // Lấy giá trị có điểm tốt nhất
        const bestSupplier = this.getMaxOfArrayObj(listBidSupplierTraveValue)

        itemResult.bestScore = bestSupplier?.score
        itemResult.bestSupplierName = (await (await bestSupplier.bidSupplier).supplier).name
        itemResult.bestValue = bestSupplier?.value

        // Nếu kiểu list thì lọc qua để lấy name
        if (itemResult.type === enumData.DataType.List.code) {
          const listValue = await (await bestSupplier.bidTrade).bidTradeListDetails
          const find = listValue.find((p) => p.id === bestSupplier.value)
          itemResult.bestValue = find?.name
        }
        if (supplierTraveValue) {
          itemResult.rank = this.getCurrentRank(listBidSupplierTraveValue, supplierTraveValue?.bidSupplierId)
        }
      }

      // Lọc qua danh sách con
      itemResult.childs = []
      const itemChilds = await item.childs
      if (itemChilds?.length > 0) {
        for (const itemC of itemChilds) {
          let itemResultC: any = {}
          itemResultC.id = itemC.id
          itemResultC.name = itemC.name
          itemResultC.percent = itemC.percent
          itemResultC.percentRule = itemC.percentRule
          itemResultC.type = itemC.type
          itemResultC.sort = itemC.sort

          // Lấy giá trị mà NCC tương ứng đã nôpk
          const supplierTraveValueC = await bidTradeValue.find((p) => p.bidTradeId === itemC.id && p.bidSupplierId === data.bidSupplierId)

          itemResultC.value = supplierTraveValueC?.value

          // Nếu kiểu list thì lọc qua danh sách để lấy name
          if (itemResultC.type === enumData.DataType.List.code) {
            const listValueC = await itemC.offerTradeListDetails
            const findC = listValueC.find((p) => p.id === itemResultC.value)
            itemResultC.value = findC?.name
          }
          itemResultC.score = supplierTraveValueC?.score

          // Danh sách giá trị tất cả các NCC nộp theo tiêu chí này
          const listBidSupplierTradeValueC = await bidTradeValue.filter((p) => p.bidTradeId === itemC.id)
          // Nêu có và là loại list hoặc number thì bắt đầu tính điểm max và rank
          if (listBidSupplierTradeValueC.length > 0 && setType.has(itemResultC.type)) {
            // Lấy giá trị có điểm tốt nhất
            const bestSupplierC = this.getMaxOfArrayObj(listBidSupplierTradeValueC)

            itemResultC.bestScore = bestSupplierC?.score
            itemResultC.bestSupplierName = (await (await bestSupplierC.bidSupplier).supplier).name
            itemResultC.bestValue = bestSupplierC?.value
            // Nếu kiểu list thì lọc qua để lấy name
            if (itemResultC.type === enumData.DataType.List.code) {
              const listValueC = await (await bestSupplierC.bidTrade).bidTradeListDetails
              const findC = listValueC.find((p) => p.id === bestSupplierC.value)
              itemResultC.bestValue = findC?.name
            }
            if (supplierTraveValueC) {
              itemResultC.rank = this.getCurrentRank(listBidSupplierTradeValueC, supplierTraveValueC?.bidSupplierId)
            }
          }
          itemResult.childs.push(itemResultC)
        }
      }
      itemResult.childs = itemResult.childs.sort((a, b) => a.sort - b.sort)

      result.push(itemResult)
    }
    result = result.sort((a, b) => a.sort - b.sort)

    return result
  }

  /** Tính điểm đánh giá điều kiện thương mại */
  calScoreTradeItem(item: any, value: string) {
    let score = 0
    if (item.type === enumData.DataType.Number.code && value && value.trim() != '') {
      let temp = 0
      const x = +value
      // Tính theo chiều thuận
      if (item.isCalUp) {
        if (x >= item.percentRule) {
          temp = item.percent
        } else {
          temp = (x * item.percent) / item.percentRule // giá trị * tỉ trọng / điều kiện b
        }
      }
      // Tính theo chiều nghịch
      else {
        if (x <= item.percentRule) {
          temp = item.percent
        } else if (x >= item.percentDownRule) {
          temp = 0
        } else {
          temp = ((item.percentDownRule - x) * item.percent) / (item.percentDownRule - item.percentRule)
        }
      }

      if (isNaN(temp)) {
        score += 0
      } else if (!isFinite(temp)) {
        score += 0
      } else {
        score += temp
      }
    } else if (item.type === enumData.DataType.List.code) {
      const itemChosen = item.__bidTradeListDetails__.find((p) => p.id === value)
      const tem = itemChosen ? itemChosen.value : 0
      const temp = (tem * item.percent) / 100
      if (isNaN(temp)) {
        score += 0
      } else if (!isFinite(temp)) {
        score += 0
      } else {
        score += temp
      }
    }

    if (isNaN(score) || !isFinite(score)) return 0

    return score
  }

  /** Tạo đánh giá điều kiện thương mại cho gói thầu */
  async createTradeRate(user: UserDto, data: { id: string; listItem: any[] }) {
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionCreateBidTradeRate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    const bid = await this.repo.findOne({ where: { id: data.id } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    for (const item of data.listItem) {
      for (const bidSupplierItem of item.lstBidSupplier) {
        await this.bidSupplierRepo.update(bidSupplierItem.id, {
          statusTrade: enumData.BidSupplierTradeStatus.DaXacNhan.code,
          isTradeValid: bidSupplierItem.isTradeValid,
          noteTrade: bidSupplierItem.noteTrade,
          scoreManualTrade: bidSupplierItem.scoreManualTrade,
          updatedBy: user.id,
        })
      }
    }

    let status = bid.status
    if (
      (bid.statusRateTech === enumData.BidTechRateStatus.DaTao.code || bid.statusRateTech === enumData.BidTechRateStatus.DaDuyet.code) &&
      (bid.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code || bid.statusRatePrice === enumData.BidPriceRateStatus.DaDuyet.code)
    ) {
      status = enumData.BidStatus.DangDuyetDanhGia.code
    }
    await this.repo.update(data.id, {
      statusRateTrade: enumData.BidTradeRateStatus.DaTao.code,
      status,
      updatedBy: user.id,
    })

    if (bid.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code || bid.statusRatePrice === enumData.BidPriceRateStatus.DaDuyet.code) {
      // this.emailService.GuiMpoLeadDuyetDanhGiaGia(data.id)
    }

    // tạo quyền duyệt kết quả đánh giá điều kiện thương mại

    return { message: 'Gửi yêu cầu phê duyệt kết quả đánh giá điều kiện thương mại thành công.' }
  }
  /** Duyệt đánh giá chào giá và điều kiện thương mại */
  async approveTradeRate(user: UserDto, data: { id: string; listItem: any[]; comment: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    // const objPermission = await this.checkPermissionApproveBidPriceRate(user, data.id)
    // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: data.id } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    for (const item of data.listItem) {
      for (const bidSupplierItem of item.lstBidSupplier) {
        await this.bidSupplierRepo.update(bidSupplierItem.id, {
          statusTrade: enumData.BidSupplierTradeStatus.DaDuyet.code,
          statusPrice: enumData.BidSupplierPriceStatus.DaDuyet.code,
          noteMPOLeader: bidSupplierItem.noteMPOLeader,
          updatedBy: user.id,
        })
      }
    }

    let status = bid.status
    // if (bid.statusRateTech === enumData.BidTechRateStatus.DaDuyet.code) {
    status = enumData.BidStatus.HoanTatDanhGia.code
    // }
    await this.repo.update(data.id, {
      statusRateTrade: enumData.BidTradeRateStatus.DaDuyet.code,
      statusRatePrice: enumData.BidPriceRateStatus.DaDuyet.code,
      status,
      updatedBy: user.id,
    })

    return { message: 'Duyệt đánh giá chào giá và điều kiện thương mại thành công.' }
    // }
  }
  /** Từ chối đánh giá chào giá và điều kiện thương mại */
  async rejectTradeRate(user: UserDto, data: { id: string; listItem: any[]; comment: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionApproveBidPriceRate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: data.id } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    for (const item of data.listItem) {
      for (const bidSupplierItem of item.lstBidSupplier) {
        await this.bidSupplierRepo.update(bidSupplierItem.id, {
          statusTrade: enumData.BidSupplierTradeStatus.DangDanhGia.code,
          statusPrice: enumData.BidSupplierPriceStatus.DangDanhGia.code,
          noteMPOLeader: bidSupplierItem.noteMPOLeader,
          updatedBy: user.id,
        })
      }
    }

    await this.repo.update(data.id, {
      statusRateTrade: enumData.BidTradeRateStatus.TuChoi.code,
      statusRatePrice: enumData.BidPriceRateStatus.TuChoi.code,
      status: enumData.BidStatus.DangDanhGia.code,
      updatedBy: user.id,
    })

    // gửi email bộ phận thương mại
    this.emailService.GuiMpoTuChoiDanhGiaGia(data.id)

    return { message: 'Từ chối đánh giá chào giá và điều kiện thương mại thành công.' }
  }
  //#endregion

  //#region bidRatePrice

  /** Check quyền tạo đánh giá giá cho gói thầu */
  async checkPermissionCreateBidPriceRate(user: UserDto, bidId: string) {
    let result = true
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (bid) {
      if (
        bid.statusRatePrice === enumData.BidPriceRateStatus.DangTao.code ||
        bid.statusRatePrice === null ||
        bid.statusRatePrice === enumData.BidPriceRateStatus.TuChoi.code
      ) {
        // result = await this.bidEmployeeAccessRepo.isMPO(user, bidId)
        // if (!result) {
        //   message = 'Bạn không có quyền đánh giá giá cho gói thầu.'
        // }
      } else {
        result = false
        message = 'Gói thầu đã được đánh giá giá.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Lấy ds NCC tham gia thầu và tính điểm */
  async loadPriceRate(user: UserDto, bidId: string) {
    const bidSupplierPriceValueRepo = this.repo.manager.getRepository(BidSupplierPriceValueEntity)
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res = await this.repo.getBid2(user, bidId)
    const dicStatusFile: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierFileStatus)
      lstStatus.forEach((c) => (dicStatusFile[c.code] = c.name))
    }
    const dicStatusPrice: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierPriceStatus)
      lstStatus.forEach((c) => (dicStatusPrice[c.code] = c.name))
    }
    const lstItem = []
    for (const item of res.listItem) {
      if (item.isExGr) lstItem.push(item)
    }
    res.listItem = lstItem
    for (const item of res.listItem) {
      // Lấy template bảng chào giá của Item
      item.lstBidPrice = await this.bidPriceRepo.getPrice(user, item.id)
      // Lấy hồ sơ giá của các NCC tham gia
      item.lstBidSupplier = await this.bidSupplierRepo.find({
        where: { offerId: item.offerId, parentId: Not(IsNull()) },
        relations: { supplier: true, offerSupplierPriceValue: true },
        order: { createdAt: 'DESC' },
      })
      if (item.lstBidSupplier.length === 0) continue

      const lstBidSupplierId = item.lstBidSupplier.map((p) => p.id)

      const scoreDLC = item.scoreDLC || 0

      const lstBidSupplierPriceValue = await bidSupplierPriceValueRepo.find({
        // where: { bidSupplierId: In(lstBidSupplierId) },
        where: { bidSupplierId: In(lstBidSupplierId) },
      })
      // Lọc qua danh sách các hạng mục - tính điểm cho từng hạng mục
      for (const itemBidPrice of item.lstBidPrice) {
        const supplierValue = lstBidSupplierPriceValue.filter((p) => p.bidPriceId === itemBidPrice.id).sort((a, b) => +a.value - +b.value)

        if (supplierValue.length > 0) {
          // Tìm ra giá trị nhỏ nhất theo hạng mục này
          const minValue = this.getMinOfArrayObj(supplierValue)

          const supplierValueLength = supplierValue.length

          const listSupplierScore = supplierValue.map((p) => +p.value)

          // Từ danh sách điểm tạm sẽ tính được độ lệch chuẩn
          const dlc = coreHelper.calDLC(listSupplierScore)

          // Lọc qua danh sach điểm này lại và tính điểm thiệt dựa vào độ lệch chuẩn ở trên
          for (let i = 0; i < supplierValueLength; i++) {
            const itemBidSupplierValue = supplierValue[i]
            let score = scoreDLC
            if (i !== 0 && dlc > 0) {
              score = scoreDLC - (+itemBidSupplierValue.value - +minValue.value) / dlc
            }
            itemBidSupplierValue.score = score
            // Lưu điểm
            await bidSupplierPriceValueRepo.update(itemBidSupplierValue.id, { score, updatedBy: user.id })
          }
        }
      }

      const dicSup = []
      for (const bidSupplier of item.lstBidSupplier) {
        dicSup[bidSupplier.supplierId] = bidSupplier
      }
      // Lọc qua danh sách NCC tính tổng điểm các hạng mục -> điểm tạm
      for (const bidSupplier of Object.values(dicSup)) {
        bidSupplier.supplierCode = bidSupplier.__supplier__.code
        bidSupplier.supplierName = bidSupplier.__supplier__.name
        delete bidSupplier.__supplier__
        bidSupplier.statusFileName = dicStatusFile[bidSupplier.statusFile]
        bidSupplier.statusPriceName = dicStatusPrice[bidSupplier.statusPrice]

        const lstPriceValue = lstBidSupplierPriceValue.filter((c) => c.bidSupplierId == bidSupplier.id)
        let scorePrice = 0
        for (const priceValue of lstPriceValue) {
          scorePrice += priceValue.score
        }
        bidSupplier.scorePrice = scorePrice

        bidSupplier.rankABCD = coreHelper.rankABCD(bidSupplier.scorePrice)
      }

      const listBidSupplierScorePrice = item.lstBidSupplier.map((p) => p.scorePrice)

      // Từ danh sách điểm tạm sẽ tính được độ lệch chuẩn
      const dlcBidSupplier = coreHelper.calDLC(listBidSupplierScorePrice)
      const maxScorePrice = Math.max(...listBidSupplierScorePrice)

      item.lstBidSupplier.sort((a, b) => b.scorePrice - a.scorePrice)
      const length = item.lstBidSupplier.length

      // Lọc qua danh sách NCC tính lại điểm cho các NCC
      for (let i = 0; i < length; i++) {
        const itemBidSupplier = item.lstBidSupplier[i]

        let scorePrice = scoreDLC
        if (i !== 0 && dlcBidSupplier > 0) {
          scorePrice = scoreDLC - (maxScorePrice - itemBidSupplier.scorePrice) / dlcBidSupplier
        }

        itemBidSupplier.scorePrice = scorePrice

        // Lưu điểm
        await this.bidSupplierRepo.update(itemBidSupplier.id, {
          scorePrice: itemBidSupplier.scorePrice,
          updatedBy: user.id,
        })
      }
    }

    res.canApprove = true

    return res
  }

  /** Lấy danh sách sách bidPrice và điểm cao nhất tương ứng */
  async loadBestPriceValue(user: UserDto, data: { bidId: string; bidSupplierId: string }) {
    const objBidSupplier = await this.bidSupplierRepo.findOne({
      where: { id: data.bidSupplierId, offerId: data.bidId },
      relations: { supplier: true, offerSupplierPriceValue: true, offerSupplierPriceColValue: true },
    })
    if (!objBidSupplier) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bidSupplierPriceColValue = await objBidSupplier.offerSupplierPriceColValue
    const bidSupplier = await this.bidSupplierRepo.find({ where: { offerId: data.bidId }, select: { id: true } })
    const listBidSupplierId = bidSupplier.map((p) => p.id)

    const bidPrices = await this.bidPriceRepo.getPrice(user, data.bidId)
    const bidPriceCols = await this.bidPriceColRepo.getBidPriceColAll(user, data.bidId)

    // Danh sách tất cả giá trị các NCC đã nộp
    const bidPriceValue = await this.repo.manager.getRepository(OfferSupplierPriceValueEntity).find({
      where: { offerSupplierId: In(listBidSupplierId) },
    })

    // function get data dynamic col by row
    const getDataRow = (row: any) => {
      for (const col of bidPriceCols) {
        row[col.id] = ''
        if (col.colType === enumData.ColType.MPO.code) {
          if (row.__bidPriceColValue__?.length > 0) {
            const cell = row.__bidPriceColValue__.find((c: any) => c.bidPriceColId === col.id)
            if (cell) row[col.id] = cell.value
          }
        }
        if (col.colType === enumData.ColType.Supplier.code) {
          const cell = bidSupplierPriceColValue.find((c) => c.offerPriceColId === col.id && c.offerPriceId === row.id)
          if (cell) row[col.id] = cell.value
        }
      }
    }

    // Lọc qua danh sách bidPrice
    for (let i = 0, length1 = bidPrices.length; i < length1; i++) {
      const item: any = bidPrices[i]
      getDataRow(item)

      // Giá trị mà NCC tương ứng đã nộp hồ sơ
      const supplierPriceValue = bidPriceValue.find((p) => p.offerPriceId === item.id && p.offerSupplierId === data.bidSupplierId)

      item.value = supplierPriceValue?.value
      item.score = supplierPriceValue?.score

      // Danh sách giá trị tất cả các NCC nộp theo tiêu chí này
      const listBidSupplierPriceValue = bidPriceValue.filter((p) => p.offerPriceId === item.id)

      // Nêu có và là loại list hoặc number thì bắt đầu tính điểm max và rank
      if (listBidSupplierPriceValue.length > 0) {
        // Lấy giá trị có điểm tốt nhất
        const bestSupplier = this.getMinOfArrayObj(listBidSupplierPriceValue)

        item.bestScore = bestSupplier?.score
        item.bestSupplierName = (await (await bestSupplier.offerSupplier).supplier).name
        item.bestValue = bestSupplier?.value

        if (supplierPriceValue) {
          item.rank = this.getCurrentRank(listBidSupplierPriceValue, supplierPriceValue?.offerSupplierId)
        }
      }

      item.__childs__ = item.__childs__ || []
      for (let i2 = 0, length2 = item.__childs__.length; i2 < length2; i2++) {
        const item2: any = item.__childs__[i2]
        getDataRow(item2)

        item2.__childs__ = item2.__childs__ || []
        for (let i3 = 0, length3 = item2.__childs__.length; i3 < length3; i3++) {
          const item3: any = item2.__childs__[i3]
          getDataRow(item3)
        }
      }
    }

    const lstCustomPrice = await this.repo.manager
      .getRepository(BidSupplierCustomPriceValueEntity)
      .find({ where: { bidSupplierId: data.bidSupplierId }, order: { sort: 'ASC', createdAt: 'ASC' } })

    return [bidPrices, bidPriceCols, bidSupplierPriceColValue, objBidSupplier, lstCustomPrice]
  }

  /** Tạo đánh giá giá cho gói thầu */
  async createPriceRate(user: UserDto, data: { id: string; listItem: any[] }) {
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionCreateBidPriceRate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: data.id } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    for (const item of data.listItem) {
      for (const bidSupplierItem of item.lstBidSupplier) {
        await this.bidSupplierRepo.update(bidSupplierItem.id, {
          scorePrice: bidSupplierItem.scorePrice,
          statusPrice: enumData.BidSupplierPriceStatus.DaXacNhan.code,
          isPriceValid: bidSupplierItem.isPriceValid,
          notePrice: bidSupplierItem.notePrice,
          scoreManualPrice: bidSupplierItem.scoreManualPrice,
          updatedBy: user.id,
        })
      }
    }

    let status = bid.status
    if (
      (bid.statusRateTech === enumData.BidTechRateStatus.DaTao.code || bid.statusRateTech === enumData.BidTechRateStatus.DaDuyet.code) &&
      (bid.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code || bid.statusRateTrade === enumData.BidTradeRateStatus.DaDuyet.code)
    ) {
      status = enumData.BidStatus.DangDuyetDanhGia.code
    }
    await this.repo.update(data.id, {
      statusRatePrice: enumData.BidPriceRateStatus.DaTao.code,
      status,
      updatedBy: user.id,
    })

    // // Bid History
    // const bidHistory = new BidHistoryEntity()
    // bidHistory.companyId = user.companyId
    // bidHistory.createdBy = user.id
    // bidHistory.bidId = data.id
    // bidHistory.employeeId = user.employeeId
    // bidHistory.status = enumData.BidHistoryStatus.DanhGiaGia.code
    // bidHistory.save()

    if (bid.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code || bid.statusRateTrade === enumData.BidTradeRateStatus.DaDuyet.code) {
      this.emailService.GuiMpoLeadDuyetDanhGiaGia(data.id)
    }

    // tạo quyền duyệt kết quả đánh giá giá

    return { message: 'Tạo đánh giá bảng chào giá thành công.' }
  }

  /** Duyệt đánh giá chào giá và điều kiện thương mại */

  /** Check quyền duyệt đánh giá điều kiện thương mại & giá cho gói thầu */
  async checkPermissionApproveBidPriceRate(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (bid) {
      if (bid.statusRatePrice === enumData.BidPriceRateStatus.DaDuyet.code || bid.statusRateTrade === enumData.BidTradeRateStatus.DaDuyet.code) {
        message = 'Gói thầu đã được xét duyệt đánh giá bảng chào giá, cơ cấu giá và điều kiện thương mại.'
      } else if (bid.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code && bid.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code) {
        // const flagPermission = await this.bidEmployeeAccessRepo.isMPOLeader(user, bidId)
        if (true) {
          result = true
        } else {
          message = 'Bạn không có quyền xét duyệt đánh giá bảng chào giá, cơ cấu giá và điều kiện thương mại.'
        }
      } else if (bid.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code) {
        message = 'Chưa có yêu cầu xét duyệt đánh giá điều kiện thương mại.'
      } else if (bid.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code) {
        message = 'Chưa có yêu cầu xét duyệt đánh giá bảng chào giá, cơ cấu giá.'
      }
    }

    return { hasPermission: result, message }
  }

  //#endregion

  getMaxOfArrayObj(data: any[]) {
    const maxL = data.reduce((max, game) => {
      const first = max.score
      const second = game.score
      if (first === second) {
        const firstValue = +max.value
        const secondValue = +game.value
        return firstValue > secondValue ? max : game
      } else return first > second ? max : game
    })

    return maxL
  }

  getMinOfArrayObj(data: any[]) {
    const minL = data.reduce((min, game) => {
      const firstValue = +min.value
      const secondValue = +game.value
      return firstValue < secondValue ? min : game
    })

    return minL
  }

  getCurrentRank(data: any[], bidSupplierId: string) {
    let rank = 0
    if (data.length > 0) {
      const listFilter = data.sort((a, b) => b.score - a.score)
      const index = listFilter.findIndex((p) => p.bidSupplierId === bidSupplierId)
      if (index) rank = index
    }

    return `${rank + 1} /${data.length}`
  }
  //#region Truy vấn thông tin gói thầu

  /** Danh sách gói thầu đã hoàn tất */
  async resultPagination(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    let whereCon: any = {
      parentId: IsNull(),
      status: enumData.BidStatus.HoanTat.code,
      employeeAccess: { employeeId: user.employeeId },
      companyId: user.companyId,
      isDeleted: false,
    }
    if (data.where.status?.length > 0) whereCon.status = In(data.where.status)
    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.publicDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.dateFrom) {
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}")`)
    } else if (data.where.dateTo) {
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.dateTo).format('YYYY-MM-DD')}")`)
    }

    if (data.where.name) {
      whereCon = [
        { ...whereCon, code: Like(`%${data.where.name}%`) },
        { ...whereCon, name: Like(`%${data.where.name}%`) },
      ]
    }
    if (data.where.serviceId) whereCon.childs = { serviceId: data.where.serviceId }

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      select: {
        id: true,
        code: true,
        name: true,
        status: true,
        createdAt: true,
      },
    })
    if (res[1] == 0) return res
    const lstId = res[0].map((c) => c.id)
    // const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
    //   where: { bidId: In(lstId), isDeleted: false },
    //   relations: { employee: true },
    //   select: {
    //     id: true,
    //     bidId: true,
    //     type: true,
    //     employeeId: true,
    //     employee: {
    //       id: true,
    //       name: true,
    //     },
    //   },
    // })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }

    for (const item of res[0]) {
      // const lstAccess = lstEmployeeAccess.filter((c) => c.bidId == item.id)

      // const objTech = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
      // item.techName = objTech?.__employee__?.name || ''
      // const objMpo = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
      // item.mpoName = objMpo?.__employee__?.name || ''

      // const lstAccessUser = lstAccess.filter((c) => c.employeeId === user.employeeId)
      // item.isMPO = lstAccessUser.some((c) => c.type === enumData.BidRuleType.MPO.code)
      // item.isMPOLeader = lstAccessUser.some((c) => c.type === enumData.BidRuleType.MPOLeader.code)
      // item.isTech = lstAccessUser.some((c) => c.type === enumData.BidRuleType.Tech.code)
      // item.isTechLeader = lstAccessUser.some((c) => c.type === enumData.BidRuleType.TechLeader.code)
      // item.isMember = lstAccessUser.some((c) => c.type === enumData.BidRuleType.Memmber.code)

      item.statusName = dicStatus[item.status]
    }

    return res
  }
  /** Tính điểm đánh giá kỹ thuật */
  calScoreTechItem(item: any, value: string) {
    let score = 0
    if (item.type === enumData.DataType.Number.code && value && value.trim() != '') {
      let temp = 0
      const x = +value
      // Tính theo chiều thuận
      if (item.isCalUp) {
        if (x >= item.percentRule) {
          temp = item.percent
        } else {
          temp = (x * item.percent) / item.percentRule // giá trị * tỉ trọng / điều kiện b
        }
      }
      // Tính theo chiều nghịch
      else {
        if (x <= item.percentRule) {
          temp = item.percent
        } else if (x >= item.percentDownRule) {
          temp = 0
        } else {
          temp = ((item.percentDownRule - x) * item.percent) / (item.percentDownRule - item.percentRule)
        }
      }

      if (isNaN(temp)) {
        score += 0
      } else if (!isFinite(temp)) {
        score += 0
      } else {
        score += temp
      }
    } else if (item.type === enumData.DataType.List.code) {
      const itemChosen = item.__bidTechListDetails__.find((p) => p.id === value)
      const tem = itemChosen ? itemChosen.value : 0
      const temp = (tem * item.percent) / 100
      if (isNaN(temp)) {
        score += 0
      } else if (!isFinite(temp)) {
        score += 0
      } else {
        score += temp
      }
    }

    if (isNaN(score) || !isFinite(score)) return 0

    return score
  }
  //#endregion
}
