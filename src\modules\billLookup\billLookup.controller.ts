import { Controller, UseGuards, Post, Body, Request } from '@nestjs/common'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { BillLookupCreateDto, BillLookupUpdateDto } from './dto'
import { BillLookupImportExcelDto } from './dto/billLookupExcel.dto'
import { Request as IRequest } from 'express'
import { BillLookupService } from './billLookup.service'
import { ApeAuthGuard, RoleGuard } from '../common/guards'

@ApiBearerAuth()
@ApiTags('BillLookup')
@Controller('bill_lookup')
export class BillLookupController {
  constructor(private readonly service: BillLookupService) {}

  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> da<PERSON> sách tra cứu hóa đơn' })
  @Post('find')
  public async find(@CurrentUser() user: UserDto) {
    return await this.service.find(user)
  }

  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách tra cứu hóa đơn phân trang' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto, @Request() req: IRequest) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo tra cứu hóa đơn' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: BillLookupCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Chỉnh sửa tra cứu hóa đơn' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: BillLookupUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Chi tiết tra cứu hóa đơn' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetail(user, data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật trạng thái isDelete' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active_status')
  public async updateActiveStatus(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateActiveStatus(user, data)
  }
}
