import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsArray, IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class POCreateExcelDto {
  @ApiPropertyOptional()
  @IsArray()
  lstDataTable1: lstDataTable1Dto[]

  @ApiPropertyOptional()
  @IsArray()
  lstDataTable2: lstDataTable2Dto[]

  @ApiPropertyOptional()
  @IsArray()
  lstDataTable3: lstDataTable3Dto[]
}

export class lstDataTable1Dto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  objectCode: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  serviceLevel1: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  supplierCode: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  title: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  zenId: string

  @ApiProperty()
  anotherRoleIds: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  confirmId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  cancelPOId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  poPaymentId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  editPOId: string

  @ApiProperty()
  @IsOptional()
  bidCode: string

  @ApiProperty()
  @IsNotEmpty()
  deliveryDate: Date

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  company: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  currency: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  email: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  phone: string

  @ApiPropertyOptional()
  @IsOptional()
  description: string

  @ApiPropertyOptional()
  @IsOptional()
  appendixContract: string

  @ApiPropertyOptional()
  @IsOptional()
  paymentPlanType: string

  @ApiPropertyOptional()
  @IsOptional()
  operator: string

  @ApiPropertyOptional()
  @IsOptional()
  type: string

  @ApiPropertyOptional()
  @IsOptional()
  region: string
}

export class lstDataTable2Dto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  zenListId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  serviceCode: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  serviceId: string

  @ApiPropertyOptional()
  @IsOptional()
  name: string

  @ApiPropertyOptional()
  @IsOptional()
  unit: string

  @ApiPropertyOptional()
  @IsOptional()
  price: number

  @ApiPropertyOptional()
  @IsOptional()
  quantity: number

  @ApiPropertyOptional()
  @IsOptional()
  money: number

  @ApiPropertyOptional()
  @IsOptional()
  descriptionProduct: string

  @ApiPropertyOptional()
  @IsOptional()
  note: string
}

export class lstDataTable3Dto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  zenListDetailId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  namePayment: string

  @ApiProperty()
  @IsNotEmpty()
  time: Date

  @ApiProperty()
  @IsNotEmpty()
  percent: number

  @ApiPropertyOptional()
  @IsOptional()
  note_percent: string
}
