import { MigrationInterface, QueryRunner } from "typeorm";

export class udpateTableSchemeColumnPurchasePlanId1673693529257 implements MigrationInterface {
    name = 'udpateTableSchemeColumnPurchasePlanId1673693529257'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`scheme\` DROP FOREIGN KEY \`FK_0977846fba96761c060fc5258e6\``);
        await queryRunner.query(`DROP INDEX \`REL_0977846fba96761c060fc5258e\` ON \`scheme\``);
        await queryRunner.query(`ALTER TABLE \`scheme\` DROP COLUMN \`purchasePlanId\``);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`scheme\` ADD \`purchasePlanId\` varchar(36) NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`REL_0977846fba96761c060fc5258e\` ON \`scheme\` (\`purchasePlanId\`)`);
        await queryRunner.query(`ALTER TABLE \`scheme\` ADD CONSTRAINT \`FK_0977846fba96761c060fc5258e6\` FOREIGN KEY (\`purchasePlanId\`) REFERENCES \`purchase_plan\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
