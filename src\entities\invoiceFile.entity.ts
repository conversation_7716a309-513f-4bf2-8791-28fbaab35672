import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { InvoiceEntity } from './invoice.entity'

/** File thanh toán */
@Entity('invoice_file')
export class InvoiceFileEntity extends BaseEntity {
  /** Đường dẫn file */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  fileUrl: string

  /** Tên file */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileName: string

  /** Data liên quan */
  @Column({
    type: 'text',
    nullable: true,
  })
  data: string

  /** Thanh toán */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  invoiceId: string
  /** Thanh toán */
  @ManyToOne(() => InvoiceEntity, (p) => p.files)
  @JoinColumn({ name: 'invoiceId', referencedColumnName: 'id' })
  invoice: Promise<InvoiceEntity>
}
