import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { EmployeeRepository, POProductRepository, PORepository, SettingStringRepository } from '../../repositories'
import { POController } from './po.controller'
import { POService } from './po.service'
import { EmailModule } from '../email/email.module'

@Module({
  imports: [EmailModule, TypeOrmExModule.forCustomRepository([PORepository, EmployeeRepository, SettingStringRepository, POProductRepository])],
  controllers: [POController],
  providers: [POService],
  exports: [POService],
})
export class POModule {}
