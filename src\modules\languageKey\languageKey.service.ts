import { Injectable } from '@nestjs/common'
import { In } from 'typeorm'
import { IMPORT_SUCCESS, UPDATE_SUCCESS, enumLanguage } from '../../constants'
import { UserDto } from '../../dto'
import { LanguageKeyEntity } from '../../entities'
import { coreHelper } from '../../helpers'
import { LanguageKeyRepository } from '../../repositories'
import { LanguageFilterDto, LanguageUpdateDto } from './dto'

/** Service xử lý các yêu cầu liên quan đến ngôn ngữ. */
@Injectable()
export class LanguageKeyService {
  constructor(private repo: LanguageKeyRepository) {}

  /** Cập nhật thông tin ngôn ngữ. */
  async updateData(user: UserDto, data: LanguageUpdateDto) {
    if (!data.languageType) throw new Error(`<PERSON>ui lòng chọn loại ngôn ngữ trước.`)
    if (!enumLanguage.LangugeType[data.languageType]) throw new Error(`<PERSON>ại ngôn ngữ không tồn tại!`)
    if (!data.items?.length) throw new Error(`Vui lòng sửa ít nhất 1 dòng dữ liệu!`)

    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(LanguageKeyEntity)
      const lstSave: LanguageKeyEntity[] = []
      for (const item of data.items) {
        const languageExist = await repo.findOne({
          where: { key: item.key, languageType: data.languageType, companyId: user.companyId },
          select: { id: true },
        })
        if (languageExist) {
          languageExist.value = item.value
          languageExist.description = item.description
          languageExist.updatedAt = new Date()
          languageExist.updatedBy = user.id
          lstSave.push(languageExist)
        } else {
          const languageNew = new LanguageKeyEntity()
          languageNew.value = item.value
          languageNew.description = item.description
          languageNew.path = item.path
          languageNew.companyId = user.companyId
          languageNew.createdAt = new Date()
          languageNew.createdBy = user.id
          languageNew.key = item.key
          languageNew.languageType = data.languageType
          lstSave.push(languageNew)
        }
      }
      await repo.save(lstSave, { chunk: 200 })
    })
    return { message: UPDATE_SUCCESS }
  }

  /** Lấy ds ngôn ngữ default */
  public async loadData(data: LanguageFilterDto) {
    if (!data.languageType) return []

    const res = coreHelper.convertObjToArray(enumLanguage.LanguageKey)
    const dicLanguageCore: any = {}
    {
      const lstLanguageCore = await this.repo.find({ where: { languageType: data.languageType } })
      lstLanguageCore.forEach((c) => (dicLanguageCore[c.key] = c))
    }

    // lượt qua từng key gán lại
    for (let item of res) {
      item.languageType = data.languageType
      item.typeName = enumLanguage.LangugeType[data.languageType]?.name
      // nếu có thiết lập core thì lấy, k thì lấy default enumLanguage
      const languageCore = dicLanguageCore[item.key]
      if (languageCore) {
        item.value = languageCore.value
        item.description = languageCore.description
      }
    }

    return res
  }

  /** Lấy ds ngôn ngữ default theo tenant */
  public async loadLanguageByTenant(user: UserDto, data: LanguageFilterDto) {
    if (!data.languageType) return []
    const where: any = { languageType: data.languageType }
    //nếu có tenant thì lấy theo tenant (admin)

    if (user.companyId) where.companyId = user.companyId
    const res = coreHelper.convertObjToArray(enumLanguage.LanguageKey)
    const dicLanguageCore: any = {}
    {
      const lstLanguageCore = await this.repo.find({ where: where })
      lstLanguageCore.forEach((c) => (dicLanguageCore[c.key] = c))
    }

    // lượt qua từng key gán lại
    for (let item of res) {
      item.languageType = data.languageType
      item.typeName = enumLanguage.LangugeType[data.languageType]?.name
      // nếu có thiết lập core thì lấy, k thì lấy default enumLanguage
      const languageCore = dicLanguageCore[item.key]
      if (languageCore) {
        item.value = languageCore.value
        item.description = languageCore.description
      }
    }

    return res
  }
  /** Lấy ds key ngôn ngữ default */
  public async loadListLanguageKey() {
    return coreHelper.convertObjToArray(enumLanguage.LanguageKey)
  }

  /** Cập nhật ngôn ngữ bằng excel. */
  async importExcel(user: UserDto, data: LanguageUpdateDto) {
    if (!data.languageType) throw new Error(`Vui lòng chọn Loại ngôn ngữ!`)
    if (!enumLanguage.LangugeType[data.languageType]) throw new Error(`Loại ngôn ngữ không tồn tại!`)
    if (!data.items?.length) throw new Error(`Vui lòng thêm ít nhất 1 dòng dữ liệu!`)

    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(LanguageKeyEntity)
      const lstSave: LanguageKeyEntity[] = []
      const dicKey: any = {}
      {
        const lstLanguageKey = await coreHelper.convertObjToArray(enumLanguage.LanguageKey)
        lstLanguageKey.forEach((c) => (dicKey[c.key] = c))
      }

      const lstKey = data.items.map((c) => c.key)
      const dicItem: any = {}
      {
        const lstLanguageExist = await repo.find({ where: { key: In(lstKey), languageType: data.languageType, companyId: user.companyId } })
        lstLanguageExist.forEach((c) => (dicItem[c.key] = c))
      }

      for (const [idx, item] of data.items.entries()) {
        if (!dicKey[item.key]) throw new Error(`[ Dòng ${idx + 3} - Mã không tồn tại ]`)
        if (dicKey[item.key].path != item.path) throw new Error(`[ Dòng ${idx + 3} - Đường dẫn không tương ứng với Mã ]`)

        const languageExist = dicItem[item.key]
        if (languageExist) {
          languageExist.value = item.value
          languageExist.description = item.description
          languageExist.updatedAt = new Date()
          languageExist.companyId = user.companyId
          languageExist.updatedBy = user.id
          lstSave.push(languageExist)
        } else {
          const languageNew = new LanguageKeyEntity()
          languageNew.value = item.value
          languageNew.description = item.description
          languageNew.path = item.path
          languageNew.createdAt = new Date()
          languageNew.companyId = user.companyId
          languageNew.createdBy = user.id
          languageNew.key = item.key
          languageNew.languageType = data.languageType
          lstSave.push(languageNew)
        }
      }

      await repo.save(lstSave, { chunk: 200 })
    })

    return { message: IMPORT_SUCCESS }
  }
}
