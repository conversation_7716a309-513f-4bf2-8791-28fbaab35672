import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { enumData } from '../constants'
import { BaseEntity } from './base.entity'
import { ContractEntity } from './contract.entity'
import { POEntity } from './po.entity'
import { InvoiceSuggestEntity } from './invoiceSuggest.entity'

/** Tiến độ thanh toán HĐ hoặc PO không theo HĐ */
@Entity({ name: 'payment_progress' })
export class PaymentProgressEntity extends BaseEntity {
  /** PO không theo HĐ */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.paymentPlan)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  /** HĐ */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  contractId: string
  @ManyToOne(() => ContractEntity, (p) => p.paymentPlan)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  /** Tên tiến độ */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  /** % tiến độ */
  @Column({
    default: 0,
  })
  percent: number

  /** Thời gian */
  @Column({
    nullable: false,
  })
  time: Date

  /** Ghi chú */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Số tiền cần thanh toán của tiến độ */
  @Column({
    type: 'bigint',
    default: 0,
  })
  money: number

  /** Số tiền đã ĐNTT của tiến độ */
  @Column({
    type: 'bigint',
    default: 0,
  })
  suggestPaid: number

  /** Trạng thái ĐNTT, enum PaymentProgressStatus */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
    default: enumData.PaymentProgressStatus.Unpaid.code,
  })
  paymentStatus: string

  /** Lịch sử ghi nhận */
  @Column({
    type: 'longtext',
    nullable: false,
  })
  historyNote: string

  /** Danh sách PO của Tiến độ thanh toán theo HĐ */
  @OneToMany(() => POEntity, (p) => p.contractPaymentPlan)
  pos: Promise<POEntity[]>

  /** Danh sách các ĐNTT của Tiến độ thanh toán */
  @OneToMany(() => InvoiceSuggestEntity, (p) => p.paymentPlan)
  invoiceSuggests: Promise<InvoiceSuggestEntity[]>
}
