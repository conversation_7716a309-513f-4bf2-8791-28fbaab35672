import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, Join<PERSON><PERSON>umn, OneToMany } from 'typeorm'
import { ServiceEntity } from './service.entity'
import { ServiceCapacityListDetailEntity } from './serviceCapacityListDetail.entity'
import { SupplierCapacityEntity } from './supplierCapacity.entity'

@Entity('service_capacity')
export class ServiceCapacityEntity extends BaseEntity {
  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  /** Cách tính điểm theo loại càng cao càng tốt */
  @Column({
    nullable: false,
    default: true,
  })
  isCalUp: boolean

  /** Thay đổi giá trị theo năm. Chỉ áp dụng cho string - number - file */
  @Column({
    nullable: false,
    default: false,
  })
  isChangeByYear: boolean

  @Column({
    nullable: false,
    default: 'string',
  })
  type: string

  /** % tỉ trọng */
  @Column({
    type: 'float',
    nullable: true,
  })
  percent: number

  /** % điều kiện đạt tỉ trọng */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  percentRule: number

  /** % điều kiện liệt tỉ trọng khi tính theo chiều giảm dần */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  percentDownRule: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Id của công thức cha */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  parentId: string
  /** Cha */
  @ManyToOne(() => ServiceCapacityEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: ServiceCapacityEntity

  /** Con - 1 công thức sẽ có thể có nhiều con */
  @OneToMany(() => ServiceCapacityEntity, (p) => p.parent)
  childs: Promise<ServiceCapacityEntity[]>

  /** Con - 1 công thức sẽ có thể có nhiều detail list */
  @OneToMany(() => ServiceCapacityListDetailEntity, (p) => p.serviceCapacity)
  serviceCapacityListDetails: Promise<ServiceCapacityListDetailEntity[]>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  serviceId: string
  @ManyToOne(() => ServiceEntity, (p) => p.capacities)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  // 1 dịch vụ sẽ có nhiều năng lực nhà cung cấp
  @OneToMany(() => SupplierCapacityEntity, (p) => p.serviceCapacity)
  supplierCapacities: Promise<SupplierCapacityEntity[]>
}
