import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import { BranchMemberService } from './branchMember.service'
import { BranchMemberController } from './branchMember.controller'
import { BranchMemberRepository } from '../../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BranchMemberRepository])],
  controllers: [BranchMemberController],
  providers: [BranchMemberService],
})
export class BranchMemberModule {}
