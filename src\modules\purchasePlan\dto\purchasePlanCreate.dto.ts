import { IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { PurchasePlanProgressDto } from './purchasePlanProgress.dto'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'

export class PurchasePlanCreateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiPropertyOptional()
  @IsOptional()
  code: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  departmentId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  serviceId: string

  @ApiPropertyOptional()
  @IsOptional()
  quantityInbound: number

  @ApiProperty()
  @IsNotEmpty()
  budget: number

  @ApiPropertyOptional()
  @IsOptional()
  description: string

  @ApiProperty()
  __progresss__: PurchasePlanProgressDto[]
}
