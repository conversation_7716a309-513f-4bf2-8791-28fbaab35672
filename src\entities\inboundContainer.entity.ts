import { BaseEntity } from './base.entity'
import { Entity, Column, JoinColumn, ManyToOne } from 'typeorm'
import { InboundEntity } from '.'

/** Thông tin inbound container*/
@Entity('inbound_container')
export class InboundContainerEntity extends BaseEntity {
  /** Inbound */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  inboundId: string
  @ManyToOne(() => InboundEntity, (p) => p.inboundContainers)
  @JoinColumn({ name: 'inboundId', referencedColumnName: 'id' })
  inbound: Promise<InboundEntity>

  /** Số container number */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  containerNumber: string

  /** Số seal number hãng tàu */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  sealNumber: string

  /** Số seal number */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  shipSealNumber: string

  /** Loại container */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  containerType: string

  /** <PERSON>ố lượng gói (kiện hàng) */
  @Column({
    nullable: true,
    type: 'int',
  })
  packageQuantity: number
}
