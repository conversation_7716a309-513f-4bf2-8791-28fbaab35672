import { <PERSON>N<PERSON>ber, IsString, IsOptional, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
export class CapacityDetailYearCreateDto {
  @ApiPropertyOptional()
  value: string

  @ApiPropertyOptional()
  year: string
}
export class CapacityDetailDto {
  @ApiProperty()
  @IsNumber()
  sort: number

  @ApiProperty()
  @IsString()
  type: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  comment: string

  @ApiPropertyOptional()
  @IsOptional()
  value?: string

  @ApiPropertyOptional()
  listDetailYear: CapacityDetailYearCreateDto[]

  @ApiProperty()
  @IsString()
  supplierCapacityId: string

  @ApiPropertyOptional()
  childs: CapacityDetailDto[]
}

export class SaveCapacityDetailDto {
  @ApiProperty()
  @IsString()
  expertiseId: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  comment: string

  /** Không có yêu cầu điều chỉnh */
  @ApiPropertyOptional()
  isNotRequireEdit: boolean

  @ApiPropertyOptional()
  @IsOptional()
  @ValidateNested()
  @Type(() => CapacityDetailDto)
  detail?: CapacityDetailDto[]
}
