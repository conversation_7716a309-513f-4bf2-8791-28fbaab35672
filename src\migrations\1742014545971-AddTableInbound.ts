import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddTableInbound1742014545971 implements MigrationInterface {
  name = 'AddTableInbound1742014545971'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`inbound_container\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`inboundId\` varchar(255) NULL, \`containerNumber\` varchar(100) NULL, \`sealNumber\` varchar(100) NULL, \`shipSealNumber\` varchar(100) NULL, \`containerType\` varchar(100) NULL, \`packageQuantity\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`inbound\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`status\` varchar(50) NULL, \`code\` varchar(50) NULL, \`name\` varchar(150) NULL, \`deliveryDate\` datetime NULL, \`dateArrivalWarehouse\` datetime NULL, \`employeeInchargeId\` varchar(255) NULL, \`poId\` varchar(255) NULL, \`isSupplierCreate\` tinyint NULL DEFAULT 0, \`dateArrivalPort\` datetime NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `ALTER TABLE \`inbound_container\` ADD CONSTRAINT \`FK_f23decf7082028bc724c552f689\` FOREIGN KEY (\`inboundId\`) REFERENCES \`inbound\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`inbound\` ADD CONSTRAINT \`FK_23f36c98b6495d315bb829f684e\` FOREIGN KEY (\`employeeInchargeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`inbound\` ADD CONSTRAINT \`FK_c728ef0f5f8d3bb3999240174f4\` FOREIGN KEY (\`poId\`) REFERENCES \`po\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`inbound\` DROP FOREIGN KEY \`FK_c728ef0f5f8d3bb3999240174f4\``)
    await queryRunner.query(`ALTER TABLE \`inbound\` DROP FOREIGN KEY \`FK_23f36c98b6495d315bb829f684e\``)
    await queryRunner.query(`ALTER TABLE \`inbound_container\` DROP FOREIGN KEY \`FK_f23decf7082028bc724c552f689\``)
    await queryRunner.query(`DROP TABLE \`inbound\``)
    await queryRunner.query(`DROP TABLE \`inbound_container\``)
  }
}
