import { Controller, UseGuards, Post, Body, Get, Param } from '@nestjs/common'
import { PrService } from './pr.service'
import { PrCreateDto } from './dto/prCreate.dto'
import { PrUpdateDto } from './dto/prUpdate.dto'
import { CurrentUser, Roles } from '../common/decorators'
import { PaginationDto, UserDto } from '../../dto'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { enumProject } from '../../constants'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Pr')
@Controller('pr')
export class PrController {
  constructor(private readonly service: PrService) {}

  @ApiOperation({ summary: 'Danh sách PR' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: { status?: string; isDeleted?: boolean }) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách PR đã duyệt theo phân quyền và có thể tạo thầu' })
  @Roles(enumProject.Features.BID_001.code, enumProject.Features.BID_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_pr_create_bid')
  public async loadPrCreateBid(@CurrentUser() user: UserDto, @Body() data: { bidId?: string }) {
    return await this.service.loadPrCreateBid(user, data)
  }

  @ApiOperation({ summary: 'Chi tiết PR' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard)
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetail(user, data)
  }

  @ApiOperation({ summary: 'Chi tiết Item trong PR' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_item_detail')
  public async findItemDetail(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findItemDetail(user, data)
  }

  @ApiOperation({ summary: 'Danh sách PR phân trang' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  // @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  // @UseGuards(ApeAuthGuard, RoleGuard)
  // @Post('create_data_list')
  // public async createDataListArise(@CurrentUser() user: UserDto, @Body() data: PrCreateDto[]) {
  //   return await this.service.createDataList_Arise(user, data)
  // }

  @ApiOperation({ summary: 'Tạo PR theo kế hoạch' })
  @Roles(enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data_plan')
  public async createDataPlan(@CurrentUser() user: UserDto, @Body() data: PrCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật PR theo kế hoạch' })
  @Roles(enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data_plan')
  public async updateDataPrPlan(@CurrentUser() user: UserDto, @Body() data: PrUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Tạo PR phát sinh' })
  @Roles(enumProject.Features.PR_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data_arise')
  public async createDataArise(@CurrentUser() user: UserDto, @Body() data: PrCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật PR phát sinh' })
  @Roles(enumProject.Features.PR_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data_arise')
  public async updateDataArise(@CurrentUser() user: UserDto, @Body() data: PrUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Duyệt PR' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('approve_pr')
  public async approvePR(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.approvePR(user, data)
  }

  @ApiOperation({ summary: 'Từ chối PR' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('cancel_pr')
  public async cancelPR(@CurrentUser() user: UserDto, @Body() data: { id: string; reason?: string }) {
    return await this.service.cancelPR(user, data)
  }

  @ApiOperation({ summary: 'Thực hiện PR' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('process_pr')
  public async processPR(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.processPR(user, data)
  }

  @ApiOperation({ summary: 'Đóng PR' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('close_pr')
  public async closePR(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.closePR(user, data)
  }

  //#region itemTech

  @ApiOperation({ summary: 'Tải template kỹ thuật từ Item' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('item/load_tech/:id')
  public async loadTech(@CurrentUser() user: UserDto, @Param('id') itemId: string) {
    return await this.service.loadTech(user, itemId)
  }

  @ApiOperation({ summary: 'Lấy template kỹ thuật của Item Pr' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('item/get_tech/:id')
  public async getTech(@CurrentUser() user: UserDto, @Param('id') itemId: string) {
    return await this.service.getTech(user, itemId)
  }

  @ApiOperation({ summary: 'Xóa tiêu chí kỹ thuật của Item Pr' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('item/tech_delete_data')
  public async techDeleteData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.techDeleteData(user, data.id)
  }

  @ApiOperation({ summary: 'Xóa tất cả tiêu chí kỹ thuật của Item Pr' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('item/tech_deleteall_data')
  public async techDeleteAllData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.techDeleteAllData(user, data.id)
  }

  @ApiOperation({ summary: 'Import tiêu chí kỹ thuật của Item Pr' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('item/tech_import/:itemId')
  public async tech_import(
    @CurrentUser() user: UserDto,
    @Param('itemId') itemId: string,
    @Body() data: { lstDataTable1: any[]; lstDataTable2: any[] },
  ) {
    return await this.service.tech_import(user, itemId, data)
  }

  @ApiOperation({ summary: 'Lấy tiêu chí kỹ thuật cấp 1 của Item Pr' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('item/tech_get_data/:id')
  public async techGetData(@CurrentUser() user: UserDto, @Param('id') itemId: string) {
    return await this.service.techGetData(user, itemId)
  }

  @ApiOperation({ summary: 'Tạo tiêu chí kỹ thuật của Item Pr' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('item/tech_create_data')
  public async techCreateData(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.techCreateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật tiêu chí kỹ thuật của Item Pr' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('item/tech_update_data')
  public async techUpdateData(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.techUpdateData(user, data)
  }

  @ApiOperation({ summary: 'Danh sách lựa chọn cho tiêu chí List' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('item/tech_listdetail_list/:id')
  public async itemTechListDetail_list(@CurrentUser() user: UserDto, @Param('id') id: string) {
    return await this.service.itemTechListDetail_list(user, id)
  }

  @ApiOperation({ summary: 'Tạo lựa chọn cho tiêu chí List' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('item/tech_listdetail_create_data')
  public async itemTechListDetail_create_data(@CurrentUser() user: UserDto, @Body() data: { itemTechId: string; value: number; name: string }) {
    return await this.service.itemTechListDetail_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật lựa chọn cho tiêu chí List' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('item/tech_listdetail_update_data')
  public async itemTechListDetail_update_data(
    @CurrentUser() user: UserDto,
    @Body() data: { id: string; itemTechId: string; value: number; name: string },
  ) {
    return await this.service.itemTechListDetail_update_data(user, data)
  }

  @ApiOperation({ summary: 'Xóa lựa chọn cho tiêu chí List' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('item/tech_listdetail_delete_data')
  public async itemTechListDetail_delete_data(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.itemTechListDetail_delete_data(user, data.id)
  }

  @ApiOperation({ summary: 'Yêu Cầu Kiểm Tra Lại' })
  @Roles(enumProject.Features.PR_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_recheck_pr')
  public async updateRechekPR(@CurrentUser() user: UserDto, @Body() data: { id: string; noteReCheck?: string }) {
    return await this.service.updateRechekPR(user, data)
  }

  //#endregion
}
