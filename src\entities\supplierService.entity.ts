import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { ServiceEntity } from './service.entity'
import { SupplierEntity } from './supplier.entity'
import { SupplierCapacityEntity } from './supplierCapacity.entity'
import { SupplierExpertiseEntity } from './supplierExpertise.entity'

@Entity('supplier_service')
export class SupplierServiceEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  score: number

  @Column({
    nullable: true,
  })
  approveDate: Date

  /** Trạng thái Item trong NCC enum SupplierServiceStatus */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
    default: 'ChuaThamDinh',
  })
  statusExpertise: string

  @Column({
    type: 'text',
    nullable: true,
  })
  comment: string

  @Column({
    type: 'text',
    nullable: true,
  })
  approverComment: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  serviceId: string
  @ManyToOne(() => ServiceEntity, (p) => p.supplierServices)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.supplierServices)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Tổng doanh thu (cộng dồn khi trúng thầu hoặc khởi tạo qua hàm gen lại cho data cũ) */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  totalPrice: number

  // Phân loại Doanh nghiệp
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  supplierType: string

  @OneToMany(() => SupplierCapacityEntity, (p) => p.supplierService)
  capacities: Promise<SupplierCapacityEntity[]>

  @OneToMany(() => SupplierExpertiseEntity, (p) => p.supplier)
  supplierExpertise: Promise<SupplierExpertiseEntity[]>
}
