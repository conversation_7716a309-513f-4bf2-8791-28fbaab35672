import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { HomePageController } from './homePage.controller'
import { HomePageService } from './homePage.service'
import {
  BannerClientRepository,
  LinkClientRepository,
  ServiceRepository,
  SettingStringClientRepository,
  SupplierNotifyRepository,
} from '../../repositories'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      BannerClientRepository,
      LinkClientRepository,
      SettingStringClientRepository,
      ServiceRepository,
      SupplierNotifyRepository,
    ]),
  ],
  controllers: [HomePageController],
  providers: [HomePageService],
})
export class HomePageModule {}
