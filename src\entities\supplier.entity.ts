import { BaseEntity } from './base.entity'
import { Entity, Column, Join<PERSON><PERSON>umn, OneToMany, OneToOne } from 'typeorm'
import { SupplierCapacityEntity } from './supplierCapacity.entity'
import { SupplierExpertiseEntity } from './supplierExpertise.entity'
import { UserEntity } from './user.entity'
import { BidSupplierEntity } from './bidSupplier.entity'
import { SupplierServiceEntity } from './supplierService.entity'
import { BidDealSupplierEntity } from './bidDealSupplier.entity'
import { BidAuctionSupplierEntity } from './bidAuctionSupplier.entity'
import { SupplierNotifyEntity } from './supplierNotify.entity'
import { ContractEntity } from './contract.entity'
import { POEntity } from './po.entity'
import { POHistoryEntity } from './poHistory.entity'
import { SupplierHistoryEntity } from './supplierHistory.entity'
import { OfferSupplierEntity } from './offerSupplier.entity'
import { BillEntity } from './bill.entity'
import { OfferDealSupplierEntity } from './offerDealSupplier.entity'
import { PaymentEntity } from './payment.entity'

/** Thông tin NCC */
@Entity('supplier')
export class SupplierEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  description: string

  /** Mã số doanh nghiệp */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  // Tên chính thức
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  // Tên giao dịch
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  dealName: string

  // Địa chỉ trụ sở
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  address: string

  // Địa chỉ giao dịch
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  dealAddress: string

  // Giấy phép đăng ký kinh doanh/Mã số thuế --> URL
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  fileMST: string

  // Người đại diện pháp luật
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  represen: string

  // Tên giám đốc
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  chief: string

  // Số tài khoản ngân hàng
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  bankNumber: string

  // Tên ngân hàng
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  bankname: string

  // Tên chi nhánh ngân hàng
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  bankBrand: string

  // File đính kèm thông báo mở tài khoản/mẫu 08 -->URL
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileAccount: string

  // Người liên hệ
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  contactName: string

  // Email
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  email: string

  // Điện thoại
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  phone: string

  // Năm thành lập công ty
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  createYear: string

  // Vốn điều lệ (tỷ đồng)
  @Column({
    type: 'float',
    nullable: false,
  })
  capital: number

  // Tài sản cố định (tỷ đồng)
  @Column({
    type: 'float',
    nullable: false,
  })
  assets: number

  // File đính kèm hóa đơn mẫu/phiếu thu/biên lai --> URL
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  fileBill: string

  // File đính kèm thông tin phát hành hóa đơn --> URL
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileInfoBill: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  userId: string
  @OneToOne(() => UserEntity, (p) => p.supplier, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId', referencedColumnName: 'id' })
  user: Promise<UserEntity>

  /** employeeId của người giới thiệu (không relation) */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  introducerId: string

  /** Các thông tin năng lực Item của NCC */
  @OneToMany(() => SupplierCapacityEntity, (p) => p.supplier)
  supplierCapacities: Promise<SupplierCapacityEntity[]>

  /** Các Item NCC */
  @OneToMany(() => SupplierServiceEntity, (p) => p.supplier)
  supplierServices: Promise<SupplierServiceEntity[]>

  /** Các lần thẩm định */
  @OneToMany(() => SupplierExpertiseEntity, (p) => p.supplier)
  supplierExpertise: Promise<SupplierExpertiseEntity[]>

  @OneToMany(() => SupplierNotifyEntity, (p) => p.supplier)
  supplierNotifys: Promise<SupplierNotifyEntity[]>

  /** Danh sách nhà cung cấp đấu thầu */
  @OneToMany(() => BidSupplierEntity, (p) => p.supplier)
  bidSupplier: Promise<BidSupplierEntity[]>

  /** Các lần tham gia đàm phán */
  @OneToMany(() => BidDealSupplierEntity, (p) => p.supplier)
  bidDealSupplier: Promise<BidDealSupplierEntity[]>

  /** Các lần tham gia đấu giá */
  @OneToMany(() => BidAuctionSupplierEntity, (p) => p.supplier)
  bidAuctionSupplier: Promise<BidAuctionSupplierEntity[]>

  /** Các HĐ */
  @OneToMany(() => ContractEntity, (p) => p.supplier)
  contracts: Promise<ContractEntity[]>

  /** Các PO */
  @OneToMany(() => POEntity, (p) => p.supplier)
  pos: Promise<POEntity[]>

  /** Lịch sử PO */
  @OneToMany(() => POHistoryEntity, (p) => p.supplier)
  poHistorys: Promise<POHistoryEntity[]>

  /** Lịch sử NCC */
  @OneToMany(() => SupplierHistoryEntity, (p) => p.supplier)
  supplierHistorys: Promise<SupplierHistoryEntity[]>

  @OneToMany(() => OfferSupplierEntity, (p) => p.offer)
  offerSupplier: Promise<OfferSupplierEntity[]>

  @OneToMany(() => BillEntity, (p) => p.supplier)
  bills: Promise<BillEntity[]>

  @OneToMany(() => OfferDealSupplierEntity, (p) => p.supplier)
  offerDealSupplier: Promise<OfferDealSupplierEntity[]>

  @OneToMany(() => PaymentEntity, (p) => p.supplier)
  payments: Promise<PaymentEntity[]>
}
