import { MigrationInterface, QueryRunner } from "typeorm";

export class udpateTableScheme1673691921805 implements MigrationInterface {
    name = 'udpateTableScheme1673691921805'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_c5ea8ff2353ce3a8adf1cb2caf\` ON \`pr\``);
        await queryRunner.query(`ALTER TABLE \`scheme\` DROP COLUMN \`totalMoney\``);
        await queryRunner.query(`ALTER TABLE \`scheme\` ADD \`totalMoney\` decimal(20,4) NOT NULL DEFAULT '0.0000'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`scheme\` DROP COLUMN \`totalMoney\``);
        await queryRunner.query(`ALTER TABLE \`scheme\` ADD \`totalMoney\` float(12) NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_c5ea8ff2353ce3a8adf1cb2caf\` ON \`pr\` (\`schemeId\`)`);
    }

}
