import { Body, Controller, Post, Request, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { InboundService } from '../services/inbound.service'
import { PaginationDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { InboundCreateDto, InboundLoadByMonthDto, InboundUpdateDto, InboundUpdateStatusDto } from '../dto'
import { Request as IRequest } from 'express'
import { ApeAuthGuard, RoleGuard } from '../../common/guards'

/** inbound */
@ApiBearerAuth()
@ApiTags('Inbound')
@UseGuards(ApeAuthGuard, RoleGuard)
@Controller('inbound')
export class InboundController {
  constructor(private readonly service: InboundService) {}

  @ApiOperation({ summary: 'Danh sách phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật' })
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: InboundUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật trạng thái' })
  @Post('update_status')
  public async updateStatus(@CurrentUser() user: UserDto, @Body() data: InboundUpdateStatusDto) {
    return await this.service.updateStatus(user, data)
  }

  @ApiOperation({ summary: 'Hàm tạo mới mã inbound' })
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: InboundCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Hàm xoá inbound với cascade' })
  @Post('delete_data')
  public async deleteData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.deleteData(user, data)
  }

  @ApiOperation({ summary: 'Lấy dữ liệu chi tiết' })
  @Post('load_detail')
  public async dataDetail(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.loadDetail(user, data)
  }

  @ApiOperation({ summary: 'Lấy List container' })
  @Post('load_list_container')
  public async loadListContainer(@CurrentUser() user: UserDto, @Body() data: { listInboundId: string[] }) {
    return await this.service.loadListContainer(user, data)
  }

  @ApiOperation({ summary: 'Lấy dữ liệu theo tháng' })
  @Post('load_data_by_month')
  public async loadByMonth(@CurrentUser() user: UserDto, @Body() data: InboundLoadByMonthDto) {
    return await this.service.loadDataByMonth(user, data)
  }

  @ApiOperation({ summary: 'Danh sách phân trang' })
  @Post('load_pagination_by_date')
  public async paginationByDate(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.loadPaginationByDate(user, data)
  }

  @ApiOperation({ summary: 'Load danh sách inbound' })
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: { listPoId?: string[] }) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Hàm hủy inbound' })
  @Post('update_cancel')
  public async updateCancel(@CurrentUser() user: UserDto, @Body() data: InboundUpdateStatusDto) {
    return await this.service.updateCancel(user, data)
  }
}
