import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class PaymentProgressUpdateOrder {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  poId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  invoiceNo: string

  @ApiProperty()
  link: string

  @ApiProperty()
  @IsNotEmpty()
  fileList: any[]
}
