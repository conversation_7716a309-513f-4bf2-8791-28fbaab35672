import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { WarehouseService } from './warehouse.service'
import { WarehouseController } from './warehouse.controller'
import { WarehouseRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([WarehouseRepository])],
  controllers: [WarehouseController],
  providers: [WarehouseService],
})
export class WarehouseModule {}
