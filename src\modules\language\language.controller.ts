import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { LanguageService } from './language.service'
import { CurrentUser } from '../common/decorators'
import { PaginationDto, UserDto } from '../../dto'
import { ApeAuthGuard } from '../common/guards'
import { LanguageCreateDto, LanguageUpdateDto } from './dto'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

/** Ngôn ngữ */
@ApiBearerAuth()
@ApiTags('Language')
@Controller('language')
export class LanguageController {
  constructor(private readonly service: LanguageService) {}

  @ApiOperation({ summary: 'Danh sách ngôn ngữ' })
  @Post('find')
  public async find(@Req() req: Request) {
    return await this.service.find(req)
  }

  @ApiOperation({ summary: 'Danh sách ngôn ngữ phân trang' })
  @UseGuards(ApeAuthGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(data, user)
  }

  @ApiOperation({ summary: 'Tạo ngôn ngữ' })
  @UseGuards(ApeAuthGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: LanguageCreateDto) {
    return await this.service.createData(data, user)
  }

  @ApiOperation({ summary: 'Cập nhật ngôn ngữ' })
  @UseGuards(ApeAuthGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: LanguageUpdateDto) {
    return await this.service.updateData(data, user)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động ngôn ngữ' })
  @UseGuards(ApeAuthGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateIsDelete(data.id, user)
  }
}
