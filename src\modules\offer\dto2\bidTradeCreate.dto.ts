import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsBoolean, IsNumber } from 'class-validator'

export class BidTradeCreateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isRequired: boolean

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  type: string

  @ApiPropertyOptional()
  sort: number

  @ApiPropertyOptional()
  percent: number
  @ApiPropertyOptional()
  percentRule: number

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  level: number

  @ApiPropertyOptional()
  description: string

  @ApiPropertyOptional()
  parentId: string

  @ApiPropertyOptional()
  scoreDLC: number
  @ApiPropertyOptional()
  requiredMin: number

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  bidId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isCalUp: boolean

  @ApiPropertyOptional()
  percentDownRule: number
}
