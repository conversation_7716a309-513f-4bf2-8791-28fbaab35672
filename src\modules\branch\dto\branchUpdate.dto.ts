import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class BranchUpdateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiPropertyOptional()
  description: string

  @ApiPropertyOptional()
  level: number

  @ApiPropertyOptional()
  @IsOptional()
  parent1?: string

  @ApiPropertyOptional()
  @IsOptional()
  parent2?: string

  @ApiProperty()
  @IsString()
  type: string
}
