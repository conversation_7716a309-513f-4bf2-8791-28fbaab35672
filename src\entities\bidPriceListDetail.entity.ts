import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>umn } from 'typeorm'
import { BidPriceEntity } from './bidPrice.entity'

@Entity('bid_price_list_detail')
export class BidPriceListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  @Column({
    length: 50,
    nullable: false,
  })
  type: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  value: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidPriceId: string
  @ManyToOne(() => BidPriceEntity, (p) => p.bidPriceListDetails)
  @JoinColumn({ name: 'bidPriceId', referencedColumnName: 'id' })
  bidPrice: Promise<BidPriceEntity>
}
