import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, ManyTo<PERSON>ne, JoinC<PERSON>um<PERSON>, OneToMany } from 'typeorm'
import { BidPriceEntity } from './bidPrice.entity'
import { BidDealSupplierEntity } from './bidDealSupplier.entity'

@Entity('bid_deal_supplier_price_value')
export class BidDealSupplierPriceValueEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: true,
  })
  score: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  value: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidDealSupplierId: string
  @ManyToOne(() => BidDealSupplierEntity, (p) => p.bidDealSupplierPriceValue)
  @JoinColumn({ name: 'bidDealSupplierId', referencedColumnName: 'id' })
  bidDealSupplier: Promise<BidDealSupplierEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidPriceId: string
  @ManyToOne(() => BidPriceEntity, (p) => p.bidDealSupplierPriceValue)
  @JoinColumn({ name: 'bidPriceId', referencedColumnName: 'id' })
  bidPrice: Promise<BidPriceEntity>
}
