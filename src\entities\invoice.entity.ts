import { <PERSON>umn, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { InvoiceFileEntity } from './invoiceFile.entity'
import { InvoiceSuggestEntity } from './invoiceSuggest.entity'

/** Quản lý thanh toán */
@Entity({ name: 'invoice' })
export class InvoiceEntity extends BaseEntity {
  /** Đề nghị thanh toán */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  invoiceSuggestId: string
  @ManyToOne(() => InvoiceSuggestEntity, (p) => p.invoices)
  @JoinColumn({ name: 'invoiceSuggestId', referencedColumnName: 'id' })
  invoiceSuggest: Promise<InvoiceSuggestEntity>

  /** Số tiền thanh toán */
  @Column({
    type: 'bigint',
    default: 0,
  })
  money: number

  /** Thời gian <PERSON>h toán */
  @Column({
    nullable: false,
  })
  invoiceDate: Date

  /** Ghi chú */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Nhân viên thanh toán */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  employeeName: string

  /** Các file liên quan */
  @OneToMany(() => InvoiceFileEntity, (p) => p.invoice)
  files: Promise<InvoiceFileEntity[]>
}
