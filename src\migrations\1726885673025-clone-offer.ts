import { MigrationInterface, QueryRunner } from "typeorm";

export class cloneOffer1726885673025 implements MigrationInterface {
    name = 'cloneOffer1726885673025'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`offer_supplier_price\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`offerSupplierId\` varchar(255) NULL, \`offerPriceId\` varchar(255) NULL, \`nameCol\` varchar(250) NULL, \`price\` bigint NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`offer_price\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`nameCol\` varchar(250) NULL, \`price\` bigint NULL, \`offerServiceId\` varchar(255) NULL, \`offerId\` varchar(255) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`offer_service\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`offerId\` varchar(255) NULL, \`serviceId\` varchar(255) NULL, \`serviceName\` varchar(250) NOT NULL, \`value\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`offer\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(50) NULL, \`name\` varchar(250) NULL, \`isShowClient\` tinyint NULL DEFAULT 0, \`status\` varchar(50) NULL, \`dateStart\` datetime NOT NULL, \`dateEnd\` datetime NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`offer_supplier\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`offerId\` varchar(255) NULL, \`supplierId\` varchar(255) NULL, \`supplierCode\` varchar(50) NULL, \`supplierName\` varchar(250) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD CONSTRAINT \`FK_105573cee204cbcbb11562df909\` FOREIGN KEY (\`offerSupplierId\`) REFERENCES \`offer_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD CONSTRAINT \`FK_be6b0ac54a29b8d5466cde600ab\` FOREIGN KEY (\`offerPriceId\`) REFERENCES \`offer_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD CONSTRAINT \`FK_5b6691af02ad6cfcdd127f9e37f\` FOREIGN KEY (\`offerServiceId\`) REFERENCES \`offer_service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD CONSTRAINT \`FK_4ff3c7ddf45b1a1c320904dc112\` FOREIGN KEY (\`offerId\`) REFERENCES \`offer\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD CONSTRAINT \`FK_1bb92b5a48afe1080abfad2447e\` FOREIGN KEY (\`offerId\`) REFERENCES \`offer\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD CONSTRAINT \`FK_4a954d26c27d94ddfb54d6a617e\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD CONSTRAINT \`FK_a679f77c259d43a6b2d50292070\` FOREIGN KEY (\`offerId\`) REFERENCES \`offer\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD CONSTRAINT \`FK_ea06e85d35bc1cd664e29b4a97c\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP FOREIGN KEY \`FK_ea06e85d35bc1cd664e29b4a97c\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP FOREIGN KEY \`FK_a679f77c259d43a6b2d50292070\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP FOREIGN KEY \`FK_4a954d26c27d94ddfb54d6a617e\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP FOREIGN KEY \`FK_1bb92b5a48afe1080abfad2447e\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP FOREIGN KEY \`FK_4ff3c7ddf45b1a1c320904dc112\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP FOREIGN KEY \`FK_5b6691af02ad6cfcdd127f9e37f\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP FOREIGN KEY \`FK_be6b0ac54a29b8d5466cde600ab\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP FOREIGN KEY \`FK_105573cee204cbcbb11562df909\``);
        await queryRunner.query(`DROP TABLE \`offer_supplier\``);
        await queryRunner.query(`DROP TABLE \`offer\``);
        await queryRunner.query(`DROP TABLE \`offer_service\``);
        await queryRunner.query(`DROP TABLE \`offer_price\``);
        await queryRunner.query(`DROP TABLE \`offer_supplier_price\``);
    }

}
