import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { OfferPriceEntity } from './offerPrice.entity'

@Entity('offer_price_list_detail')
export class OfferPriceListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  @Column({
    length: 50,
    nullable: false,
  })
  type: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  offerPriceId: string
  @ManyToOne(() => OfferPriceEntity, (p) => p.offerPriceListDetails)
  @JoinColumn({ name: 'offerPriceId', referencedColumnName: 'id' })
  offerPrice: Promise<OfferPriceEntity>
}
