variables:
  REPOSITORY_URL: 077293829360.dkr.ecr.ap-southeast-1.amazonaws.com
  REGION: ap-southeast-1
  APP_NAME: ape-pms-api
  APP_STAGING_NAME: ape-pms-staging-api
  APP_PROD_NAME: ape-pms-prod-api
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_CERTDIR: '/certs'
  DOCKER_TLS_VERIFY: 1
  DOCKER_CERT_PATH: '$DOCKER_TLS_CERTDIR/client'

stages:
  - build
  - deploy
  - buildstaging
  - deploystaging

build:
  stage: build
  image:
    name: amazon/aws-cli
    entrypoint: ['']
  services:
    - docker:19.03.13-dind
  environment: production
  before_script:
    - amazon-linux-extras install docker
  script:
    - echo "Building image..."
    - docker build -t $REPOSITORY_URL/${APP_NAME}:latest -f Dockerfile.develop .

    - echo "Login ECR image..."
    - aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin $REPOSITORY_URL

    - echo "Pushing image..."
    - docker push $REPOSITORY_URL/${APP_NAME}:latest
  only:
    - develop

deploy:
  stage: deploy
  image:
    name: bitnami/kubectl:1.21
    entrypoint: ['']
  environment: production
  script:
    - echo "set image deployment..."
    - kubectl delete -f deloyment.yaml
    - kubectl apply -f deloyment.yaml
    - kubectl delete -f deloyment-p.yaml
    - kubectl apply -f deloyment-p.yaml
  only:
    - develop

buildstaging:
  stage: buildstaging
  image:
    name: amazon/aws-cli
    entrypoint: ['']
  services:
    - docker:19.03.13-dind
  environment: production
  before_script:
    - amazon-linux-extras install docker
  script:
    - echo "Building image..."
    - docker build -t $REPOSITORY_URL/${APP_STAGING_NAME}:latest -f Dockerfile.staging .

    - echo "Login ECR image..."
    - aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin $REPOSITORY_URL

    - echo "Pushing image..."
    - docker push $REPOSITORY_URL/${APP_STAGING_NAME}:latest
  only:
    - staging

deploystaging:
  stage: deploystaging
  image:
    name: bitnami/kubectl:1.21
    entrypoint: ['']
  environment: production
  script:
    - echo "set image deployment..."
    - kubectl delete -f deloyment-staging.yaml
    - kubectl apply -f deloyment-staging.yaml
    - kubectl delete -f deloyment-p-staging.yaml
    - kubectl apply -f deloyment-p-staging.yaml
  only:
    - staging
