import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import {
  AsnRepository,
  BidRepository,
  ContractRepository,
  EmployeeRepository,
  InvoiceSuggestRepository,
  ObjectRepository,
  PORepository,
  PaymentProgressRepository,
  ServiceRepository,
} from '../../repositories'
import { ContractController, ContractHistoryController } from './contract.controller'
import { ContractService } from './contract.service'
@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      ContractRepository,
      EmployeeRepository,
      ObjectRepository,
      BidRepository,
      ServiceRepository,
      InvoiceSuggestRepository,
      PaymentProgressRepository,
      PORepository,
      AsnRepository,
    ]),
  ],
  controllers: [ContractController, ContractHistoryController],
  providers: [ContractService],
  exports: [ContractService],
})
export class ContractModule {}
