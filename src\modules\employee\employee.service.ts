import { Injectable, UnauthorizedException } from '@nestjs/common'
import { Like, In } from 'typeorm'
import {
  enumData,
  ERROR_CODE_TAKEN,
  ERROR_NOT_FOUND_DATA,
  ERROR_USERNAME_TAKEN,
  PWD_SALT_ROUNDS,
  UPDATE_ACTIVE_SUCCESS,
  UPDATE_SUCCESS,
} from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { DepartmentEntity, EmployeeEntity, UserEntity } from '../../entities'
import { BranchRepository, DepartmentRepository, EmployeeRepository, UserRepository } from '../../repositories'
import { EmployeeCreateDto, EmployeeCreateExcelDto, EmployeeUpdateDto, EmployeeUpdatePasswordDto } from './dto'
import { Request as IRequest } from 'express'
import { ape<PERSON>uth<PERSON><PERSON><PERSON><PERSON><PERSON>, coreHelper, enumApeAuth } from '../../helpers'
import { hash } from 'bcrypt'

@Injectable()
export class EmployeeService {
  constructor(
    private readonly repo: EmployeeRepository,
    private userRepo: UserRepository,
    private departmentRepository: DepartmentRepository,
    private branchRepository: BranchRepository,
  ) {}

  /** Lấy ds nhân viên, isGet: lấy thêm thông tin */
  public async find(user: UserDto, data: { departmentId?: string; branchId?: string; bidId?: string; isGet?: boolean }) {
    const whereCon: any = { companyId: user.companyId, isDeleted: false }
    if (data.departmentId) whereCon.departmentId = data.departmentId
    if (data.branchId) whereCon.department = { branchId: data.branchId }
    if (data.bidId) whereCon.bidAccess = { bidId: data.bidId }
    const isGet = data.isGet || false
    const res: any[] = await this.repo.find({
      where: whereCon,
      relations: { department: { branch: isGet } },
      order: { name: 'ASC' },
      select: {
        id: true,
        name: true,
        email: isGet,
        department: { id: true, name: true, branch: isGet ? { id: true, name: true } : false },
        userId: true,
      },
    })

    for (const item of res) {
      if (item.__department__) {
        item.departmentName = item.__department__.name
        if (item.__department__.__branch__) {
          item.branchName = item.__department__.__branch__.name
        }
      }
      delete item.__department__
    }

    return res
  }

  public async createData(user: UserDto, data: EmployeeCreateDto, req: IRequest) {
    const objCheckCode = await this.repo.findOne({ where: { code: data.code, companyId: user.companyId }, select: { id: true } })
    if (objCheckCode) throw new Error(ERROR_CODE_TAKEN)

    // create user
    const objCheckUsername = await this.userRepo.findOne({ where: { username: data.username, companyId: user.companyId }, select: { id: true } })
    if (objCheckUsername) throw new Error(ERROR_USERNAME_TAKEN)

    return await this.repo.manager.transaction(async (manager) => {
      const employeeRepo = manager.getRepository(EmployeeEntity)
      const userRepo = manager.getRepository(UserEntity)

      const isProduct = process.env.IS_PRODUCT == 'true'
      if (isProduct) {
        if (!req || !req.headers || !req.headers.authorization) throw new UnauthorizedException('Không có quyền truy cập! (code: BEARER_TOKEN_ERROR)')
        await apeAuthApiHelper.createUserPMS(
          req.headers.authorization,
          user.companyId,
          enumApeAuth.UserType.CompanyPackageEmployee,
          data.username,
          data.password,
        )
      }

      const userNew = new UserEntity()
      userNew.companyId = user.companyId
      userNew.createdBy = user.id
      userNew.username = data.username
      if (!isProduct) userNew.password = data.password
      userNew.type = enumData.UserType.Employee.code
      const userCreated = await userRepo.save(userNew)

      // create employee
      const employeeNew = new EmployeeEntity()
      employeeNew.companyId = user.companyId
      employeeNew.createdBy = user.id
      employeeNew.code = data.code
      employeeNew.name = data.name
      employeeNew.email = data.email
      employeeNew.departmentId = data.departmentId
      employeeNew.branchId = data.branchId
      employeeNew.description = data.description
      employeeNew.userId = userCreated.id
      const createdEntity = await employeeRepo.save(employeeNew)

      await userRepo.update(userCreated.id, { employeeId: createdEntity.id })

      return createdEntity
    })
  }

  public async updateData(user: UserDto, data: EmployeeUpdateDto) {
    return await this.repo.manager.transaction(async (manager) => {
      const employeeRepo = manager.getRepository(EmployeeEntity)
      const departmentRepo = manager.getRepository(DepartmentEntity)

      const entity = await employeeRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      // Nếu thay đổi mã
      if (data.code != entity.code) {
        const objCheckCode = await employeeRepo.findOne({ where: { code: data.code, companyId: user.companyId }, select: { id: true } })
        if (objCheckCode) throw new Error(ERROR_CODE_TAKEN)
      }

      // Nếu thay đổi phòng ban
      if (data.departmentId != entity.departmentId || !entity.branchId) {
        const department = await departmentRepo.findOne({ where: { id: data.departmentId, companyId: user.companyId } })
        if (!department) throw new Error('Phòng ban không còn tồn tại')
        entity.departmentId = data.departmentId
        entity.branchId = department.branchId
      }

      entity.name = data.name
      entity.email = data.email
      entity.code = data.code
      entity.description = data.description
      entity.updatedBy = user.id
      const updatedEntity = await employeeRepo.save(entity)

      return updatedEntity
    })
  }

  public async createDataExcel(user: UserDto, lstData: EmployeeCreateExcelDto[], req: IRequest) {
    //check xem data có trùng nhau hoặc không có không
    if (lstData.length < 1) throw new Error('Vui lòng điền ít nhất 1 dòng thông tin!')
    {
      const dublicateArayCode = coreHelper.findDuplicates(lstData, 'code')
      if (dublicateArayCode.length > 0) throw new Error(`Danh sách mã nhân viên trùng nhau ${dublicateArayCode.toString()}`)
    }

    //lọc lấy danh sách code của nhân viên
    const codes = lstData.map(function (obj) {
      return obj.code
    })

    // tìm ra những mã bị trùng dưới data base
    const dupCount = await this.repo.find({ where: { code: In(codes) } })
    if (dupCount.length > 0) {
      const dupCode = lstData.map(function (obj) {
        return obj.code
      })
      throw new Error(`Danh sách mã nhân viên trùng nhau [${dupCode.toString()}]`)
    }
    // danh sách phòng ban
    const dictDepartment: any = {}
    let lstDepartment: any = []
    {
      lstDepartment = await this.departmentRepository.find({ where: { isDeleted: false } })
      lstDepartment.forEach((c) => (dictDepartment[c.code] = c.id))
    }

    // danh sách chi nhánh
    const dicBranch: any = {}
    let lstBranch: any = []
    {
      lstBranch = await this.branchRepository.find({ where: { isDeleted: false } })
      lstBranch.forEach((c) => (dicBranch[c.code] = c.id))
    }

    return await this.repo.manager.transaction(async (manager) => {
      const employeeRepo = manager.getRepository(EmployeeEntity)
      const userRepo = manager.getRepository(UserEntity)
      for (const data of lstData) {
        if (data.departmentCode) {
          if (!dictDepartment[data.departmentCode]) throw new Error(`Mã phòng ban [${data.departmentCode}] không có trong hệ thống`)
        }

        if (data.branchCode) {
          if (!dicBranch[data.branchCode]) throw new Error(`Mã chi nhánh [${data.branchCode}] không có trong hệ thống`)
        }

        const isProduct = process.env.IS_PRODUCT == 'true'
        if (isProduct) {
          if (!req || !req.headers || !req.headers.authorization)
            throw new UnauthorizedException('Không có quyền truy cập! (code: BEARER_TOKEN_ERROR)')
          await apeAuthApiHelper.createUserPMS(
            req.headers.authorization,
            user.companyId,
            enumApeAuth.UserType.CompanyPackageEmployee,
            data.username,
            data.password,
          )
        }
        const userNew = new UserEntity()
        userNew.companyId = user.companyId
        userNew.createdBy = user.id
        userNew.username = data.username
        if (!isProduct) userNew.password = data.password
        userNew.type = enumData.UserType.Employee.code
        const userCreated = await userRepo.save(userNew)

        // create employee
        const employeeNew = new EmployeeEntity()
        employeeNew.companyId = user.companyId
        employeeNew.createdBy = user.id
        employeeNew.code = data.code
        employeeNew.name = data.name
        employeeNew.email = data.email
        employeeNew.branchId = dicBranch[data.branchCode]
        employeeNew.departmentId = dictDepartment[data.departmentCode]
        employeeNew.description = data.description
        employeeNew.userId = userCreated.id
        const createdEntity = await employeeRepo.save(employeeNew)
        const employeeId = createdEntity.id
        await userRepo.update(userCreated.id, { employeeId: employeeId })
      }
    })
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { companyId: user.companyId }
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.departmentId) whereCon.departmentId = data.where.departmentId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { department: true, user: true },
      order: { createdAt: 'DESC' },
    })

    for (const item of res[0]) {
      item.departmentName = item.__department__?.name || ''
      item.username = item.__user__?.username || ''

      delete item.__department__
      delete item.__user__
    }

    return res
  }

  public async updateIsDelete(user: UserDto, data: { id: string }, req: IRequest) {
    const entity: any = await this.repo.findOne({
      where: { id: data.id, companyId: user.companyId },
      relations: { user: true },
      select: { id: true, isDeleted: true, userId: true, user: { id: true, username: true, type: true, isDeleted: true } },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (!entity.userId) throw new Error('Nhân viên chưa có tài khoản!')

    const userEmployee = entity.__user__
    const newIsDeleted = !entity.isDeleted

    const isProduct = process.env.IS_PRODUCT == 'true'
    if (isProduct) {
      if (!req || !req.headers || !req.headers.authorization) throw new UnauthorizedException('Không có quyền truy cập! (code: BEARER_TOKEN_ERROR)')
      await apeAuthApiHelper.updateStatusUser(req.headers.authorization, {
        companyId: user.companyId,
        type: enumApeAuth.UserType.CompanyPackageEmployee,
        username: userEmployee.username,
        isDeleted: newIsDeleted,
      })
    }
    await this.repo.update(entity.id, { isDeleted: newIsDeleted, updatedBy: user.id })
    await this.userRepo.update(entity.userId, { isDeleted: newIsDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async updatePassword(user: UserDto, data: EmployeeUpdatePasswordDto) {
    const isProduct = process.env.IS_PRODUCT == 'true'
    const employeeEntity: any = await this.repo.findOne({
      where: { id: data.id, companyId: user.companyId },
      relations: { user: isProduct },
      select: { id: true, userId: true, user: isProduct ? { id: true, username: true } : false },
    })
    if (!employeeEntity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (!employeeEntity.userId) throw new Error('Nhân viên chưa thiết lập user')

    // Update user bên auth
    if (isProduct) {
      const userEntity = employeeEntity.__user__
      await apeAuthApiHelper.updatePasswordSecret(user.companyId, userEntity.username, data.newPassword)
    } else {
      const hashedPassword = await hash(data.newPassword, PWD_SALT_ROUNDS)
      await this.userRepo.update(employeeEntity.userId, { password: hashedPassword, updatedBy: user.id })
    }

    return { message: UPDATE_SUCCESS }
  }
}
