import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm/typeorm-ex.module'
import { LanguageKeyController } from './languageKey.controller'
import { LanguageKeyService } from './languageKey.service'
import { LanguageKeyRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([LanguageKeyRepository])],
  providers: [LanguageKeyService],
  controllers: [LanguageKeyController],
})
export class LanguageKeyModule {}
