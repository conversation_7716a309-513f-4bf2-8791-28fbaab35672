import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsArray, IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { PoProductDto } from './poProduct.dto'
import { PoProgressDto } from './poProgress.dto'

export class POCreateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  objectId: string

  @ApiProperty()
  @IsOptional()
  serviceLevel1: string

  @ApiProperty()
  @IsNotEmpty()
  title: string

  @ApiProperty()
  @IsArray()
  @IsNotEmpty()
  anotherRoleIds: string[]

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  confirmId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  cancelId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  poPaymentId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  editPOId: string

  @ApiProperty()
  @IsNotEmpty()
  deliveryDate: Date

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  paymentPlanType: string

  @ApiProperty()
  @IsOptional()
  contractId: string

  @ApiProperty()
  @IsOptional()
  contractPaymentPlanId: string

  @ApiProperty()
  @IsOptional()
  supplierId: string

  @ApiProperty()
  @IsOptional()
  bidId: string

  @ApiProperty()
  @IsOptional()
  prId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  company: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  currency: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  email: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  phone: string

  @ApiPropertyOptional()
  @IsOptional()
  description: string

  @ApiPropertyOptional()
  @IsOptional()
  operator: string

  @ApiPropertyOptional()
  @IsOptional()
  type: string

  @ApiPropertyOptional()
  @IsOptional()
  region: string

  @ApiPropertyOptional()
  @IsArray()
  lstProduct: PoProductDto[]

  @ApiPropertyOptional()
  @IsArray()
  lstPaymentProgress: PoProgressDto[]
}
