import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { SupplierEntity } from './supplier.entity'
import { AuctionEntity } from './auction.entity'

/** Các NCC tham gia đấu giá */
@Entity('auction_supplier')
export class AuctionSupplierEntity extends BaseEntity {
  /** Lần đấu giá */
  @Column({ type: 'varchar', length: 36, nullable: false })
  auctionId: string
  @ManyToOne(() => AuctionEntity, (p) => p.auctionSuppliers)
  @JoinColumn({ name: 'auctionId', referencedColumnName: 'id' })
  auction: Promise<AuctionEntity>

  /** NCC */
  @Column({ type: 'varchar', length: 36, nullable: false })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.bidSupplier)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Gi<PERSON> đấu trước đó */
  @Column({ type: 'bigint', nullable: true })
  submitPriceOld?: number

  /** Giá đấu */
  @Column({ type: 'bigint', nullable: true })
  submitPrice?: number

  /** Ngày nộp đấu giá */
  @Column({ nullable: true })
  submitDate?: Date

  /** Thứ hạng */
  @Column({ type: 'int', nullable: true })
  rank?: number
}
