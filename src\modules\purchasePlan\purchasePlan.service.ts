import { Injectable } from '@nestjs/common'
import { PurchasePlanCreateDto, PurchasePlanUpdateDto } from './dto'
import { PaginationDto, UserDto } from '../../dto'
import { PurchasePlanRepository } from '../../repositories'
import { enumData, ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS } from '../../constants'
import { Equal, In, IsNull, Like, Not } from 'typeorm'
import {
  EmailTemplateEntity,
  EmployeeWarningEntity,
  PurchasePlanEntity,
  PurchasePlanHistoryEntity,
  PurchasePlanProgressEntity,
  UserEntity,
} from '../../entities'
import { ConflictException } from '@nestjs/common/exceptions'
import { coreHelper } from '../../helpers'

@Injectable()
export class PurchasePlanService {
  constructor(private readonly repo: PurchasePlanRepository) {}

  public async findDetailEdit(user: UserDto, data: { id: string }) {
    const res: any = await this.repo.findOne({
      where: { id: data.id, companyId: user.companyId },
      relations: { service: { parent: { parent: { parent: true } } }, progresss: true },
      order: { progresss: { sort: 'ASC', createdAt: 'ASC' } },
    })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    return res
  }

  public async findDetail(user: UserDto, data: { id: string }) {
    const res: any = await this.repo.findOne({
      where: { id: data.id, companyId: user.companyId },
      relations: { histories: true, department: true, service: true, progresss: true },
      order: { progresss: { sort: 'ASC', createdAt: 'ASC' }, histories: { createdAt: 'ASC' } },
    })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    if (res.departmentId) res.departmentName = res.__department__.name
    delete res.__department__
    if (res.serviceId) res.itemName = res.__service__.code + ' - ' + res.__service__.name
    delete res.__service__

    return res
  }

  public async find(user: UserDto, data: {}) {
    const whereCon: any = { companyId: user.companyId, isDeleted: false }
    const res: any = await this.repo.find({ where: whereCon, order: { code: 'ASC' }, relations: { service: true } })

    for (const item of res) {
      item.serviceName = item.__service__?.name
      delete item.__service__
    }
    return res
  }

  public async createDataList(user: UserDto, lstData: PurchasePlanCreateDto[]) {
    return await this.repo.manager.transaction(async (manager) => {
      try {
        for (const data of lstData) {
          const objCheckCode = await manager
            .getRepository(PurchasePlanEntity)
            .findOne({ where: { code: data.code, companyId: user.companyId, isDeleted: false }, select: { id: true } })
          if (objCheckCode) throw new ConflictException(ERROR_CODE_TAKEN)
          const sum = data.__progresss__.reduce((sum: any, current: { quantity: any }) => +sum + +current.quantity, 0)
          const master = new PurchasePlanEntity()
          master.companyId = user.companyId
          master.createdBy = user.id
          master.name = data.name
          master.code = data.code
          master.departmentId = data.departmentId
          master.serviceId = data.serviceId
          master.quantityInbound = data.quantityInbound
          master.quantity = sum
          master.description = data.description
          master.budget = data.budget
          const masterEntity = await manager.getRepository(PurchasePlanEntity).save(master)
          for (let e of data.__progresss__) {
            const child = new PurchasePlanProgressEntity()
            child.companyId = user.companyId
            child.createdBy = user.id
            child.purchasePlanId = masterEntity.id
            child.sort = e.sort
            child.progressDate = e.progressDate
            child.quantity = e.quantity
            child.description = e.description
            await manager.getRepository(PurchasePlanProgressEntity).save(child)
          }

          //#region history

          const historyNew = new PurchasePlanHistoryEntity()
          historyNew.companyId = user.companyId
          historyNew.createdBy = user.id
          historyNew.purchasePlanId = masterEntity.id
          historyNew.description = 'Import kế hoạch mua hàng'
          historyNew.createdByName = user.username
          await manager.getRepository(PurchasePlanHistoryEntity).save(historyNew)
        }
        //#endregion
        return { message: 'Tạo mới thành công' }
      } catch (error) {
        throw error
      }
    })
  }

  public async createData(user: UserDto, data: PurchasePlanCreateDto) {
    const objCheckCode = await this.repo.findOne({ where: { code: data.code, companyId: user.companyId }, select: { id: true } })
    if (objCheckCode) throw new ConflictException(ERROR_CODE_TAKEN)

    return this.repo.manager.transaction(async (manager) => {
      try {
        const sum = data.__progresss__.reduce((sum: any, current: { quantity: any }) => +sum + +current.quantity, 0)
        const master = new PurchasePlanEntity()
        master.companyId = user.companyId
        master.createdBy = user.id
        master.name = data.name
        master.code = data.code
        master.departmentId = data.departmentId
        master.serviceId = data.serviceId
        master.quantity = sum
        master.quantityInbound = data.quantityInbound
        master.description = data.description
        master.budget = data.budget
        master.budgetRemaining = data.budget
        const masterEntity = await manager.getRepository(PurchasePlanEntity).save(master)

        for (let e of data.__progresss__) {
          const child = new PurchasePlanProgressEntity()
          child.companyId = user.companyId
          child.createdBy = user.id
          child.purchasePlanId = masterEntity.id
          child.sort = e.sort
          child.progressDate = e.progressDate
          child.quantity = e.quantity
          child.description = e.description
          await manager.getRepository(PurchasePlanProgressEntity).save(child)
        }

        //#region history

        const historyNew = new PurchasePlanHistoryEntity()
        historyNew.companyId = user.companyId
        historyNew.createdBy = user.id
        historyNew.purchasePlanId = masterEntity.id
        historyNew.description = 'Thêm mới kế hoạch mua hàng'
        historyNew.createdByName = user.username
        await manager.getRepository(PurchasePlanHistoryEntity).save(historyNew)

        //#endregion

        return { message: 'Tạo mới thành công', masterEntity }
      } catch (error) {
        throw error
      }
    })
  }

  public async updateData(user: UserDto, data: PurchasePlanUpdateDto) {
    const master = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!master) throw new Error(ERROR_NOT_FOUND_DATA)

    const objCheckCode = await this.repo.findOne({
      where: { code: data.code, companyId: user.companyId, id: Not(Equal(master.id)) },
      select: { id: true },
    })
    if (objCheckCode) throw new ConflictException(ERROR_CODE_TAKEN)
    const sum = data.__progresss__.reduce((sum: any, current: { quantity: any }) => +sum + +current.quantity, 0)
    master.name = data.name
    master.code = data.code
    master.departmentId = data.departmentId
    master.serviceId = data.serviceId
    master.quantity = sum
    master.quantityInbound = data.quantityInbound
    master.description = data.description
    master.budget = data.budget
    master.updatedBy = user.id

    return this.repo.manager.transaction(async (manager) => {
      try {
        const masterEntity = await manager.getRepository(PurchasePlanEntity).save(master)
        await manager.getRepository(PurchasePlanProgressEntity).delete({ purchasePlanId: master.id })

        for (let e of data.__progresss__) {
          const child = new PurchasePlanProgressEntity()
          child.companyId = user.companyId
          child.createdBy = user.id
          child.purchasePlanId = masterEntity.id
          child.sort = e.sort
          child.progressDate = e.progressDate
          child.quantity = e.quantity
          child.description = e.description
          await manager.getRepository(PurchasePlanProgressEntity).save(child)
        }

        //#region history

        const historyNew = new PurchasePlanHistoryEntity()
        historyNew.companyId = user.companyId
        historyNew.createdBy = user.id
        historyNew.purchasePlanId = masterEntity.id
        historyNew.description = 'Chỉnh sửa kế hoạch mua hàng'
        historyNew.createdByName = user.username
        await manager.getRepository(PurchasePlanHistoryEntity).save(historyNew)

        //#endregion

        return { message: UPDATE_ACTIVE_SUCCESS, masterEntity }
      } catch (error) {
        throw error
      }
    })
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { companyId: user.companyId }
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.departmentId) whereCon.departmentId = data.where.departmentId
    if (data.where.serviceId) whereCon.serviceId = data.where.serviceId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { histories: true, department: true, service: true },
      order: { createdAt: 'DESC' },
    })

    for (const item of res[0]) {
      if (item.departmentId) item.departmentName = item.__department__.name || ''
      if (item.serviceId) item.itemName = item.__service__.code + ' - ' + item.__service__.name

      delete item.__department__
      delete item.__service__
    }

    return res
  }

  public async updateIsDelete(user: UserDto, data: { id: string }) {
    return this.repo.manager.transaction(async (manager) => {
      const entity = await manager.getRepository(PurchasePlanEntity).findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
      entity.isDeleted = !entity.isDeleted
      entity.updatedBy = user.id
      let masterEntity = await manager.getRepository(PurchasePlanEntity).save(entity)

      //#region history
      const historyNew = new PurchasePlanHistoryEntity()
      historyNew.companyId = user.companyId
      historyNew.createdBy = user.id
      historyNew.purchasePlanId = masterEntity.id
      if (masterEntity.isDeleted === true) {
        historyNew.description = 'Ngưng hoạt động kế hoạch mua hàng'
      } else {
        historyNew.description = 'Khôi phục kế hoạch mua hàng'
      }
      historyNew.createdByName = user.username
      await manager.getRepository(PurchasePlanHistoryEntity).save(historyNew)

      //#endregion

      return { message: UPDATE_ACTIVE_SUCCESS, entity }
    })
  }

  /** Cảnh báo khi mua quá hạn ngân sách cho kế hoạch */
  public async autoCreateWarningOverBudget() {
    return this.repo.manager.transaction(async (manager) => {
      try {
        const warningType = enumData.WarningType.Purchare_Plan_Over_Budget
        const dataType = enumData.DataWarningType.Purchase_Plan.code
        await manager.getRepository(EmployeeWarningEntity).delete({
          dataId: Not(IsNull()),
          dataType: dataType,
          warningType: warningType.code,
        })

        const lstPP = await manager.getRepository(PurchasePlanEntity).find({
          where: { isDeleted: false },
        })
        if (lstPP.length > 0) {
          let html = warningType.default
          let subject = warningType.name
          const template = await manager.getRepository(EmailTemplateEntity).findOne({ where: { code: warningType.code, isDeleted: false } })
          if (template) {
            html = template.description
            subject = template.name
          }

          let lstUserId = lstPP.map((s: any) => s.createdBy)
          lstUserId = Array.from(new Set(lstUserId))
          const lstUser = await manager.getRepository(UserEntity).find({
            where: { id: In(lstUserId), isDeleted: false },
            relations: ['employee'],
          })

          let lstPPId = lstPP.map((s: any) => s.id)
          lstPPId = Array.from(new Set(lstPPId))

          for await (const pp of lstPP) {
            let createdBy = lstUser.find((s: any) => s.id == pp.createdBy)
            if (createdBy && createdBy.employeeId) {
              let emp = await createdBy.employee

              let moneyNow = 0
              let moneyPlan = pp.budget ?? 0

              if (moneyNow > moneyPlan) {
                const subject_text = coreHelper.stringInject(subject, [pp.code])
                // let link = `&nbsp; <button onclick="showDataDetail('${dataType}', '${pp.id}')">Xem Chi Tiết</button>`

                let content = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [
                  emp.name,
                  pp.name,
                  pp.code,
                  moneyPlan.toLocaleString(undefined, { minimumFractionDigits: 0 }),
                  moneyNow.toLocaleString(undefined, { minimumFractionDigits: 0 }),
                ])

                const w1 = new EmployeeWarningEntity()
                w1.warningType = warningType.code
                w1.dataType = dataType
                w1.dataId = pp.id
                w1.message = subject_text || ''
                w1.messageFull = content || ''
                w1.employeeId = createdBy.employeeId
                await manager.getRepository(EmployeeWarningEntity).save(w1)
              }
            }
          }
        }
      } catch (error) {
        throw error
      }
    })
  }
}
