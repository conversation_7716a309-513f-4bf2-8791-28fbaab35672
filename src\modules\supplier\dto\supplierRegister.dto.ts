import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsNumber } from 'class-validator'
class ListDetailYearCreateDto {
  @ApiPropertyOptional()
  value: string

  @ApiPropertyOptional()
  year: string
}

class SupplierCapacityValueCreateDto {
  @ApiPropertyOptional()
  value: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiPropertyOptional()
  listDetailYear: ListDetailYearCreateDto[]

  @ApiPropertyOptional()
  __childs__: SupplierCapacityValueCreateDto[]
}

class SupplierCapacityCreateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  serviceId: string

  @ApiProperty()
  serviceCapacity: SupplierCapacityValueCreateDto[]
}
export class SupplierRegisterDto {
  /** Mô tả về nhà cung cấp */
  @ApiProperty()
  @IsString()
  username: string

  /** Mô tả về nhà cung cấp */
  @ApiProperty()
  @IsString()
  password: string

  /** <PERSON>ô tả về nhà cung cấp */
  @ApiProperty()
  @IsString()
  confirmPassword: string

  /** Mô tả về nhà cung cấp */
  @ApiProperty()
  @IsString()
  description: string

  /** Mã số doanh nghiệp */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  code: string

  /** Tên chính thức */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  /** Tên giao dịch */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  dealName: string

  /** Địa chỉ trụ sở */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  address: string

  /** Địa chỉ giao dịch */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  dealAddress: string

  /** Giấy phép đăng ký kinh doanh/Mã số thuế --> URL */
  @ApiPropertyOptional()
  fileMST: string

  /** Người đại diện pháp luật */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  represen: string

  /** Tên giám đốc */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  chief: string

  /** Số tài khoản ngân hàng */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  bankNumber: string

  /** Tên ngân hàng */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  bankname: string

  /** Tên chi nhánh ngân hàng */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  bankBrand: string

  /** File đính kèm thông báo mở tài khoản/mẫu 08 -->URL */
  @ApiPropertyOptional()
  fileAccount: string

  /** Người liên hệ */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  contactName: string

  /** Email */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  email: string

  /** Điện thoại */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  phone: string

  /** Năm thành lập công ty */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  createYear: string

  /** Vốn điều lệ (tỷ đồng) */
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  capital: number

  /** Tài sản cố định (tỷ đồng) */
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  assets: number

  /** File đính kèm hóa đơn mẫu/phiếu thu/biên lai --> URL */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  fileBill: string

  /** File đính kèm thông tin phát hành hóa đơn --> URL */
  @ApiPropertyOptional()
  fileInfoBill: string

  // /** Ngày duyệt */
  // @IsDate()
  // approveDate: Date

  @ApiPropertyOptional()
  services: SupplierCapacityCreateDto[]

  /** Trạng thái */
  @ApiPropertyOptional()
  status: string

  /** Mã giới thiệu (Mã nhân viên giới thiệu) */
  @ApiPropertyOptional()
  introducerCode: string
}

export class SupplierAddCapacitiesDto {
  @ApiPropertyOptional()
  services: SupplierCapacityCreateDto[]
}
