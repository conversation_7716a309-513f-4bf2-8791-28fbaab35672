import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { enumData, enumProject } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { ContractCreate, ContractUpdate } from './dto'
import { ContractService } from './contract.service'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Contract')
@Controller('contract')
export class ContractController {
  constructor(private readonly service: ContractService) {}

  @ApiOperation({ summary: 'Lấy danh sách hợp đồng' })
  @Roles(enumProject.Features.PAYMENT_001.code, enumProject.Features.PAYMENT_002.code, enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: { status?: string }) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách hợp đồng tham chiếu' })
  // @Roles(enumProject.Features.PAYMENT_001.code, enumProject.Features.PAYMENT_002.code, enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_mirror')
  public async findMirror(@CurrentUser() user: UserDto, @Body() data: { status?: string }) {
    return await this.service.findMirror(user, data)
  }

  @ApiOperation({ summary: 'Lấy chi tiết hợp đồng' })
  @Roles(enumProject.Features.CONTRACT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetail2(user, data)
  }

  @ApiOperation({ summary: 'Tạo hợp đồng' })
  @Roles(enumProject.Features.CONTRACT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@Body() data: ContractCreate, @CurrentUser() user: UserDto) {
    return await this.service.createData(data, user)
  }

  @ApiOperation({ summary: 'Cập nhật hợp đồng' })
  @Roles(enumProject.Features.CONTRACT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@Body() data: ContractUpdate, @CurrentUser() user: UserDto) {
    return await this.service.updateData(data, user)
  }

  @ApiOperation({ summary: 'Danh sách hợp đồng phân trang' })
  @Roles(enumProject.Features.CONTRACT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@Body() data: PaginationDto, @CurrentUser() user: UserDto) {
    return await this.service.pagination(data, user)
  }

  @ApiOperation({ summary: 'Duyệt hợp đồng' })
  @Roles(enumProject.Features.CONTRACT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_status_processing')
  public async updateStatusProcessing(@Body() data: { id: string; reason?: string }, @CurrentUser() user: UserDto) {
    let statusConvert = enumData.ContractStatus.Processing
    return await this.service.updateStatus(data, statusConvert, user)
  }

  @ApiOperation({ summary: 'Hoàn thành hợp đồng' })
  @Roles(enumProject.Features.CONTRACT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_status_complete')
  public async updateStatusComplete(@Body() data: { id: string; reason?: string }, @CurrentUser() user: UserDto) {
    let statusConvert = enumData.ContractStatus.Complete
    return await this.service.updateStatus(data, statusConvert, user)
  }

  @ApiOperation({ summary: 'Hủy hợp đồng' })
  @Roles(enumProject.Features.CONTRACT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_status_cancel')
  public async updateStatusCancel(@Body() data: { id: string; reason?: string }, @CurrentUser() user: UserDto) {
    let statusConvert = enumData.ContractStatus.Cancel
    return await this.service.updateStatus(data, statusConvert, user)
  }

  @ApiOperation({ summary: 'Lấy danh sách hợp đồng' })
  @Post('load_contract_by_supplier')
  @UseGuards(ApeAuthGuard, RoleGuard)
  public async loadContractBySupplier(@CurrentUser() user: UserDto, @Body() data: { status?: string }) {
    return await this.service.loadContractBySupplier(user, data)
  }
}

@ApiBearerAuth()
@ApiTags('Contract')
@Controller('contractHistory')
export class ContractHistoryController {
  constructor(private readonly service: ContractService) {}

  @ApiOperation({ summary: 'Danh sách lịch sử hợp đồng phân trang' })
  @Roles(enumProject.Features.CONTRACT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async paginationHistory(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.paginationHistory(user, data)
  }
}
