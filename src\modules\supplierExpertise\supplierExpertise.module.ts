import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { SupplierExpertiseRepository, ServiceRepository, SupplierServiceRepository, SupplierRepository } from '../../repositories'
import { SupplierExpertiseController } from './supplierExpertise.controller'
import { SupplierExpertiseService } from './supplierExpertise.service'
import { EmailModule } from '../email/email.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([SupplierExpertiseRepository, ServiceRepository, SupplierServiceRepository, SupplierRepository]),
    EmailModule,
  ],
  controllers: [SupplierExpertiseController],
  providers: [SupplierExpertiseService],
})
export class SupplierExpertiseModule {}
