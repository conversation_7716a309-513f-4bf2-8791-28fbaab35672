import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, Jo<PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BranchEntity } from './branch.entity'
import { EmployeeEntity } from './employee.entity'

@Entity('branch_member')
export class BranchMemberEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.branchMembers)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  branchId: string
  @ManyToOne(() => BranchEntity, (p) => p.branchMembers)
  @JoinColumn({ name: 'branchId', referencedColumnName: 'id' })
  branch: Promise<BranchEntity>

  /** <PERSON>ạ<PERSON> enum BranchMember */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string
}
