import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>in<PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { enumData } from '../constants'
import { AsnItemEntity } from './asnItem.entity'
import { BaseEntity } from './base.entity'
import { BidEntity } from './bid.entity'
import { EmployeeEntity } from './employee.entity'
import { OfferServiceEntity } from './offerService.entity'
import { POProductEntity } from './poProduct.entity'
import { PrItemEntity } from './prItem.entity'
import { PurchasePlanEntity } from './purchasePlan.entity'
import { ServiceAccessEntity } from './serviceAccess.entity'
import { ServiceCapacityEntity } from './serviceCapacity.entity'
import { ServiceCustomPriceEntity } from './serviceCustomPrice.entity'
import { ServicePriceEntity } from './servicePrice.entity'
import { ServicePriceColEntity } from './servicePriceCol.entity'
import { ServiceTechEntity } from './serviceTech.entity'
import { ServiceTradeEntity } from './serviceTrade.entity'
import { SupplierCapacityEntity } from './supplierCapacity.entity'
import { SupplierExpertiseEntity } from './supplierExpertise.entity'
import { SupplierServiceEntity } from './supplierService.entity'

@Entity('service')
export class ServiceEntity extends BaseEntity {
  @Column({ nullable: false, default: 0 })
  percentTech: number

  @Column({ nullable: false, default: 0 })
  percentTrade: number

  @Column({ nullable: false, default: 0 })
  percentPrice: number

  @Column({ type: 'varchar', length: 50, nullable: false })
  name: string

  @Column({ type: 'varchar', length: 50, nullable: false })
  code: string

  // Cấp bậc của dịch vụ (1-2-3)
  @Column({ nullable: false })
  level: number

  // Kiểm tra xem dịch vụ này có phải là dịch vụ cuối cùng không
  @Column({ nullable: false, default: false })
  isLast: boolean
  // số lượng tồn kho của dịch vụ
  @Column({ type: 'decimal', precision: 12, scale: 2, default: 0 })
  stockQuantity: number

  @Column({ type: 'varchar', length: 250, nullable: true })
  description: string

  @Column({ type: 'varchar', length: 36, nullable: true })
  parentId: string
  // 1 dịch vụ chỉ có 1 dịch vụ cha
  @ManyToOne(() => ServiceEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: Promise<ServiceEntity>

  // 1 dịch vụ có thể có nhiều dịch vụ con
  @OneToMany(() => ServiceEntity, (p) => p.parent)
  childs: Promise<ServiceEntity[]>

  // 1 dịch vụ sẽ có nhiều cấu hình năng lực
  @OneToMany(() => ServiceCapacityEntity, (p) => p.service)
  capacities: Promise<ServiceCapacityEntity[]>

  // 1 dịch vụ sẽ có nhiều cấu hình kỹ thuật
  @OneToMany(() => ServiceTechEntity, (p) => p.service)
  techs: Promise<ServiceTechEntity[]>

  // 1 dịch vụ sẽ có nhiều cấu hình thương mại
  @OneToMany(() => ServiceTradeEntity, (p) => p.service)
  trades: Promise<ServiceTradeEntity[]>

  // 1 dịch vụ sẽ có nhiều cấu hình giá
  @OneToMany(() => ServicePriceEntity, (p) => p.service)
  prices: Promise<ServicePriceEntity[]>

  // 1 dịch vụ sẽ có nhiều cấu hình cơ cấu giá
  @OneToMany(() => ServiceCustomPriceEntity, (p) => p.service)
  customPrices: Promise<ServiceCustomPriceEntity[]>

  // 1 dịch vụ sẽ có nhiều năng lực nhà cung cấp
  @OneToMany(() => SupplierCapacityEntity, (p) => p.service)
  supplierCapacities: Promise<SupplierCapacityEntity[]>

  @OneToMany(() => SupplierServiceEntity, (p) => p.supplier)
  supplierServices: Promise<SupplierServiceEntity[]>

  @OneToMany(() => SupplierExpertiseEntity, (p) => p.service)
  supplierExpertise: Promise<SupplierExpertiseEntity[]>

  @OneToMany(() => BidEntity, (p) => p.service)
  bids: Promise<BidEntity[]>

  // Nhân viên có quyền truy cập vào loại hình dịch vụ này
  @OneToMany(() => ServiceAccessEntity, (p) => p.service)
  serviceAccess: Promise<ServiceAccessEntity[]>

  @Column({ type: 'varchar', length: 36, nullable: false })
  approveById: string
  @ManyToOne(() => EmployeeEntity, (p) => p.serviceApprover)
  @JoinColumn({ name: 'approveById', referencedColumnName: 'id' })
  approveBy: Promise<EmployeeEntity>

  /** Danh sách các cột bổ sung thêm để Doanh nghiệp nhập dữ liệu */
  @OneToMany(() => ServicePriceColEntity, (p) => p.service)
  servicePriceCols: Promise<ServicePriceColEntity[]>

  /** Công thức tính cột đơn giá */
  @Column({ type: 'text', nullable: true })
  fomular: string

  /** Cách tính điểm giá */
  @Column({ type: 'varchar', length: 50, nullable: true, default: enumData.PriceScoreCalculateWay.SumScore.code })
  wayCalScorePrice: string

  @Column({ type: 'varchar', length: 100, nullable: true, default: 'ChuaDuyet' })
  statusCapacity: string

  @OneToMany(() => PurchasePlanEntity, (p) => p.service)
  purchasePlans: Promise<PurchasePlanEntity[]>

  @OneToMany(() => AsnItemEntity, (p) => p.service)
  asnItems: Promise<AsnItemEntity[]>

  @OneToMany(() => POProductEntity, (p) => p.service)
  poProducts: Promise<POProductEntity[]>

  @OneToMany(() => PrItemEntity, (p) => p.service)
  prItems: Promise<PrItemEntity[]>

  @OneToMany(() => OfferServiceEntity, (p) => p.offer)
  offerService: Promise<OfferServiceEntity[]>
}
