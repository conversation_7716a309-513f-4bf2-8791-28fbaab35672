import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'

import { Bill<PERSON>ookupController } from './billLookup.controller'
import { BillLookupService } from './billLookup.service'
import { BillLookupRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BillLookupRepository])],
  controllers: [BillLookupController],
  providers: [BillLookupService],
})
export class BillLookupModule {}
