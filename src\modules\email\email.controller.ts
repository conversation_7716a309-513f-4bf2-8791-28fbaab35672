import { <PERSON>, <PERSON>, Param, Post } from '@nestjs/common'
import { EmailService } from './email.service'
import { EmailNodeService } from './emailNodeMailer.service'
@Controller('email')
export class EmailController {
  constructor(private readonly service: EmailService, private readonly service2: EmailNodeService) {}

  @Get('test')
  public async test() {
    const email = ''
    // return await this.service2.sendMail({})
    // return await this.service.sendEmail(
    //   '<EMAIL>',
    //   'Test HTML Email',
    //   '',
    //   '',
    //   '<h1>Hi!</h1><p>This is a test email with <strong>HTML</strong> content.</p>',
    //   '<h1>Hi!</h1><p>This is a test email with <strong>HTML</strong> content.</p>',
    //   'GuiMpoDuyetNccDangKy',
    // )
  }
}
