import { Controller, Post, UseGuards, Get, Param, Put, Body } from '@nestjs/common'
import { SupplierExpertiseService } from './supplierExpertise.service'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { CurrentUser, Roles } from '../common/decorators'
import { PaginationDto, UserDto } from '../../dto'
import { SaveLegalDetailBodyDto, SaveCapacityDetailDto } from './dto'
import { enumProject } from '../../constants'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

/** Thẩm định */
@ApiBearerAuth()
@ApiTags('Supplier')
@Controller('supplier_expertise')
export class SupplierExpertiseController {
  constructor(private readonly service: SupplierExpertiseService) {}

  @ApiOperation({ summary: '<PERSON>h sách các lần thẩm định NCC phân trang' })
  @Roles(enumProject.Features.EXPERTISE_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Lấy thông tin thẩm định pháp lý' })
  @Roles(enumProject.Features.EXPERTISE_001.code, enumProject.Features.EXPERTISE_002.code, enumProject.Features.EXPERTISE_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_law/:id')
  public async getLaw(@CurrentUser() user: UserDto, @Param('id') expertiseId: string) {
    return await this.service.getLaw(user, expertiseId)
  }

  @ApiOperation({ summary: 'Lấy thông tin thẩm định năng lực' })
  @Roles(enumProject.Features.EXPERTISE_001.code, enumProject.Features.EXPERTISE_002.code, enumProject.Features.EXPERTISE_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_capacity/:id')
  public async getCapacity(@CurrentUser() user: UserDto, @Param('id') expertiseId: string) {
    return await this.service.getCapacity(user, expertiseId)
  }

  @ApiOperation({ summary: 'Lấy quyền thẩm định' })
  @Roles(enumProject.Features.EXPERTISE_001.code, enumProject.Features.EXPERTISE_002.code, enumProject.Features.EXPERTISE_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_permission/:id')
  public async getExpertisePermissions(@CurrentUser() user: UserDto, @Param('id') expertiseId: string) {
    return await this.service.getExpertisePermissions(user, expertiseId)
  }

  @ApiOperation({ summary: 'Lưu thẩm định thông tin pháp lý' })
  @Roles(enumProject.Features.EXPERTISE_002.code, enumProject.Features.EXPERTISE_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('save_law')
  public async saveLaw(@CurrentUser() user: UserDto, @Body() data: SaveLegalDetailBodyDto) {
    return await this.service.saveLaw(user, data)
  }

  @ApiOperation({ summary: 'Lưu thẩm định thông tin năng lực' })
  @Roles(enumProject.Features.EXPERTISE_002.code, enumProject.Features.EXPERTISE_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('save_capacity')
  public async saveCapacity(@CurrentUser() user: UserDto, @Body() data: SaveCapacityDetailDto) {
    return await this.service.saveCapacity(user, data)
  }

  @ApiOperation({ summary: 'Hoàn tất thẩm định' })
  @Roles(enumProject.Features.EXPERTISE_002.code, enumProject.Features.EXPERTISE_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('finish')
  public async finishExpertise(@CurrentUser() user: UserDto, @Body() data: { expertiseId: string }) {
    return await this.service.finishExpertise(user, data)
  }

  @ApiOperation({ summary: 'Huỷ yêu cầu thẩm định' })
  @Roles(enumProject.Features.EXPERTISE_002.code, enumProject.Features.EXPERTISE_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('cancel')
  public async cancelExpertise(@CurrentUser() user: UserDto, @Body() data: { expertiseId: string }) {
    return await this.service.cancelExpertise(user, data)
  }
}
