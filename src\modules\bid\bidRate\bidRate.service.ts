import { Injectable, NotFoundException, MethodNotAllowedException, NotAcceptableException, BadRequestException } from '@nestjs/common'
import { enumData, ERROR_YOU_DO_NOT_HAVE_PERMISSION, ERROR_NOT_FOUND_DATA } from '../../../constants'
import { EmailService } from '../../email/email.service'
import {
  BidRepository,
  BidEmployeeAccessRepository,
  BidSupplierRepository,
  BidDealRepository,
  BidPriceColRepository,
  BidTechRepository,
  BidTradeRepository,
  BidPriceRepository,
} from '../../../repositories'
import { PaginationDto, UserDto } from '../../../dto'
import {
  BidSupplierTechValueEntity,
  BidSupplierTradeValueEntity,
  BidSupplierPriceValueEntity,
  BidSupplierCustomPriceValueEntity,
  BidHistoryEntity,
  SupplierCapacityEntity,
  SupplierEntity,
  SupplierServiceEntity,
  BidAuctionSupplierEntity,
  BidDealSupplierEntity,
  BidSupplierEntity,
  BidDealEntity,
  BidAuctionEntity,
  PrEntity,
} from '../../../entities'
import { coreHelper } from '../../../helpers'
import { In, IsNull, Like, Raw } from 'typeorm'
import * as moment from 'moment'

@Injectable()
export class BidRateService {
  constructor(
    private readonly repo: BidRepository,
    private readonly bidDealRepo: BidDealRepository,
    private readonly bidTechRepo: BidTechRepository,
    private readonly bidTradeRepo: BidTradeRepository,
    private readonly bidPriceRepo: BidPriceRepository,
    private readonly bidPriceColRepo: BidPriceColRepository,
    private readonly bidEmployeeAccessRepo: BidEmployeeAccessRepository,
    private readonly bidSupplierRepo: BidSupplierRepository,
    private readonly emailService: EmailService,
  ) {}

  /** Hàm load gói thầu module đánh giá */
  async pagination(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    let whereCon: any = { parentId: IsNull(), companyId: user.companyId, isDeleted: false }
    // Lấy gói thầu mpoLead cần duyệt
    if (data.where.isGetBidNeedApprove) {
      const whereTemp = [
        {
          companyId: user.companyId,
          employeeAccess: { employeeId: user.employeeId, type: enumData.BidRuleType.MPOLeader.code },
          status: In([enumData.BidStatus.DangDanhGia.code, enumData.BidStatus.DangDuyetDanhGia.code]),
          statusRateTech: enumData.BidTechRateStatus.DaTao.code,
        },
        {
          companyId: user.companyId,
          employeeAccess: { employeeId: user.employeeId, type: enumData.BidRuleType.MPOLeader.code },
          status: In([enumData.BidStatus.DangDanhGia.code, enumData.BidStatus.DangDuyetDanhGia.code]),
          statusRateTrade: enumData.BidTradeRateStatus.DaTao.code,
          statusRatePrice: enumData.BidPriceRateStatus.DaTao.code,
        },
        {
          companyId: user.companyId,
          employeeAccess: { employeeId: user.employeeId, type: enumData.BidRuleType.MPOLeader.code },
          status: In([enumData.BidStatus.DongThau.code, enumData.BidStatus.DangDuyetKetThucThau.code]),
        },
      ]

      const lstBidTemp = await this.repo.find({ where: whereTemp, select: { id: true } })
      const lstBidId = lstBidTemp.map((c) => c.id)

      if (lstBidId.length == 0) return [[], 0]
      whereCon.id = In(lstBidId)
    }
    // Lấy gói thầu có quyền xem
    else whereCon.employeeAccess = { employeeId: user.employeeId }

    if (data.where.serviceId) whereCon.childs = { serviceId: data.where.serviceId }

    let lstStatus = [
      enumData.BidStatus.GoiThauTam.code,
      enumData.BidStatus.DangNhanBaoGia.code,
      enumData.BidStatus.DangDanhGia.code,
      enumData.BidStatus.DangDuyetDanhGia.code,
      enumData.BidStatus.HoanTatDanhGia.code,
      enumData.BidStatus.DongDauGia.code,
      enumData.BidStatus.DongDamPhanGia.code,
      enumData.BidStatus.DangDauGia.code,
      enumData.BidStatus.DangDamPhanGia.code,
      enumData.BidStatus.DongThau.code,
      enumData.BidStatus.DuyetNCCThangThau.code,
      enumData.BidStatus.DangDuyetKetThucThau.code,
    ]
    if (data.where.status?.length > 0) lstStatus = data.where.status
    whereCon.status = In(lstStatus)

    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.publicDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.dateFrom) {
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}")`)
    } else if (data.where.dateTo) {
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.dateTo).format('YYYY-MM-DD')}")`)
    }

    // Tìm theo mã số hoặc tên gói thầu
    if (data.where.name) {
      whereCon = [
        { ...whereCon, code: Like(`%${data.where.name}%`) },
        { ...whereCon, name: Like(`%${data.where.name}%`) },
      ]
    }

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      select: {
        id: true,
        createdAt: true,
        code: true,
        name: true,
        status: true,
        publicDate: true,
        statusRateTech: true,
        statusRateTrade: true,
        statusRatePrice: true,
        isRequestDelete: true,
        statusResetPrice: true,
        isAutoBid: true,
        fileScan: true,
        noteFinishBidMPO: true,
      },
    })
    if (res[0].length == 0) return res

    const lstId = res[0].map((c) => c.id)
    const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
      where: { bidId: In(lstId), companyId: user.companyId, isDeleted: false },
      relations: { employee: true },
      select: {
        id: true,
        bidId: true,
        type: true,
        employeeId: true,
        employee: {
          id: true,
          name: true,
        },
      },
    })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }

    for (const item of res[0]) {
      const lstAccess = lstEmployeeAccess.filter((c) => c.bidId == item.id)
      const objTech = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
      item.techName = objTech?.__employee__?.name || ''
      const objMpo = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
      item.mpoName = objMpo?.__employee__?.name || ''

      const lstAccessUser = lstAccess.filter((c) => c.employeeId === user.employeeId)
      item.isMPO = lstAccessUser.some((c) => c.type === enumData.BidRuleType.MPO.code)
      item.isMPOLeader = lstAccessUser.some((c) => c.type === enumData.BidRuleType.MPOLeader.code)
      item.isTech = lstAccessUser.some((c) => c.type === enumData.BidRuleType.Tech.code)
      item.isTechLeader = lstAccessUser.some((c) => c.type === enumData.BidRuleType.TechLeader.code)
      item.isMember = lstAccessUser.some((c) => c.type === enumData.BidRuleType.Memmber.code)

      item.statusName = dicStatus[item.status]

      // mpo có quyền như tech
      item.isTech = item.isTech || item.isMPO
      // mpoLead có quyền như techLead
      item.isTechLeader = item.isTechLeader || item.isMPOLeader

      item.isShowCopy = item.isMPO || item.isMPOLeader

      item.isShowDelete = item.isMPO && !item.isRequestDelete

      item.isShowApproveDelete = item.isMPOLeader && item.isRequestDelete

      // mở thầu
      item.isShowOpenBid = item.status === enumData.BidStatus.DangNhanBaoGia.code && (item.isMPO || item.isMPOLeader)

      // biên bản mở thầu
      item.isShowProtocolOpenBid = item.status !== enumData.BidStatus.DangNhanBaoGia.code && (item.isMPO || item.isMPOLeader)

      // report: các thành viên trong hội đồng thầu, mpo, mpoLead
      item.isShowReport = (item.isMPO || item.isMPOLeader || item.isMember) && item.status !== enumData.BidStatus.DangNhanBaoGia.code

      // phân tích giá
      item.isShowAnalysis = (item.isMPO || item.isMPOLeader) && item.status !== enumData.BidStatus.DangNhanBaoGia.code

      // Kết thúc nộp chào giá hiệu chỉnh
      item.isShowEndResetPrice = item.statusResetPrice == enumData.BidResetPriceStatus.DaTao.code && (item.isMPO || item.isMPOLeader)

      // đánh giá
      if (
        item.statusResetPrice !== enumData.BidResetPriceStatus.DaTao.code &&
        (item.status === enumData.BidStatus.DangDanhGia.code || item.status === enumData.BidStatus.DangDuyetDanhGia.code)
      ) {
        // nút đánh giá kỹ thuật
        item.isShowTechRate = item.isTech || item.isTechLeader
        if (item.isShowTechRate) {
          if (item.statusRateTech === enumData.BidTechRateStatus.DangTao.code) {
            item.techType = 'dashed'
          }
          if (item.statusRateTech === enumData.BidTechRateStatus.TuChoi.code) {
            item.techType = 'danger'
          }
          if (item.statusRateTech === enumData.BidTechRateStatus.DaTao.code) {
            item.techType = 'warning'
          }
          if (item.statusRateTech === enumData.BidTechRateStatus.DaDuyet.code) {
            item.techType = 'success'
          }
        }

        // nút đánh giá thương mại
        item.isShowTradeRate = item.isMPO || item.isMPOLeader
        if (item.isShowTradeRate) {
          if (item.statusRateTrade === enumData.BidTradeRateStatus.DangTao.code) {
            item.tradeType = 'dashed'
          }
          if (item.statusRateTrade === enumData.BidTradeRateStatus.TuChoi.code) {
            item.tradeType = 'danger'
          }
          if (item.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code) {
            item.tradeType = 'warning'
          }
          if (item.statusRateTrade === enumData.BidTradeRateStatus.DaDuyet.code) {
            item.tradeType = 'success'
          }
        }

        // nút đánh giá chào giá
        item.isShowPriceRate = item.isMPO || item.isMPOLeader
        if (item.isShowPriceRate) {
          if (item.statusRatePrice === enumData.BidPriceRateStatus.DangTao.code) {
            item.priceType = 'dashed'
          }
          if (item.statusRatePrice === enumData.BidPriceRateStatus.TuChoi.code) {
            item.priceType = 'danger'
          }
          if (item.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code) {
            item.priceType = 'warning'
          }
          if (item.statusRatePrice === enumData.BidPriceRateStatus.DaDuyet.code) {
            item.priceType = 'success'
          }
        }
      }

      // Cấu hình lại bảng giá
      item.isShowResetPrice =
        (item.status === enumData.BidStatus.HoanTatDanhGia.code ||
          item.status === enumData.BidStatus.DongDamPhanGia.code ||
          item.status === enumData.BidStatus.DongDauGia.code) &&
        (item.isMPO || item.isMPOLeader)

      if (item.statusResetPrice == enumData.BidResetPriceStatus.ChuaTao.code || item.statusResetPrice == enumData.BidResetPriceStatus.KetThuc.code) {
        // Chọn ncc thắng thầu
        item.isShowEnd =
          (item.status === enumData.BidStatus.HoanTatDanhGia.code ||
            item.status === enumData.BidStatus.DongDamPhanGia.code ||
            item.status === enumData.BidStatus.DongDauGia.code) &&
          item.isMPO

        // Duyệt chọn ncc thắng thầu
        item.isShowAcceptEnd = item.status === enumData.BidStatus.DongThau.code && item.isMPOLeader

        // Đàm phán giá
        item.isShowDeal =
          (item.status === enumData.BidStatus.HoanTatDanhGia.code || item.status === enumData.BidStatus.DongDamPhanGia.code) && item.isMPO

        // Đấu giá
        item.isShowAuction = item.status === enumData.BidStatus.HoanTatDanhGia.code && item.isMPO

        // Đàm phán giá & Đấu giá
        item.isShowDealAuction = item.status === enumData.BidStatus.HoanTatDanhGia.code && item.isMPO
      }

      // In hồ sơ thầu
      item.isShowPrint = item.status === enumData.BidStatus.DuyetNCCThangThau.code && (item.isMPO || item.isMPOLeader)

      // Gửi yêu cầu phê duyệt kết thúc thầu
      item.isShowSendRequestFinishBid = item.status === enumData.BidStatus.DuyetNCCThangThau.code && item.isMPO

      // Phê duyệt kết thúc thầu
      item.isShowApproveFinishBid = item.status === enumData.BidStatus.DangDuyetKetThucThau.code && item.isMPOLeader
    }

    return res
  }

  //#region bidRateTech

  /** Check quyền tạo đánh giá kỹ thuật cho gói thầu */
  private async checkPermissionCreateBidTechRate(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId }, select: { id: true, statusRateTech: true } })
    if (!bid) return { hasPermission: result, message }

    if (bid.statusRateTech === enumData.BidTechRateStatus.DangTao.code || bid.statusRateTech === enumData.BidTechRateStatus.TuChoi.code) {
      result = await this.bidEmployeeAccessRepo.isTech(user, bidId)
      if (!result) message = 'Bạn không có quyền đánh giá kỹ thuật cho gói thầu.'
    } else {
      result = false
      message = 'Gói thầu đã được đánh giá kỹ thuật.'
    }

    return { hasPermission: result, message }
  }

  /** Lấy ds NCC tham gia thầu và tính điểm */
  async loadTechRate(user: UserDto, bidId: string) {
    const bidSupplierTechValueRepo = this.repo.manager.getRepository(BidSupplierTechValueEntity)

    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res = await this.repo.getBid2(user, bidId)
    const dicStatusFile: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierFileStatus)
      lstStatus.forEach((c) => (dicStatusFile[c.code] = c.name))
    }
    const dicStatusTech: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierTechStatus)
      lstStatus.forEach((c) => (dicStatusTech[c.code] = c.name))
    }
    const setType = new Set()
    setType.add(enumData.DataType.Number.code)
    setType.add(enumData.DataType.List.code)
    for (const item of res.listItem) {
      // Lấy template hồ sơ kỹ thuật của Item
      item.lstBidTech = await this.bidTechRepo.getTech(user, item.id)
      // Lấy hồ sơ kỹ thuật của các NCC tham gia
      item.lstBidSupplier = await this.bidSupplierRepo.find({
        where: { bidId: item.id, statusFile: enumData.BidSupplierFileStatus.HopLe.code, companyId: user.companyId },
        relations: { supplier: true, bidSupplierTechValue: true },
      })
      if (item.lstBidSupplier.length === 0) continue

      //Lấy tiêu chí tính điểm (cấp 1 & kiểu number hoặc list)
      var lstBidTechCal = item.lstBidTech.filter((c) => c.parentId === null && setType.has(c.type))

      // với từng NCC tham gia Item
      for (const bidSupplier of item.lstBidSupplier) {
        bidSupplier.supplierCode = bidSupplier.__supplier__.code
        bidSupplier.supplierName = bidSupplier.__supplier__.name
        delete bidSupplier.__supplier__
        bidSupplier.statusFileName = dicStatusFile[bidSupplier.statusFile]
        bidSupplier.statusTechName = dicStatusTech[bidSupplier.statusTech]

        if (bidSupplier.scoreTech === 0) {
          const lstBidSupplierTechValue = bidSupplier.__bidSupplierTechValue__ || []
          let scoreTech = 0
          let isHighlight = false
          let isNotHaveMinValue = false

          // với từng tiêu chí cần tính điểm
          for (const bidTech of lstBidTechCal) {
            const itemChilds = bidTech.__childs__ || []
            if (itemChilds.length > 0) {
              let scoreTechChild = 0
              for (const itemChild of itemChilds) {
                const objValueChild = lstBidSupplierTechValue.find((c: any) => c.bidTechId === itemChild.id)
                if (objValueChild) {
                  const tem = this.calScoreTechItem(itemChild, objValueChild.value)
                  objValueChild.score = tem

                  // Lưu điểm
                  await bidSupplierTechValueRepo.update(objValueChild.id, { score: tem, updatedBy: user.id })
                  scoreTechChild += tem
                  isHighlight = this.checkHighlightTechItem(itemChild, objValueChild.value)
                  isNotHaveMinValue = this.checkMinValueTechItem(itemChild, objValueChild.value)
                }
              }
              const temp = (bidTech.percent * scoreTechChild) / 100
              scoreTech += temp

              // Lưu điểm
              await bidSupplierTechValueRepo.update({ bidTechId: bidTech.id, bidSupplierId: bidSupplier.id }, { score: temp, updatedBy: user.id })
            } else {
              const objValue = lstBidSupplierTechValue.find((c: any) => c.bidTechId === bidTech.id)
              if (objValue) {
                const temp = this.calScoreTechItem(bidTech, objValue.value)
                objValue.score = temp
                scoreTech += temp

                isHighlight = this.checkHighlightTechItem(bidTech, objValue.value)
                isNotHaveMinValue = this.checkMinValueTechItem(bidTech, objValue.value)

                // Lưu điểm
                await bidSupplierTechValueRepo.update({ bidTechId: bidTech.id, bidSupplierId: bidSupplier.id }, { score: temp, updatedBy: user.id })
              }
            }
          }
          if (isNaN(scoreTech) || !isFinite(scoreTech)) bidSupplier.scoreTech = 0
          bidSupplier.scoreTech = scoreTech
          bidSupplier.isHighlight = isHighlight
          bidSupplier.isNotHaveMinValue = isNotHaveMinValue

          // Lưu điểm
          await this.bidSupplierRepo.update(bidSupplier.id, {
            scoreTech: bidSupplier.scoreTech,
            isHighlight: bidSupplier.isHighlight,
            isNotHaveMinValue: bidSupplier.isNotHaveMinValue,
            updatedBy: user.id,
          })
        }

        bidSupplier.rankABCD = coreHelper.rankABCD(bidSupplier.scoreTech)
      }

      // sort
      item.lstBidSupplier.sort((a, b) => b.scoreTech - a.scoreTech)
      item.lstBidSupplier.sort((a, b) => (a.isNotHaveMinValue ? 1 : 0) - (b.isNotHaveMinValue ? 1 : 0))
    }

    return res
  }

  /**
   * Lấy danh sách bidTech và điểm cao nhất tương ứng
   * @param data.bidId - Item trong gói thầu
   * @param data.bidSupplierId - NCC tham gia Item
   */
  async loadBestTechValue(user: UserDto, data: { bidId: string; bidSupplierId: string }) {
    const lstBidSupplier = await this.bidSupplierRepo.find({ where: { bidId: data.bidId, companyId: user.companyId }, select: { id: true } })
    const listBidSupplierId = lstBidSupplier.map((p) => p.id)
    if (listBidSupplierId.length == 0) throw new Error('Không có hồ sơ thầu hợp lệ')

    // Danh sách bidTech
    const bidTechs = await this.bidTechRepo.find({ where: { bidId: data.bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false } })

    // Danh sách tất cả giá trị các NCC đã nộp
    const bidTechValue = await this.repo.manager.getRepository(BidSupplierTechValueEntity).find({
      where: { bidSupplierId: In(listBidSupplierId), companyId: user.companyId },
      select: {
        id: true,
        bidTechId: true,
        bidSupplierId: true,
        value: true,
        score: true,
      },
    })

    let result = []
    const length = bidTechs.length

    const setType = new Set()
    setType.add(enumData.DataType.Number.code)
    setType.add(enumData.DataType.List.code)

    // Lọc qua danh sách bidTech
    for (let i = 0; i < length; i++) {
      let itemResult: any = {}
      const item = bidTechs[i]
      itemResult.id = item.id
      itemResult.name = item.name
      itemResult.percent = item.percent
      itemResult.percentRule = item.percentRule
      itemResult.type = item.type
      itemResult.sort = item.sort

      // Giá trị mà NCC tương ứng đã nộp hồ sơ
      const supplierTechValue = bidTechValue.find((p) => p.bidTechId === item.id && p.bidSupplierId === data.bidSupplierId)

      itemResult.value = supplierTechValue?.value

      // Nếu kiểu list thì lọc để lấy name
      if (itemResult.type === enumData.DataType.List.code) {
        const listValue = await item.bidTechListDetails
        const find = listValue.find((p) => p.id === itemResult.value)
        itemResult.value = find?.name
      }
      itemResult.score = supplierTechValue?.score

      // Danh sách giá trị tất cả các NCC nộp theo tiêu chí này
      const listBidSupplierTechValue = bidTechValue.filter((p) => p.bidTechId === item.id)

      // Nêu có và là loại list hoặc number thì bắt đầu tính điểm max và rank
      if (listBidSupplierTechValue.length > 0 && setType.has(itemResult.type)) {
        // Lấy giá trị có điểm tốt nhất
        const bestSupplier = this.getMaxOfArrayObj(listBidSupplierTechValue)

        itemResult.bestScore = bestSupplier?.score
        itemResult.bestSupplierName = (await (await bestSupplier.bidSupplier).supplier).name
        itemResult.bestValue = bestSupplier?.value

        // Nếu kiểu list thì lọc qua để lấy name
        if (itemResult.type === enumData.DataType.List.code) {
          const listValue = await (await bestSupplier.bidTech).bidTechListDetails
          const find = listValue.find((p) => p.id === bestSupplier.value)
          itemResult.bestValue = find?.name
        }
        if (supplierTechValue) {
          itemResult.rank = this.getCurrentRank(listBidSupplierTechValue, supplierTechValue?.bidSupplierId)
        }
      }

      // Lọc qua danh sách con
      itemResult.childs = []
      const itemChilds = await item.childs
      if (itemChilds?.length > 0) {
        for (const itemC of itemChilds) {
          let itemResultC: any = {}
          itemResultC.id = itemC.id
          itemResultC.name = itemC.name
          itemResultC.percent = itemC.percent
          itemResultC.percentRule = itemC.percentRule
          itemResultC.type = itemC.type
          itemResultC.sort = itemC.sort

          // Lấy giá trị mà NCC tương ứng đã nôpk
          const supplierTechValueC = bidTechValue.find((p) => p.bidTechId === itemC.id && p.bidSupplierId === data.bidSupplierId)

          itemResultC.value = supplierTechValueC?.value

          // Nếu kiểu list thì lọc qua danh sách để lấy name
          if (itemResultC.type === enumData.DataType.List.code) {
            const listValueC = await itemC.bidTechListDetails
            const findC = listValueC.find((p) => p.id === itemResultC.value)
            itemResultC.value = findC?.name
          }
          itemResultC.score = supplierTechValueC?.score

          // Danh sách giá trị tất cả các NCC nộp theo tiêu chí này
          const listBidSupplierTechValueC = bidTechValue.filter((p) => p.bidTechId === itemC.id)
          // Nêu có và là loại list hoặc number thì bắt đầu tính điểm max và rank
          if (listBidSupplierTechValueC.length > 0 && setType.has(itemResultC.type)) {
            // Lấy giá trị có điểm tốt nhất
            const bestSupplierC = this.getMaxOfArrayObj(listBidSupplierTechValueC)

            itemResultC.bestScore = bestSupplierC?.score
            itemResultC.bestSupplierName = (await (await bestSupplierC.bidSupplier).supplier).name
            itemResultC.bestValue = bestSupplierC?.value
            // Nếu kiểu list thì lọc qua để lấy name
            if (itemResultC.type === enumData.DataType.List.code) {
              const listValueC = await (await bestSupplierC.bidTech).bidTechListDetails
              const findC = listValueC.find((p) => p.id === bestSupplierC.value)
              itemResultC.bestValue = findC?.name
            }
            if (supplierTechValueC) {
              itemResultC.rank = this.getCurrentRank(listBidSupplierTechValueC, supplierTechValueC?.bidSupplierId)
            }
          }
          itemResult.childs.push(itemResultC)
        }
      }
      itemResult.childs = itemResult.childs.sort((a, b) => a.sort - b.sort)

      result.push(itemResult)
    }
    result = result.sort((a, b) => a.sort - b.sort)

    return result
  }

  /** Tính điểm đánh giá kỹ thuật */
  calScoreTechItem(item: any, value: string) {
    let score = 0
    if (item.type === enumData.DataType.Number.code && value && value.trim() != '') {
      let temp = 0
      const x = +value
      // Tính theo chiều thuận
      if (item.isCalUp) {
        if (x >= item.percentRule) {
          temp = item.percent
        } else {
          temp = (x * item.percent) / item.percentRule // giá trị * tỉ trọng / điều kiện b
        }
      }
      // Tính theo chiều nghịch
      else {
        if (x <= item.percentRule) {
          temp = item.percent
        } else if (x >= item.percentDownRule) {
          temp = 0
        } else {
          temp = ((item.percentDownRule - x) * item.percent) / (item.percentDownRule - item.percentRule)
        }
      }

      if (isNaN(temp)) {
        score += 0
      } else if (!isFinite(temp)) {
        score += 0
      } else {
        score += temp
      }
    } else if (item.type === enumData.DataType.List.code) {
      const itemChosen = item.__bidTechListDetails__.find((p) => p.id === value)
      const tem = itemChosen ? itemChosen.value : 0
      const temp = (tem * item.percent) / 100
      if (isNaN(temp)) {
        score += 0
      } else if (!isFinite(temp)) {
        score += 0
      } else {
        score += temp
      }
    }

    if (isNaN(score) || !isFinite(score)) return 0

    return score
  }

  /**  Check highlight đánh giá kỹ thuật */
  checkHighlightTechItem(item: any, value: string) {
    let isHighlight = false
    if (!item.hightlightValue) {
      return isHighlight
    }

    if (item.type === enumData.DataType.Number.code && value && value.trim() != '') {
      if (+value >= item.hightlightValue) {
        isHighlight = true
      }
    } else if (item.type === enumData.DataType.List.code) {
      const itemChosen = item.__bidTechListDetails__.find((p) => p.id === value)
      const temp = itemChosen ? itemChosen.value : 0
      if (temp >= item.hightlightValue) {
        isHighlight = true
      }
    }

    return isHighlight
  }

  /** Check isNotHaveMinValue đánh giá kỹ thuật */
  checkMinValueTechItem(item: any, value: string) {
    let isNotHaveMinValue = false
    if (!item.requiredMin) {
      return isNotHaveMinValue
    }

    if (item.type === enumData.DataType.Number.code && value && value.trim() != '') {
      if (+value < item.requiredMin) {
        isNotHaveMinValue = true
      }
    } else if (item.type === enumData.DataType.List.code) {
      const itemChosen = item.__bidTechListDetails__.find((p) => p.id === value)
      const temp = itemChosen ? itemChosen.value : 0
      if (temp < item.requiredMin) {
        isNotHaveMinValue = true
      }
    }

    return isNotHaveMinValue
  }

  /** Tạo đánh giá kỹ thuật cho gói thầu */
  async createTechRate(user: UserDto, data: { id: string; listItem: any[] }) {
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionCreateBidTechRate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    for (const item of data.listItem) {
      for (const bidSupplierItem of item.lstBidSupplier) {
        await this.bidSupplierRepo.update(bidSupplierItem.id, {
          statusTech: enumData.BidSupplierTechStatus.DaXacNhan.code,
          isTechValid: bidSupplierItem.isTechValid,
          noteTech: bidSupplierItem.noteTech,
          scoreManualTech: bidSupplierItem.scoreManualTech,
          updatedBy: user.id,
        })
      }
    }

    let status = bid.status

    if (
      (bid.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code || bid.statusRateTrade === enumData.BidTradeRateStatus.DaDuyet.code) &&
      (bid.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code || bid.statusRatePrice === enumData.BidPriceRateStatus.DaDuyet.code)
    ) {
      status = enumData.BidStatus.DangDuyetDanhGia.code
    }

    await this.repo.update(data.id, {
      statusRateTech: enumData.BidTechRateStatus.DaTao.code,
      status,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.DanhGiaKyThuat.code
    bidHistory.save()

    // gửi email trưởng bộ phận kỹ thuật
    this.emailService.GuiTechLeadDuyetDanhGiaKyThuat(data.id)

    return { message: 'Gửi yêu cầu phê duyệt kết quả đánh giá năng lực thành công.' }
  }

  /** Check quyền duyệt đánh giá kỹ thuật cho gói thầu */
  async checkPermissionApproveBidTechRate(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId } })
    if (bid) {
      if (bid.statusRateTech === enumData.BidTechRateStatus.DaTao.code) {
        const flagPermission = await this.bidEmployeeAccessRepo.isTechLeader(user, bidId)
        if (flagPermission) {
          result = true
        } else {
          message = 'Bạn không có quyền xét duyệt đánh giá năng lực, kỹ thuật cho gói thầu này.'
        }
      } else if (bid.statusRateTech === enumData.BidTechRateStatus.DaDuyet.code) {
        message = 'Gói thầu đã được xét duyệt đánh giá năng lực, kỹ thuật.'
      } else {
        message = 'Chưa có yêu cầu xét duyệt đánh giá năng lực, kỹ thuật.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Duyệt đánh giá kỹ thuật */
  async approveTechRate(user: UserDto, data: { id: string; listItem: any[] }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionApproveBidTechRate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    for (const item of data.listItem) {
      for (const bidSupplierItem of item.lstBidSupplier) {
        await this.bidSupplierRepo.update(bidSupplierItem.id, {
          statusTech: enumData.BidSupplierTechStatus.DaDuyet.code,
          noteTechLeader: bidSupplierItem.noteTechLeader,
          updatedBy: user.id,
        })
      }
    }

    let status = bid.status
    if (bid.statusRateTrade === enumData.BidTradeRateStatus.DaDuyet.code && bid.statusRatePrice === enumData.BidPriceRateStatus.DaDuyet.code) {
      status = enumData.BidStatus.HoanTatDanhGia.code
    }

    await this.repo.update(data.id, {
      statusRateTech: enumData.BidTechRateStatus.DaDuyet.code,
      status,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.DuyetDanhGiaKyThuat.code
    bidHistory.save()

    // gửi email
    this.emailService.ThongBaoDuyetDanhGiaKyThuat(data.id)

    return { message: 'Duyệt kết quả đánh giá năng lực thành công.' }
  }

  /** Từ chối đánh giá kỹ thuật */
  async rejectTechRate(user: UserDto, data: { id: string; listItem: any[] }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionApproveBidTechRate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    for (const item of data.listItem) {
      for (const bidSupplierItem of item.lstBidSupplier) {
        await this.bidSupplierRepo.update(bidSupplierItem.id, {
          statusTech: enumData.BidSupplierTechStatus.DangDanhGia.code,
          noteTechLeader: bidSupplierItem.noteTechLeader,
          updatedBy: user.id,
        })
      }
    }

    await this.repo.update(data.id, {
      statusRateTech: enumData.BidTechRateStatus.TuChoi.code,
      status: enumData.BidStatus.DangDanhGia.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.TuChoiDanhGiaKyThuat.code
    bidHistory.save()

    // gửi email bộ phận kỹ thuật
    this.emailService.GuiTechTuChoiDanhGiaKyThuat(data.id)

    return { message: 'Từ chối đánh giá kỹ thuật thành công.' }
  }
  //#endregion

  //#region bidRateTrade

  /** Check quyền tạo đánh giá điều kiện thương mại cho gói thầu */
  async checkPermissionCreateBidTradeRate(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId } })
    if (bid) {
      if (bid.statusRateTrade === enumData.BidTradeRateStatus.DangTao.code || bid.statusRateTrade === enumData.BidTradeRateStatus.TuChoi.code) {
        result = await this.bidEmployeeAccessRepo.isMPO(user, bidId)
        if (!result) {
          message = 'Bạn không có quyền đánh giá điều kiện thương mại cho gói thầu.'
        }
      } else {
        result = false
        message = 'Gói thầu đã được đánh giá điều kiện thương mại.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Lấy ds NCC tham gia thầu và tính điểm */
  async loadTradeRate(user: UserDto, bidId: string) {
    const bidSupplierTradeValueRepo = this.repo.manager.getRepository(BidSupplierTradeValueEntity)

    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res = await this.repo.getBid2(user, bidId)
    const dicStatusFile: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierFileStatus)
      lstStatus.forEach((c) => (dicStatusFile[c.code] = c.name))
    }
    const dicStatusTrade: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierTradeStatus)
      lstStatus.forEach((c) => (dicStatusTrade[c.code] = c.name))
    }
    const setType = new Set()
    setType.add(enumData.DataType.Number.code)
    setType.add(enumData.DataType.List.code)
    for (const item of res.listItem) {
      // Lấy template hồ sơ thương mại của gói thầu
      item.lstBidTrade = await this.bidTradeRepo.getTrade(user, item.id)
      // Lấy hồ sơ thương mại của các NCC tham gia
      item.lstBidSupplier = await this.bidSupplierRepo.find({
        where: { bidId: item.id, statusFile: enumData.BidSupplierFileStatus.HopLe.code, companyId: user.companyId },
        relations: { supplier: true, bidSupplierTradeValue: true },
      })
      if (item.lstBidSupplier.length === 0) continue

      //Lấy tiêu chí tính điểm (cấp 1 & kiểu number hoặc list)
      var lstBidTradeCal = item.lstBidTrade.filter((c) => c.parentId === null && setType.has(c.type))

      // Tính cho từng NCC tham gia thầu
      for (const bidSupplier of item.lstBidSupplier) {
        bidSupplier.supplierCode = bidSupplier.__supplier__.code
        bidSupplier.supplierName = bidSupplier.__supplier__.name
        delete bidSupplier.__supplier__
        bidSupplier.statusFileName = dicStatusFile[bidSupplier.statusFile]
        bidSupplier.statusTechName = dicStatusTrade[bidSupplier.statusTech]
        bidSupplier.statusTradeName = dicStatusTrade[bidSupplier.statusTrade]

        if (bidSupplier.scoreTrade === 0) {
          const lstBidSupplierTradeValue = bidSupplier.__bidSupplierTradeValue__ || []
          delete bidSupplier.__bidSupplierTradeValue__
          let scoreTrade = 0
          // với từng tiêu chí cần tính điểm
          for (const bidTrade of lstBidTradeCal) {
            const itemChilds = bidTrade.__childs__ || []
            if (itemChilds.length > 0) {
              let scoreTradeChild = 0
              for (const itemChild of itemChilds) {
                const objValueChild = lstBidSupplierTradeValue.find((c) => c.bidTradeId === itemChild.id)
                if (objValueChild) {
                  const tem = this.calScoreTradeItem(itemChild, objValueChild.value)
                  objValueChild.score = tem
                  scoreTradeChild += tem
                  // Lưu điểm
                  await bidSupplierTradeValueRepo.update(objValueChild.id, { score: tem, updatedBy: user.id })
                }
              }
              const temp = (bidTrade.percent * scoreTradeChild) / 100
              scoreTrade += temp
              // Lưu điểm
              await bidSupplierTradeValueRepo.update({ bidTradeId: bidTrade.id, bidSupplierId: bidSupplier.id }, { score: temp, updatedBy: user.id })
            } else {
              const objValue = lstBidSupplierTradeValue.find((c) => c.bidTradeId === bidTrade.id)
              if (objValue) {
                const temp = this.calScoreTradeItem(bidTrade, objValue.value)
                objValue.score = temp
                scoreTrade += temp
                // Lưu điểm
                await bidSupplierTradeValueRepo.update(
                  { bidTradeId: bidTrade.id, bidSupplierId: bidSupplier.id },
                  { score: temp, updatedBy: user.id },
                )
              }
            }
          }
          if (isNaN(scoreTrade)) {
            bidSupplier.scoreTrade = 0
          } else if (!isFinite(scoreTrade)) {
            bidSupplier.scoreTrade = 0
          } else bidSupplier.scoreTrade = scoreTrade
          // Lưu điểm
          await this.bidSupplierRepo.update(bidSupplier.id, { scoreTrade: bidSupplier.scoreTrade, updatedBy: user.id })
        }

        bidSupplier.rankABCD = coreHelper.rankABCD(bidSupplier.scoreTrade)
      }

      // sort
      item.lstBidSupplier.sort((a, b) => b.scoreTrade - a.scoreTrade)
    }

    return res
  }

  /**
   * Lấy danh sách bidTrade và điểm cao nhất tương ứng
   * @param data.bidId - Item trong gói thầu
   * @param data.bidSupplierId - NCC tham gia Item
   */
  async loadBestTradeValue(user: UserDto, data: { bidId: string; bidSupplierId: string }) {
    const lstBidSupplier = await this.bidSupplierRepo.find({ where: { bidId: data.bidId, companyId: user.companyId }, select: { id: true } })
    if (lstBidSupplier.length == 0) throw new Error('Không có hồ sơ thầu hợp lệ')
    const listBidSupplierId = lstBidSupplier.map((p) => p.id)

    // Danh sách bidTrade
    const bidTrades = await this.bidTradeRepo.find({ where: { bidId: data.bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false } })

    // Danh sách tất cả giá trị các NCC đã nộp
    const bidTradeValue = await this.repo.manager.getRepository(BidSupplierTradeValueEntity).find({
      where: { bidSupplierId: In(listBidSupplierId), companyId: user.companyId },
      select: {
        id: true,
        bidTradeId: true,
        bidSupplierId: true,
        value: true,
        score: true,
      },
    })

    let result = []
    const length = bidTrades.length

    const setType = new Set()
    setType.add(enumData.DataType.Number.code)
    setType.add(enumData.DataType.List.code)

    // Lọc qua danh sách bidTrade
    for (let i = 0; i < length; i++) {
      let itemResult: any = {}
      const item = bidTrades[i]
      itemResult.id = item.id
      itemResult.name = item.name
      itemResult.percent = item.percent
      itemResult.percentRule = item.percentRule
      itemResult.type = item.type
      itemResult.sort = item.sort

      // Giá trị mà NCC tương ứng đã nộp hồ sơ
      const supplierTraveValue = bidTradeValue.find((p) => p.bidTradeId === item.id && p.bidSupplierId === data.bidSupplierId)

      itemResult.value = supplierTraveValue?.value

      // Nếu kiểu list thì lọc để lấy name
      if (itemResult.type === enumData.DataType.List.code) {
        const listValue = await item.bidTradeListDetails
        const find = listValue.find((p) => p.id === itemResult.value)
        itemResult.value = find?.name
      }
      itemResult.score = supplierTraveValue?.score

      // Danh sách giá trị tất cả các NCC nộp theo tiêu chí này
      const listBidSupplierTraveValue = bidTradeValue.filter((p) => p.bidTradeId === item.id)

      // Nêu có và là loại list hoặc number thì bắt đầu tính điểm max và rank
      if (listBidSupplierTraveValue.length > 0 && setType.has(itemResult.type)) {
        // Lấy giá trị có điểm tốt nhất
        const bestSupplier = this.getMaxOfArrayObj(listBidSupplierTraveValue)

        itemResult.bestScore = bestSupplier?.score
        itemResult.bestSupplierName = (await (await bestSupplier.bidSupplier).supplier).name
        itemResult.bestValue = bestSupplier?.value

        // Nếu kiểu list thì lọc qua để lấy name
        if (itemResult.type === enumData.DataType.List.code) {
          const listValue = await (await bestSupplier.bidTrade).bidTradeListDetails
          const find = listValue.find((p) => p.id === bestSupplier.value)
          itemResult.bestValue = find?.name
        }
        if (supplierTraveValue) {
          itemResult.rank = this.getCurrentRank(listBidSupplierTraveValue, supplierTraveValue?.bidSupplierId)
        }
      }

      // Lọc qua danh sách con
      itemResult.childs = []
      const itemChilds = await item.childs
      if (itemChilds?.length > 0) {
        for (const itemC of itemChilds) {
          let itemResultC: any = {}
          itemResultC.id = itemC.id
          itemResultC.name = itemC.name
          itemResultC.percent = itemC.percent
          itemResultC.percentRule = itemC.percentRule
          itemResultC.type = itemC.type
          itemResultC.sort = itemC.sort

          // Lấy giá trị mà NCC tương ứng đã nôpk
          const supplierTraveValueC = await bidTradeValue.find((p) => p.bidTradeId === itemC.id && p.bidSupplierId === data.bidSupplierId)

          itemResultC.value = supplierTraveValueC?.value

          // Nếu kiểu list thì lọc qua danh sách để lấy name
          if (itemResultC.type === enumData.DataType.List.code) {
            const listValueC = await itemC.bidTradeListDetails
            const findC = listValueC.find((p) => p.id === itemResultC.value)
            itemResultC.value = findC?.name
          }
          itemResultC.score = supplierTraveValueC?.score

          // Danh sách giá trị tất cả các NCC nộp theo tiêu chí này
          const listBidSupplierTradeValueC = await bidTradeValue.filter((p) => p.bidTradeId === itemC.id)
          // Nêu có và là loại list hoặc number thì bắt đầu tính điểm max và rank
          if (listBidSupplierTradeValueC.length > 0 && setType.has(itemResultC.type)) {
            // Lấy giá trị có điểm tốt nhất
            const bestSupplierC = this.getMaxOfArrayObj(listBidSupplierTradeValueC)

            itemResultC.bestScore = bestSupplierC?.score
            itemResultC.bestSupplierName = (await (await bestSupplierC.bidSupplier).supplier).name
            itemResultC.bestValue = bestSupplierC?.value
            // Nếu kiểu list thì lọc qua để lấy name
            if (itemResultC.type === enumData.DataType.List.code) {
              const listValueC = await (await bestSupplierC.bidTrade).bidTradeListDetails
              const findC = listValueC.find((p) => p.id === bestSupplierC.value)
              itemResultC.bestValue = findC?.name
            }
            if (supplierTraveValueC) {
              itemResultC.rank = this.getCurrentRank(listBidSupplierTradeValueC, supplierTraveValueC?.bidSupplierId)
            }
          }
          itemResult.childs.push(itemResultC)
        }
      }
      itemResult.childs = itemResult.childs.sort((a, b) => a.sort - b.sort)

      result.push(itemResult)
    }
    result = result.sort((a, b) => a.sort - b.sort)

    return result
  }

  /** Tính điểm đánh giá điều kiện thương mại */
  calScoreTradeItem(item: any, value: string) {
    let score = 0
    if (item.type === enumData.DataType.Number.code && value && value.trim() != '') {
      let temp = 0
      const x = +value
      // Tính theo chiều thuận
      if (item.isCalUp) {
        if (x >= item.percentRule) {
          temp = item.percent
        } else {
          temp = (x * item.percent) / item.percentRule // giá trị * tỉ trọng / điều kiện b
        }
      }
      // Tính theo chiều nghịch
      else {
        if (x <= item.percentRule) {
          temp = item.percent
        } else if (x >= item.percentDownRule) {
          temp = 0
        } else {
          temp = ((item.percentDownRule - x) * item.percent) / (item.percentDownRule - item.percentRule)
        }
      }

      if (isNaN(temp)) {
        score += 0
      } else if (!isFinite(temp)) {
        score += 0
      } else {
        score += temp
      }
    } else if (item.type === enumData.DataType.List.code) {
      const itemChosen = item.__bidTradeListDetails__.find((p) => p.id === value)
      const tem = itemChosen ? itemChosen.value : 0
      const temp = (tem * item.percent) / 100
      if (isNaN(temp)) {
        score += 0
      } else if (!isFinite(temp)) {
        score += 0
      } else {
        score += temp
      }
    }

    if (isNaN(score) || !isFinite(score)) return 0

    return score
  }

  /** Tạo đánh giá điều kiện thương mại cho gói thầu */
  async createTradeRate(user: UserDto, data: { id: string; listItem: any[] }) {
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionCreateBidTradeRate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    const bid = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    for (const item of data.listItem) {
      for (const bidSupplierItem of item.lstBidSupplier) {
        await this.bidSupplierRepo.update(bidSupplierItem.id, {
          statusTrade: enumData.BidSupplierTradeStatus.DaXacNhan.code,
          isTradeValid: bidSupplierItem.isTradeValid,
          noteTrade: bidSupplierItem.noteTrade,
          scoreManualTrade: bidSupplierItem.scoreManualTrade,
          updatedBy: user.id,
        })
      }
    }

    let status = bid.status
    if (
      (bid.statusRateTech === enumData.BidTechRateStatus.DaTao.code || bid.statusRateTech === enumData.BidTechRateStatus.DaDuyet.code) &&
      (bid.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code || bid.statusRatePrice === enumData.BidPriceRateStatus.DaDuyet.code)
    ) {
      status = enumData.BidStatus.DangDuyetDanhGia.code
    }
    await this.repo.update(data.id, {
      statusRateTrade: enumData.BidTradeRateStatus.DaTao.code,
      status,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.DanhGiaThuongMai.code
    bidHistory.save()

    if (bid.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code || bid.statusRatePrice === enumData.BidPriceRateStatus.DaDuyet.code) {
      this.emailService.GuiMpoLeadDuyetDanhGiaGia(data.id)
    }

    return { message: 'Gửi yêu cầu phê duyệt kết quả đánh giá điều kiện thương mại thành công.' }
  }
  /** Duyệt đánh giá chào giá và điều kiện thương mại */
  async approveTradeRate(user: UserDto, data: { id: string; listItem: any[] }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionApproveBidPriceRate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    for (const item of data.listItem) {
      for (const bidSupplierItem of item.lstBidSupplier) {
        await this.bidSupplierRepo.update(bidSupplierItem.id, {
          statusTrade: enumData.BidSupplierTradeStatus.DaDuyet.code,
          statusPrice: enumData.BidSupplierPriceStatus.DaDuyet.code,
          noteMPOLeader: bidSupplierItem.noteMPOLeader,
          updatedBy: user.id,
        })
      }
    }

    let status = bid.status
    if (bid.statusRateTech === enumData.BidTechRateStatus.DaDuyet.code) {
      status = enumData.BidStatus.HoanTatDanhGia.code
    }
    await this.repo.update(data.id, {
      statusRateTrade: enumData.BidTradeRateStatus.DaDuyet.code,
      statusRatePrice: enumData.BidPriceRateStatus.DaDuyet.code,
      status,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.DuyetDanhGiaThuongMai.code
    bidHistory.save()

    this.emailService.ThongBaoDuyetDanhGiaGia(data.id)

    return { message: 'Duyệt đánh giá chào giá và điều kiện thương mại thành công.' }
  }
  /** Từ chối đánh giá chào giá và điều kiện thương mại */
  async rejectTradeRate(user: UserDto, data: { id: string; listItem: any[] }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionApproveBidPriceRate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    for (const item of data.listItem) {
      for (const bidSupplierItem of item.lstBidSupplier) {
        await this.bidSupplierRepo.update(bidSupplierItem.id, {
          statusTrade: enumData.BidSupplierTradeStatus.DangDanhGia.code,
          statusPrice: enumData.BidSupplierPriceStatus.DangDanhGia.code,
          noteMPOLeader: bidSupplierItem.noteMPOLeader,
          updatedBy: user.id,
        })
      }
    }

    await this.repo.update(data.id, {
      statusRateTrade: enumData.BidTradeRateStatus.TuChoi.code,
      statusRatePrice: enumData.BidPriceRateStatus.TuChoi.code,
      status: enumData.BidStatus.DangDanhGia.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.TuChoiDanhGiaThuongMai.code
    bidHistory.save()

    // gửi email bộ phận thương mại
    this.emailService.GuiMpoTuChoiDanhGiaGia(data.id)

    return { message: 'Từ chối đánh giá chào giá và điều kiện thương mại thành công.' }
  }
  //#endregion

  //#region bidRatePrice

  /** Check quyền tạo đánh giá giá cho gói thầu */
  async checkPermissionCreateBidPriceRate(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId } })
    if (bid) {
      if (bid.statusRatePrice === enumData.BidPriceRateStatus.DangTao.code || bid.statusRatePrice === enumData.BidPriceRateStatus.TuChoi.code) {
        result = await this.bidEmployeeAccessRepo.isMPO(user, bidId)
        if (!result) {
          message = 'Bạn không có quyền đánh giá giá cho gói thầu.'
        }
      } else {
        result = false
        message = 'Gói thầu đã được đánh giá giá.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Lấy ds NCC tham gia thầu và tính điểm */
  async loadPriceRate(user: UserDto, bidId: string) {
    const bidSupplierPriceValueRepo = this.repo.manager.getRepository(BidSupplierPriceValueEntity)
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res = await this.repo.getBid2(user, bidId)
    const dicStatusFile: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierFileStatus)
      lstStatus.forEach((c) => (dicStatusFile[c.code] = c.name))
    }
    const dicStatusPrice: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierPriceStatus)
      lstStatus.forEach((c) => (dicStatusPrice[c.code] = c.name))
    }
    for (const item of res.listItem) {
      // Lấy template bảng chào giá của Item
      item.lstBidPrice = await this.bidPriceRepo.getPrice(user, item.id)
      // Lấy hồ sơ giá của các NCC tham gia
      item.lstBidSupplier = await this.bidSupplierRepo.find({
        where: { bidId: item.id, statusFile: enumData.BidSupplierFileStatus.HopLe.code, companyId: user.companyId },
        relations: { supplier: true, bidSupplierPriceValue: true },
      })
      if (item.lstBidSupplier.length === 0) continue

      const lstBidSupplierId = item.lstBidSupplier.map((p) => p.id)

      const scoreDLC = item.scoreDLC

      const lstBidSupplierPriceValue = await bidSupplierPriceValueRepo.find({
        where: { bidSupplierId: In(lstBidSupplierId), companyId: user.companyId },
      })
      // Lọc qua danh sách các hạng mục - tính điểm cho từng hạng mục
      for (const itemBidPrice of item.lstBidPrice) {
        const supplierValue = lstBidSupplierPriceValue.filter((p) => p.bidPriceId === itemBidPrice.id).sort((a, b) => +a.value - +b.value)

        if (supplierValue.length > 0) {
          // Tìm ra giá trị nhỏ nhất theo hạng mục này
          const minValue = this.getMinOfArrayObj(supplierValue)

          const supplierValueLength = supplierValue.length

          const listSupplierScore = supplierValue.map((p) => +p.value)

          // Từ danh sách điểm tạm sẽ tính được độ lệch chuẩn
          const dlc = coreHelper.calDLC(listSupplierScore)

          // Lọc qua danh sach điểm này lại và tính điểm thiệt dựa vào độ lệch chuẩn ở trên
          for (let i = 0; i < supplierValueLength; i++) {
            const itemBidSupplierValue = supplierValue[i]
            let score = scoreDLC
            if (i !== 0 && dlc > 0) {
              score = scoreDLC - (+itemBidSupplierValue.value - +minValue.value) / dlc
            }
            itemBidSupplierValue.score = score
            // Lưu điểm
            await bidSupplierPriceValueRepo.update(itemBidSupplierValue.id, { score, updatedBy: user.id })
          }
        }
      }

      // Lọc qua danh sách NCC tính tổng điểm các hạng mục -> điểm tạm
      for (const bidSupplier of item.lstBidSupplier) {
        bidSupplier.supplierCode = bidSupplier.__supplier__.code
        bidSupplier.supplierName = bidSupplier.__supplier__.name
        delete bidSupplier.__supplier__
        bidSupplier.statusFileName = dicStatusFile[bidSupplier.statusFile]
        bidSupplier.statusPriceName = dicStatusPrice[bidSupplier.statusPrice]

        const lstPriceValue = lstBidSupplierPriceValue.filter((c) => c.bidSupplierId == bidSupplier.id)
        let scorePrice = 0
        for (const priceValue of lstPriceValue) {
          scorePrice += priceValue.score
        }
        bidSupplier.scorePrice = scorePrice

        bidSupplier.rankABCD = coreHelper.rankABCD(bidSupplier.scorePrice)
      }

      const listBidSupplierScorePrice = item.lstBidSupplier.map((p) => p.scorePrice)

      // Từ danh sách điểm tạm sẽ tính được độ lệch chuẩn
      const dlcBidSupplier = coreHelper.calDLC(listBidSupplierScorePrice)
      const maxScorePrice = Math.max(...listBidSupplierScorePrice)

      item.lstBidSupplier.sort((a, b) => b.scorePrice - a.scorePrice)
      const length = item.lstBidSupplier.length

      // Lọc qua danh sách NCC tính lại điểm cho các NCC
      for (let i = 0; i < length; i++) {
        const itemBidSupplier = item.lstBidSupplier[i]

        let scorePrice = scoreDLC
        if (i !== 0 && dlcBidSupplier > 0) {
          scorePrice = scoreDLC - (maxScorePrice - itemBidSupplier.scorePrice) / dlcBidSupplier
        }

        itemBidSupplier.scorePrice = scorePrice

        // Lưu điểm
        await this.bidSupplierRepo.update(itemBidSupplier.id, {
          scorePrice: itemBidSupplier.scorePrice,
          updatedBy: user.id,
        })
      }
    }

    return res
  }

  /** Lấy danh sách sách bidPrice và điểm cao nhất tương ứng */
  async loadBestPriceValue(user: UserDto, data: { bidId: string; bidSupplierId: string }) {
    const objBidSupplier = await this.bidSupplierRepo.findOne({
      where: { id: data.bidSupplierId, bidId: data.bidId, companyId: user.companyId },
      relations: { supplier: true, bidSupplierPriceValue: true, bidSupplierPriceColValue: true },
    })
    if (!objBidSupplier) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bidSupplierPriceColValue = await objBidSupplier.bidSupplierPriceColValue
    const bidSupplier = await this.bidSupplierRepo.find({ where: { bidId: data.bidId, companyId: user.companyId }, select: { id: true } })
    const listBidSupplierId = bidSupplier.map((p) => p.id)

    const bidPrices = await this.bidPriceRepo.getPrice(user, data.bidId)
    const bidPriceCols = await this.bidPriceColRepo.getBidPriceColAll(user, data.bidId)

    // Danh sách tất cả giá trị các NCC đã nộp
    const bidPriceValue = await this.repo.manager.getRepository(BidSupplierPriceValueEntity).find({
      where: { bidSupplierId: In(listBidSupplierId), companyId: user.companyId },
    })

    // function get data dynamic col by row
    const getDataRow = (row: any) => {
      for (const col of bidPriceCols) {
        row[col.id] = ''
        if (col.colType === enumData.ColType.MPO.code) {
          if (row.__bidPriceColValue__?.length > 0) {
            const cell = row.__bidPriceColValue__.find((c: any) => c.bidPriceColId === col.id)
            if (cell) row[col.id] = cell.value
          }
        }
        if (col.colType === enumData.ColType.Supplier.code) {
          const cell = bidSupplierPriceColValue.find((c) => c.bidPriceColId === col.id && c.bidPriceId === row.id)
          if (cell) row[col.id] = cell.value
        }
      }
    }

    // Lọc qua danh sách bidPrice
    for (let i = 0, length1 = bidPrices.length; i < length1; i++) {
      const item: any = bidPrices[i]
      getDataRow(item)

      // Giá trị mà NCC tương ứng đã nộp hồ sơ
      const supplierPriceValue = bidPriceValue.find((p) => p.bidPriceId === item.id && p.bidSupplierId === data.bidSupplierId)

      item.value = supplierPriceValue?.value
      item.score = supplierPriceValue?.score

      // Danh sách giá trị tất cả các NCC nộp theo tiêu chí này
      const listBidSupplierPriceValue = bidPriceValue.filter((p) => p.bidPriceId === item.id)

      // Nêu có và là loại list hoặc number thì bắt đầu tính điểm max và rank
      if (listBidSupplierPriceValue.length > 0) {
        // Lấy giá trị có điểm tốt nhất
        const bestSupplier = this.getMinOfArrayObj(listBidSupplierPriceValue)

        item.bestScore = bestSupplier?.score
        item.bestSupplierName = (await (await bestSupplier.bidSupplier).supplier).name
        item.bestValue = bestSupplier?.value

        if (supplierPriceValue) {
          item.rank = this.getCurrentRank(listBidSupplierPriceValue, supplierPriceValue?.bidSupplierId)
        }
      }

      item.__childs__ = item.__childs__ || []
      for (let i2 = 0, length2 = item.__childs__.length; i2 < length2; i2++) {
        const item2: any = item.__childs__[i2]
        getDataRow(item2)

        item2.__childs__ = item2.__childs__ || []
        for (let i3 = 0, length3 = item2.__childs__.length; i3 < length3; i3++) {
          const item3: any = item2.__childs__[i3]
          getDataRow(item3)
        }
      }
    }

    const lstCustomPrice = await this.repo.manager
      .getRepository(BidSupplierCustomPriceValueEntity)
      .find({ where: { bidSupplierId: data.bidSupplierId }, order: { sort: 'ASC', createdAt: 'ASC' } })

    return [bidPrices, bidPriceCols, bidSupplierPriceColValue, objBidSupplier, lstCustomPrice]
  }

  /** Tạo đánh giá giá cho gói thầu */
  async createPriceRate(user: UserDto, data: { id: string; listItem: any[] }) {
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionCreateBidPriceRate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    for (const item of data.listItem) {
      for (const bidSupplierItem of item.lstBidSupplier) {
        await this.bidSupplierRepo.update(bidSupplierItem.id, {
          scorePrice: bidSupplierItem.scorePrice,
          statusPrice: enumData.BidSupplierPriceStatus.DaXacNhan.code,
          isPriceValid: bidSupplierItem.isPriceValid,
          notePrice: bidSupplierItem.notePrice,
          scoreManualPrice: bidSupplierItem.scoreManualPrice,
          updatedBy: user.id,
        })
      }
    }

    let status = bid.status
    if (
      (bid.statusRateTech === enumData.BidTechRateStatus.DaTao.code || bid.statusRateTech === enumData.BidTechRateStatus.DaDuyet.code) &&
      (bid.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code || bid.statusRateTrade === enumData.BidTradeRateStatus.DaDuyet.code)
    ) {
      status = enumData.BidStatus.DangDuyetDanhGia.code
    }
    await this.repo.update(data.id, {
      statusRatePrice: enumData.BidPriceRateStatus.DaTao.code,
      status,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.DanhGiaGia.code
    bidHistory.save()

    if (bid.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code || bid.statusRateTrade === enumData.BidTradeRateStatus.DaDuyet.code) {
      this.emailService.GuiMpoLeadDuyetDanhGiaGia(data.id)
    }

    return { message: 'Tạo đánh giá bảng chào giá thành công.' }
  }

  /** Check quyền duyệt đánh giá điều kiện thương mại & giá cho gói thầu */
  async checkPermissionApproveBidPriceRate(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId } })
    if (bid) {
      if (bid.statusRatePrice === enumData.BidPriceRateStatus.DaDuyet.code || bid.statusRateTrade === enumData.BidTradeRateStatus.DaDuyet.code) {
        message = 'Gói thầu đã được xét duyệt đánh giá bảng chào giá, cơ cấu giá và điều kiện thương mại.'
      } else if (bid.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code && bid.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code) {
        const flagPermission = await this.bidEmployeeAccessRepo.isMPOLeader(user, bidId)
        if (flagPermission) {
          result = true
        } else {
          message = 'Bạn không có quyền xét duyệt đánh giá bảng chào giá, cơ cấu giá và điều kiện thương mại.'
        }
      } else if (bid.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code) {
        message = 'Chưa có yêu cầu xét duyệt đánh giá điều kiện thương mại.'
      } else if (bid.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code) {
        message = 'Chưa có yêu cầu xét duyệt đánh giá bảng chào giá, cơ cấu giá.'
      }
    }

    return { hasPermission: result, message }
  }

  //#endregion

  getMaxOfArrayObj(data: any[]) {
    const maxL = data.reduce((max, game) => {
      const first = max.score
      const second = game.score
      if (first === second) {
        const firstValue = +max.value
        const secondValue = +game.value
        return firstValue > secondValue ? max : game
      } else return first > second ? max : game
    })

    return maxL
  }

  getMinOfArrayObj(data: any[]) {
    const minL = data.reduce((min, game) => {
      const firstValue = +min.value
      const secondValue = +game.value
      return firstValue < secondValue ? min : game
    })

    return minL
  }

  getCurrentRank(data: any[], bidSupplierId: string) {
    let rank = 0
    if (data.length > 0) {
      const listFilter = data.sort((a, b) => b.score - a.score)
      const index = listFilter.findIndex((p) => p.bidSupplierId === bidSupplierId)
      if (index) rank = index
    }

    return `${rank + 1} /${data.length}`
  }

  //#region Phân tích giá

  /** Xếp hạng theo giá thấp nhất (Giá theo từng hạng mục) */
  async loadRankByMinPrice(user: UserDto, filter: { bidId: string; lstId: string[] }) {
    const bid = await this.repo.findOne({ where: { id: filter.bidId, companyId: user.companyId } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    // Danh sách bidPrice
    let bidPrices = await this.bidPriceRepo.getPrice(user, filter.bidId)
    if (filter?.lstId?.length > 0) {
      bidPrices = bidPrices.filter((c) => filter.lstId.includes(c.id))
    }

    const bidPriceCols = await this.bidPriceColRepo.getBidPriceColMPO(user, filter.bidId)

    // Lấy giá mới nhất các NCC chào (cùng số lượng)
    const lstDataPrice = await this.bidDealRepo.getPriceValueNewest(user, bid.id)

    let lstResult: any[] = []
    const length = bidPrices.length
    // Lọc qua danh sách bidPrice
    for (let i = 0; i < length; i++) {
      // Lấy từng hạng mục để xử lý và map dữ liệu
      const item = bidPrices[i]

      // Các giá trị mà NCC đã đàm phán hoặc có hồ sơ hợp lệ
      const lstBidSupplierPriceValueByItem = lstDataPrice
        .filter((p) => p.bidPriceId === item.id)
        .sort((a, b) => {
          if (a.value === '' || a.value === '0') return 1
          return +a.value - +b.value
        })

      const len = lstBidSupplierPriceValueByItem.length
      let rank = 1
      const lstRank = []
      for (let j = 0; j < len; ) {
        const itemTemp = lstBidSupplierPriceValueByItem[j]
        let temp: any = {}
        temp.unitPrice = +itemTemp.value
        temp.number = itemTemp.number
        item.number = itemTemp.number
        temp.price = temp.unitPrice * item.number
        temp.priceDiff = 0
        if (lstRank.length > 0) {
          temp.priceDiff = temp.price - lstRank[0].price
        }
        temp.lstSupplier = []
        temp.lstSupplier.push({
          supplierName: itemTemp.supplierName,
          supplierScorePrice: itemTemp.supplierScorePrice,
        })
        if (j < len - 1) {
          for (let k = j + 1; k < len; k++) {
            if (lstBidSupplierPriceValueByItem[k].value === itemTemp.value) {
              const itemTempC = lstBidSupplierPriceValueByItem[k]
              temp.lstSupplier.push({
                supplierName: itemTempC.supplierName,
                supplierScorePrice: itemTempC.supplierScorePrice,
              })
              j = k + 1
            } else {
              j = k
              rank = rank + 1
              k = len
            }
          }
          lstRank.push(temp)
        } else {
          lstRank.push(temp)
          j = len
        }
      }
      lstResult.push({ ...item, lstRank })
    }

    // find num rank to gen col
    let numRank = 0
    for (const item of lstResult) {
      if (item.lstRank && item.lstRank.length > numRank) {
        numRank = item.lstRank.length
      }
    }

    const lstTitleLv1 = []
    for (let i = 0; i < numRank; i++) {
      lstTitleLv1.push(0)
    }

    for (const item of lstResult) {
      for (let i = 0; i < numRank; i++) {
        const itemRank = item.lstRank[i]
        if (itemRank) {
          lstTitleLv1[i] += itemRank.price
        }
      }
      for (const col of bidPriceCols) {
        item[col.id] = ''
        if (item.__bidPriceColValue__?.length > 0) {
          const cell = item.__bidPriceColValue__.find((c: any) => c.bidPriceColId === col.id)
          if (cell) item[col.id] = cell.value
        }
      }
    }

    return [lstResult, bidPriceCols, lstTitleLv1]
  }

  /** Xếp hạng theo tổng điểm giá dạng 1 (Giá theo từng NCC) */
  async loadRankBySumPrice(user: UserDto, filter: { bidId: string; lstId: string[] }) {
    let lstResult: any[] = []
    const bid = await this.repo.findOne({ where: { id: filter.bidId, companyId: user.companyId } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    // Danh sách bidPrice
    let bidPrices = await this.bidPriceRepo.getPrice(user, filter.bidId)
    if (filter?.lstId?.length > 0) {
      bidPrices = bidPrices.filter((c) => filter.lstId.includes(c.id))
    }
    const bidPriceCols: any[] = await this.bidPriceColRepo.getBidPriceColMPO(user, filter.bidId)
    const bidPriceColTypeSup: any[] = await this.bidPriceColRepo.find({
      where: { bidId: filter.bidId, colType: enumData.ColType.Supplier.code, companyId: user.companyId, isDeleted: false },
      order: { sort: 'ASC', createdAt: 'ASC' },
      relations: { bidSupplierPriceColValue: true },
    })

    // Lấy giá mới nhất các NCC chào (cùng số lượng)
    const lstDataPrice = await this.bidDealRepo.getPriceValueNewest(user, bid.id)

    // sắp xếp giảm dần theo điểm giá
    let lstBidSupplier: any[] = await bid.bidSuppliers

    // sắp xếp nếu k nhập giá thì đưa xuống dưới
    const lenListBidSupplier = lstBidSupplier.length
    const lenBidPrices = bidPrices.length
    for (let i = 0; i < lenListBidSupplier; i++) {
      const bidSupplier = lstBidSupplier[i]
      bidSupplier.totalPrice = 0
      for (let j = 0; j < lenBidPrices; j++) {
        const bidPrice = bidPrices[j]
        const bidSupplierPriceValue = lstDataPrice.find((c) => c.supplierId === bidSupplier.supplierId && c.bidPriceId === bidPrice.id)
        if (!bidSupplierPriceValue || bidSupplierPriceValue.value === '' || bidSupplierPriceValue.value === '0') {
          bidSupplier.isNotFillPrice = true
        } else {
          // Tính tổng giá
          bidPrice.number = bidSupplierPriceValue.number
          bidSupplier.totalPrice += +bidSupplierPriceValue.value * bidPrice.number
        }
      }
    }

    // sắp xếp theo tổng giá, giá thấp lên trước
    lstBidSupplier = lstBidSupplier.sort((a, b) => a.totalPrice - b.totalPrice)

    // sắp xếp không nhập đủ giá thì đưa xuống dưới
    const lstSupplierOk = lstBidSupplier.filter((c) => !c.isNotFillPrice)
    const lstSupplierNotOk = lstBidSupplier.filter((c) => c.isNotFillPrice === true)
    const lstSupplier = [...lstSupplierOk, ...lstSupplierNotOk]

    // Lọc qua danh sách bidPrice
    for (let i = 0; i < lenBidPrices; i++) {
      // Lấy từng hạng mục để xử lý và map dữ liệu
      const item = bidPrices[i]

      const len = lstSupplier.length
      let rank = 0
      let totalPrice = -1
      const lstRank = []
      for (let j = 0; j < len; j++) {
        const itemTemp = lstSupplier[j]
        if (itemTemp.totalPrice !== totalPrice) {
          totalPrice = itemTemp.totalPrice
          rank++
        }

        const bidSupplierPriceValue = lstDataPrice.find((c) => c.supplierId === itemTemp.supplierId && c.bidPriceId === item.id)
        if (bidSupplierPriceValue) {
          let temp: any = {}
          temp.supplierName = bidSupplierPriceValue.supplierName
          temp.totalPrice = itemTemp.totalPrice
          temp.rank = rank
          temp.unitPrice = +bidSupplierPriceValue.value
          temp.price = temp.unitPrice * item.number
          temp.priceDiff = 0
          if (lstRank.length > 0) {
            temp.priceDiff = temp.price - lstRank[0].price
          }
          temp.isNotFillPrice = itemTemp.isNotFillPrice
          temp.bidSupplierId = itemTemp.id
          lstRank.push(temp)
        }
      }

      lstResult.push({ ...item, lstRank })
    }

    let bidSuppliers: any[] = []
    if (lstResult[0]?.lstRank.length > 0) {
      bidSuppliers = [...lstResult[0].lstRank]
      for (const bidSupplier of bidSuppliers) {
        bidSupplier.totalPriceDiff = 0
      }
    }
    for (const item of lstResult) {
      for (const itemRank of item.lstRank) {
        const bidSupplier = bidSuppliers.find((c) => c.bidSupplierId == itemRank.bidSupplierId)
        if (bidSupplier) {
          bidSupplier.totalPriceDiff += itemRank.priceDiff
        }
        for (const col of bidPriceColTypeSup) {
          itemRank[col.id] = ''
          const cell = col.__bidSupplierPriceColValue__.find(
            (c: { bidPriceId: string; bidSupplierId: any }) => c.bidPriceId === item.id && c.bidSupplierId === itemRank.bidSupplierId,
          )
          if (cell) {
            itemRank[col.id] = cell.value
          }
        }
      }

      for (const col of bidPriceCols) {
        item[col.id] = ''
        if (item.__bidPriceColValue__?.length > 0) {
          const cell = item.__bidPriceColValue__.find((c: any) => c.bidPriceColId === col.id)
          if (cell) item[col.id] = cell.value
        }
      }
    }

    return [lstResult, bidPriceCols, bidPriceColTypeSup, bid, bidSuppliers]
  }

  /** Xếp hạng theo tổng giá dạng 2 (Giá theo từng NCC) (Mỗi dòng 1 NCC) */
  async loadSupplierRankBySumPrice(user: UserDto, filter: { bidId: string; lstId: string[] }) {
    const bid = await this.repo.findOne({ where: { id: filter.bidId, companyId: user.companyId } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    // Danh sách bidPrice
    let bidPrices = await this.bidPriceRepo.getPrice(user, filter.bidId)
    if (filter?.lstId?.length > 0) {
      bidPrices = bidPrices.filter((c) => filter.lstId.includes(c.id))
    }

    // Lấy giá mới nhất các NCC chào (cùng số lượng)
    const lstDataPrice = await this.bidDealRepo.getPriceValueNewest(user, bid.id)

    let lstBidSupplier = (await bid.bidSuppliers) as any[]
    const lstSupplierId = lstBidSupplier.map((c) => c.supplierId)
    if (lstSupplierId.length == 0) throw new Error('Chưa có doanh nghiệp tham gia gói thầu')
    const lstSupplier = await this.repo.manager.getRepository(SupplierEntity).find({ where: { id: In(lstSupplierId), companyId: user.companyId } })

    // sắp xếp nếu k nhập giá thì đưa xuống dưới
    const lenListBidSupplier = lstBidSupplier.length
    const lenBidPrices = bidPrices.length
    for (let i = 0; i < lenListBidSupplier; i++) {
      const bidSupplier = lstBidSupplier[i]
      const supplier = lstSupplier.find((c) => c.id == bidSupplier.supplierId)
      bidSupplier.supplierName = supplier?.name || ''
      bidSupplier.totalPrice = 0
      for (let j = 0; j < lenBidPrices; j++) {
        const bidPrice = bidPrices[j]
        const bidSupplierPriceValue = lstDataPrice.find((c) => c.supplierId === bidSupplier.supplierId && c.bidPriceId === bidPrice.id)
        bidSupplier[bidPrice.id] = {}
        if (!bidSupplierPriceValue) {
          bidSupplier.isNotFillPrice = true
          bidSupplier[bidPrice.id].unitPrice = 0
          bidSupplier[bidPrice.id].totalPrice = 0
        } else {
          // Tính tổng giá
          bidPrice.number = bidSupplierPriceValue.number

          const unitPrice = +bidSupplierPriceValue.value
          bidSupplier[bidPrice.id].unitPrice = unitPrice
          const totalPrice = unitPrice * bidPrice.number
          bidSupplier[bidPrice.id].totalPrice = totalPrice

          bidSupplier.totalPrice += totalPrice
        }
      }
    }

    // sắp xếp theo tổng giá, giá thấp lên trước
    lstBidSupplier = lstBidSupplier.sort((a, b) => a.totalPrice - b.totalPrice)

    // sắp xếp không nhập đủ giá thì đưa xuống dưới
    const lstSupplierOk = lstBidSupplier.filter((c) => !c.isNotFillPrice)
    const lstSupplierNotOk = lstBidSupplier.filter((c) => c.isNotFillPrice === true)

    lstBidSupplier = [...lstSupplierOk, ...lstSupplierNotOk]
    return [lstBidSupplier, bidPrices]
  }

  //#endregion

  //#region Báo cáo kết quả đánh giá
  private checkSameCapacity(dataReport: any) {
    dataReport.errorNotSameCapacity = ''
    const len = dataReport.bidSuppliers.length
    if (len == 0) return true

    var dic: any[] = []
    for (let i = 0; i < len; i++) {
      dic[i] = {}
      const templates = dataReport.supplierCapacities
      // ds tiêu chí năng lực NCC i
      dic[i].templates = templates
      for (const capacity of dic[i].templates) {
        dic[i][capacity.name] = capacity
      }
    }

    for (let i = 1; i < len; i++) {
      // số lượng các tiêu chí k đồng nhất
      if (dic[i].templates.length != dic[0].templates.length) {
        dataReport.errorNotSameCapacity = 'Số lượng các tiêu chí không đồng nhất!'
        return false
      }
      for (const capacity of dic[i].templates) {
        // name k đồng nhất
        if (dic[i - 1][capacity.name] == null) {
          dataReport.errorNotSameCapacity = `Tên tiêu chí ${capacity.name} không đồng nhất!`
          return false
        }
        // level k đồng nhất
        if ((dic[i][capacity.name].parentId != null) != (dic[0][capacity.name].parentId != null)) {
          dataReport.errorNotSameCapacity = `Cấp tiêu chí ${capacity.name} không đồng nhất!`
          return false
        }
        // percent k đồng nhất
        if (dic[i][capacity.name].percent != dic[0][capacity.name].percent) {
          dataReport.errorNotSameCapacity = `Tỉ trọng tiêu chí ${capacity.name} không đồng nhất!`
          return false
        }
        // type k đồng nhất
        if (dic[i][capacity.name].type != dic[0][capacity.name].type) {
          dataReport.errorNotSameCapacity = `Loại dữ liệu tiêu chí ${capacity.name} không đồng nhất!`
          return false
        }
        // percentRule k đồng nhất
        if (dic[i][capacity.name].percentRule != dic[0][capacity.name].percentRule) {
          dataReport.errorNotSameCapacity = `Điều kiện tiêu chí ${capacity.name} đạt không đồng nhất!`
          return false
        }
      }
    }
    return true
  }

  private calScore(item: any) {
    let score = 0
    if (item.__childs__?.length > 0) {
      const length = item.__childs__.length
      let scoreC = 0
      for (let i = 0; i < length; i++) {
        // tslint:disable-next-line: no-shadowed-variable
        let temp = this.calScore(item.__childs__[i])
        if (isNaN(temp) || !isFinite(temp)) temp = 0
        scoreC += temp
      }
      const temp = (item.percent * scoreC) / 100
      score += temp
    } else {
      // tslint:disable-next-line:triple-equals
      if (item.type === enumData.DataType.Number.code && item.value && item.value.trim() != '') {
        let temp = 0
        const x = +item.value
        if (item.isCalUp) {
          if (x >= item.percentRule) {
            temp = item.percent
          } else {
            temp = (x * item.percent) / item.percentRule // giá trị * tỉ trọng / điều kiện b
          }
        } else {
          if (x <= item.percentRule) {
            temp = item.percent
          } else if (x >= item.percentDownRule) {
            temp = 0
          } else {
            temp = ((item.percentDownRule - x) * item.percent) / (item.percentDownRule - item.percentRule)
          }
        }
        if (isNaN(temp) || !isFinite(temp)) score += 0
        else score += temp
      } else if (item.type === enumData.DataType.List.code) {
        const chose = item.__supplierCapacityListDetails__.find((p: any) => p.isChosen)
        const temp = chose ? chose.value : 0
        const finalTemp = (temp * item.percent) / 100
        score += finalTemp
      }
    }
    if (isNaN(score) || !isFinite(score)) return 0

    return score
  }

  // Tính điểm năng lực
  private getSupplierScoreCapacity(row: any, bidSupplier: any, dataReport: any) {
    // lấy năng lực NCC
    const supplierCapacity = dataReport.supplierCapacities.filter((c: any) => c.supplierId == bidSupplier.supplierId)
    let supplierCapacityByRow = supplierCapacity.find((c: any) => c.name == row.name)
    if (supplierCapacityByRow) {
      const objCopy = { ...supplierCapacityByRow }
      if (!objCopy.parentId) {
        objCopy.__childs__ = supplierCapacity.filter((c: any) => c.parentId == objCopy.id)
      }

      let score = this.calScore(objCopy)
      return score
    }

    return ''
  }

  // Tính điểm kỹ thuật
  private getSupplierScoreTech(row: any, bidSupplier: any, dataReport: any) {
    const bidTech = dataReport.bidTechs.find((p: any) => p.id === row.id)
    if (bidTech && bidTech.percent > 0 && bidTech.__childs__.length > 0) {
      let temp = 0
      for (const child of bidTech.__childs__) {
        const find = bidSupplier.__bidSupplierTechValue__.find((p: { bidTechId: any }) => p.bidTechId === child.id)
        if (find && find.score > 0) {
          temp += find.score
        }
      }
      temp = (temp * bidTech.percent) / 100
      return temp
    } else if (bidSupplier.__bidSupplierTechValue__) {
      const find = bidSupplier.__bidSupplierTechValue__.find((p: { bidTechId: any }) => p.bidTechId === row.id)
      if (find) {
        return find.score
      }
      return ''
    } else {
      return ''
    }
  }

  // Tính điểm thương mại
  private getSupplierScoreTrade(row: any, bidSupplier: any, dataReport: any) {
    const bidTrade = dataReport.bidTrades.find((p: any) => p.id === row.id)
    if (bidTrade && bidTrade.percent > 0 && bidTrade.__childs__.length > 0) {
      let temp = 0
      for (const child of bidTrade.__childs__) {
        const find = bidSupplier.__bidSupplierTradeValue__.find((p: { bidTradeId: any }) => p.bidTradeId === child.id)
        if (find && find.score > 0) {
          temp += find.score
        }
      }
      temp = (temp * bidTrade.percent) / 100
      return temp
    } else if (bidSupplier.__bidSupplierTradeValue__) {
      const find = bidSupplier.__bidSupplierTradeValue__.find((p: { bidTradeId: any }) => p.bidTradeId === row.id)
      if (find) {
        return find.score
      }
      return ''
    } else {
      return ''
    }
  }

  // Tính điểm giá
  private getSupplierValue(row: any, bidSupplier: any) {
    const res = {
      number: 0,
      value: 0,
      price: 0,
    }
    if (bidSupplier.__bidSupplierPriceValue__) {
      const find = bidSupplier.__bidSupplierPriceValue__.find((p: { bidPriceId: any }) => p.bidPriceId === row.id)
      if (find && find.value !== '' && find.value != null) {
        res.value = +find.value
        res.number = +find.number
        if (!find.number) res.number = row.number
        res.price = res.value * res.number
      }
    }
    return res
  }

  /** Lấy giá mới nhất của từng NCC tham gia gói thầu */
  private async getDataPriceNewest(user: UserDto, bidId: string) {
    const result: any[] = []
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId } })
    if (!bid) throw new NotFoundException('Không tìm thấy thông tin gói thầu.')

    const bidSupplierRepo = this.repo.manager.getRepository(BidSupplierEntity)
    const bidDealRepo = this.repo.manager.getRepository(BidDealEntity)
    const bidDealSupplierRepo = this.repo.manager.getRepository(BidDealSupplierEntity)
    const bidAuctionRepo = this.repo.manager.getRepository(BidAuctionEntity)
    const bidAuctionSupplierRepo = this.repo.manager.getRepository(BidAuctionSupplierEntity)

    // Lấy hồ sơ của các NCC tham gia
    const lstBidSupplier = await bidSupplierRepo.find({ where: { bidId, companyId: user.companyId } })
    if (lstBidSupplier.length == 0) return result

    // Danh sách các lần đàm phán giá của gói thầu
    const lstBidDeal = await bidDealRepo.find({
      where: { bidId, companyId: user.companyId },
      order: { createdAt: 'DESC' },
    })
    const lstBidDealId = lstBidDeal.map((c) => c.id)
    let lstBidDealSupplier: BidDealSupplierEntity[] = []
    if (lstBidDealId.length > 0) {
      lstBidDealSupplier = await bidDealSupplierRepo.find({
        where: {
          bidDealId: In(lstBidDealId),
          status: enumData.BidDealSupplierStatus.DaGuiGiaMoi.code,
          companyId: user.companyId,
        },
        order: { createdAt: 'DESC' },
      })
    }

    // Danh sách các lần đấu giá của gói thầu
    const lstBidAuction = await bidAuctionRepo.find({
      where: { bidId, companyId: user.companyId },
      order: { createdAt: 'DESC' },
    })
    const lstBidAuctionId = lstBidAuction.map((c) => c.id)
    let lstBidAuctionSupplier: BidAuctionSupplierEntity[] = []
    if (lstBidAuctionId.length > 0) {
      lstBidAuctionSupplier = await bidAuctionSupplierRepo.find({
        where: {
          bidAuctionId: In(lstBidAuctionId),
          status: enumData.BidAuctionSupplierStatus.DaDauGia.code,
          companyId: user.companyId,
        },
        order: { createdAt: 'DESC' },
      })
    }

    const lstPrice: any[] = []
    for (const bidSupplier of lstBidSupplier) {
      let submitType = 0 //0: hồ sơ giá, 1: đàm phán giá, 2: đấu giá
      // Nếu tham gia cả 2 thì so sánh lấy lần gần nhất
      const bidDealSupplier = lstBidDealSupplier.find((c) => c.supplierId == bidSupplier.supplierId)
      const bidAuctionSupplier = lstBidAuctionSupplier.find((c) => c.supplierId == bidSupplier.supplierId)
      if (bidDealSupplier != null && bidAuctionSupplier != null) {
        if (bidDealSupplier.createdAt > bidAuctionSupplier.createdAt) {
          submitType = 1
        } else {
          submitType = 2
        }
      } else if (bidDealSupplier != null) submitType = 1
      else if (bidAuctionSupplier != null) submitType = 2

      if (submitType == 1 && bidDealSupplier) {
        const bidDeal = lstBidDeal.find((c) => c.id == bidDealSupplier.bidDealId)
        if (bidDeal) {
          const lstBidDealPrice = await bidDeal.bidDealPrices
          const lstPriceValue: any[] = await bidDealSupplier.bidDealSupplierPriceValue
          // Lấy số lượng theo số lượng đàm phán
          for (const itemPriceValue of lstPriceValue) {
            const bidDealPrice = lstBidDealPrice.find((c) => c.bidPriceId == itemPriceValue.bidPriceId)
            if (bidDealPrice && bidDealPrice.number) {
              itemPriceValue.number = bidDealPrice.number
            }
          }
          const submitDate = bidDealSupplier.submitDate || bidDealSupplier.createdAt
          lstPrice.push({ ...bidSupplier, lstPriceValue, submitType, submitDate })
        }
      } else if (submitType == 2 && bidAuctionSupplier) {
        const lstPriceValue = await bidAuctionSupplier.bidAuctionSupplierPriceValue
        const submitDate = bidAuctionSupplier.submitDate || bidAuctionSupplier.createdAt
        lstPrice.push({ ...bidSupplier, lstPriceValue, submitType, submitDate })
      } else if (
        bidSupplier.status === enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code ||
        bidSupplier.status === enumData.BidSupplierStatus.DangDanhGia.code ||
        bidSupplier.status === enumData.BidSupplierStatus.DaDanhGia.code
      ) {
        const submitDate = bidSupplier.submitDate || bidSupplier.createdAt
        // nếu chưa reset giá thì lấy kết quả chào giá, còn không thì lấy kết quả chào giá bổ sung
        if (bid.statusResetPrice === enumData.BidResetPriceStatus.ChuaTao.code) {
          const lstPriceValue = await bidSupplier.bidSupplierPriceValue
          lstPrice.push({ ...bidSupplier, lstPriceValue, submitType, submitDate })
        } else if (bidSupplier.statusResetPrice == enumData.BidSupplierResetPriceStatus.DaBoSung.code) {
          const lstPriceValue = await bidSupplier.bidSupplierPriceValue
          lstPrice.push({ ...bidSupplier, lstPriceValue, submitType, submitDate })
        }
      }
    }

    return lstPrice
  }

  /** Báo cáo kết quả đánh giá */
  async getDataReportRateBid(user: UserDto, bidId: string) {
    const res: any = {}
    const bid: any = await this.repo.findOne({
      where: { id: bidId, companyId: user.companyId },
      select: { id: true, name: true, status: true, approveChooseSupplierWinDate: true },
    })
    if (!bid) throw new NotFoundException('Không tìm thấy thông tin gói thầu.')
    res.id = bid.id
    res.bidName = bid.name
    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }
    res.statusName = dicStatus[res.status]

    res.approveChooseSupplierWinDate = bid.approveChooseSupplierWinDate
    res.listItem = await this.repo.find({
      where: { parentId: bid.id },
      relations: { service: true },
      select: {
        id: true,
        hiddenScore: true,
        percentTech: true,
        percentTrade: true,
        percentPrice: true,
        serviceId: true,
        service: { code: true, name: true },
      },
    })

    for (const bidChild of res.listItem) {
      bidChild.itemName = bidChild.__service__.code + ' - ' + bidChild.__service__.name
      delete bidChild.__service__

      // Lấy template hồ sơ kỹ thuật của gói thầu
      bidChild.bidTechs = await this.bidTechRepo.getTech(user, bidChild.id)
      // Lấy template ĐKTM của gói thầu
      bidChild.bidTrades = await this.bidTradeRepo.getTrade(user, bidChild.id)
      // Lấy template bảng chào giá của gói thầu
      bidChild.bidPrices = await this.bidPriceRepo.getPrice(user, bidChild.id)
      // Lấy hồ sơ của các NCC tham gia
      bidChild.lstBidSupplier = await this.bidSupplierRepo.find({
        where: { bidId: bidChild.id, companyId: user.companyId },
        relations: { supplier: true, bidSupplierTechValue: true, bidSupplierTradeValue: true },
      })

      bidChild.supplierCapacities = []
      const lstBidSupplierTemp = []
      const bidSupplierFileStatus: any = enumData.BidSupplierFileStatus
      const lstBidSupplierPriceValue = await this.getDataPriceNewest(user, bidChild.id)
      for (const bidSupplier of bidChild.lstBidSupplier) {
        bidSupplier.successBidStatus = bidSupplier.isSuccessBid ? 'Trúng thầu' : 'Không trúng thầu'
        bidSupplier.validStatus = bidSupplierFileStatus[bidSupplier.statusFile].name
        const bidSupplierValue = lstBidSupplierPriceValue.find((c) => c.id == bidSupplier.id)
        if (bidSupplierValue) {
          lstBidSupplierTemp.push({
            ...bidSupplier,
            __bidSupplierPriceValue__: bidSupplierValue.lstPriceValue,
          })
        }
      }

      if (lstBidSupplierTemp.length == 0) continue

      const lstValue = lstBidSupplierTemp.map((c) => c.scorePrice)
      const maxValue = Math.max(...lstValue)
      const dlc = coreHelper.calDLC(lstValue)

      const lstValueManual = lstBidSupplierTemp.map((c) => c.scoreManualPrice)
      const maxValueManual = Math.max(...lstValueManual)
      const dlcManual = coreHelper.calDLC(lstValueManual)

      const lstSupplierId = lstBidSupplierTemp.map((c) => c.supplierId).filter((value, idx, self) => self.indexOf(value) == idx)
      const lstSupplierService = await this.repo.manager.getRepository(SupplierServiceEntity).find({
        where: {
          supplierId: In(lstSupplierId),
          serviceId: bidChild.serviceId,
          companyId: user.companyId,
          isDeleted: false,
        },
      })

      bidChild.lstBidSupplier = []
      for (const item of lstBidSupplierTemp) {
        let isHasTotal = false
        let total = 0
        let totalManual = 0
        if (item.statusTech === enumData.BidSupplierTechStatus.DaXacNhan.code || item.statusTech === enumData.BidSupplierTechStatus.DaDuyet.code) {
          isHasTotal = true
          total += (item.scoreTech * bidChild.percentTech) / 100
          totalManual += (item.scoreManualTech * bidChild.percentTech) / 100
        } else {
          item.scoreTech = -1
          item.scoreManualTech = -1
        }

        if (
          item.statusTrade === enumData.BidSupplierTradeStatus.DaXacNhan.code ||
          item.statusTrade === enumData.BidSupplierTradeStatus.DaDuyet.code
        ) {
          isHasTotal = true
          total += (item.scoreTrade * bidChild.percentTrade) / 100
          totalManual += (item.scoreManualTrade * bidChild.percentTrade) / 100
        } else {
          item.scoreTrade = -1
          item.scoreManualTrade = -1
        }

        if (
          item.statusPrice === enumData.BidSupplierPriceStatus.DaXacNhan.code ||
          item.statusPrice === enumData.BidSupplierPriceStatus.DaDuyet.code
        ) {
          isHasTotal = true
          let priceScore = 0
          if (dlc > 0) {
            priceScore = bidChild.percentPrice - (maxValue - item.scorePrice) / dlc
          } else {
            priceScore = bidChild.percentPrice
          }
          total += priceScore

          let priceManualScore = 0
          if (dlcManual > 0) {
            priceManualScore = bidChild.percentPrice - (maxValueManual - item.scoreManualPrice) / dlcManual
          } else {
            priceManualScore = bidChild.percentPrice
          }
          totalManual += priceManualScore
        } else {
          item.scorePrice = -1
          item.scoreManualPrice = -1
        }

        if (!isHasTotal) {
          total = -1
          totalManual = -1
        }

        const supplierService = lstSupplierService.find((c) => c.supplierId == item.supplierId)
        var totalScoreCapacity = 0
        if (supplierService) totalScoreCapacity = supplierService.score

        bidChild.lstBidSupplier.push({
          ...item,
          scoreTotal: total,
          scoreManualTotal: totalManual,
          scoreCapacity: totalScoreCapacity,
        })
      }

      bidChild.supplierCapacities = await this.repo.manager.getRepository(SupplierCapacityEntity).find({
        where: { supplierId: In(lstSupplierId), serviceId: bidChild.serviceId, companyId: user.companyId, isDeleted: false },
        relations: { supplierCapacityListDetails: true },
      })

      //#region code FE
      let isSameCapacity = true
      //Kiểm tra hồ sơ năng lực đồng nhất
      if (bidChild.lstBidSupplier.length > 0) {
        isSameCapacity = await this.checkSameCapacity(bidChild)
      }

      //#region Tính tổng tỉ trọng

      // Template theo NCC
      bidChild.capacities = []

      if (isSameCapacity && bidChild.lstBidSupplier.length > 0) {
        // lấy template năng lực theo supplier đầu tiên
        const bidSupplierFirst = bidChild.lstBidSupplier[0]
        const templateCapacity = bidChild.supplierCapacities.filter((c: any) => c.supplierId == bidSupplierFirst.supplierId)
        bidChild.capacities = templateCapacity.filter((c: any) => c.parentId == null)
        bidChild.capacities = bidChild.capacities.sort((a: any, b: any) => a.sort - b.sort)
        for (const capacity of bidChild.capacities) {
          const lstChild = templateCapacity.filter((c: any) => c.parentId == capacity.id)
          capacity.__childs__ = lstChild.sort((a: any, b: any) => a.sort - b.sort)
        }
      }
      bidChild.totalPercentTech = 0
      for (const tech of bidChild.bidTechs) {
        tech.__childs__ = tech.__childs__.filter((c: any) => c.isDeleted === false)
        if (tech.percent > 0) bidChild.totalPercentTech += tech.percent
      }

      bidChild.totalPercentTrade = 0
      for (const trade of bidChild.bidTrades) {
        trade.__childs__ = trade.__childs__.filter((c: any) => c.isDeleted === false)
        if (trade.percent > 0) bidChild.totalPercentTrade += trade.percent
      }

      for (const priceLv1 of bidChild.bidPrices) {
        priceLv1.__childs__ = priceLv1.__childs__.filter((c: any) => c.isDeleted === false)
        for (const priceLv2 of priceLv1.__childs__) {
          priceLv2.__childs__ = priceLv2.__childs__.filter((c: any) => c.isDeleted === false)
        }
      }
      //#endregion

      //#region Tính tổng điểm
      for (const bidSupplier of bidChild.lstBidSupplier) {
        bidSupplier.supplierName = bidSupplier.__supplier__.name
        if (isSameCapacity) {
          for (const capacity of bidChild.capacities) {
            capacity[bidSupplier.id] = this.getSupplierScoreCapacity(capacity, bidSupplier, bidChild)
            for (const child of capacity.__childs__) {
              child[bidSupplier.id] = this.getSupplierScoreCapacity(child, bidSupplier, bidChild)
            }
          }
        }

        for (const tech of bidChild.bidTechs) {
          tech[bidSupplier.id] = this.getSupplierScoreTech(tech, bidSupplier, bidChild)
          for (const child of tech.__childs__) {
            child[bidSupplier.id] = this.getSupplierScoreTech(child, bidSupplier, bidChild)
          }
        }

        for (const trade of bidChild.bidTrades) {
          trade[bidSupplier.id] = this.getSupplierScoreTrade(trade, bidSupplier, bidChild)
          for (const child of trade.__childs__) {
            child[bidSupplier.id] = this.getSupplierScoreTrade(child, bidSupplier, bidChild)
          }
        }

        bidSupplier.totalPrice = 0
        for (const priceLv1 of bidChild.bidPrices) {
          priceLv1[bidSupplier.id] = this.getSupplierValue(priceLv1, bidSupplier)
          const price = priceLv1[bidSupplier.id].price
          if (price > 0) bidSupplier.totalPrice += price
          for (const priceLv2 of priceLv1.__childs__) {
            priceLv2[bidSupplier.id] = this.getSupplierValue(priceLv2, bidSupplier)
            for (const priceLv3 of priceLv2.__childs__) {
              priceLv3[bidSupplier.id] = this.getSupplierValue(priceLv3, bidSupplier)
            }
          }
        }
      }
      //#endregion

      //#region Tính xếp loại

      let len = bidChild.lstBidSupplier.length
      bidChild.lstBidSupplier = bidChild.lstBidSupplier.sort(
        (a: { scoreCapacity: number }, b: { scoreCapacity: number }) => b.scoreCapacity - a.scoreCapacity,
      )
      let rank = 0
      let currentScore = 0
      for (let i = 0; i < len; i++) {
        let scoreCapacity = bidChild.lstBidSupplier[i].scoreCapacity
        if (scoreCapacity != currentScore) {
          rank++
          currentScore = scoreCapacity
        }
        bidChild.lstBidSupplier[i].rankCapacity = rank
      }

      bidChild.lstBidSupplier = bidChild.lstBidSupplier.sort((a: any, b: any) => b.scoreTech - a.scoreTech)
      rank = 0
      currentScore = 0
      for (let i = 0; i < len; i++) {
        let scoreTech = bidChild.lstBidSupplier[i].scoreTech
        if (scoreTech >= 0 && scoreTech != currentScore) {
          rank++
          currentScore = scoreTech
        }
        bidChild.lstBidSupplier[i].rankTech = rank
      }

      bidChild.lstBidSupplier = bidChild.lstBidSupplier.sort((a: any, b: any) => b.scoreTrade - a.scoreTrade)
      rank = 0
      currentScore = 0
      for (let i = 0; i < len; i++) {
        let scoreTrade = bidChild.lstBidSupplier[i].scoreTrade
        if (scoreTrade >= 0 && scoreTrade != currentScore) {
          rank++
          currentScore = scoreTrade
        }
        bidChild.lstBidSupplier[i].rankTrade = rank
      }

      // xếp hạng theo tổng giá
      bidChild.lstBidSupplier = bidChild.lstBidSupplier.sort((a: any, b: any) => a.totalPrice - b.totalPrice)
      rank = 0
      currentScore = 0
      for (let i = 0; i < len; i++) {
        let totalPrice = bidChild.lstBidSupplier[i].totalPrice
        if (totalPrice != currentScore) {
          rank++
          currentScore = totalPrice
        }
        bidChild.lstBidSupplier[i].rankPrice = rank
      }

      // xếp hạng theo điểm giá
      bidChild.lstBidSupplier = bidChild.lstBidSupplier.sort((a: any, b: any) => b.scorePrice - a.scorePrice)
      rank = 0
      currentScore = 0
      for (let i = 0; i < len; i++) {
        let score = bidChild.lstBidSupplier[i].scorePrice
        if (score >= 0 && score != currentScore) {
          rank++
          currentScore = score
        }
        bidChild.lstBidSupplier[i].rankScorePrice = rank
      }

      // xếp hạng tổng
      bidChild.lstBidSupplier = bidChild.lstBidSupplier.sort((a: any, b: any) => b.scoreTotal - a.scoreTotal)
      rank = 0
      currentScore = 0
      for (let i = 0; i < len; i++) {
        let scoreTotal = bidChild.lstBidSupplier[i].scoreTotal
        if (scoreTotal > 0 && scoreTotal != currentScore) {
          rank++
          currentScore = scoreTotal
        }
        bidChild.lstBidSupplier[i].rankTotalScore = rank
      }

      //#endregion

      //#endregion
    }

    return res
  }

  /** In kết quả đánh giá */
  async getDataPrintRateBid(user: UserDto, bidId: string) {
    const res: any = await this.repo.findOne({
      where: { id: bidId, companyId: user.companyId },
      relations: { employeeAccess: { employee: true }, childs: { service: true }, bidType: true, masterBidGuarantee: true },
    })
    if (!res) throw new NotFoundException('Không tìm thấy thông tin gói thầu.')

    const lstAccess = res.__employeeAccess__ || []
    delete res.__employeeAccess__
    res.listMember = lstAccess
      .filter((c) => c.type === enumData.BidRuleType.Memmber.code)
      .map((c) => c.__employee__.name)
      .join(' - ')

    res.listOther = lstAccess
      .filter((c) => c.type === enumData.BidRuleType.Other.code)
      .map((c) => c.__employee__.name)
      .join(' - ')

    if (res.prId) {
      const pr = await this.repo.manager.getRepository(PrEntity).findOne({ where: { id: res.prId }, select: { id: true, code: true } })
      if (pr) res.prCode = pr.code
    }

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }
    res.statusName = dicStatus[res.status]

    res.bidTypeName = res.__bidType__?.name
    delete res.__bidType__
    res.masterBidGuaranteeName = res.__masterBidGuarantee__?.name
    delete res.__masterBidGuarantee__

    const mpoObj = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
    if (mpoObj) res.mpo = mpoObj.__employee__.name

    const mpoLeaderObj = lstAccess.find((c) => c.type === enumData.BidRuleType.MPOLeader.code)
    if (mpoLeaderObj) res.mpoLeader = mpoLeaderObj.__employee__.name

    const techObj = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
    if (techObj) res.tech = techObj.__employee__.name

    const techLeaderObj = lstAccess.find((c) => c.type === enumData.BidRuleType.TechLeader.code)
    if (techLeaderObj) res.techLeader = techLeaderObj.__employee__.name

    res.listItem = []
    for (const item of res.__childs__) {
      res.listItem.push({
        id: item.id,
        itemName: item.__service__?.code + ' - ' + item.__service__?.name,
        quantityItem: item.quantityItem,
        percentTech: item.percentTech,
        percentTrade: item.percentTrade,
        percentPrice: item.percentPrice,
        serviceId: item.serviceId,
      })
    }
    delete res.__childs__

    for (const bidItem of res.listItem) {
      // Lấy hồ sơ của các NCC tham gia
      bidItem.lstBidSupplier = await this.bidSupplierRepo.find({
        where: { bidId: bidItem.id, companyId: user.companyId },
        relations: { supplier: true },
      })
      if (bidItem.lstBidSupplier.length == 0) continue

      const bidSupplierFileStatus: any = enumData.BidSupplierFileStatus
      for (const bidSupplier of bidItem.lstBidSupplier) {
        bidSupplier.supplierName = bidSupplier.__supplier__.name
        delete bidSupplier.__supplier__
        bidSupplier.validStatus = bidSupplierFileStatus[bidSupplier.statusFile]?.name
        bidSupplier.successBidStatus = bidSupplier.isSuccessBid ? 'Trúng thầu' : 'Không trúng thầu'
      }

      const lstValue = bidItem.lstBidSupplier.map((c) => c.scorePrice)
      const maxValue = Math.max(...lstValue)
      const dlc = coreHelper.calDLC(lstValue)

      const lstValueManual = bidItem.lstBidSupplier.map((c) => c.scoreManualPrice)
      const maxValueManual = Math.max(...lstValueManual)
      const dlcManual = coreHelper.calDLC(lstValueManual)

      const lstSupplierId = bidItem.lstBidSupplier.map((c) => c.supplierId)
      const lstSupplierService = await this.repo.manager.getRepository(SupplierServiceEntity).find({
        where: {
          supplierId: In(lstSupplierId),
          serviceId: bidItem.serviceId,
          companyId: user.companyId,
          isDeleted: false,
        },
        select: { supplierId: true, score: true },
      })

      for (const item of bidItem.lstBidSupplier) {
        let isHasTotal = false
        item.scoreTotal = 0
        item.scoreManualTotal = 0
        if (item.statusTech === enumData.BidSupplierTechStatus.DaXacNhan.code || item.statusTech === enumData.BidSupplierTechStatus.DaDuyet.code) {
          isHasTotal = true
          item.scoreTotal += (item.scoreTech * bidItem.percentTech) / 100
          item.scoreManualTotal += (item.scoreManualTech * bidItem.percentTech) / 100
        } else {
          item.scoreTech = -1
          item.scoreManualTech = -1
        }

        if (
          item.statusTrade === enumData.BidSupplierTradeStatus.DaXacNhan.code ||
          item.statusTrade === enumData.BidSupplierTradeStatus.DaDuyet.code
        ) {
          isHasTotal = true
          item.scoreTotal += (item.scoreTrade * bidItem.percentTrade) / 100
          item.scoreManualTotal += (item.scoreManualTrade * bidItem.percentTrade) / 100
        } else {
          item.scoreTrade = -1
          item.scoreManualTrade = -1
        }

        if (
          item.statusPrice === enumData.BidSupplierPriceStatus.DaXacNhan.code ||
          item.statusPrice === enumData.BidSupplierPriceStatus.DaDuyet.code
        ) {
          isHasTotal = true
          let priceScore = 0
          if (dlc > 0) {
            priceScore = bidItem.percentPrice - (maxValue - item.scorePrice) / dlc
          } else {
            priceScore = bidItem.percentPrice
          }
          item.scoreTotal += priceScore

          let priceManualScore = 0
          if (dlcManual > 0) {
            priceManualScore = bidItem.percentPrice - (maxValueManual - item.scoreManualPrice) / dlcManual
          } else {
            priceManualScore = bidItem.percentPrice
          }
          item.scoreManualTotal += priceManualScore
        } else {
          item.scorePrice = -1
          item.scoreManualPrice = -1
        }

        if (!isHasTotal) {
          item.scoreTotal = -1
          item.scoreManualTotal = -1
        }

        const supplierService = lstSupplierService.find((c) => c.supplierId == item.supplierId)
        item.scoreCapacity = 0
        if (supplierService) item.scoreCapacity = supplierService.score
      }
    }

    return res
  }

  //#endregion

  //#region Phê duyệt kết thúc thầu

  /** Kiểm tra quyền Gửi yêu cầu phê duyệt kết thúc thầu */
  private async checkPermissionSendRequestFinishBid(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId } })
    if (bid) {
      if (bid.status === enumData.BidStatus.DuyetNCCThangThau.code) {
        result = await this.bidEmployeeAccessRepo.isMPO(user, bidId)
        if (!result) message = 'Bạn không có quyền gửi yêu cầu phê duyệt kết thúc thầu.'
      } else message = 'Gói thầu đã được gửi yêu cầu phê duyệt kết thúc thầu.'
    }

    return { hasPermission: result, message }
  }

  /** Kiểm tra quyền Phê duyệt kết thúc thầu */
  async checkPermissionApproveFinishBid(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId } })
    if (bid) {
      if (bid.status === enumData.BidStatus.DangDuyetKetThucThau.code) {
        result = await this.bidEmployeeAccessRepo.isMPOLeader(user, bidId)
        if (!result) message = 'Bạn không có quyền phê duyệt kết thúc thầu.'
      } else message = 'Gói thầu đã được phê duyệt kết thúc thầu.'
    }

    return { hasPermission: result, message }
  }

  /** Gửi yêu cầu phê duyệt kết thúc thầu */
  async sendRequestFinishBid(user: UserDto, data: { id: string; fileScan: string; noteFinishBidMPO: string }) {
    if (!data.id) throw new BadRequestException(ERROR_NOT_FOUND_DATA)
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionSendRequestFinishBid(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(data.id, {
      status: enumData.BidStatus.DangDuyetKetThucThau.code,
      noteFinishBidMPO: data.noteFinishBidMPO,
      fileScan: data.fileScan,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.GuiYeuCauPheDuyetKetThucThau.code
    bidHistory.save()

    // Gửi email thông báo mpoLeader
    // await this.emailService.ThongBaoKetQuaDauThauDuocDuyet(data.id)

    return { message: 'Phê duyệt NCC thắng thầu thành công.' }
  }

  /** Phê duyệt kết thúc thầu */
  async approveFinishBid(user: UserDto, data: { id: string }) {
    if (!data.id) throw new BadRequestException(ERROR_NOT_FOUND_DATA)
    const supplierServiceRepo = this.repo.manager.getRepository(SupplierServiceEntity)
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionApproveFinishBid(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId, isDeleted: false } })
    if (!bid) throw new Error('Gói thầu không còn tồn tại.')

    await this.repo.update(data.id, {
      status: enumData.BidStatus.HoanTat.code,
      bidCloseDate: new Date(),
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.PheDuyetKetThucThau.code
    bidHistory.save()

    const lstBidSupplier: any[] = await this.bidSupplierRepo.find({
      where: {
        bid: { parentId: data.id, companyId: user.companyId, isDeleted: false },
        bidSupplierPrices: { bidPriceLevel: 1, isDeleted: false },
        isDeleted: false,
      },
      relations: { bidSupplierPrices: true },
      select: { id: true, totalPrice: true, isSuccessBid: true, supplierId: true, bidSupplierPrices: { id: true, price: true } },
    })
    for (const bidSupplier of lstBidSupplier) {
      let totalPrice1 = 0
      let lstPrice = bidSupplier.__bidSupplierPrices__ || []
      if (lstPrice.length > 0) {
        for (const item of lstPrice) {
          totalPrice1 += +item.price
        }

        // cập nhật giá cho supplierService nếu NCC trúng thầu
        if (!bidSupplier.isSuccessBid) continue

        const supplierService = await supplierServiceRepo.findOne({
          where: {
            supplierId: bidSupplier.supplierId,
            serviceId: bid.serviceId,
            companyId: user.companyId,
            isDeleted: false,
          },
          select: { id: true, totalPrice: true },
        })
        if (!supplierService) continue

        let totalPrice2 = supplierService.totalPrice ? +supplierService.totalPrice : 0
        totalPrice2 += totalPrice1
        await supplierServiceRepo.update(supplierService.id, { totalPrice: totalPrice2, updatedBy: user.id })
      }

      await this.bidSupplierRepo.update(bidSupplier.id, { totalPrice: totalPrice1, updatedBy: user.id })
    }

    // Gửi email thông báo nội bộ
    this.emailService.ThongBaoKetQuaDauThauDuocDuyet(data.id)
    // Gửi email NCC trúng thầu
    this.emailService.ThongBaoTrungThau(data.id)
    // Gửi email cảm ơn NCC tham gia đấu thầu nhưng không trúng
    this.emailService.ThuCamOnThamGiaThau(data.id)

    return { message: 'Phê duyệt kết thúc thầu thành công.' }
  }

  //#endregion

  //#region Đàm phán/ Đấu giá

  /** DS item khi Đàm phán/ Đấu giá */
  async itemPagination(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    /* tìm bid cha để lấy danh sách item*/
    const parentBid: any = await this.repo.findOne({ where: { id: data.where.bidId }, relations: { bidItems: true } })
    if (!parentBid) throw new BadRequestException('Không xác định được gói thầu!')

    let numItem = 0
    for (const item of parentBid.__bidItems__) {
      numItem += item.quantityItem
    }
    const res: any[] = await this.repo.findAndCount({
      where: { parentId: data.where.bidId, isDeleted: false },
      relations: { service: true },
      skip: data.skip,
      take: data.take,
      select: { id: true, quantityItem: true, status: true, service: { code: true, name: true } },
    })
    if (res[0].length == 0) return res

    const setStatus = new Set()
    setStatus.add(enumData.BidStatus.DangDamPhanGia.code)
    setStatus.add(enumData.BidStatus.DongDamPhanGia.code)
    setStatus.add(enumData.BidStatus.DangDauGia.code)
    setStatus.add(enumData.BidStatus.DongDauGia.code)

    for (const item of res[0]) {
      item.itemName = item.__service__.code + ' - ' + item.__service__.name
      delete item.__service__

      item.quantityItem = numItem
      if (!setStatus.has(item.status)) {
        item.statusColor = '#28a745'
        item.statusName = 'Sẵn sàng'
        continue
      }

      item.statusColor = enumData.BidStatus[item.status].color
      item.statusName = enumData.BidStatus[item.status].name
    }

    return res
  }

  //#endregion

  //#region Truy vấn thông tin gói thầu

  /** Danh sách gói thầu đã hoàn tất */
  async resultPagination(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    let whereCon: any = {
      parentId: IsNull(),
      status: enumData.BidStatus.HoanTat.code,
      employeeAccess: { employeeId: user.employeeId },
      companyId: user.companyId,
      isDeleted: false,
    }
    if (data.where.status?.length > 0) whereCon.status = In(data.where.status)
    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.publicDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.dateFrom) {
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}")`)
    } else if (data.where.dateTo) {
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.dateTo).format('YYYY-MM-DD')}")`)
    }

    if (data.where.name) {
      whereCon = [
        { ...whereCon, code: Like(`%${data.where.name}%`) },
        { ...whereCon, name: Like(`%${data.where.name}%`) },
      ]
    }
    if (data.where.serviceId) whereCon.childs = { serviceId: data.where.serviceId }

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      select: {
        id: true,
        code: true,
        name: true,
        status: true,
        createdAt: true,
      },
    })
    if (res[1] == 0) return res
    const lstId = res[0].map((c) => c.id)
    const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
      where: { bidId: In(lstId), companyId: user.companyId, isDeleted: false },
      relations: { employee: true },
      select: {
        id: true,
        bidId: true,
        type: true,
        employeeId: true,
        employee: {
          id: true,
          name: true,
        },
      },
    })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }

    for (const item of res[0]) {
      const lstAccess = lstEmployeeAccess.filter((c) => c.bidId == item.id)

      const objTech = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
      item.techName = objTech?.__employee__?.name || ''
      const objMpo = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
      item.mpoName = objMpo?.__employee__?.name || ''

      const lstAccessUser = lstAccess.filter((c) => c.employeeId === user.employeeId)
      item.isMPO = lstAccessUser.some((c) => c.type === enumData.BidRuleType.MPO.code)
      item.isMPOLeader = lstAccessUser.some((c) => c.type === enumData.BidRuleType.MPOLeader.code)
      item.isTech = lstAccessUser.some((c) => c.type === enumData.BidRuleType.Tech.code)
      item.isTechLeader = lstAccessUser.some((c) => c.type === enumData.BidRuleType.TechLeader.code)
      item.isMember = lstAccessUser.some((c) => c.type === enumData.BidRuleType.Memmber.code)

      item.statusName = dicStatus[item.status]
    }

    return res
  }

  //#endregion
}
