import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { BillService } from './bill.service'
import { BillController } from './bill.controller'
import { HttpModule } from '@nestjs/axios'
import { BillRepository } from '../../repositories/bill.repository'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BillRepository]), HttpModule],
  controllers: [BillController],
  providers: [BillService],
})
export class BillModule {}
