import { MigrationInterface, QueryRunner } from "typeorm";

export class createTicketEntity1673004195050 implements MigrationInterface {
    name = 'createTicketEntity1673004195050'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`ticket_history\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`ticketId\` varchar(36) NOT NULL, \`description\` text NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`ticket\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(50) NOT NULL, \`name\` varchar(250) NOT NULL, \`description\` text NULL, \`status\` varchar(50) NOT NULL DEFAULT 'MoiTao', \`flag\` tinyint NOT NULL DEFAULT 0, UNIQUE INDEX \`IDX_2ebff7c458ed09689a53242fec\` (\`code\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`ticket_comment\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`ticketId\` varchar(36) NOT NULL, \`userId\` varchar(36) NOT NULL, \`comment\` text NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`ticket_history\` ADD CONSTRAINT \`FK_2bde375c7f9f2ffd77381df611b\` FOREIGN KEY (\`ticketId\`) REFERENCES \`ticket\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`ticket_comment\` ADD CONSTRAINT \`FK_653665e6f8dd8c3d0d0f3a07598\` FOREIGN KEY (\`ticketId\`) REFERENCES \`ticket\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`ticket_comment\` ADD CONSTRAINT \`FK_3d08f54062e7f42b6ff1ca26b23\` FOREIGN KEY (\`userId\`) REFERENCES \`user\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`ticket_comment\` DROP FOREIGN KEY \`FK_3d08f54062e7f42b6ff1ca26b23\``);
        await queryRunner.query(`ALTER TABLE \`ticket_comment\` DROP FOREIGN KEY \`FK_653665e6f8dd8c3d0d0f3a07598\``);
        await queryRunner.query(`ALTER TABLE \`ticket_history\` DROP FOREIGN KEY \`FK_2bde375c7f9f2ffd77381df611b\``);
        await queryRunner.query(`DROP TABLE \`ticket_comment\``);
        await queryRunner.query(`DROP INDEX \`IDX_2ebff7c458ed09689a53242fec\` ON \`ticket\``);
        await queryRunner.query(`DROP TABLE \`ticket\``);
        await queryRunner.query(`DROP TABLE \`ticket_history\``);
    }

}
