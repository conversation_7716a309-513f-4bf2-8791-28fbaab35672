import { Module } from '@nestjs/common'
import { ReportService } from './report.service'
import { ReportController } from './report.controller'
import { TypeOrmExModule } from '../../typeorm'
import {
  BidEmployeeAccessRepository,
  BidPriceRepository,
  BidRepository,
  BidSupplierRepository,
  ServiceRepository,
  SupplierRepository,
  SupplierServiceRepository,
} from '../../repositories'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      BidRepository,
      SupplierRepository,
      BidSupplierRepository,
      BidPriceRepository,
      BidEmployeeAccessRepository,
      ServiceRepository,
      SupplierServiceRepository,
    ]),
  ],
  controllers: [ReportController],
  providers: [ReportService],
})
export class ReportModule {}
