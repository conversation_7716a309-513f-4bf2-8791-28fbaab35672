import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableService1750995938350 implements MigrationInterface {
    name = 'UpdateTableService1750995938350'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`service\` ADD \`stockQuantity\` decimal(12,2) NOT NULL DEFAULT '0.00'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`service\` DROP COLUMN \`stockQuantity\``);
    }

}
