import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, BeforeInsert, BeforeUpdate } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierEntity } from './supplier.entity'
import { EmployeeEntity } from './employee.entity'
import { UserConfirmCodeEntity } from './userConfirmCode.entity'
import { PWD_SALT_ROUNDS } from '../constants'
import { compare, hash } from 'bcrypt'

/** Danh sách tài khoản */
@Entity({ name: 'user' })
export class UserEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  username: string

  @Column({
    name: 'password',
    type: 'text',
    nullable: false,
  })
  password: string

  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (this.password) {
      const hashedPassword = await hash(this.password, PWD_SALT_ROUNDS)
      this.password = hashedPassword
    }
  }

  comparePassword(candidate: string) {
    return compare(candidate, this.password)
  }

  /** Loại user: enum UserType */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string

  /** Nhà cung cấp */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  supplierId: string
  @OneToOne(() => SupplierEntity, (p) => p.user, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: SupplierEntity

  /** Nhân viên */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  employeeId: string
  @OneToOne(() => EmployeeEntity, (p) => p.user)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: EmployeeEntity

  /** JSON DS quyền của user */
  @Column({
    type: 'longtext',
    nullable: true,
  })
  roles: string

  /** DS mã xác nhận của user */
  @OneToMany(() => UserConfirmCodeEntity, (p) => p.user)
  userConfirm: Promise<UserConfirmCodeEntity[]>
}
