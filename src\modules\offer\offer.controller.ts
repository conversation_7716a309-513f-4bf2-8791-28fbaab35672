import { Body, Controller, Get, Param, Post, Put, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { ApeAuthGuard } from '../common/guards'
import {
  BidCustomPriceCreateDto,
  BidCustomPriceUpdateDto,
  BidPriceColCreateDto,
  BidPriceColUpdateDto,
  BidPriceCreateDto,
  BidPriceUpdateDto,
  BidTradeCreateDto,
  BidTradeUpdateDto,
} from './dto2'
import { OfferService } from './offer.service'
import { OfferPriceService } from './offerPrice.service'
import { OfferSupplerService } from './offerSupplier.service'
import { OfferTradeService } from './offerTrade.service'

@UseGuards(ApeAuthGuard)
@ApiTags('Offer')
@Controller('offer')
export class OfferController {
  constructor(
    private readonly service: OfferService,
    private readonly offerTradeService: OfferTradeService,
    private readonly offerPriceService: OfferPriceService,
    private readonly offerSupplerService: OfferSupplerService,
  ) {}
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Hàm thêm mới kế hoạch' })
  @Post('create_data')
  async createData(@CurrentUser() user: UserDto, @Body() data: any) {
    return this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Hàm thêm mới kế hoạch' })
  @Post('create_data_client')
  async createDataClient(@CurrentUser() user: UserDto, @Body() data: any) {
    return this.service.createDataClient(user, data)
  }

  @ApiOperation({ summary: 'Hàm thêm mới kế hoạch' })
  @Post('get_offer_deal_id')
  async getOfferDealId(@CurrentUser() user: UserDto, @Body() data: any) {
    return this.service.getOfferDealId(user, data)
  }

  @ApiOperation({ summary: 'Hàm thêm mới kế hoạch' })
  @Post('send_offer')
  async sendOffer(@CurrentUser() user: UserDto, @Body() data: any) {
    return this.service.sendOffer(user, data)
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Hàm thêm mới kế hoạch' })
  @Post('update_data')
  async updateData(@CurrentUser() user: UserDto, @Body() data: any) {
    return this.service.updateData(user, data)
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Danh sách kế hoạch' })
  @Post('find_one')
  async findOne(@Body() data: { id: string }, @CurrentUser() user: UserDto) {
    return await this.service.findOne(user, data)
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Danh sách kế hoạch' })
  @Post('find')
  async find(@CurrentUser() user: UserDto, @Body() data: { status?: string; offerTypeCode: string }) {
    return await this.service.find(user, data)
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Danh sách nhà cung cấp theo yêu cầu báo giá' })
  @Post('load_offer_by_supplier')
  async loadOfferBySupplier(@CurrentUser() user: UserDto, @Body() data: { offerId: string }) {
    return await this.service.loadOfferBySupplier(user, data)
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Danh sách kế hoạch' })
  @Post('pagination')
  async pagination(@Body() data: PaginationDto, @CurrentUser() user: UserDto) {
    return await this.service.pagination(user, data)
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Danh sách kế hoạch' })
  @Post('send_approve')
  async sendApprove(@Body() data: { targetId: string }, @CurrentUser() user: UserDto) {
    return await this.service.sendApprove(user, data)
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Danh sách kế hoạch' })
  @Post('approve')
  async approve(@Body() data: { targetId: string }, @CurrentUser() user: UserDto) {
    return await this.service.approve(user, data)
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Danh sách kế hoạch' })
  @Post('public')
  async public(@Body() data: { targetId: string }, @CurrentUser() user: UserDto) {
    return await this.service.public(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds gói thầu' })
  @Post('find_with_ex')
  public async findWithExmat(@CurrentUser() user: UserDto, @Body() data: { exMatGrId?: string }) {
    return await this.service.findWithExmat(user, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách PR đã duyệt theo phân quyền và có thể tạo thầu' })
  @Post('find_item')
  public async findItem(@CurrentUser() user: UserDto, @Body() data: { lstId: string[] }) {
    return await this.service.findItem(user, data)
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Danh sách kế hoạch' })
  @Post('update_time')
  async updateTime(@Body() data: { targetId: string; timePeriod: Date }, @CurrentUser() user: UserDto) {
    return await this.service.updateTime(data, user)
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Danh sách kế hoạch' })
  @Post('config_price_pagination')
  async configPagination(@Body() data: { id: string }, @CurrentUser() user: UserDto) {
    return await this.service.configPagination(user, data)
  }
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Danh sách lựa chọn cho tiêu chí kiểu List' })
  @Get('listdetail_list/:id')
  public async bidTechListDetail_list(@CurrentUser() user: UserDto, @Param('id') id: any) {
    return await this.service.bidTechListDetail_list(user, id)
  }
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Tạo lựa chọn cho tiêu chí kiểu List' })
  @Post('list_detail_create_data')
  public async listDetailCreateData(@CurrentUser() user: UserDto, @Body() data: { offerServiceId: string; nameCol: string; price: number }) {
    return await this.service.listDetailCreateData(user, data)
  }
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Cập nhật lựa chọn cho tiêu chí kiểu List' })
  @Post('list_detail_update_data')
  public async listDetailUpdateData(
    @CurrentUser() user: UserDto,
    @Body() data: { id: string; offerServiceId: string; nameCol: string; price: number },
  ) {
    return await this.service.listDetailUpdateData(user, data)
  }
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Xóa lựa chọn cho tiêu chí kiểu List' })
  @Post('list_detail_delete_data')
  public async listDetailDeleteData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.listDetailDeleteData(user, data.id)
  }

  @ApiOperation({ summary: 'Danh sách kế hoạch' })
  @Post('pagination_client')
  async paginationClient(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.paginationClient(user, data)
  }

  @ApiOperation({ summary: 'Danh sách kế hoạch' })
  @Post('pagination_client_no_token')
  async paginationClientNoToken(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.paginationClientNoToken(user, data)
  }

  @ApiOperation({ summary: 'Danh sách kế hoạch' })
  @Post('detail_client')
  async detailClient(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.detailClient(user, data)
  }

  @ApiOperation({ summary: 'Chi tiết báo giá' })
  @Post('detail_offer_client')
  async detailOfferClient(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.detailOfferClient(user, data)
  }

  @ApiOperation({ summary: 'Tải template ĐKTM từ Item' })
  @Get('load_trade/:id')
  public async loadTrade(@CurrentUser() user: UserDto, @Param('id') bidItemId: string) {
    return await this.offerTradeService.loadTrade(user, bidItemId)
  }

  @ApiOperation({ summary: 'Lưu template ĐKTM cho gói thầu' })
  @Post('create_trade/:id')
  public async createTrade(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { noteTrade: string }) {
    return await this.offerTradeService.createTrade(user, bidId, data)
  }

  @ApiOperation({ summary: 'Duyệt thiết lập điều kiện thương mại của gói thầu' })
  @Post('trade_accept/:id')
  public async tradeAccept(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { noteTrade?: string }) {
    return await this.offerTradeService.tradeAccept(user, bidId, data)
  }

  @ApiOperation({ summary: 'Lấy thiết lập ĐKTM của gói thầu' })
  @Get('get_trade/:id')
  public async getTrade(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.offerTradeService.getTrade(user, bidId)
  }

  @ApiOperation({ summary: 'Lấy data cbb tiêu chí cấp 1' })
  @Get('trade_get_data/:bidid')
  public async tradeGetData(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.offerTradeService.tradeGetData(user, bidId)
  }

  @ApiOperation({ summary: 'Tạo thêm ĐKTM' })
  @Post('trade_create_data')
  public async tradeCreateData(@CurrentUser() user: UserDto, @Body() data: BidTradeCreateDto) {
    return await this.offerTradeService.tradeCreateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật ĐKTM' })
  @Post('trade_update_data')
  public async tradeUpdateData(@CurrentUser() user: UserDto, @Body() data: BidTradeUpdateDto) {
    return await this.offerTradeService.tradeUpdateData(user, data)
  }

  @ApiOperation({ summary: 'Xoá ĐKTM' })
  @Post('trade_delete_data')
  public async tradeDeleteData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.offerTradeService.tradeDeleteData(user, data.id)
  }

  @ApiOperation({ summary: 'Xoá tất cả ĐKTM' })
  @Post('trade_deleteall_data')
  public async tradeDeleteAllData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.offerTradeService.tradeDeleteAllData(user, data.id)
  }

  @ApiOperation({ summary: 'Import excel ĐKTM' })
  @Post('trade_import/:bidId')
  public async trade_import(
    @CurrentUser() user: UserDto,
    @Param('bidId') bidId: string,
    @Body() data: { lstDataTable1: any[]; lstDataTable2: any[] },
  ) {
    return await this.offerTradeService.trade_import(user, bidId, data)
  }

  @ApiOperation({ summary: 'Danh sách lựa chọn cho tiêu chí kiểu List' })
  @Get('trade_listdetail_list/:id')
  public async bidTradeListDetail_list(@CurrentUser() user: UserDto, @Param('id') id: any) {
    return await this.offerTradeService.bidTradeListDetail_list(user, id)
  }

  @ApiOperation({ summary: 'Tạo lựa chọn cho tiêu chí kiểu List' })
  @Post('trade_listdetail_create_data')
  public async bidTradeListDetail_create_data(
    @CurrentUser() user: UserDto,
    @Body() data: { id: string; bidTradeId: string; name: string; value: number },
  ) {
    return await this.offerTradeService.bidTradeListDetail_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật lựa chọn cho tiêu chí kiểu List' })
  @Post('trade_listdetail_update_data')
  public async bidTradeListDetail_update_data(
    @CurrentUser() user: UserDto,
    @Body() data: { id: string; bidTradeId: string; name: string; value: number },
  ) {
    return await this.offerTradeService.bidTradeListDetail_update_data(user, data)
  }

  @ApiOperation({ summary: 'Xóa lựa chọn cho tiêu chí kiểu List' })
  @Post('trade_listdetail_delete_data')
  public async bidTradeListDetail_delete_data(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.offerTradeService.bidTradeListDetail_delete_data(user, data.id)
  }

  //#region bidPrice

  @ApiOperation({ summary: 'Lấy các hạng mục giá của gói thầu' })
  @Post('price_find')
  public async price_find(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.offerPriceService.price_find(user, data)
  }

  @ApiOperation({ summary: 'Tải các hạng mục chào giá từ template Item' })
  @Get('load_price/:id')
  public async loadPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.offerPriceService.loadPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Tải các hạng mục chào giá từ template Item' })
  @Get('load_price_service/:id')
  public async loadPriceService(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.offerPriceService.loadPriceItem(user, bidId)
  }

  @ApiOperation({ summary: 'Tải các hạng mục cơ cấu giá từ template Item' })
  @Get('load_customprice/:id')
  public async loadCustomPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.offerPriceService.loadCustomPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Lưu template chào giá cho gói thầu' })
  @Post('create_price/:id')
  public async createPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { notePrice: string }) {
    return await this.offerPriceService.createPrice(user, bidId, data)
  }

  @ApiOperation({ summary: 'Lưu template ĐKTM cho gói thầu' })
  @Post('send_price/:id')
  public async sendPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { noteTrade: string }) {
    return await this.offerPriceService.sendPrice(user, bidId, data)
  }

  @ApiOperation({ summary: 'Duyệt thiết lập  hạng mục cơ cấu, cơ cấu giá của gói thầu' })
  @Post('price_accept/:id')
  public async priceAccept(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { notePrice?: string }) {
    return await this.offerPriceService.priceAccept(user, bidId, data)
  }

  @ApiOperation({ summary: 'Lấy template chào giá cho gói thầu' })
  @Get('get_price/:id')
  public async getPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.offerPriceService.getPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Lưu cấu hình bảng giá' })
  @UseGuards(ApeAuthGuard)
  @Post('save_reset_price/:id')
  public async saveResetPrice(
    @CurrentUser() user: UserDto,
    @Param('id') bidId: string,
    @Body()
    data: {
      lstSupplierId: string[]
      resetPriceEndDate: Date
      isRequireFilePriceDetail: boolean
      isRequireFileTechDetail: boolean
    },
  ) {
    return await this.offerPriceService.saveResetPrice(user, bidId, data)
  }

  @ApiOperation({ summary: 'Danh sách ncc đã nộp chào giá hiệu chỉnh' })
  @UseGuards(ApeAuthGuard)
  @Get('bid_supplier_join_reset_price/:bidid')
  public async bidSupplierJoinResetPrice(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.offerPriceService.bidSupplierJoinResetPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Kết thúc nộp chào giá hiệu chỉnh' })
  @UseGuards(ApeAuthGuard)
  @Put('end_reset_price/:id')
  public async endResetPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.offerPriceService.endResetPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Lấy template cơ cấu giá của gói thầu' })
  @Get('get_customprice/:id')
  public async getCustomPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.offerPriceService.getCustomPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Tạo hạng mục giá mới' })
  @Post('price_create_data')
  public async priceCreateData(@CurrentUser() user: UserDto, @Body() data: BidPriceCreateDto) {
    return await this.offerPriceService.priceCreateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật hạng mục giá' })
  @Post('price_update_data')
  public async priceUpdateData(@CurrentUser() user: UserDto, @Body() data: BidPriceUpdateDto) {
    return await this.offerPriceService.priceUpdateData(user, data)
  }

  @ApiOperation({ summary: 'Xóa hạng mục giá' })
  @Post('price_delete_data')
  public async priceDeleteData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.offerPriceService.priceDeleteData(user, data.id)
  }

  @ApiOperation({ summary: 'Xóa tất cả hạng mục giá của gói thầu' })
  @Post('price_deleteall_data')
  public async priceDeleteAllData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.offerPriceService.priceDeleteAllData(user, data.id)
  }

  @ApiOperation({ summary: 'Lưu công thức tính đơn giá' })
  @Post('setting_fomular')
  public async setting_fomular(@CurrentUser() user: UserDto, @Body() data: { id: string; fomular: string }) {
    return await this.offerPriceService.setting_fomular(user, data)
  }

  @ApiOperation({ summary: 'Setup cách tính điểm giá của Item gói thầu' })
  @Post('setting_way_cal_score_price')
  public async saveSettingPriceCalWay(@CurrentUser() user: UserDto, @Body() data: { id: string; wayCalScorePrice: string }) {
    return await this.offerPriceService.saveSettingPriceCalWay(user, data)
  }

  @ApiOperation({ summary: 'Import excel chào giá' })
  @Post('price_import/:bidId')
  public async price_import(
    @CurrentUser() user: UserDto,
    @Param('bidId') bidId: string,
    @Body() data: { lstDataTable1: any[]; lstDataTable2: any[] },
  ) {
    return await this.offerPriceService.price_import(user, bidId, data)
  }

  @ApiOperation({ summary: 'Tạo hạng mục cơ cấu giá mới' })
  @Post('customprice_create_data')
  public async customPriceCreateData(@CurrentUser() user: UserDto, @Body() data: BidCustomPriceCreateDto) {
    return await this.offerPriceService.customPriceCreateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật hạng mục cơ cấu giá' })
  @Post('customprice_update_data')
  public async customPriceUpdateData(@CurrentUser() user: UserDto, @Body() data: BidCustomPriceUpdateDto) {
    return await this.offerPriceService.customPriceUpdateData(user, data)
  }

  @ApiOperation({ summary: 'Xóa hạng mục cơ cấu giá' })
  @Post('customprice_delete_data')
  public async customPriceDeleteData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.offerPriceService.customPriceDeleteData(user, data.id)
  }

  @ApiOperation({ summary: 'Xóa tất cả hạng mục cơ cấu giá của Item gói thầu' })
  @Post('customprice_deleteall_data')
  public async customPriceDeleteAllData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.offerPriceService.customPriceDeleteAllData(user, data.id)
  }

  @ApiOperation({ summary: 'Import template cơ cấu giá Item gói thầu' })
  @Post('customprice_import/:bidId')
  public async custompriceImport(@CurrentUser() user: UserDto, @Param('bidId') bidId: string, @Body() data: { lstData: any[] }) {
    return await this.offerPriceService.custompriceImport(user, bidId, data)
  }

  @ApiOperation({ summary: 'Danh sách cột động Item' })
  @Get('price_col_list/:id')
  public async bidPriceCol_list(@CurrentUser() user: UserDto, @Param('id') id: any) {
    return await this.offerPriceService.bidPriceCol_list(user, id)
  }

  @ApiOperation({ summary: 'Tạo cột động Item' })
  @Post('price_col_create_data')
  public async bidPriceCol_create_data(@CurrentUser() user: UserDto, @Body() data: BidPriceColCreateDto) {
    return await this.offerPriceService.bidPriceCol_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật cột động Item' })
  @Post('price_col_update_data')
  public async bidPriceCol_update_data(@CurrentUser() user: UserDto, @Body() data: BidPriceColUpdateDto) {
    return await this.offerPriceService.bidPriceCol_update_data(user, data)
  }

  @ApiOperation({ summary: 'Xóa cột động Item' })
  @Post('price_col_delete_data')
  public async bidPriceCol_delete_data(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.offerPriceService.bidPriceCol_delete_data(user, data.id)
  }

  @ApiOperation({ summary: 'Xóa tất cả cột động Item gói thầu' })
  @Post('price_col_delete_all_data')
  public async bidPriceCol_delete_all_data(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.offerPriceService.bidPriceCol_delete_all_data(user, data.bidId)
  }

  @ApiOperation({ summary: 'Danh sách thông tin mở rộng của hạng mục chào giá' })
  @Get('price_listdetail_list/:id')
  public async bidPriceListDetail_list(@CurrentUser() user: UserDto, @Param('id') id: any) {
    return await this.offerPriceService.bidPriceListDetail_list(user, id)
  }

  @ApiOperation({ summary: 'Tạo thông tin mở rộng của hạng mục chào giá' })
  @Post('price_listdetail_create_data')
  public async bidPriceListDetail_create_data(
    @CurrentUser() user: UserDto,
    @Body() data: { bidPriceId: string; name: string; type: string; value: string },
  ) {
    return await this.offerPriceService.bidPriceListDetail_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật thông tin mở rộng của hạng mục chào giá' })
  @Post('price_listdetail_update_data')
  public async bidPriceListDetail_update_data(
    @CurrentUser() user: UserDto,
    @Body() data: { id: string; bidPriceId: string; name: string; type: string; value: string },
  ) {
    return await this.offerPriceService.bidPriceListDetail_update_data(user, data)
  }

  @ApiOperation({ summary: 'Xóa thông tin mở rộng của hạng mục chào giá' })
  @Post('price_listdetail_delete_data')
  public async bidPriceListDetail_delete_data(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.offerPriceService.bidPriceListDetail_delete_data(user, data.id)
  }

  @ApiOperation({ summary: 'Lấy ds PR của chào giá được chọn' })
  @Post('find_offer_pr')
  public async findOfferPr(@CurrentUser() user: UserDto, @Body() data: { ltsOfferId: [] }) {
    return await this.service.findOfferPr(user, data)
  }
  //#endregion

  @ApiOperation({ summary: 'Lấy danh sách đàm phán giá' })
  @Post('get_list_bid_result_deal')
  public async getListBidResultDeal(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.getListBidResultDeal(user, data)
  }

  @ApiOperation({ summary: 'Lấy chi tiết đàm phán giá' })
  @Get('get_bid_result_deal/:biddealid')
  public async getBidResultDeal(@CurrentUser() user: UserDto, @Param('biddealid') bidDealId: string) {
    return await this.service.getBidResultDeal(user, bidDealId)
  }

  @ApiOperation({ summary: 'Lấy chi tiết đàm phán giá theo Doanh nghiệp' })
  @Get('get_bid_result_deal_supplier_detail/:biddealsupplierid')
  public async getBidResultDealSupplierDetail(@CurrentUser() user: UserDto, @Param('biddealsupplierid') bidDealSupplierId: string) {
    return await this.service.getBidResultDealSupplierDetail(user, bidDealSupplierId)
  }

  @ApiOperation({ summary: 'Lấy ds báo giá theo shipment và biddingTypeCode ' })
  @Post('find_with_shipment')
  public async findWithShipment(@CurrentUser() user: UserDto, @Body() data: { shipmentId?: string; biddingTypeCode?: string }) {
    return await this.service.findWithShipment(user, data)
  }

  @ApiOperation({ summary: 'Lấy offer  supplier theo bao gia' })
  @Post('load_offer_supplier_from_offer')
  public async loadOfferSupplierFromOffer(@CurrentUser() user: UserDto, @Body() data: { offerId: string }) {
    return await this.service.loadOfferSupplierFromOffer(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds báo giá shipment' })
  @Post('load_supplier_shipment_value')
  public async loadSupplierShipmentValue(@CurrentUser() user: UserDto, @Body() data: { offerSupplierId: string }) {
    return await this.service.loadSupplierShipmentValue(user, data)
  }

  //#region bidChooseSupplier

  @ApiOperation({ summary: 'Lấy danh sách NCC mời tham gia thầu' })
  @Post('load_supplier_invite')
  public async loadSupplierInvite(
    @CurrentUser() user: UserDto,
    @Body() data: { bidId: string; supplierName?: string; lstStatus?: string[]; typeGetData: number },
  ) {
    return await this.offerSupplerService.loadSupplierInvite(user, data)
  }

  @ApiOperation({ summary: 'Chọn NCC mời tham gia thầu' })
  @Post('bid_choose_supplier')
  public async bidChooseSupplier(@CurrentUser() user: UserDto, @Body() data: { bidId: string; lstData: any[] }) {
    return await this.offerSupplerService.bidChooseSupplier(user, data)
  }
  //#endregion
}
