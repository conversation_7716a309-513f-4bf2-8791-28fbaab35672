import { IsNull, Repository } from 'typeorm'
import { enumData } from '../constants/enumData'
import { ERROR_NOT_FOUND_DATA } from '../constants'
import { SupplierCapacityEntity, SupplierServiceEntity } from '../entities'
import { CustomRepository } from '../typeorm'
import { UserDto } from '../dto'

@CustomRepository(SupplierServiceEntity)
export class SupplierServiceRepository extends Repository<SupplierServiceEntity> {
  async calScore(supplierServiceId: string) {
    const supplierService = await this.findOne({
      where: { id: supplierServiceId, isDeleted: false },
      relations: { capacities: { supplierCapacityListDetails: true, childs: { supplierCapacityListDetails: true } } },
    })
    let score = 0
    if (supplierService) {
      const listCapacity = (await supplierService.capacities).filter((p) => p.isDeleted === false && p.parentId === null)
      for (let item of listCapacity) {
        const itemChilds = await item.childs
        if (itemChilds && itemChilds.length > 0) {
          const lenght = itemChilds.length
          let scoreC = 0
          for (let i = 0; i < lenght; i++) {
            const tem = this.calScoreItem(itemChilds[i])
            scoreC += tem
          }
          const temp = (item.percent * scoreC) / 100

          score += temp
        } else {
          const temp = this.calScoreItem(item)

          score += temp
        }
      }

      if (isNaN(score) || !isFinite(score)) return 0

      return +score.toFixed(2)
    } else {
      throw ERROR_NOT_FOUND_DATA
    }
  }

  calScoreItem(item: any) {
    let score = 0
    if (item.type === enumData.DataType.Number.code && item.value && item.value.trim() != '') {
      let temp = 0
      const x = +item.value
      if (item.isCalUp) {
        if (x >= item.percentRule) {
          temp = item.percent
        } else {
          temp = (x * item.percent) / item.percentRule // giá trị * tỉ trọng / điều kiện b
        }
      } else {
        if (x <= item.percentRule) {
          temp = item.percent
        } else if (x >= item.percentDownRule) {
          temp = 0
        } else {
          temp = ((item.percentDownRule - x) * item.percent) / (item.percentDownRule - item.percentRule)
        }
      }
      if (isNaN(temp)) {
        score += 0
      } else if (!isFinite(temp)) {
        score += 0
      } else {
        score += temp
      }
    } else if (item.type === enumData.DataType.List.code) {
      const chose = item.__supplierCapacityListDetails__.find((p) => p.isChosen)
      const temp = chose ? chose.value : 0
      const finalTemp = (temp * item.percent) / 100
      if (isNaN(finalTemp)) {
        score += 0
      } else if (!isFinite(finalTemp)) {
        score += 0
      } else {
        score += finalTemp
      }
    }

    if (isNaN(score) || !isFinite(score)) return 0

    return score
  }

  async findCapacity(user: UserDto, supplierServiceId: string) {
    return this.manager.getRepository(SupplierCapacityEntity).find({
      where: { supplierServiceId, parentId: IsNull(), companyId: user.companyId, isDeleted: false },
      relations: {
        supplierCapacityListDetails: true,
        supplierCapacityYearValues: true,
        childs: { supplierCapacityListDetails: true, supplierCapacityYearValues: true },
      },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })
  }
}
