import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTableBill1740386473439 implements MigrationInterface {
    name = 'AddTableBill1740386473439'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`bill\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(100) NOT NULL, \`description\` text NULL, \`fileXml\` text NULL, \`status\` varchar(50) NULL, \`paymentStatus\` varchar(50) NULL, \`supplierId\` varchar(255) NULL, \`poId\` varchar(255) NULL, \`contractId\` varchar(255) NULL, \`invoiceValue\` bigint NULL, \`totalInvoiceValue\` float NULL, \`vat\` bigint NULL, \`referencesInvoice\` varchar(50) NULL, \`currencyName\` varchar(100) NULL, \`bizziCompanyId\` varchar(255) NULL, \`fileAttach\` text NULL, \`billLookupCode\` varchar(100) NULL, \`billLookupId\` varchar(255) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`bill\` ADD CONSTRAINT \`FK_12cc7e32306c5b263f6fbcfd1e5\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`bill\` ADD CONSTRAINT \`FK_42dbdce0becb29d047587aedb9b\` FOREIGN KEY (\`poId\`) REFERENCES \`po\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`bill\` ADD CONSTRAINT \`FK_d5e2ad96d3fb0c0013b1a0176bb\` FOREIGN KEY (\`contractId\`) REFERENCES \`contract\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`bill\` ADD CONSTRAINT \`FK_586340736f555e633b893bf191e\` FOREIGN KEY (\`billLookupId\`) REFERENCES \`bill_lookup\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bill\` DROP FOREIGN KEY \`FK_586340736f555e633b893bf191e\``);
        await queryRunner.query(`ALTER TABLE \`bill\` DROP FOREIGN KEY \`FK_d5e2ad96d3fb0c0013b1a0176bb\``);
        await queryRunner.query(`ALTER TABLE \`bill\` DROP FOREIGN KEY \`FK_42dbdce0becb29d047587aedb9b\``);
        await queryRunner.query(`ALTER TABLE \`bill\` DROP FOREIGN KEY \`FK_12cc7e32306c5b263f6fbcfd1e5\``);
        await queryRunner.query(`DROP TABLE \`bill\``);
    }

}
