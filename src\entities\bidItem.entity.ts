import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, Join<PERSON><PERSON>umn, OneToMany } from 'typeorm'
import { BidPriceColValueEntity } from './bidPriceColValue.entity'
import { BidSupplierPriceColValueEntity } from './bidSupplierPriceColValue.entity'
import { BidEntity } from './bid.entity'
import { MaterialEntity } from './material.entity'

@Entity('bid_item')
export class BidItemEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidId: string

  @ManyToOne(() => BidEntity, (p) => p.bidItems)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  /** Số lượng của Item trong gói thầu chi tiết */
  @Column({
    nullable: true,
    default: 0,
  })
  quantityItem: number

  /** Tên hàng hóa  */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: false,
  })
  productName: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  itemId: string

  @ManyToOne(() => MaterialEntity, (p) => p.bidItems)
  @JoinColumn({ name: 'itemId', referencedColumnName: 'id' })
  item: Promise<MaterialEntity>
}
