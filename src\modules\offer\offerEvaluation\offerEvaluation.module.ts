import { Module } from '@nestjs/common'
import { EmailModule } from '../../email/email.module'
import { TypeOrmExModule } from '../../../typeorm'
import { OfferPrItemRepository, OfferRepository, OfferSupplierRepository } from '../../../repositories/offer.repository'
import { OfferEvaluationController } from './offerEvaluation.controller'
import { OfferEvaluationService } from './offerEvaluation.service'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([OfferRepository, OfferSupplierRepository, OfferPrItemRepository]), EmailModule],
  controllers: [OfferEvaluationController],
  providers: [OfferEvaluationService],
  exports: [OfferEvaluationService],
})
export class OfferEvaluationModule {}
