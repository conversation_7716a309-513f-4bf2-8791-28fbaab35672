import { Injectable } from '@nestjs/common'
import { coreHelper } from '../helpers'
// import * as moment from 'moment'
@Injectable()
export class AppService {
  /** Kiểm tra tình trạng server */
  healthCheck() {
    return 'This server is healthy.'
  }

  /** Kiểm tra thời gian theo timezone của server */
  checkTimeZone() {
    let newDate = new Date()
    let newDateTz = coreHelper.newDateTZ()

    return `newDate: ${newDate}\nnewDateTZ+7: ${newDateTz}`
  }
}
