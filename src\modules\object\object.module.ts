import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { ObjectService } from './object.service'
import { ObjectController } from './object.controller'
import { ObjectRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([ObjectRepository])],
  controllers: [ObjectController],
  providers: [ObjectService],
})
export class ObjectModule {}
