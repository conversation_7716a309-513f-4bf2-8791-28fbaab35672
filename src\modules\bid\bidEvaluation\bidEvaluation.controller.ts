import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { enumProject } from '../../../constants'
import { UserDto } from '../../../dto'
import { CurrentUser, Roles } from '../../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../../common/guards'
import { BidEvaluationService } from './bidEvaluation.service'

/** Chọn NCC thắng thầu */
@ApiBearerAuth()
@ApiTags('Bid')
@Controller('bid_evaluation')
export class BidEvaluationController {
  constructor(private readonly service: BidEvaluationService) {}

  @ApiOperation({ summary: 'Tự động chọn NCC thắng thầu và kết thúc thầu' })
  @Roles(enumProject.Features.BID_013.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('auto_bid')
  public async autoBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.autoBid(user, data)
  }

  @ApiOperation({ summary: 'Load ds Doanh nghiệp để chọn trúng thầu' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_supplier_data')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadSupplierData(user, data)
  }

  @ApiOperation({ summary: 'Chọn Doanh nghiệp trúng thầu, trượt thầu theo từng Item' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('evaluation_bid_supplier')
  public async evaluationBidSupplier(@CurrentUser() user: UserDto, @Body() data: { bidId: string; listItem: any[]; comment: string }) {
    return await this.service.evaluationBidSupplier(user, data)
  }

  @ApiOperation({ summary: 'Phê duyệt Doanh nghiệp thắng thầu' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('approve_supplier_win_bid')
  public async approveSupplierWinBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string; comment: string }) {
    return await this.service.approveSupplierWinBid(user, data)
  }

  @ApiOperation({ summary: 'Yêu cầu đánh giá và chọn lại Doanh nghiệp thắng thầu' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('reject_supplier_win_bid')
  public async rejectSupplierWinBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string; comment: string }) {
    return await this.service.rejectSupplierWinBid(user, data)
  }
}
