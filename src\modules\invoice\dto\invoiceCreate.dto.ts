import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class InvoiceCreate {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  invoiceSuggestId: string

  /** <PERSON>ố tiền cần thanh toán */
  @ApiPropertyOptional()
  moneyNeedPaid: number
  /** Số tiền thanh toán */
  @ApiPropertyOptional()
  money: number

  @ApiProperty()
  @IsNotEmpty()
  invoiceDate: Date

  @ApiPropertyOptional()
  description: string

  @ApiProperty()
  @IsNotEmpty()
  fileList: any[]
}
