import {
  Injectable,
  NotFoundException,
  BadRequestException,
  MethodNotAllowedException,
  NotAcceptableException,
  UnauthorizedException,
} from '@nestjs/common'
import {
  ERROR_YOU_DO_NOT_HAVE_PERMISSION,
  ERROR_NOT_FOUND_DATA,
  ERROR_SUPPLIER_USED_TEMPLATE,
  ERROR_INVALID_FOMULAR,
  UPDATE_SUCCESS,
  CREATE_SUCCESS,
  DELETE_SUCCESS,
  IMPORT_SUCCESS,
} from '../../constants'
import { EmailService } from '../email/email.service'
import {
  ServiceRepository,
  EmployeeRepository,
  BidTypeRepository,
  SettingStringRepository,
  BidRepository,
  BidEmployeeAccessRepository,
  ServiceTechRepository,
  ServiceTradeRepository,
  ServicePriceRepository,
  SupplierServiceRepository,
  ServiceCustomPriceRepository,
  BidCustomPriceRepository,
  BidTechRepository,
  BidTradeRepository,
  BidPriceRepository,
  BidPriceColRepository,
  BidSupplierRepository,
  SupplierRepository,
  BidSupplierPriceRepository,
  BidItemRepository,
  MaterialRepository,
} from '../../repositories'
import * as moment from 'moment'
import { apeAuthApiHelper, coreHelper } from '../../helpers'
import { enumData } from '../../constants/enumData'
import { PaginationDto, UserDto } from '../../dto'
import { In, IsNull, Like, Not, Raw, Repository } from 'typeorm'
import {
  SupplierCreateTechItemDto,
  SupplierCreateTradeItemDto,
  SupplierCreatePriceItemDto,
  BidCreateDto,
  BidUpdateDto,
  SupplierCreateCustomPriceItemDto,
  BidUpdateSettingDto,
} from './dto'
import {
  BidAuctionEntity,
  BidAuctionPriceEntity,
  BidAuctionSupplierEntity,
  BidAuctionSupplierPriceValueEntity,
  BidCustomPriceEntity,
  BidDealEntity,
  BidDealPriceEntity,
  BidDealSupplierEntity,
  BidDealSupplierPriceValueEntity,
  BidEmployeeAccessEntity,
  BidEntity,
  BidHistoryEntity,
  BidPriceColEntity,
  BidPriceColValueEntity,
  BidPriceEntity,
  BidPriceListDetailEntity,
  BidSupplierCustomPriceValueEntity,
  BidSupplierEntity,
  BidSupplierPriceColValueEntity,
  BidSupplierPriceEntity,
  BidSupplierPriceValueEntity,
  BidSupplierTechValueEntity,
  BidSupplierTradeValueEntity,
  BidTechEntity,
  BidTechListDetailEntity,
  BidTradeEntity,
  BidTradeListDetailEntity,
  EmailTemplateEntity,
  EmployeeEntity,
  EmployeeWarningEntity,
  MaterialEntity,
  PrEntity,
  PrItemEntity,
  ServiceEntity,
  ServicePriceColEntity,
  SupplierServiceEntity,
  UserEntity,
} from '../../entities'
import {
  BidCustomPriceCreateDto,
  BidCustomPriceUpdateDto,
  BidPriceColCreateDto,
  BidPriceColUpdateDto,
  BidPriceCreateDto,
  BidPriceUpdateDto,
  BidTechCreateDto,
  BidTechUpdateDto,
  BidTradeCreateDto,
  BidTradeUpdateDto,
} from './dto2'
import { BidItemEntity } from '../../entities/bidItem.entity'
import { IdDto } from '../../dto/id.dto'

@Injectable()
export class BidService {
  constructor(
    private readonly repo: BidRepository,
    private readonly emailService: EmailService,
    private readonly serviceRepo: ServiceRepository,
    private readonly employeeRepo: EmployeeRepository,
    private readonly settingStringRepo: SettingStringRepository,
    private readonly bidTypeRepo: BidTypeRepository,
    private readonly bidEmployeeAccessRepo: BidEmployeeAccessRepository,
    private readonly serviceTechRepo: ServiceTechRepository,
    private readonly bidTechRepo: BidTechRepository,
    private readonly bidItemRepo: BidItemRepository,
    private readonly materialRepository: MaterialRepository,
    private readonly serviceTradeRepo: ServiceTradeRepository,
    private readonly bidTradeRepo: BidTradeRepository,
    private readonly servicePriceRepo: ServicePriceRepository,
    private readonly serviceCustomPriceRepo: ServiceCustomPriceRepository,
    private readonly bidPriceRepo: BidPriceRepository,
    private readonly bidPriceColRepo: BidPriceColRepository,
    private readonly bidCustomPriceRepo: BidCustomPriceRepository,
    private readonly supplierServiceRepo: SupplierServiceRepository,
    private readonly bidSupplierRepo: BidSupplierRepository,
    private readonly supplierRepo: SupplierRepository,
  ) {}

  //#region get data

  /** Lấy ds gói thầu */
  async find(user: UserDto, data: { status?: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const whereCon: any = { parentId: IsNull(), companyId: user.companyId, isDeleted: false }
    if (data.status) whereCon.status = data.status
    const res: any = await this.repo.find({
      where: whereCon,
    })

    return res
  }

  async pagination(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    let whereCon: any = { parentId: IsNull(), companyId: user.companyId, isDeleted: false }
    // Lấy gói thầu mpoLead cần duyệt
    if (data.where.isGetBidNeedApprove) {
      // Lấy những gói thầu mà mpoLead có quyền thấy
      const whereTemp = [
        {
          companyId: user.companyId,
          employeeAccess: { employeeId: user.employeeId, type: enumData.BidRuleType.MPOLeader.code },
          status: In([enumData.BidStatus.ChoDuyetGoiThauTam.code, enumData.BidStatus.DangDuyetGoiThau.code]),
        },
        {
          companyId: user.companyId,
          employeeAccess: { employeeId: user.employeeId, type: enumData.BidRuleType.MPOLeader.code },
          status: enumData.BidStatus.DangCauHinhGoiThau.code,
          statusTech: enumData.BidTechStatus.DaTao.code,
        },
      ]

      const lstBidTemp = await this.repo.find({ where: whereTemp, select: { id: true } })
      const lstBidId = lstBidTemp.map((c) => c.id)
      if (lstBidId.length == 0) return [[], 0]

      whereCon.id = In(lstBidId)
    }
    // Lấy gói thầu có quyền xem
    else whereCon.employeeAccess = { employeeId: user.employeeId }

    if (data.where.serviceId) whereCon.childs = { serviceId: data.where.serviceId }

    let listBidStatus = [
      enumData.BidStatus.GoiThauTam.code,
      enumData.BidStatus.ChoDuyetGoiThauTam.code,
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    if (data.where.status?.length > 0) listBidStatus = data.where.status
    whereCon.status = In(listBidStatus)

    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.publicDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.dateFrom) {
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}")`)
    } else if (data.where.dateTo) {
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.dateTo).format('YYYY-MM-DD')}")`)
    }

    // Tìm theo mã số hoặc tên gói thầu
    if (data.where.name) {
      whereCon = [
        { ...whereCon, code: Like(`%${data.where.name}%`) },
        { ...whereCon, name: Like(`%${data.where.name}%`) },
      ]
    }

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      select: {
        id: true,
        createdAt: true,
        code: true,
        name: true,
        status: true,
        publicDate: true,
        statusTech: true,
        statusTrade: true,
        statusPrice: true,
        statusChooseSupplier: true,
        isRequestDelete: true,
      },
    })
    if (res[0].length == 0) return res
    const lstId = res[0].map((c) => c.id)
    const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
      where: { bidId: In(lstId), companyId: user.companyId, isDeleted: false },
      relations: { employee: true },
      select: {
        id: true,
        bidId: true,
        type: true,
        employeeId: true,
        employee: {
          id: true,
          name: true,
        },
      },
    })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }

    const lstStatusCanEdit = [
      enumData.BidStatus.GoiThauTam.code,
      enumData.BidStatus.ChoDuyetGoiThauTam.code,
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    const lstStatusCanSetting = [
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    for (const item of res[0]) {
      const lstAccess = lstEmployeeAccess.filter((c) => c.bidId == item.id)

      const objTech = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
      item.techName = objTech?.__employee__?.name || ''
      const objMpo = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
      item.mpoName = objMpo?.__employee__?.name || ''

      const lstAccessUser = lstAccess.filter((c) => c.employeeId === user.employeeId)
      item.isMPO = lstAccessUser.some((c) => c.type === enumData.BidRuleType.MPO.code)
      item.isMPOLeader = lstAccessUser.some((c) => c.type === enumData.BidRuleType.MPOLeader.code)
      item.isTech = lstAccessUser.some((c) => c.type === enumData.BidRuleType.Tech.code)
      item.isTechLeader = lstAccessUser.some((c) => c.type === enumData.BidRuleType.TechLeader.code)
      item.isMember = lstAccessUser.some((c) => c.type === enumData.BidRuleType.Memmber.code)

      item.statusName = dicStatus[item.status]

      // mpo có quyền như tech
      item.isTech = item.isTech || item.isMPO
      // mpoLead có quyền như techLead
      item.isTechLeader = item.isTechLeader || item.isMPOLeader

      if (lstStatusCanEdit.includes(item.status)) {
        item.isShowEditBid =
          (item.isMPO && item.status !== enumData.BidStatus.DangNhanBaoGia.code) ||
          (item.isMPOLeader && item.status == enumData.BidStatus.ChoDuyetGoiThauTam.code)
      }
      if (lstStatusCanSetting.includes(item.status)) {
        item.isShowBidTech = item.isTech || item.isTechLeader
        item.isShowBidPrice = item.isMPO || item.isMPOLeader
        item.isShowBidTrade = item.isMPO || item.isMPOLeader
        item.isShowChoseSupplier =
          (item.isMPO || item.isMPOLeader) &&
          item.statusTech === enumData.BidPriceStatus.DaDuyet.code &&
          (item.statusPrice === enumData.BidPriceStatus.DaTao.code || item.statusPrice === enumData.BidPriceStatus.DaDuyet.code) &&
          (item.statusTrade === enumData.BidTradeStatus.DaTao.code || item.statusTrade === enumData.BidTradeStatus.DaDuyet.code)
        item.isShowBiddingFromAdminToSupplier = (item.isMPO || item.isMPOLeader) && item.status === enumData.BidStatus.DangNhanBaoGia.code
      }

      item.isShowEditSettingRate = item.isMPO && item.status === enumData.BidStatus.DangNhanBaoGia.code

      item.isShowSendEmail = item.isMPO || item.isMPOLeader

      item.isShowCopy = item.isMPO || item.isMPOLeader

      item.isShowDelete = item.isMPO && !item.isRequestDelete

      item.isShowApproveDelete = item.isMPOLeader && item.isRequestDelete

      if (item.statusTech === enumData.BidTechStatus.DangTao.code) {
        item.techType = 'dashed'
      }
      if (item.statusTech === enumData.BidTechStatus.TuChoi.code) {
        item.techType = 'danger'
      }
      if (item.statusTech === enumData.BidTechStatus.DaTao.code) {
        item.techType = 'warning'
      }
      if (item.statusTech === enumData.BidTechStatus.DaDuyet.code) {
        item.techType = 'success'
      }
      if (item.statusTrade === enumData.BidTradeStatus.DangTao.code) {
        item.tradeType = 'dashed'
      }
      if (item.statusTrade === enumData.BidTradeStatus.TuChoi.code) {
        item.tradeType = 'danger'
      }
      if (item.statusTrade === enumData.BidTradeStatus.DaTao.code) {
        item.tradeType = 'warning'
      }
      if (item.statusTrade === enumData.BidTradeStatus.DaDuyet.code) {
        item.tradeType = 'success'
      }

      if (item.statusPrice === enumData.BidPriceStatus.DangTao.code) {
        item.priceType = 'dashed'
      }
      if (item.statusPrice === enumData.BidPriceStatus.TuChoi.code) {
        item.priceType = 'danger'
      }
      if (item.statusPrice === enumData.BidPriceStatus.DaTao.code) {
        item.priceType = 'warning'
      }
      if (item.statusPrice === enumData.BidPriceStatus.DaDuyet.code) {
        item.priceType = 'success'
      }

      if (item.statusChooseSupplier === enumData.BidChooseSupplierStatus.DangChon.code) {
        item.chooseSupplierType = 'dashed'
      }
      if (item.statusChooseSupplier === enumData.BidChooseSupplierStatus.TuChoi.code) {
        item.chooseSupplierType = 'danger'
      }
      if (item.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaChon.code) {
        item.chooseSupplierType = 'warning'
      }
      if (item.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaDuyet.code) {
        item.chooseSupplierType = 'success'
      }
    }

    return res
  }

  async findDetailEdit(user: UserDto, data: { id: string }) {
    const res: any = await this.repo.findOne({
      where: { id: data.id, companyId: user.companyId },
      relations: {
        employeeAccess: true,
        childs: { service: true, prItem: true },
        bidItems: { item: true },
        service: { parent: { parent: { parent: { parent: true } } } },
      },
    })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)
    if (res.isDeleted) throw new Error('Gói thầu đã ngưng hoạt động')
    // const service = await this.serviceRepo.findOne({ where: { id: res.serviceId } })
    // res.serviceId = []
    // if (service.parentId) res.serviceId.push(service.parentId)
    // res.serviceId.push(service.id)

    const lstAccess = res.__employeeAccess__ || []
    delete res.__employeeAccess__
    res.anotherRoleIds = lstAccess.filter((c) => c.type === enumData.BidRuleType.Memmber.code).map((c) => c.employeeId)

    res.otherRoleIds = lstAccess.filter((c) => c.type === enumData.BidRuleType.Other.code).map((c) => c.employeeId)

    const mpoObj = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
    if (mpoObj) {
      res.mpoId = mpoObj.employeeId
      res.isMPO = mpoObj.employeeId == user.employeeId
    }

    const mpoLeaderObj = lstAccess.find((c) => c.type === enumData.BidRuleType.MPOLeader.code)
    if (mpoLeaderObj) {
      res.mpoLeadId = mpoLeaderObj.employeeId
      res.isMPOLeader = mpoLeaderObj.employeeId == user.employeeId
    }

    const techObj = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
    if (techObj) {
      res.techId = techObj.employeeId
      res.isTech = techObj.employeeId == user.employeeId
    }

    const techLeaderObj = lstAccess.find((c) => c.type === enumData.BidRuleType.TechLeader.code)
    if (techLeaderObj) {
      res.techLeadId = techLeaderObj.employeeId
      res.isTechLeader = techLeaderObj.employeeId == user.employeeId
    }

    res.isShowSendMPOLeadAccept = res.status == enumData.BidStatus.GoiThauTam.code && res.isMPO
    res.isShowAcceptBid = res.status == enumData.BidStatus.ChoDuyetGoiThauTam.code && res.isMPOLeader
    res.isAllowPrintBid = res.isMPO || res.isMPOLeader
    {
      const lstStatusCanDeleteBid = [enumData.BidStatus.GoiThauTam.code, enumData.BidStatus.ChoDuyetGoiThauTam.code]
      res.isShowDeleteBid = lstStatusCanDeleteBid.includes(res.status) && res.isMPO
    }
    {
      const lstStatusCanEdit = [
        enumData.BidStatus.GoiThauTam.code,
        enumData.BidStatus.ChoDuyetGoiThauTam.code,
        enumData.BidStatus.DangCauHinhGoiThau.code,
        enumData.BidStatus.DangChonNCC.code,
        enumData.BidStatus.TuChoiGoiThau.code,
        enumData.BidStatus.DangDuyetGoiThau.code,
        enumData.BidStatus.DangNhanBaoGia.code,
      ]
      res.isAllowEditBid = lstStatusCanEdit.includes(res.status) && res.isMPO
    }

    res.__bidItems__ = res.__bidItems__ || []

    res.listItem = []
    for (const item of res.__bidItems__) {
      res.listItem.push({
        id: item.id,
        itemId: item.itemId,
        itemCode: item.__item__?.code,
        itemName: item.__item__?.name,
        quantityItem: item.quantityItem,
        quantityCreatedBid: item.quantityItem,
        quantity: res.__prItem__?.quantity || 0,
        quantityBid: res.__prItem__?.quantityBid || 0,
        prItemId: res.prItemId,
        percentTech: res.percentTech,
        percentTrade: res.percentTrade,
        percentPrice: res.percentPrice,
      })
    }
    delete res.__childs__

    return res
  }

  async getBidStatus(user: UserDto, id: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res: any = await this.repo.findOne({ where: { id, companyId: user.companyId }, select: { id: true, status: true } })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    return res
  }

  //#endregion

  //#region create Bid

  /** Tạo gói thầu */
  async createBid(user: UserDto, data: BidCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const dateNow = new Date()
    if (data.listItem.length == 0) throw new BadRequestException('Vui lòng thiết lập danh sách Item.')

    const codeTemp = moment(dateNow).format('YYYYMM')
    const lastSort = await this.findLastSort(user, this.repo, codeTemp + '_')
    const sortString = ('00' + (lastSort + 1)).slice(-3)
    data.code = codeTemp + '_' + sortString
    data.publicDate = dateNow
    data.startBidDate = new Date(data.submitEndDate)
    data.startBidDate.setDate(data.startBidDate.getDate() + 1)
    data.startBidDate.setHours(8, 0, 0, 0)
    if (data.startBidDate >= data.timeCheckTechDate) {
      throw new BadRequestException('Thời điểm mở thầu phải sớm hơn thời hạn đánh giá yêu cầu kỹ thuật, năng lực.')
    }

    return this.repo.manager.transaction(async (manager) => {
      const bidRepo = manager.getRepository(BidEntity)
      const bidItemRepo = manager.getRepository(BidItemEntity)
      const bidEmployeeAccessRepo = manager.getRepository(BidEmployeeAccessEntity)
      const bidHistoryRepo = manager.getRepository(BidHistoryEntity)
      const prRepo = manager.getRepository(PrEntity)
      const prItemRepo = manager.getRepository(PrItemEntity)

      let lstPrItem = []
      if (data.prId) {
        const pr: any = await prRepo.findOne({
          where: { id: data.prId, companyId: user.companyId, prItems: { isDeleted: false } },
          relations: { prItems: true },
        })
        if (!pr) throw new Error(`Không tìm thấy PR.`)
        if (pr.isDeleted) throw new Error(`PR [${pr.code}] đã ngưng hoạt động.`)
        if (!pr.isAllowBid) throw new Error(`PR [${pr.code}] đã được tạo thầu.`)

        lstPrItem = pr.__prItems__ || []
        if (lstPrItem.length == 0) throw new Error(`PR [${pr.code}] không có Item`)

        // check lại các item xem số lượng vừa bị điều chỉnh không
        for (const item of data.listItem) {
          const prItem = lstPrItem.find((c) => c.id == item.prItemId)
          if (!prItem) throw new Error(`Không tìm thấy Item [${item.itemName}] trong PR [${pr.code}]`)
          if (item.quantityCreatedBid > prItem.quantity - prItem.quantityBid) {
            throw new Error(`Số lượng tạo thầu của Item [${item.itemName}] vượt số lượng cần đặt trong PR [${pr.code}]`)
          }
        }
      }

      const bid = new BidEntity()
      bid.companyId = user.companyId
      bid.createdBy = user.id
      bid.code = data.code
      bid.name = data.name
      bid.prId = data.prId
      bid.serviceInvite = data.serviceInvite
      bid.acceptEndDate = data.acceptEndDate
      bid.submitEndDate = data.submitEndDate
      bid.addressSubmit = data.addressSubmit ? data.addressSubmit : ''
      bid.companyInvite = data.companyInvite
      bid.listAddress = data.listAddress ? data.listAddress : ''
      bid.publicDate = data.publicDate
      bid.bidTypeId = data.bidTypeId
      bid.hiddenScore = data.hiddenScore
      bid.isSkipEnd = data.hiddenScore
      bid.isNotImportFromAdmin = data.isNotImportFromAdmin
      bid.watchProfile = data.watchProfile

      bid.timeserving = data.timeserving
      bid.scoreDLC = data.scoreDLC
      if (Array.isArray(data.serviceId)) {
        bid.serviceId = data.serviceId[data.serviceId.length - 1]
      } else {
        bid.serviceId = data.serviceId
      }
      // bid.serviceId = data.serviceId[0]
      bid.startBidDate = data.startBidDate
      bid.moneyGuarantee = data.moneyGuarantee
      bid.timeGuarantee = data.timeGuarantee
      bid.masterBidGuaranteeId = data.masterBidGuaranteeId
      bid.timeTechDate = data.timeTechDate
      bid.timePriceDate = data.timePriceDate
      bid.timeCheckTechDate = data.timeCheckTechDate
      bid.timeCheckPriceDate = data.timeCheckPriceDate
      bid.status = enumData.BidStatus.GoiThauTam.code
      bid.isShowHomePage = data.isShowHomePage
      bid.isSendEmailInviteBid = data.isSendEmailInviteBid
      bid.isAutoBid = data.isAutoBid
      bid.fileDrawing = data.fileDrawing
      bid.fileJD = data.fileJD
      bid.fileKPI = data.fileKPI
      bid.fileRule = data.fileRule
      bid.fileDocument = data.fileDocument
      bid.fileAnother = data.fileAnother

      bid.statusTech = enumData.BidTechStatus.DangTao.code
      bid.statusTrade = enumData.BidTradeStatus.DangTao.code
      bid.statusPrice = enumData.BidPriceStatus.DangTao.code
      bid.statusRateTech = enumData.BidTechRateStatus.ChuaTao.code
      bid.statusRateTrade = enumData.BidTradeRateStatus.ChuaTao.code
      bid.statusRatePrice = enumData.BidPriceRateStatus.ChuaTao.code

      const bidEntity = await bidRepo.save(bid)

      // Xử lý khi có data.listItem, mỗi item là 1 bid mới có parentId là bidEntity.id
      let lastSort = 0

      // Copy thông tin gói thầu
      const bidItem: any = { ...bid }
      delete bidItem.id
      bidItem.parentId = bidEntity.id
      bidItem.serviceId = bid.serviceId
      let service = await manager.getRepository(ServiceEntity).findOne({ where: { id: bid.serviceId } })
      bidItem.percentTech = service.percentTech || 0
      bidItem.percentTrade = service.percentTrade || 0
      bidItem.percentPrice = bid.percentPrice || 0
      bidItem.prItemId = bid.prItemId

      if (lastSort == 0) lastSort = await this.findLastSort(user, bidRepo, bid.code + '/')
      lastSort++
      const sortString = ('00' + lastSort).slice(-3)
      bidItem.code = bid.code + '/' + sortString
      bidItem.createdBy = user.id
      const bidItemNew = bidRepo.create({ ...bidItem })
      await bidRepo.save(bidItemNew)

      // if (data.prId) {
      //   // update lại số lượng đã tạo thầu của item
      //   const prItem = lstPrItem.find((c) => c.id == item.prItemId)
      //   if (prItem) {
      //     prItem.quantityBid += bidItem.quantityItem
      //     await prItemRepo.update(prItem.id, { quantityBid: prItem.quantityBid, updatedBy: user.id })
      //   }
      // }
      for (const item of data.listItem) {
        const product = await this.materialRepository.findOne({ where: { id: item.itemId } })
        const productName = product?.name
        const bidItemNew = bidItemRepo.create({ bidId: bid.id, productName: productName, itemId: item.itemId, quantityItem: item.quantityCreatedBid })
        await bidItemRepo.save(bidItemNew)
      }

      //#region PERMISSION
      // MPO
      const mpoAccess = new BidEmployeeAccessEntity()
      mpoAccess.companyId = user.companyId
      mpoAccess.createdBy = user.id
      mpoAccess.bidId = bidEntity.id
      mpoAccess.employeeId = data.mpoId
      mpoAccess.type = enumData.BidRuleType.MPO.code
      await bidEmployeeAccessRepo.save(mpoAccess)

      // MPO Lead
      const mpoLeadAccess = new BidEmployeeAccessEntity()
      mpoLeadAccess.companyId = user.companyId
      mpoLeadAccess.createdBy = user.id
      mpoLeadAccess.bidId = bidEntity.id
      mpoLeadAccess.employeeId = data.mpoLeadId
      mpoLeadAccess.type = enumData.BidRuleType.MPOLeader.code
      await bidEmployeeAccessRepo.save(mpoLeadAccess)

      // Tech
      const techAccess = new BidEmployeeAccessEntity()
      techAccess.companyId = user.companyId
      techAccess.createdBy = user.id
      techAccess.bidId = bidEntity.id
      techAccess.employeeId = data.techId
      techAccess.type = enumData.BidRuleType.Tech.code
      await bidEmployeeAccessRepo.save(techAccess)

      // Tech Lead
      const techLeadAccess = new BidEmployeeAccessEntity()
      techLeadAccess.companyId = user.companyId
      techLeadAccess.createdBy = user.id
      techLeadAccess.bidId = bidEntity.id
      techLeadAccess.employeeId = data.techLeadId
      techLeadAccess.type = enumData.BidRuleType.TechLeader.code
      await bidEmployeeAccessRepo.save(techLeadAccess)

      // Member
      data.anotherRoleIds = data.anotherRoleIds || []
      for (const item of data.anotherRoleIds) {
        const anotherAccess = new BidEmployeeAccessEntity()
        anotherAccess.companyId = user.companyId
        anotherAccess.createdBy = user.id
        anotherAccess.bidId = bidEntity.id
        anotherAccess.employeeId = item
        anotherAccess.type = enumData.BidRuleType.Memmber.code
        await bidEmployeeAccessRepo.save(anotherAccess)
      }

      // Other
      data.otherRoleIds = data.otherRoleIds || []
      for (const item of data.otherRoleIds) {
        const otherAccess = new BidEmployeeAccessEntity()
        otherAccess.companyId = user.companyId
        otherAccess.createdBy = user.id
        otherAccess.bidId = bidEntity.id
        otherAccess.employeeId = item
        otherAccess.type = enumData.BidRuleType.Other.code
        await bidEmployeeAccessRepo.save(otherAccess)
      }
      //#endregion

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = bidEntity.id
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.TaoGoiThau.code
      await bidHistoryRepo.save(bidHistory)

      return { message: CREATE_SUCCESS }
    })
  }

  /** Chỉnh sửa thông tin gói thầu */
  async updateBid(user: UserDto, data: BidUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const objPermission = await this.checkPermissionMpoEdit(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    if (data.listItem.length == 0) throw new BadRequestException('Vui lòng thiết lập danh sách Item.')

    data.startBidDate = new Date(data.submitEndDate)
    data.startBidDate.setDate(data.startBidDate.getDate() + 1)
    data.startBidDate.setHours(8, 0, 0, 0)
    if (data.startBidDate >= data.timeCheckTechDate) {
      throw new BadRequestException('Thời điểm mở thầu phải sớm hơn thời hạn đánh giá yêu cầu kỹ thuật, năng lực.')
    }

    await this.repo.manager.transaction(async (manager) => {
      const bidRepo = manager.getRepository(BidEntity)
      const bidHistoryRepo = manager.getRepository(BidHistoryEntity)
      const prRepo = manager.getRepository(PrEntity)
      // const prItemRepo = manager.getRepository(PrItemEntity)
      const bidItemRepo = manager.getRepository(BidItemEntity)
      const materiaItemRepo = manager.getRepository(MaterialEntity)

      const bid = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

      bid.isShowHomePage = data.isShowHomePage
      bid.isSendEmailInviteBid = data.isSendEmailInviteBid
      bid.isAutoBid = data.isAutoBid
      bid.name = data.name
      bid.serviceInvite = data.serviceInvite
      bid.acceptEndDate = data.acceptEndDate
      bid.submitEndDate = data.submitEndDate
      bid.addressSubmit = data.addressSubmit ?? ''
      bid.companyInvite = data.companyInvite
      bid.listAddress = data.listAddress ?? ''
      bid.publicDate = data.publicDate
      if (Array.isArray(data.serviceId)) {
        bid.serviceId = data.serviceId[data.serviceId.length - 1]
      } else {
        bid.serviceId = data.serviceId
      }
      // bid.serviceId = data.serviceId[0]
      bid.bidTypeId = data.bidTypeId
      bid.timeserving = data.timeserving
      bid.hiddenScore = data.hiddenScore
      bid.isSkipEnd = data.hiddenScore
      bid.isNotImportFromAdmin = data.isNotImportFromAdmin
      bid.watchProfile = data.watchProfile

      bid.scoreDLC = data.scoreDLC
      bid.startBidDate = data.startBidDate
      bid.moneyGuarantee = data.moneyGuarantee
      bid.timeGuarantee = data.timeGuarantee
      bid.masterBidGuaranteeId = data.masterBidGuaranteeId
      bid.timeTechDate = data.timeTechDate
      bid.timePriceDate = data.timePriceDate
      bid.timeCheckTechDate = data.timeCheckTechDate
      bid.timeCheckPriceDate = data.timeCheckPriceDate

      bid.fileDrawing = data.fileDrawing
      bid.fileJD = data.fileJD
      bid.fileKPI = data.fileKPI
      bid.fileRule = data.fileRule
      bid.fileDocument = data.fileDocument
      bid.fileAnother = data.fileAnother

      bid.updatedBy = user.id

      bidItemRepo.delete({ bidId: bid.id })

      await bidRepo.save(bid)

      for (const item of data.listItem) {
        const product = await materiaItemRepo.findOne({ where: { id: item.itemId } })
        const productName = product?.name
        const bidItemNew = bidItemRepo.create({
          bidId: bid.id,
          productName: productName,
          itemId: item.itemId,
          quantityItem: item.quantityCreatedBid,
        })
        await bidItemRepo.save(bidItemNew)
      }
      // Nếu có thay đổi Item: thêm, xóa Item, sửa sản lượng Item, đổi Pr
      // if (data.isChangeItem) {
      //   let prNew: any
      //   // Nếu gói thầu PR và PR khác PR ban đầu thì check lại PR có đc tạo thầu hay k
      //   //#region check quantity PR allow create bid
      //   if (data.prId) {
      //     const prNew: any = await prRepo.findOne({
      //       where: { id: data.prId, companyId: user.companyId, prItems: { isDeleted: false } },
      //       relations: { prItems: true },
      //     })
      //     if (!prNew) throw new Error(`Không tìm thấy PR`)
      //     if (prNew.isDeleted) throw new Error(`PR [${prNew.code}] đã ngưng hoạt động.`)
      //     if (!prNew.isAllowBid && data.prId != bid.prId) throw new Error(`PR [${prNew.code}] đã được tạo thầu`)

      //     // check item trong Pr
      //     const lstPrItem = prNew.__prItems__ || []
      //     if (lstPrItem.length == 0) throw new Error(`PR [${prNew.code}] không có Item`)

      //     // check lại các item xem số lượng vừa bị điều chỉnh không
      //     for (const item of data.listItem) {
      //       const prItem = lstPrItem.find((c) => c.id == item.prItemId)
      //       if (!prItem) throw new Error(`Không tìm thấy Item [${item.itemName}] trong PR [${prNew.code}]`)
      //       if (item.quantityCreatedBid - item.quantityItem > prItem.quantity - prItem.quantityBid) {
      //         throw new Error(`Số lượng tạo thầu của Item [${item.itemName}] trong PR [${prNew.code}] vượt số lượng cần đặt`)
      //       }
      //     }
      //   }
      //   //#endregion

      //   //#region Xử lý DS ITEM trước đó
      //   const lstBidItem = await bidRepo.find({
      //     where: { parentId: bid.id, companyId: user.companyId, isDeleted: false },
      //     relations: { prItem: true },
      //   })
      //   for (const bidItem of lstBidItem) {
      //     // Nếu DS ITEM từ PR thì update lại quantityBid cho PR
      //     if (bidItem.prItemId) {
      //       const prItem = await prItemRepo.findOne({
      //         where: { id: bidItem.prItemId, companyId: user.companyId, isDeleted: false },
      //         select: { id: true, quantityBid: true },
      //       })
      //       if (!prItem) throw new Error('prItem không còn tồn tại!')

      //       prItem.quantityBid -= bidItem.quantityItem
      //       await prItemRepo.update(prItem.id, { quantityBid: prItem.quantityBid, updatedBy: user.id })
      //     }
      //   }
      //   // xóa các item trong gói thầu
      //   {
      //     const lstKeepItemId = data.listItem.filter((c) => !c.isNew).map((c) => c.id)
      //     const whereTemp: any = { parentId: bid.id }
      //     if (lstKeepItemId.length > 0) whereTemp.id = Not(In(lstKeepItemId))
      //     const lstBidItemDelete = await bidRepo.find({ where: whereTemp, select: { id: true } })
      //     if (lstBidItemDelete.length > 0) {
      //       const lstDeleteItemId = lstBidItemDelete.map((c) => c.id)
      //       // xóa bidTech
      //       const lstBidTechDelete = await manager.getRepository(BidTechEntity).find({ where: { bidId: In(lstDeleteItemId) }, select: { id: true } })
      //       if (lstBidTechDelete.length > 0) {
      //         const lstDeleteId = lstBidTechDelete.map((c) => c.id)
      //         await manager.getRepository(BidTechListDetailEntity).delete({ bidTechId: In(lstDeleteId) })
      //         await manager.getRepository(BidTechEntity).delete({ bidId: In(lstDeleteItemId), level: 2 })
      //         await manager.getRepository(BidTechEntity).delete({ bidId: In(lstDeleteItemId) })
      //       }
      //       // xóa bidTrade
      //       const lstBidTradeDelete = await manager
      //         .getRepository(BidTradeEntity)
      //         .find({ where: { bidId: In(lstDeleteItemId) }, select: { id: true } })
      //       if (lstBidTradeDelete.length > 0) {
      //         const lstDeleteId = lstBidTradeDelete.map((c) => c.id)
      //         await manager.getRepository(BidTradeListDetailEntity).delete({ bidTradeId: In(lstDeleteId) })
      //         await manager.getRepository(BidTradeEntity).delete({ bidId: In(lstDeleteItemId), level: 2 })
      //         await manager.getRepository(BidTradeEntity).delete({ bidId: In(lstDeleteItemId) })
      //       }
      //       // xóa bidPrice
      //       const lstBidPriceDelete = await manager
      //         .getRepository(BidPriceEntity)
      //         .find({ where: { bidId: In(lstDeleteItemId) }, select: { id: true } })
      //       if (lstBidPriceDelete.length > 0) {
      //         const lstDeleteId = lstBidPriceDelete.map((c) => c.id)
      //         await manager.getRepository(BidPriceListDetailEntity).delete({ bidPriceId: In(lstDeleteId) })
      //         await manager.getRepository(BidPriceColValueEntity).delete({ bidPriceId: In(lstDeleteId) })
      //         await manager.getRepository(BidPriceEntity).delete({ bidId: In(lstDeleteItemId), level: 3 })
      //         await manager.getRepository(BidPriceEntity).delete({ bidId: In(lstDeleteItemId), level: 2 })
      //         await manager.getRepository(BidPriceEntity).delete({ bidId: In(lstDeleteItemId) })
      //       }
      //       // xóa bidCustomPrice
      //       await manager.getRepository(BidCustomPriceEntity).delete({ bidId: In(lstDeleteItemId) })
      //       // xóa bidPriceCol
      //       await manager.getRepository(BidPriceColEntity).delete({ bidId: In(lstDeleteItemId) })
      //       // xóa bidSupplier
      //       await manager.getRepository(BidSupplierEntity).delete({ bidId: In(lstDeleteItemId) })
      //       // xóa bidItem
      //       await bidRepo.delete({ id: In(lstDeleteItemId) })
      //     }
      //   }

      //   if (bid.prId) {
      //     const prOld = await prRepo.findOne({ where: { id: bid.prId, companyId: user.companyId } })
      //     if (prOld && !prOld.isAllowBid) await prRepo.update(prOld.id, { isAllowBid: true, updatedBy: user.id })
      //   }
      //   //#endregion

      //   //#region Thêm DS ITEM
      //   let lstPrItem = []
      //   if (data.prId) {
      //     lstPrItem = await prItemRepo.find({ where: { prId: data.prId, companyId: user.companyId, isDeleted: false } })
      //   }
      //   let lastSort = 0
      //   for (const item of data.listItem) {
      //     if (item.isNew) {
      //       // copy thông tin gói thầu
      //       const bidItem = bidRepo.create({ ...bid })
      //       delete bidItem.id
      //       delete bidItem.createdAt
      //       delete bidItem.updatedAt
      //       delete bidItem.createdBy
      //       delete bidItem.updatedBy
      //       bidItem.parentId = bid.id
      //       bidItem.serviceId = item.serviceId
      //       bidItem.quantityItem = item.quantityCreatedBid
      //       bidItem.prId = data.prId
      //       bidItem.percentTech = item.percentTech || 0
      //       bidItem.percentTrade = item.percentTrade || 0
      //       bidItem.percentPrice = item.percentPrice || 0
      //       bidItem.prItemId = item.prItemId
      //       bidItem.createdBy = user.id

      //       lastSort++
      //       const sortString = ('00' + lastSort).slice(-3)
      //       bidItem.code = bid.code + '/' + sortString

      //       await bidRepo.save(bidItem)
      //     } else {
      //       lastSort++
      //       const sortString = ('00' + lastSort).slice(-3)
      //       const code = bid.code + '/' + sortString
      //       await bidRepo.update(item.id, {
      //         code,
      //         quantityItem: item.quantityCreatedBid,
      //         percentTech: item.percentTech || 0,
      //         percentTrade: item.percentTrade || 0,
      //         percentPrice: item.percentPrice || 0,
      //         updatedBy: user.id,
      //       })
      //     }

      //     if (data.prId && item.prItemId) {
      //       // update lại số lượng đã tạo thầu của item
      //       const prItem = lstPrItem.find((c) => c.id == item.prItemId)
      //       if (prItem) {
      //         if (isNaN(item.quantityCreatedBid)) {
      //           item.quantityCreatedBid = 0
      //         }
      //         prItem.quantityBid += item.quantityCreatedBid
      //         await prItemRepo.update(prItem.id, { quantityBid: prItem.quantityBid, updatedBy: user.id })
      //       }
      //     }
      //   }

      //   // kiểm tra để cập nhật prNew.isAllowBid
      //   if (prNew && prNew.isAllowBid) {
      //     // nếu tất cả item đều có "số lượng đã tạo thầu" = "số lượng đặt" thì đánh dấu PR không được tạo thầu nữa

      //     if (lstPrItem.every((c) => c.quantityBid == c.quantity)) {
      //       await prRepo.update(data.prId, { isAllowBid: false, updatedBy: user.id })
      //     }
      //   }
      //   //#endregion

      //   if (data.prId) bid.prId = data.prId
      //   else bid.prId = null
      // }

      // Nếu có thay đổi HĐ thầu
      const bidEmployeeAccessRepo = manager.getRepository(BidEmployeeAccessEntity)
      if (data.isChangeEmployee) {
        // Xoá hết hội đồng gói thầu và tạo lại
        await bidEmployeeAccessRepo.delete({ bidId: bid.id })

        // MPO
        const mpoAccess = new BidEmployeeAccessEntity()
        mpoAccess.companyId = user.companyId
        mpoAccess.createdBy = user.id
        mpoAccess.bidId = bid.id
        mpoAccess.employeeId = data.mpoId
        mpoAccess.type = enumData.BidRuleType.MPO.code
        await bidEmployeeAccessRepo.save(mpoAccess)

        // MPO Lead
        const mpoLeadAccess = new BidEmployeeAccessEntity()
        mpoLeadAccess.companyId = user.companyId
        mpoLeadAccess.createdBy = user.id
        mpoLeadAccess.bidId = bid.id
        mpoLeadAccess.employeeId = data.mpoLeadId
        mpoLeadAccess.type = enumData.BidRuleType.MPOLeader.code
        await bidEmployeeAccessRepo.save(mpoLeadAccess)

        // Tech
        const techAccess = new BidEmployeeAccessEntity()
        techAccess.companyId = user.companyId
        techAccess.createdBy = user.id
        techAccess.bidId = bid.id
        techAccess.employeeId = data.techId
        techAccess.type = enumData.BidRuleType.Tech.code
        await bidEmployeeAccessRepo.save(techAccess)

        // Tech Lead
        const techLeadAccess = new BidEmployeeAccessEntity()
        techLeadAccess.companyId = user.companyId
        techLeadAccess.createdBy = user.id
        techLeadAccess.bidId = bid.id
        techLeadAccess.employeeId = data.techLeadId
        techLeadAccess.type = enumData.BidRuleType.TechLeader.code
        await bidEmployeeAccessRepo.save(techLeadAccess)

        data.anotherRoleIds = data.anotherRoleIds || []
        for (const item of data.anotherRoleIds) {
          const anotherAccess = new BidEmployeeAccessEntity()
          anotherAccess.companyId = user.companyId
          anotherAccess.createdBy = user.id
          anotherAccess.bidId = bid.id
          anotherAccess.employeeId = item
          anotherAccess.type = enumData.BidRuleType.Memmber.code
          await bidEmployeeAccessRepo.save(anotherAccess)
        }

        // Other
        data.otherRoleIds = data.otherRoleIds || []
        for (const item of data.otherRoleIds) {
          const otherAccess = new BidEmployeeAccessEntity()
          otherAccess.companyId = user.companyId
          otherAccess.createdBy = user.id
          otherAccess.bidId = bid.id
          otherAccess.employeeId = item
          otherAccess.type = enumData.BidRuleType.Other.code
          await bidEmployeeAccessRepo.save(otherAccess)
        }
      }

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = bid.id
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.SuaTaoGoiThau.code
      await bidHistoryRepo.save(bidHistory)

      return { message: UPDATE_SUCCESS }
    })

    // gửi email thông báo Chỉnh sửa thông tin chung của gói thầu thành công (trừ trường hợp gói thầu tạm)
    const bid = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId }, select: { status: true } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    if (bid.status != enumData.BidStatus.GoiThauTam.code && bid.status != enumData.BidStatus.ChoDuyetGoiThauTam.code) {
      await this.emailService.updateBidSuccess(data.id)
    }

    return { message: UPDATE_SUCCESS }
  }

  /** Copy gói thầu */
  async copyBid(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    return this.repo.manager.transaction(async (manager) => {
      const bidRepo = manager.getRepository(BidEntity)
      const bidEmployeeAccessRepo = manager.getRepository(BidEmployeeAccessEntity)
      const bidHistoryRepo = manager.getRepository(BidHistoryEntity)
      const bidTechRepo = manager.getRepository(BidTechEntity)
      const bidTechListDetailRepo = manager.getRepository(BidTechListDetailEntity)
      const bidTradeRepo = manager.getRepository(BidTradeEntity)
      const bidTradeListDetailRepo = manager.getRepository(BidTradeListDetailEntity)
      const bidPriceRepo = manager.getRepository(BidPriceEntity)
      const bidPriceListDetailRepo = manager.getRepository(BidPriceListDetailEntity)
      const bidPriceColRepo = manager.getRepository(BidPriceColEntity)
      const bidPriceColValueRepo = manager.getRepository(BidPriceColValueEntity)
      const bidCustomPriceRepo = manager.getRepository(BidCustomPriceEntity)
      const bidSupplierRepo = manager.getRepository(BidSupplierEntity)
      const bidItemRepo = manager.getRepository(BidItemEntity)
      const materiaItemRepo = manager.getRepository(MaterialEntity)

      // Gói thầu gốc
      const bidSrc = await bidRepo.findOne({ where: { id: bidId, companyId: user.companyId } })
      if (!bidSrc) throw new Error('Không tìm thấy gói thầu cần copy.')
      if (bidSrc.prId) throw new Error('Gói thầu theo PR, không được copy.')

      const dateNow = new Date()

      const codeTemp = moment(dateNow).format('YYYYMM')
      const lastSort = await this.findLastSort(user, bidRepo, codeTemp + '_')
      const sortString = ('00' + (lastSort + 1)).slice(-3)

      let bidCopy = bidRepo.create({ ...bidSrc })
      bidCopy.publicDate = new Date()
      // gen lại mã gói thầu mới
      bidCopy.code = codeTemp + '_' + sortString
      // reset lại các trạng thái đánh giá của gói thầu
      bidCopy.statusResetPrice = enumData.BidResetPriceStatus.ChuaTao.code
      bidCopy.statusRateTech = enumData.BidTechRateStatus.ChuaTao.code
      bidCopy.statusRateTrade = enumData.BidTradeRateStatus.ChuaTao.code
      bidCopy.statusRatePrice = enumData.BidPriceRateStatus.ChuaTao.code
      bidCopy.statusChooseSupplier = enumData.BidChooseSupplierStatus.ChuaChon.code
      if (
        bidSrc.statusChooseSupplier == enumData.BidChooseSupplierStatus.TuChoi.code ||
        bidSrc.statusChooseSupplier == enumData.BidChooseSupplierStatus.DaDuyet.code
      ) {
        bidCopy.statusChooseSupplier = enumData.BidChooseSupplierStatus.DaChon.code
      }
      // gán lại các thông tin người tạo gói thầu
      delete bidCopy.id
      delete bidCopy.createdAt
      delete bidCopy.updatedAt
      delete bidCopy.updatedBy
      bidCopy.createdBy = user.id
      // Các trạng thái sau khi gói thầu đã mở
      const lstStatus = [
        enumData.BidStatus.DangNhanBaoGia.code,
        enumData.BidStatus.DangDanhGia.code,
        enumData.BidStatus.DangDuyetDanhGia.code,
        enumData.BidStatus.HoanTatDanhGia.code,
        enumData.BidStatus.DangDamPhanGia.code,
        enumData.BidStatus.DongDamPhanGia.code,
        enumData.BidStatus.DangDauGia.code,
        enumData.BidStatus.DongDauGia.code,
        enumData.BidStatus.DongThau.code,
        enumData.BidStatus.HoanTat.code,
      ]
      if (lstStatus.includes(bidSrc.status)) {
        bidCopy.status = enumData.BidStatus.DangDuyetGoiThau.code
      }
      bidCopy = await bidRepo.save(bidCopy)

      //#region Employee Access
      const lstEmployeeAccessSrc = await bidSrc.employeeAccess
      for (const employeeAccessSrc of lstEmployeeAccessSrc) {
        const employeeAccessCopy = bidEmployeeAccessRepo.create({ ...employeeAccessSrc })
        // gán lại gói thầu
        employeeAccessCopy.bidId = bidCopy.id
        delete employeeAccessCopy.id
        delete employeeAccessCopy.createdAt
        delete employeeAccessCopy.updatedAt
        employeeAccessCopy.createdBy = user.id
        await bidEmployeeAccessRepo.save(employeeAccessCopy)
      }
      //#endregion

      //#region Bid Supplier
      const lstBidSupplierSrc = await bidSrc.bidSuppliers
      for (const bidSupplierSrc of lstBidSupplierSrc) {
        const bidSupplierCopy = bidSupplierRepo.create({ ...bidSupplierSrc })
        delete bidSupplierCopy.id
        delete bidSupplierCopy.createdAt
        delete bidSupplierCopy.updatedAt
        delete bidSupplierCopy.updatedBy
        delete bidSupplierCopy.dataJson
        // gán lại gói thầu
        bidSupplierCopy.bidId = bidCopy.id
        // reset các biến đánh giá
        bidSupplierCopy.isTechValid = true
        bidSupplierCopy.isTradeValid = true
        bidSupplierCopy.isPriceValid = true
        bidSupplierCopy.isSuccessBid = false
        bidSupplierCopy.isHighlight = false
        bidSupplierCopy.isNotHaveMinValue = false
        bidSupplierCopy.note = ''
        bidSupplierCopy.noteTech = ''
        bidSupplierCopy.noteTrade = ''
        bidSupplierCopy.notePrice = ''
        bidSupplierCopy.noteTechLeader = ''
        bidSupplierCopy.noteMPOLeader = ''
        bidSupplierCopy.noteSuccessBid = ''
        bidSupplierCopy.scoreTech = 0
        bidSupplierCopy.scoreTrade = 0
        bidSupplierCopy.scorePrice = 0
        bidSupplierCopy.scoreManualTech = 0
        bidSupplierCopy.scoreManualTrade = 0
        bidSupplierCopy.scoreManualPrice = 0
        bidSupplierCopy.status = enumData.BidSupplierStatus.DaDuocChon.code
        bidSupplierCopy.statusTech = enumData.BidSupplierTechStatus.ChuaXacNhan.code
        bidSupplierCopy.statusTrade = enumData.BidSupplierTradeStatus.ChuaXacNhan.code
        bidSupplierCopy.statusPrice = enumData.BidSupplierPriceStatus.ChuaXacNhan.code
        bidSupplierCopy.statusResetPrice = enumData.BidSupplierResetPriceStatus.KhongYeuCau.code
        bidSupplierCopy.createdBy = user.id
        await bidSupplierRepo.save(bidSupplierCopy)
      }
      //#endregion

      //#region Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = bidCopy.id
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.SaoChepGoiThau.code
      bidHistory.description = `Gói thầu gốc ${bidSrc.code}`
      await bidHistoryRepo.save(bidHistory)
      //#endregion

      //#region Bid Item
      const lstBidItemSrc = await bidSrc.childs
      let lastSortItem = 0
      for (const bidItemSrc of lstBidItemSrc) {
        lastSortItem++
        const sortString = ('00' + lastSortItem).slice(-3)
        let bidItemCopy = bidRepo.create({ ...bidItemSrc })
        // gen lại mã gói thầu mới
        bidItemCopy.code = bidCopy.code + '_' + sortString
        bidItemCopy.createdBy = user.id
        bidItemCopy.parentId = bidCopy.id
        delete bidItemCopy.id
        delete bidItemCopy.createdAt
        delete bidItemCopy.updatedAt
        delete bidItemCopy.updatedBy
        bidItemCopy = await bidRepo.save(bidItemCopy)

        //#region Bid Tech
        const lstBidTechSrc = await bidItemSrc.techs
        if (lstBidTechSrc.length > 0) {
          const lstTechSrc1 = lstBidTechSrc.filter((c) => c.level == 1)
          for (const tech1Src of lstTechSrc1) {
            let tech1Copy = bidTechRepo.create({ ...tech1Src })
            // gán lại gói thầu
            tech1Copy.bidId = bidItemCopy.id
            tech1Copy.createdBy = user.id
            delete tech1Copy.id
            delete tech1Copy.createdAt
            delete tech1Copy.updatedAt
            tech1Copy = await bidTechRepo.save(tech1Copy)

            if (tech1Copy.type === enumData.DataType.List.code) {
              const lstDetailSrc = await tech1Src.bidTechListDetails
              if (lstDetailSrc.length > 0) {
                for (const detailSrc of lstDetailSrc) {
                  const detailCopy = bidTechListDetailRepo.create({ ...detailSrc })
                  // gán lại bidTechId
                  detailCopy.bidTechId = tech1Copy.id
                  detailCopy.createdBy = user.id
                  delete detailCopy.id
                  delete detailCopy.createdAt
                  delete detailCopy.updatedAt
                  await bidTechListDetailRepo.save(detailCopy)
                }
              }
            }

            const lstTechSrc2 = lstBidTechSrc.filter((c) => c.parentId == tech1Src.id)
            for (const tech2Src of lstTechSrc2) {
              let tech2Copy = bidTechRepo.create({ ...tech2Src })
              // gán lại gói thầu
              tech2Copy.bidId = bidItemCopy.id
              // gán lại parent
              tech2Copy.parentId = tech1Copy.id
              tech2Copy.createdBy = user.id
              delete tech2Copy.id
              delete tech2Copy.createdAt
              delete tech2Copy.updatedAt
              tech2Copy = await bidTechRepo.save(tech2Copy)

              if (tech2Copy.type === enumData.DataType.List.code) {
                const lstDetailSrc = await tech2Src.bidTechListDetails
                if (lstDetailSrc.length > 0) {
                  for (const detailSrc of lstDetailSrc) {
                    const detailCopy = bidTechListDetailRepo.create({ ...detailSrc })
                    // gán lại bidTechId
                    detailCopy.bidTechId = tech2Copy.id
                    detailCopy.createdBy = user.id
                    delete detailCopy.id
                    delete detailCopy.createdAt
                    delete detailCopy.updatedAt
                    await bidTechListDetailRepo.save(detailCopy)
                  }
                }
              }
            }
          }
        }
        //#endregion

        //#region Bid Trade
        const lstBidTradeSrc = await bidItemSrc.trades
        if (lstBidTradeSrc.length > 0) {
          const lstTradeSrc1 = lstBidTradeSrc.filter((c) => c.level == 1)
          for (const trade1Src of lstTradeSrc1) {
            let trade1Copy = bidTradeRepo.create({ ...trade1Src })
            // gán lại gói thầu
            trade1Copy.bidId = bidItemCopy.id
            trade1Copy.createdBy = user.id
            delete trade1Copy.id
            delete trade1Copy.createdAt
            delete trade1Copy.updatedAt
            trade1Copy = await bidTradeRepo.save(trade1Copy)

            if (trade1Copy.type === enumData.DataType.List.code) {
              const lstDetailSrc = await trade1Src.bidTradeListDetails
              if (lstDetailSrc.length > 0) {
                for (const detailSrc of lstDetailSrc) {
                  const detailCopy = bidTradeListDetailRepo.create({ ...detailSrc })
                  // gán lại bidTradeId
                  detailCopy.bidTradeId = trade1Copy.id
                  detailCopy.createdBy = user.id
                  delete detailCopy.id
                  delete detailCopy.createdAt
                  delete detailCopy.updatedAt
                  await bidTradeListDetailRepo.save(detailCopy)
                }
              }
            }

            const lstTradeSrc2 = lstBidTradeSrc.filter((c) => c.parentId == trade1Src.id)
            for (const trade2Src of lstTradeSrc2) {
              let trade2Copy = bidTradeRepo.create({ ...trade2Src })
              // gán lại gói thầu
              trade2Copy.bidId = bidItemCopy.id
              // gán lại parent
              trade2Copy.parentId = trade1Copy.id
              trade2Copy.createdBy = user.id
              delete trade2Copy.id
              delete trade2Copy.createdAt
              delete trade2Copy.updatedAt
              trade2Copy = await bidTradeRepo.save(trade2Copy)

              if (trade2Copy.type === enumData.DataType.List.code) {
                const lstDetailSrc = await trade2Src.bidTradeListDetails
                if (lstDetailSrc.length > 0) {
                  for (const detailSrc of lstDetailSrc) {
                    const detailCopy = bidTradeListDetailRepo.create({ ...detailSrc })
                    // gán lại bidTradeId
                    detailCopy.bidTradeId = trade2Copy.id
                    detailCopy.createdBy = user.id
                    delete detailCopy.id
                    delete detailCopy.createdAt
                    delete detailCopy.updatedAt
                    await bidTradeListDetailRepo.save(detailCopy)
                  }
                }
              }
            }
          }
        }
        //#endregion

        //#region Bid Price
        const lstBidPriceSrc = await bidItemSrc.prices
        const dicPrice: any = {}
        if (lstBidPriceSrc.length > 0) {
          const lstPriceSrc1 = lstBidPriceSrc.filter((c) => c.level == 1)
          for (const price1Src of lstPriceSrc1) {
            let price1Copy = bidPriceRepo.create({ ...price1Src })
            // gán lại gói thầu
            price1Copy.bidId = bidItemCopy.id
            price1Copy.createdBy = user.id
            delete price1Copy.id
            delete price1Copy.createdAt
            delete price1Copy.updatedAt
            price1Copy = await bidPriceRepo.save(price1Copy)
            dicPrice[price1Src.id] = price1Copy.id

            // BLOCK tạo bidPriceListDetail
            {
              const lstDetailSrc = await price1Src.bidPriceListDetails
              if (lstDetailSrc.length > 0) {
                for (const detailSrc of lstDetailSrc) {
                  const detailCopy = bidPriceListDetailRepo.create({ ...detailSrc })
                  // gán lại bidPriceId
                  detailCopy.bidPriceId = price1Copy.id
                  detailCopy.createdBy = user.id
                  delete detailCopy.id
                  delete detailCopy.createdAt
                  delete detailCopy.updatedAt
                  await bidPriceListDetailRepo.save(detailCopy)
                }
              }
            }

            const lstPriceSrc2 = lstBidPriceSrc.filter((c) => c.parentId == price1Src.id)
            for (const price2Src of lstPriceSrc2) {
              let price2Copy = bidPriceRepo.create({ ...price2Src })
              // gán lại gói thầu
              price2Copy.bidId = bidItemCopy.id
              // gán lại parent
              price2Copy.parentId = price1Copy.id
              price2Copy.createdBy = user.id
              delete price2Copy.id
              delete price2Copy.createdAt
              delete price2Copy.updatedAt
              price2Copy = await bidPriceRepo.save(price2Copy)
              dicPrice[price2Src.id] = price2Copy.id

              // BLOCK tạo bidPriceListDetail
              {
                const lstDetailSrc = await price2Src.bidPriceListDetails
                if (lstDetailSrc.length > 0) {
                  for (const detailSrc of lstDetailSrc) {
                    const detailCopy = bidPriceListDetailRepo.create({ ...detailSrc })
                    // gán lại bidPriceId
                    detailCopy.bidPriceId = price2Copy.id
                    detailCopy.createdBy = user.id
                    delete detailCopy.id
                    delete detailCopy.createdAt
                    delete detailCopy.updatedAt
                    await bidPriceListDetailRepo.save(detailCopy)
                  }
                }
              }

              const lstPriceSrc3 = lstBidPriceSrc.filter((c) => c.parentId == price2Src.id)
              for (const price3Src of lstPriceSrc3) {
                let price3Copy = bidPriceRepo.create({ ...price3Src })
                // gán lại gói thầu
                price3Copy.bidId = bidItemCopy.id
                // gán lại parent
                price3Copy.parentId = price2Copy.id
                price3Copy.createdBy = user.id
                delete price3Copy.id
                delete price3Copy.createdAt
                delete price3Copy.updatedAt
                price3Copy = await bidPriceRepo.save(price3Copy)
                dicPrice[price3Src.id] = price3Copy.id

                // BLOCK tạo bidPriceListDetail
                {
                  const lstDetailSrc = await price3Src.bidPriceListDetails
                  if (lstDetailSrc.length > 0) {
                    for (const detailSrc of lstDetailSrc) {
                      const detailCopy = bidPriceListDetailRepo.create({ ...detailSrc })
                      // gán lại bidPriceId
                      detailCopy.bidPriceId = price3Copy.id
                      detailCopy.createdBy = user.id
                      delete detailCopy.id
                      delete detailCopy.createdAt
                      delete detailCopy.updatedAt
                      await bidPriceListDetailRepo.save(detailCopy)
                    }
                  }
                }
              }
            }
          }
        }
        //#endregion

        //#region Bid Price Col
        const lstColSrc = await bidItemSrc.bidPriceCols
        if (lstColSrc.length > 0) {
          for (const colSrc of lstColSrc) {
            let colCopy = bidPriceColRepo.create({ ...colSrc })
            // gán lại gói thầu
            colCopy.bidId = bidItemCopy.id
            colCopy.createdBy = user.id
            delete colCopy.id
            delete colCopy.createdAt
            delete colCopy.updatedAt
            colCopy = await bidPriceColRepo.save(colCopy)

            const lstColValueSrc = await colSrc.bidPriceColValue
            for (const colValue of lstColValueSrc) {
              const colValueCopy = bidPriceColValueRepo.create({ ...colValue })
              // gán lại gói thầu
              colValueCopy.bidPriceColId = colCopy.id
              colValueCopy.bidPriceId = dicPrice[colValueCopy.bidPriceId]
              colValueCopy.createdBy = user.id
              delete colValueCopy.id
              delete colValueCopy.createdAt
              delete colValueCopy.updatedAt
              await bidPriceColValueRepo.save(colValueCopy)
            }
          }
        }
        //#endregion

        //#region Bid Custom Price
        const lstCustomPriceSrc = await bidItemSrc.customPrices
        for (const customPriceSrc of lstCustomPriceSrc) {
          const customPriceCopy = bidCustomPriceRepo.create({ ...customPriceSrc })
          // gán lại gói thầu
          customPriceCopy.bidId = bidItemCopy.id
          customPriceCopy.createdBy = user.id
          delete customPriceCopy.id
          delete customPriceCopy.createdAt
          delete customPriceCopy.updatedAt
          await bidCustomPriceRepo.save(customPriceCopy)
        }
        //#endregion

        //#region Bid Supplier
        const lstBidSupplierSrc = await bidItemSrc.bidSuppliers
        for (const bidSupplierSrc of lstBidSupplierSrc) {
          const bidSupplierCopy = bidSupplierRepo.create({ ...bidSupplierSrc })
          delete bidSupplierCopy.id
          delete bidSupplierCopy.createdAt
          delete bidSupplierCopy.updatedAt
          // gán lại gói thầu
          bidSupplierCopy.bidId = bidItemCopy.id
          // reset các biến đánh giá
          bidSupplierCopy.isTechValid = true
          bidSupplierCopy.isTradeValid = true
          bidSupplierCopy.isPriceValid = true
          bidSupplierCopy.isSuccessBid = false
          bidSupplierCopy.isHighlight = false
          bidSupplierCopy.isNotHaveMinValue = false
          bidSupplierCopy.note = ''
          bidSupplierCopy.noteTech = ''
          bidSupplierCopy.noteTrade = ''
          bidSupplierCopy.notePrice = ''
          bidSupplierCopy.noteTechLeader = ''
          bidSupplierCopy.noteMPOLeader = ''
          bidSupplierCopy.noteSuccessBid = ''
          bidSupplierCopy.scoreTech = 0
          bidSupplierCopy.scoreTrade = 0
          bidSupplierCopy.scorePrice = 0
          bidSupplierCopy.scoreManualTech = 0
          bidSupplierCopy.scoreManualTrade = 0
          bidSupplierCopy.scoreManualPrice = 0
          bidSupplierCopy.status = enumData.BidSupplierStatus.DaDuocChon.code
          bidSupplierCopy.statusTech = enumData.BidSupplierTechStatus.ChuaXacNhan.code
          bidSupplierCopy.statusTrade = enumData.BidSupplierTradeStatus.ChuaXacNhan.code
          bidSupplierCopy.statusPrice = enumData.BidSupplierPriceStatus.ChuaXacNhan.code
          bidSupplierCopy.createdBy = user.id
          await bidSupplierRepo.save(bidSupplierCopy)
        }
        //#endregion
      }

      const lstBidItem = await bidItemRepo.find({ where: { bidId: bidSrc.id } })
      if (lstBidItem)
        for (const item of lstBidItem) {
          const product = await materiaItemRepo.findOne({ where: { id: item.itemId } })
          const productName = product?.name
          // const bidItemNew = bidItemRepo.create(item)
          item.bidId = bidCopy.id
          item.productName = productName
          await bidItemRepo.save(item)
        }
      //#endregion

      return { message: `Sao chép thành công, gói thầu có mã TBMT [${bidCopy.code}] được tạo ra từ gói thầu có mã TBMT [${bidSrc.code}].` }
    })
  }

  /** Kiểm tra quyền sửa thông tin gói thầu */
  async checkPermissionMpoEdit(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const lstStatusCanEdit = [
      enumData.BidStatus.GoiThauTam.code,
      enumData.BidStatus.ChoDuyetGoiThauTam.code,
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    const bid = await this.repo.findOne({ where: { id: bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false } })
    if (bid) {
      if (lstStatusCanEdit.includes(bid.status)) {
        result = await this.bidEmployeeAccessRepo.isMPO(user, bid.id)
        if (!result) {
          message = 'Bạn không có quyền chỉnh sửa thông tin gói thầu.'
        }
      } else {
        result = false
        message = 'Chỉ được phép chỉnh sửa thông tin gói thầu khi chưa mở thầu.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Kiểm tra trước khi vào transaction import */
  private async checkImportBids(user: UserDto, data: any) {
    const checkResult: { isCheckError: boolean; lstError: any[]; message: string } = {
      isCheckError: false,
      lstError: [],
      message: '',
    }

    const lstBidType = await this.bidTypeRepo.find({ where: { companyId: user.companyId, isDeleted: false } }) //get bidTypeId from bidTypeName
    const lstEmployee = await this.employeeRepo.find({ where: { companyId: user.companyId, isDeleted: false } }) //get employeeIds from employeeNames
    const lstEmployeeName = lstEmployee.map((c) => c.name)
    const lstCompanyInvite = await this.settingStringRepo.find({
      where: {
        companyId: user.companyId,
        type: enumData.SettingStringType.company,
        isDeleted: false,
      },
    })
    const lstAddressSubmit = await this.settingStringRepo.find({
      where: {
        companyId: user.companyId,
        type: enumData.SettingStringType.address,
        isDeleted: false,
      },
    })
    const lstMasterBidGuarantee = await this.settingStringRepo.find({
      where: {
        companyId: user.companyId,
        type: enumData.SettingStringType.masterBidGuarantee,
        isDeleted: false,
      },
    })

    // Kiểm tra từng row
    const lstErr = []
    for (const row of data.lstData) {
      // zenId: 'STT *' đã check ở FE

      // companyInvite: 'Công ty mời thầu *'
      if (row.companyInvite != null) row.companyInvite = (row.companyInvite + '').trim()
      if (row.companyInvite == null || row.companyInvite === '') {
        const errorMessage = '[Công ty mời thầu *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else if (!lstCompanyInvite.some((c) => c.name == row.companyInvite)) {
        const errorMessage = `[Công ty mời thầu *] có tên [${row.companyInvite}] không tồn tại trong hệ thống`
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // addressSubmit: 'Địa chỉ nộp hồ sơ thầu'
      if (row.addressSubmit != null) row.addressSubmit = (row.addressSubmit + '').trim()
      if (row.addressSubmit != null && row.addressSubmit !== '' && !lstAddressSubmit.some((c) => c.name == row.addressSubmit)) {
        const errorMessage = `[Địa chỉ nộp hồ sơ thầu] có tên [${row.addressSubmit}] không tồn tại trong hệ thống`
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // listAddress: 'Các địa điểm thực hiện gói thầu'
      if (row.listAddress != null) row.listAddress = (row.listAddress + '').trim()

      // anotherRoleNames: 'Các thành viên hội đồng xét thầu *'
      if (row.anotherRoleNames != null) row.anotherRoleNames = (row.anotherRoleNames + '').trim()
      if (row.anotherRoleNames == null || row.anotherRoleNames === '') {
        const errorMessage = '[Các thành viên hội đồng xét thầu *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const lstAnotherRoleName = row.anotherRoleNames.split(',').map((c: string) => c.trim())
        const lstAnotherRoleNameNotIn = lstAnotherRoleName.filter((c: any) => !lstEmployeeName.includes(c))
        if (lstAnotherRoleNameNotIn.length > 0) {
          const errorMessage = `[Các thành viên hội đồng xét thầu *] có các nhân viên [${lstAnotherRoleNameNotIn.join()}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
      }

      // otherRoleNames: 'Các thành viên khác'
      if (row.otherRoleNames != null) row.otherRoleNames = (row.otherRoleNames + '').trim()
      if (row.otherRoleNames != null && row.otherRoleNames !== '') {
        const lstOtherRoleName = row.otherRoleNames.split(',').map((c: string) => c.trim())
        const lstOtherRoleNameNotIn = lstOtherRoleName.filter((c: any) => !lstEmployeeName.includes(c))
        if (lstOtherRoleNameNotIn.length > 0) {
          const errorMessage = `[Các thành viên khác] có các nhân viên [${lstOtherRoleNameNotIn.join()}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
      }

      // name: 'Tên gói thầu *'
      if (row.name != null) row.name = (row.name + '').trim()
      if (row.name == null || row.name === '') {
        const errorMessage = '[Tên gói thầu *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // scoreDLC: 'Điểm tối đa cho các hạng mục chào giá *'
      if (row.scoreDLC != null) row.scoreDLC = (row.scoreDLC + '').trim()
      if (row.scoreDLC == null || row.scoreDLC === '') {
        const errorMessage = '[Điểm tối đa cho các hạng mục chào giá *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const scoreDLC = +row.scoreDLC
        if (isNaN(scoreDLC) || !isFinite(scoreDLC)) {
          const errorMessage = '[Điểm tối đa cho các hạng mục chào giá *] không hợp lệ'
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.scoreDLC = scoreDLC
      }

      // bidTypeName: 'Hình thức đấu thầu *'
      if (row.bidTypeName != null) row.bidTypeName = (row.bidTypeName + '').trim()
      if (row.bidTypeName == null || row.bidTypeName === '') {
        const errorMessage = '[Hình thức đấu thầu *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const bidTypeObj = lstBidType.find((c) => c.name == row.bidTypeName)
        if (!bidTypeObj) {
          const errorMessage = `[Hình thức đấu thầu *] có tên [${row.bidTypeName}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.bidTypeId = bidTypeObj.id
      }

      // acceptEndDate: '3.Ngày hết hạn xác nhận tham gia đấu thầu *'
      if (row.acceptEndDate != null) row.acceptEndDate = (row.acceptEndDate + '').trim()
      if (row.acceptEndDate == null || row.acceptEndDate === '') {
        const errorMessage = '[3.Ngày hết hạn xác nhận tham gia đấu thầu *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const acceptEndDate = new Date(row.acceptEndDate)
        if (isNaN(acceptEndDate.getTime())) {
          const errorMessage = `[3.Ngày hết hạn xác nhận tham gia đấu thầu *] là [${row.acceptEndDate}] không hợp lệ, ngày phải có định dạng yyyy/mm/dd HH:mm hoặc yyyy-mm-dd HH:mm`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.acceptEndDate = acceptEndDate
      }

      // submitEndDate: '4.Ngày hết hạn nộp hồ sơ thầu *'
      if (row.submitEndDate != null) row.submitEndDate = (row.submitEndDate + '').trim()
      if (row.submitEndDate == null || row.submitEndDate === '') {
        const errorMessage = '[4.Ngày hết hạn nộp hồ sơ thầu *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const submitEndDate = new Date(row.submitEndDate)
        if (isNaN(submitEndDate.getTime())) {
          const errorMessage = `[4.Ngày hết hạn nộp hồ sơ thầu *] là [${row.submitEndDate}, ngày phải có định dạng yyyy/mm/dd HH:mm hoặc yyyy-mm-dd HH:mm`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.submitEndDate = submitEndDate
      }

      // timeserving: 'Hiệu lực hợp đồng (tháng) *'
      if (row.timeserving != null) row.timeserving = (row.timeserving + '').trim()
      if (row.timeserving == null || row.timeserving === '') {
        const errorMessage = '[Hiệu lực hợp đồng (tháng) *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const timeserving = +row.timeserving
        if (isNaN(timeserving) || !isFinite(timeserving)) {
          const errorMessage = '[Hiệu lực hợp đồng (tháng) *] không hợp lệ'
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.timeserving = timeserving
      }

      // masterBidGuaranteeName: 'Hình thức bảo lãnh dự thầu'
      if (row.masterBidGuaranteeName != null) row.masterBidGuaranteeName = (row.masterBidGuaranteeName + '').trim()
      if (row.masterBidGuaranteeName != null && row.masterBidGuaranteeName !== '') {
        const masterBidGuaranteeObj = lstMasterBidGuarantee.find((c) => c.name == row.masterBidGuaranteeName)
        if (!masterBidGuaranteeObj) {
          const errorMessage = `[Hình thức bảo lãnh dự thầu] có tên [${row.masterBidGuaranteeName}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.masterBidGuaranteeId = masterBidGuaranteeObj.id
      }

      // moneyGuarantee: 'Số tiền bảo lãnh dự thầu (VNĐ)'
      if (row.moneyGuarantee != null) row.moneyGuarantee = (row.moneyGuarantee + '').trim()
      if (row.moneyGuarantee != null || row.moneyGuarantee !== '') {
        const moneyGuarantee = +row.moneyGuarantee
        if (isNaN(moneyGuarantee) || !isFinite(moneyGuarantee)) {
          const errorMessage = '[Số tiền bảo lãnh dự thầu (VNĐ)] không hợp lệ'
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.moneyGuarantee = moneyGuarantee
      }

      // timeGuarantee: 'Thời hạn bảo lãnh dự thầu (tháng)'
      if (row.timeGuarantee != null) row.timeGuarantee = (row.timeGuarantee + '').trim()
      if (row.timeGuarantee != null || row.timeGuarantee !== '') {
        const timeGuarantee = +row.timeGuarantee
        if (isNaN(timeGuarantee) || !isFinite(timeGuarantee)) {
          const errorMessage = '[Thời hạn bảo lãnh dự thầu (tháng)] không hợp lệ'
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.timeGuarantee = timeGuarantee
      }

      // mpoName: 'Thành viên phụ trách mua hàng *'
      if (row.mpoName != null) row.mpoName = (row.mpoName + '').trim()
      if (row.mpoName == null || row.mpoName === '') {
        const errorMessage = '[Thành viên phụ trách mua hàng *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const employeeObj = lstEmployee.find((c) => c.name == row.mpoName)
        if (!employeeObj) {
          const errorMessage = `[Thành viên phụ trách mua hàng *] có tên [${row.mpoName}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.mpoId = employeeObj.id
      }

      // mpoLeadName: 'Người duyệt nội dung mua hàng *'
      if (row.mpoLeadName != null) row.mpoLeadName = (row.mpoLeadName + '').trim()
      if (row.mpoLeadName == null || row.mpoLeadName === '') {
        const errorMessage = '[Người duyệt nội dung mua hàng *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const employeeObj = lstEmployee.find((c) => c.name == row.mpoLeadName)
        if (!employeeObj) {
          const errorMessage = `[Người duyệt nội dung mua hàng *] có tên [${row.mpoLeadName}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.mpoLeadId = employeeObj.id
      }

      // techName: 'Thành viên phụ trách yêu cầu kỹ thuật *'
      if (row.techName != null) row.techName = (row.techName + '').trim()
      if (row.techName == null || row.techName === '') {
        const errorMessage = '[Thành viên phụ trách yêu cầu kỹ thuật *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const employeeObj = lstEmployee.find((c) => c.name == row.techName)
        if (!employeeObj) {
          const errorMessage = `[Thành viên phụ trách yêu cầu kỹ thuật *] có tên [${row.techName}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.techId = employeeObj.id
      }

      // techLeadName: 'Người duyệt yêu cầu kỹ thuật *'
      if (row.techLeadName != null) row.techLeadName = (row.techLeadName + '').trim()
      if (row.techLeadName == null || row.techLeadName === '') {
        const errorMessage = '[Người duyệt yêu cầu kỹ thuật *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const employeeObj = lstEmployee.find((c) => c.name == row.techLeadName)
        if (!employeeObj) {
          const errorMessage = `[Người duyệt yêu cầu kỹ thuật *] có tên [${row.techLeadName}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.techLeadId = employeeObj.id
      }

      // timeTechDate: '1.Thời hạn thiết lập các yêu cầu kỹ thuật *'
      if (row.timeTechDate != null) row.timeTechDate = (row.timeTechDate + '').trim()
      if (row.timeTechDate == null || row.timeTechDate === '') {
        const errorMessage = '[1.Thời hạn thiết lập các yêu cầu kỹ thuật *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const timeTechDate = new Date(row.timeTechDate)
        if (isNaN(timeTechDate.getTime())) {
          const errorMessage = `[1.Thời hạn thiết lập các yêu cầu kỹ thuật *] là [${row.timeTechDate}] không hợp lệ, ngày phải có định dạng yyyy/mm/dd HH:mm hoặc yyyy-mm-dd HH:mm`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.timeTechDate = timeTechDate
      }

      // timePriceDate: '2.Thời hạn thiết lập các hạng mục chào giá và điều kiện thương mại *'
      if (row.timePriceDate != null) row.timePriceDate = (row.timePriceDate + '').trim()
      if (row.timePriceDate == null || row.timePriceDate === '') {
        const errorMessage = '[2.Thời hạn thiết lập các hạng mục chào giá và điều kiện thương mại *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const timePriceDate = new Date(row.timePriceDate)
        if (isNaN(timePriceDate.getTime())) {
          const errorMessage = `[2.Thời hạn thiết lập các hạng mục chào giá và điều kiện thương mại *] là [${row.timePriceDate}] không hợp lệ, ngày phải có định dạng yyyy/mm/dd HH:mm hoặc yyyy-mm-dd HH:mm`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.timePriceDate = timePriceDate
      }

      // timeCheckTechDate: '5.Thời hạn đánh giá các yêu cầu kỹ thuật *'
      if (row.timeCheckTechDate != null) row.timeCheckTechDate = (row.timeCheckTechDate + '').trim()
      if (row.timeCheckTechDate == null || row.timeCheckTechDate === '') {
        const errorMessage = '[5.Thời hạn đánh giá các yêu cầu kỹ thuật *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const timeCheckTechDate = new Date(row.timeCheckTechDate)
        if (isNaN(timeCheckTechDate.getTime())) {
          const errorMessage = `[5.Thời hạn đánh giá các yêu cầu kỹ thuật *] là [${row.timeCheckTechDate}] không hợp lệ, ngày phải có định dạng yyyy/mm/dd HH:mm hoặc yyyy-mm-dd HH:mm`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.timeCheckTechDate = timeCheckTechDate
      }

      // timeCheckPriceDate: '6.Thời hạn đánh giá kết quả chào giá và điều kiện thương mại *'
      if (row.timeCheckPriceDate != null) row.timeCheckPriceDate = (row.timeCheckPriceDate + '').trim()
      if (row.timeCheckPriceDate == null || row.timeCheckPriceDate === '') {
        const errorMessage = '[6.Thời hạn đánh giá kết quả chào giá và điều kiện thương mại *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const timeCheckPriceDate = new Date(row.timeCheckPriceDate)
        if (isNaN(timeCheckPriceDate.getTime())) {
          const errorMessage = `[6.Thời hạn đánh giá kết quả chào giá và điều kiện thương mại *] là [${row.timeCheckPriceDate}] không hợp lệ, ngày phải có định dạng yyyy/mm/dd HH:mm hoặc yyyy-mm-dd HH:mm`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.timeCheckPriceDate = timeCheckPriceDate
      }

      // percentTech: 'Tỉ lệ phần trăm kỹ thuật *'
      if (row.percentTech != null) row.percentTech = (row.percentTech + '').trim()
      if (row.percentTech == null || row.percentTech === '') {
        const errorMessage = '[Tỉ lệ phần trăm kỹ thuật *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const percentTech = +row.percentTech
        if (isNaN(percentTech) || !isFinite(percentTech)) {
          const errorMessage = '[Tỉ lệ phần trăm kỹ thuật *] không hợp lệ'
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.percentTech = percentTech
      }

      // percentTrade: 'Tỉ lệ phần trăm ĐKTM *'
      if (row.percentTrade != null) row.percentTrade = (row.percentTrade + '').trim()
      if (row.percentTrade == null || row.percentTrade === '') {
        const errorMessage = '[Tỉ lệ phần trăm ĐKTM *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const percentTrade = +row.percentTrade
        if (isNaN(percentTrade) || !isFinite(percentTrade)) {
          const errorMessage = '[Tỉ lệ phần trăm ĐKTM *] không hợp lệ'
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.percentTrade = percentTrade
      }

      // percentPrice: 'Tỉ lệ phần trăm giá *'
      if (row.percentPrice != null) row.percentPrice = (row.percentPrice + '').trim()
      if (row.percentPrice == null || row.percentPrice === '') {
        const errorMessage = '[Tỉ lệ phần trăm giá *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const percentPrice = +row.percentPrice
        if (isNaN(percentPrice) || !isFinite(percentPrice)) {
          const errorMessage = '[Tỉ lệ phần trăm giá *] không hợp lệ'
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.percentPrice = percentPrice
      }

      // serviceInvite: 'Mô tả nội dung mời thầu *'
      if (row.serviceInvite != null) row.serviceInvite = (row.serviceInvite + '').trim()
      if (row.serviceInvite == null || row.serviceInvite === '') {
        const errorMessage = '[Mô tả nội dung mời thầu *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // isShowHomePage: 'Cho phép hiển thị thông tin mời thầu ở trang tin đấu thầu'
      if (row.isShowHomePage != null) row.isShowHomePage = (row.isShowHomePage + '').toUpperCase()
      if (row.isShowHomePage == 'TRUE' || row.isShowHomePage == 'X') {
        row.isShowHomePage = true
      } else {
        row.isShowHomePage = false
      }

      // isSendEmailInviteBid: 'Gửi thông báo mời thầu cho Doanh nghiệp'
      if (row.isSendEmailInviteBid != null) row.isSendEmailInviteBid = (row.isSendEmailInviteBid + '').toUpperCase()
      if (row.isSendEmailInviteBid == 'TRUE' || row.isSendEmailInviteBid == 'X') {
        row.isSendEmailInviteBid = true
      } else {
        row.isSendEmailInviteBid = false
      }

      // isAutoBid: 'Tự động chọn NCC thắng thầu và kết thúc thầu'
      if (row.isAutoBid != null) row.isAutoBid = (row.isAutoBid + '').toUpperCase()
      if (row.isAutoBid == 'TRUE' || row.isAutoBid == 'X') {
        row.isAutoBid = true
      } else {
        row.isAutoBid = false
      }
    }

    if (lstErr.length > 0) {
      checkResult.lstError = lstErr
      checkResult.isCheckError = true
      checkResult.message = `Có ${checkResult.lstError.length} dòng lỗi, vui lòng xem chi tiết lỗi và kiểm tra lại file!`
    }

    return checkResult
  }

  private async findLastSort(user: UserDto, bidRepo: Repository<BidEntity>, code: string) {
    const objData = await bidRepo.findOne({
      where: { code: Like(`%${code}%`), parentId: IsNull(), companyId: user.companyId },
      order: { createdAt: 'DESC' },
      select: { id: true, code: true },
    })

    if (!objData) return 0

    const sortString = objData.code.slice(-3)
    return +sortString
  }

  /** Import Gói thầu */
  public async importBids(user: UserDto, data: { lstData: any[] }) {
    // Check user
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    // Check & validate data
    const checkResult = await this.checkImportBids(user, data)
    if (checkResult.lstError.length > 0) {
      return checkResult
    }

    const employeeId = user.employeeId
    // trả về thông báo số Gói thầu được import thành công, và danh sách các dòng với chi tiết lỗi từng dòng
    return await this.repo.manager.transaction(async (manager) => {
      const checkResult: any = { isCheckError: false, lstError: [], message: '', lstSupplierId: [] }
      let numRowSuccess = 0

      const bidRepo = manager.getRepository(BidEntity)
      const serviceRepo = manager.getRepository(ServiceEntity)
      const employeeRepo = manager.getRepository(EmployeeEntity)
      const bidEmployeeAccessRepo = manager.getRepository(BidEmployeeAccessEntity)
      const bidHistoryRepo = manager.getRepository(BidHistoryEntity)
      const lstEmployee = await employeeRepo.find({ where: { companyId: user.companyId, isDeleted: false } })

      // Lưu từng row
      const lstErr = []
      for (const row of data.lstData) {
        try {
          let bid = new BidEntity()
          bid.companyId = user.companyId
          bid.createdBy = user.id
          bid.name = row.name
          const dateNow = new Date()
          const objService = await serviceRepo.findOne({ where: { id: row.serviceId, companyId: user.companyId } })
          if (!objService) {
            throw new NotFoundException('Lĩnh vực mua hàng không còn tồn tại.')
          }
          const codeTemp = moment(dateNow).format('YYYYMM') + objService.name
          const lastSort = await this.findLastSort(user, bidRepo, codeTemp + '_')
          const sortString = ('00' + (lastSort + 1)).slice(-3)
          row.publicDate = dateNow
          row.startBidDate = new Date(row.submitEndDate)
          row.startBidDate.setDate(row.startBidDate.getDate() + 1)
          row.startBidDate.setHours(8, 0, 0, 0)

          if (row.startBidDate >= row.timeCheckTechDate) {
            throw new BadRequestException('Thời điểm mở thầu phải sớm hơn thời hạn đánh giá yêu cầu kỹ thuật, năng lực.')
          }
          if (row.timeTechDate <= dateNow) {
            throw new BadRequestException('Ngày (1) phải trễ hơn ngày hiện tại.')
          }
          if (row.timePriceDate <= dateNow) {
            throw new BadRequestException('Ngày (2) phải trễ hơn ngày hiện tại.')
          }
          if (row.timeTechDate >= row.acceptEndDate) {
            throw new BadRequestException('Ngày (1) phải sớm hơn ngày (3).')
          }
          if (row.timePriceDate >= row.acceptEndDate) {
            throw new BadRequestException('Ngày (2) phải sớm hơn ngày (3).')
          }
          if (row.acceptEndDate >= row.submitEndDate) {
            throw new BadRequestException('Ngày (3) phải sớm hơn ngày (4).')
          }
          if (row.submitEndDate >= row.timeCheckTechDate) {
            throw new BadRequestException('Ngày (4) phải sớm hơn ngày (5).')
          }
          if (row.submitEndDate >= row.timeCheckPriceDate) {
            throw new BadRequestException('Ngày (4) sớm hơn ngày (6).')
          }
          bid.code = codeTemp + '_' + sortString

          // gói thầu mới tạo, mặc định lấy theo service
          bid.fomular = objService.fomular

          bid.serviceInvite = row.serviceInvite

          bid.acceptEndDate = row.acceptEndDate

          bid.submitEndDate = row.submitEndDate

          bid.addressSubmit = row.addressSubmit || ''

          bid.companyInvite = row.companyInvite

          bid.listAddress = row.listAddress || ''

          bid.publicDate = row.publicDate

          bid.bidTypeId = row.bidTypeId

          bid.timeserving = row.timeserving

          bid.scoreDLC = row.scoreDLC

          bid.startBidDate = row.startBidDate

          bid.moneyGuarantee = row.moneyGuarantee
          bid.timeGuarantee = row.timeGuarantee
          bid.masterBidGuaranteeId = row.masterBidGuaranteeId

          bid.timeTechDate = row.timeTechDate
          bid.timePriceDate = row.timePriceDate
          bid.timeCheckTechDate = row.timeCheckTechDate
          bid.timeCheckPriceDate = row.timeCheckPriceDate
          bid.status = enumData.BidStatus.GoiThauTam.code

          bid.isShowHomePage = row.isShowHomePage
          bid.isSendEmailInviteBid = row.isSendEmailInviteBid
          bid.isAutoBid = row.isAutoBid

          bid.serviceId = row.serviceId
          bid.percentTech = row.percentTech
          bid.percentTrade = row.percentTrade
          bid.percentPrice = row.percentPrice

          bid.statusTech = enumData.BidTechStatus.DangTao.code
          bid.statusTrade = enumData.BidTradeStatus.DangTao.code
          bid.statusPrice = enumData.BidPriceStatus.DangTao.code
          bid.statusRateTech = enumData.BidTechRateStatus.ChuaTao.code
          bid.statusRateTrade = enumData.BidTradeRateStatus.ChuaTao.code
          bid.statusRatePrice = enumData.BidPriceRateStatus.ChuaTao.code

          bid.createdBy = user.id

          const bidEntity = await bidRepo.save(bid)

          // MPO
          const mpoAccess = new BidEmployeeAccessEntity()
          mpoAccess.companyId = user.companyId
          mpoAccess.createdBy = user.id
          mpoAccess.bidId = bidEntity.id
          mpoAccess.employeeId = row.mpoId
          mpoAccess.type = enumData.BidRuleType.MPO.code
          await bidEmployeeAccessRepo.save(mpoAccess)

          // MPO Lead
          const mpoLeadAccess = new BidEmployeeAccessEntity()
          mpoLeadAccess.companyId = user.companyId
          mpoLeadAccess.createdBy = user.id
          mpoLeadAccess.bidId = bidEntity.id
          mpoLeadAccess.employeeId = row.mpoLeadId
          mpoLeadAccess.type = enumData.BidRuleType.MPOLeader.code
          await bidEmployeeAccessRepo.save(mpoLeadAccess)

          // Tech
          const techAccess = new BidEmployeeAccessEntity()
          techAccess.companyId = user.companyId
          techAccess.createdBy = user.id
          techAccess.bidId = bidEntity.id
          techAccess.employeeId = row.techId
          techAccess.type = enumData.BidRuleType.Tech.code
          await bidEmployeeAccessRepo.save(techAccess)

          // Tech Lead
          const techLeadAccess = new BidEmployeeAccessEntity()
          techLeadAccess.companyId = user.companyId
          techLeadAccess.createdBy = user.id
          techLeadAccess.bidId = bidEntity.id
          techLeadAccess.employeeId = row.techLeadId
          techLeadAccess.type = enumData.BidRuleType.TechLeader.code
          await bidEmployeeAccessRepo.save(techLeadAccess)

          // Member
          const lstMemberName = row.anotherRoleNames.split(',').map((c: string) => c.trim())

          for (const memberName of lstMemberName) {
            const employee = lstEmployee.find((c) => c.name == memberName)
            if (employee) {
              const anotherAccess = new BidEmployeeAccessEntity()
              anotherAccess.companyId = user.companyId
              anotherAccess.createdBy = user.id
              anotherAccess.bidId = bidEntity.id
              anotherAccess.employeeId = employee.id
              anotherAccess.type = enumData.BidRuleType.Memmber.code
              await bidEmployeeAccessRepo.save(anotherAccess)
            }
          }

          // Other
          if (row.otherRoleNames) {
            const lstOtherName = row.otherRoleNames.split(',').map((c: string) => c.trim())

            for (const otherName of lstOtherName) {
              const employee = lstEmployee.find((c) => c.name == otherName)
              if (employee) {
                const anotherAccess = new BidEmployeeAccessEntity()
                anotherAccess.companyId = user.companyId
                anotherAccess.createdBy = user.id
                anotherAccess.bidId = bidEntity.id
                anotherAccess.employeeId = employee.id
                anotherAccess.type = enumData.BidRuleType.Other.code
                await bidEmployeeAccessRepo.save(anotherAccess)
              }
            }
          }

          // Bid History
          const bidHistory = new BidHistoryEntity()
          bidHistory.companyId = user.companyId
          bidHistory.createdBy = user.id
          bidHistory.bidId = bidEntity.id
          bidHistory.employeeId = employeeId
          bidHistory.status = enumData.BidHistoryStatus.TaoGoiThauExcel.code
          await bidHistoryRepo.save(bidHistory)

          numRowSuccess++
        } catch (error) {
          lstErr.push({ ...row, errorMessage: JSON.stringify(error) })
        }
      }

      if (lstErr.length > 0) {
        checkResult.lstError = lstErr
        checkResult.message = `Import thành công ${numRowSuccess}/${data.lstData.length} LVMH!`
      } else {
        checkResult.message = `Import thành công ${data.lstData.length} LVMH!`
      }

      return checkResult
    })
  }

  /** Kiểm tra quyền sửa thông tin gói thầu */
  async checkPermissionMpoDelete(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const bid = await this.repo.findOne({
      where: [{ id: bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false }, { childs: { id: bidId, isDeleted: false } }],
    })
    if (bid) {
      result = await this.bidEmployeeAccessRepo.isMPO_MPOLeader(user, bid.id)
      if (!result) {
        message = 'Bạn không có quyền xóa gói thầu.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Yêu cầu hủy gói thầu */
  async requestDeleteBid(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const objPermission = await this.checkPermissionMpoDelete(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    let isTempBid = bid.status == enumData.BidStatus.GoiThauTam.code
    // Nếu gói thầu tạm thì xóa trực tiếp mà không cần tạo yêu cầu xóa và đợi duyệt
    if (isTempBid) {
      await this.confirmDeleteBid(bidId, user)
    } else {
      await this.repo.update(bidId, { isRequestDelete: true, updatedBy: user.id })

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = bidId
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.YeuCauHuyGoiThau.code
      bidHistory.save()
    }

    if (!isTempBid) this.emailService.GuiMPOLeadHuyGoiThau(bidId)

    return { message: 'Yêu cầu xóa gói thầu thành công.' }
  }

  async confirmDeleteBid(bidId: string, user: UserDto) {
    await this.repo.manager.transaction(async (manager) => {
      const bidRepo = manager.getRepository(BidEntity)
      const bidHistoryRepo = manager.getRepository(BidHistoryEntity)
      const prRepo = manager.getRepository(PrEntity)
      const prItemRepo = manager.getRepository(PrItemEntity)

      const bid = await bidRepo.findOne({
        where: { id: bidId, companyId: user.companyId },
        select: { id: true, status: true, isRequestDelete: true, prId: true },
      })
      if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

      if (bid.status !== enumData.BidStatus.GoiThauTam.code && !bid.isRequestDelete) {
        throw new Error('Chỉ được xóa gói thầu nếu là gói thầu tạm hoặc có yêu cầu hủy gói thầu từ Nhân viên phụ trách mua hàng')
      }

      await bidRepo.update(bidId, { isDeleted: true, updatedBy: user.id })

      // Cập nhật lại Pr nếu gói thầu tạo từ PR
      if (bid.prId) {
        const lstBidItem = await bidRepo.find({
          where: { parentId: bidId, isDeleted: false },
          select: { id: true, prItemId: true, quantityItem: true },
        })
        for (const bidItem of lstBidItem) {
          // Nếu DS ITEM từ PR thì update lại quantityBid cho PR
          if (bidItem.prItemId) {
            const prItem = await prItemRepo.findOne({
              where: { id: bidItem.prItemId, isDeleted: false },
              select: { id: true, quantityBid: true },
            })
            if (!prItem) throw new Error('prItem không còn tồn tại!')

            prItem.quantityBid -= bidItem.quantityItem
            await prItemRepo.update(prItem.id, { quantityBid: prItem.quantityBid, updatedBy: user.id })
          }
        }

        const pr = await prRepo.findOne({ where: { id: bid.prId }, select: { id: true, isAllowBid: true } })
        if (pr && !pr.isAllowBid) await prRepo.update(pr.id, { isAllowBid: true, updatedBy: user.id })
      }

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = bidId
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.XacNhanHuyGoiThau.code
      await bidHistoryRepo.save(bidHistory)
    })
  }

  /** Xác nhận hủy gói thầu */
  async deleteBid(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const objPermission = await this.checkPermissionMpoDelete(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.confirmDeleteBid(bidId, user)

    //send mail cho các Doanh nghiệp khi MPOLeader phê duyệt hủy gói thầu
    await this.emailService.ThongBaoHuyGoiThauNCC(bidId)

    return { message: 'Xóa gói thầu thành công.' }
  }

  /** Kiểm tra quyền Gửi MPOLeader phê duyệt gói thầu tạm */
  async checkPermissionSendMpoleaderCheckBid(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const lstStatus = [enumData.BidStatus.GoiThauTam.code]
    const bid = await this.repo.findOne({
      where: [{ id: bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false }, { childs: { id: bidId, isDeleted: false } }],
    })
    if (bid) {
      if (lstStatus.includes(bid.status)) {
        result = await this.bidEmployeeAccessRepo.isMPO(user, bid.id)
        if (!result) {
          message = 'Bạn không có quyền gửi MPOLeader phê duyệt gói thầu tạm.'
        }
      } else {
        result = false
        message = 'Gói thầu đã thay đổi trạng thái, vui lòng kiểm tra lại.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Gửi MPOLeader phê duyệt gói thầu tạm */
  async sendMpoleaderCheckBid(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionSendMpoleaderCheckBid(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(bidId, { status: enumData.BidStatus.ChoDuyetGoiThauTam.code, updatedBy: user.id })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.YeuCauDuyetGoiThauTam.code
    bidHistory.save()

    //send mail MPOLeader
    this.emailService.GuiMPOLeadDuyetGoiThauTam(bidId)

    return { message: 'Đã gửi yêu cầu phê duyệt gói thầu tạm.' }
  }

  /** Kiểm tra quyền phê duyệt gói thầu tạm */
  async checkPermissionAcceptBid(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const lstStatus = [enumData.BidStatus.ChoDuyetGoiThauTam.code]
    const bid = await this.repo.findOne({ where: { id: bidId, parentId: IsNull(), companyId: user.companyId } })
    if (bid) {
      if (lstStatus.includes(bid.status)) {
        result = await this.bidEmployeeAccessRepo.isMPOLeader(user, bidId)
        if (!result) {
          message = 'Bạn không có quyền phê duyệt gói thầu tạm.'
        }
      } else {
        result = false
        message = 'Gói thầu đã thay đổi trạng thái, vui lòng kiểm tra lại.'
      }
    }

    return { hasPermission: result, message }
  }

  /** MPOLeader phê duyệt gói thầu tạm */
  async mpoleaderAcceptBid(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionAcceptBid(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(bidId, { status: enumData.BidStatus.DangCauHinhGoiThau.code, updatedBy: user.id })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.DuyetGoiThauTam.code
    bidHistory.save()

    //send mail nội bộ
    this.emailService.ThongBaoDaTaoGoiThau(bidId)

    return { message: 'Đã phê duyệt gói thầu tạm thành công.' }
  }

  /** MPOLeader duyệt thầu nhanh */
  async mpoleaderAcceptBidQuick(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionAcceptBid(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(bidId, {
      status: enumData.BidStatus.DangCauHinhGoiThau.code,
      statusTech: enumData.BidTechStatus.DaDuyet.code,
      statusTrade: enumData.BidTradeStatus.DaDuyet.code,
      statusPrice: enumData.BidPriceStatus.DangTao.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.DuyetThauNhanh.code
    bidHistory.save()

    //send mail nội bộ
    this.emailService.ThongBaoDaTaoGoiThau(bidId)

    return { message: 'Đã duyệt thầu nhanh thành công.' }
  }

  /** MPOLeader từ chối gói thầu tạm */
  async mpoleaderRejectBid(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionAcceptBid(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(bidId, {
      status: enumData.BidStatus.GoiThauTam.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.TuChoiGoiThauTam.code
    bidHistory.save()

    //send mail MPO
    this.emailService.GuiMPOKiemTraLaiGoiThauTam(bidId)

    return { message: 'Đã gửi yêu cầu kiểm tra lại gói thầu tạm thành công.' }
  }

  /** Chỉnh sửa thông tin gói thầu */
  async updateSettingRate(user: UserDto, data: BidUpdateSettingDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bid = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId, isDeleted: false } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    let isSendEmailInviteBidOld = bid.isSendEmailInviteBid
    let isSendEmailInviteBidNew = data.isSendEmailInviteBid

    const objPermission = await this.checkPermissionMpoEdit(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    data.startBidDate = new Date(data.submitEndDate)
    data.startBidDate.setDate(data.startBidDate.getDate() + 1)
    data.startBidDate.setHours(8, 0, 0, 0)
    if (data.startBidDate >= data.timeCheckTechDate) {
      throw new BadRequestException('Thời điểm mở thầu phải sớm hơn thời hạn đánh giá yêu cầu kỹ thuật, năng lực.')
    }

    bid.acceptEndDate = data.acceptEndDate
    bid.submitEndDate = data.submitEndDate
    bid.startBidDate = data.startBidDate
    bid.timeCheckTechDate = data.timeCheckTechDate
    bid.timeCheckPriceDate = data.timeCheckPriceDate
    bid.percentTech = data.percentTech
    bid.percentTrade = data.percentTrade
    bid.percentPrice = data.percentPrice
    bid.isAutoBid = data.isAutoBid
    if (!bid.hasSendEmailInviteBid) {
      bid.isSendEmailInviteBid = data.isSendEmailInviteBid
    }
    bid.updatedBy = user.id
    await bid.save()

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bid.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.SuaTaoGoiThauSauDuyet.code
    bidHistory.save()

    // nếu tích "Gửi thông báo mời thầu Doanh nghiệp qua email" khi chưa gửi mail mời thầu và gói thầu trang thái Đang mời thầu => gửi mail lại
    if (!isSendEmailInviteBidOld && isSendEmailInviteBidNew && !bid.hasSendEmailInviteBid && bid.status == enumData.BidStatus.DangNhanBaoGia.code) {
      this.emailService.GuiNccThongBaoMoiThau(user, data.id)
    }

    return { message: 'Chỉnh sửa thông tin chung của gói thầu thành công.' }
  }

  /** Kiểm tra quyền sửa template gói thầu, nếu đã xác nhận tham gia thì không được sửa nữa (trừ trường hợp hiệu chỉnh giá) */
  async checkPermissionMpoEditTemplate(user: UserDto, bidId: string) {
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId } })
    if (bid && bid.statusResetPrice == enumData.BidResetPriceStatus.DangTao.code) {
      return true
    }

    let result = true
    const lstBidSupplierStatus = [
      enumData.BidSupplierStatus.DaXacNhanThamGiaThau.code,
      enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
      enumData.BidSupplierStatus.DangDanhGia.code,
      enumData.BidSupplierStatus.DaDanhGia.code,
    ]

    const lstBidSupplier = await this.bidSupplierRepo.find({
      where: { bidId, status: In(lstBidSupplierStatus), companyId: user.companyId },
      select: { id: true },
    })

    if (lstBidSupplier.length > 0) {
      result = false
    }

    return result
  }

  //#endregion

  //#region bidTech

  /** Check quyền tạo thiết lập yêu cầu kỹ thuật cho gói thầu */
  async checkPermissionTechCreate(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const lstStatusCanEdit = [
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    const bid = await this.repo.findOne({
      where: [{ id: bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false }, { childs: { id: bidId, isDeleted: false } }],
    })
    if (bid) {
      if (lstStatusCanEdit.includes(bid.status)) {
        result = await this.bidEmployeeAccessRepo.isTech(user, bid.id)
        if (!result) {
          message = 'Bạn không có quyền thiết lập yêu cầu kỹ thuật cho gói thầu.'
        }
      } else {
        result = false
        message = 'Chỉ được phép thiết lập yêu cầu kỹ thuật khi chưa mở thầu.'
      }
    }

    return { hasPermission: result, message }
  }

  async creatingTech(user: UserDto, bidId: string) {
    await this.repo.update(bidId, {
      statusTech: enumData.BidTechStatus.DangTao.code,
      status: enumData.BidStatus.DangCauHinhGoiThau.code,
      updatedBy: user.id,
    })
  }

  /** Lấy thông tin thiết lập yêu cầu kỹ thuật của lĩnh vực mời thầu */
  async loadTech(user: UserDto, bidItemId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bid = await this.repo.findOne({ where: { id: bidItemId, companyId: user.companyId, isDeleted: false } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionTechCreate(user, bidItemId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidItemId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    let lstServiceTech = await this.serviceTechRepo.find({
      where: { serviceId: bid.serviceId, parentId: IsNull(), companyId: user.companyId, isDeleted: false },
      relations: { childs: { serviceTechListDetails: true }, serviceTechListDetails: true },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })

    // Tạo danh sách yêu cầu kỹ thuật cho gói thầu theo cấu hình kỹ thuật
    await this.repo.manager.transaction(async (manager) => {
      const bidTechRepo = manager.getRepository(BidTechEntity)
      const bidTechListDetailRepo = manager.getRepository(BidTechListDetailEntity)
      for (const a of lstServiceTech) {
        const item = new BidTechEntity()
        item.companyId = user.companyId
        item.createdBy = user.id
        item.bidId = bidItemId
        item.sort = a.sort
        item.name = a.name
        item.isRequired = a.isRequired
        item.type = a.type
        item.percent = a.percent
        item.percentRule = a.percentRule
        item.isCalUp = a.isCalUp
        item.percentDownRule = a.percentDownRule
        item.level = a.level
        item.description = a.description
        item.parentId = a.parentId
        item.scoreDLC = a.scoreDLC
        item.requiredMin = a.requiredMin
        item.isHighlight = a.isHighlight
        item.hightlightValue = a.hightlightValue
        const bidTechEntity = await bidTechRepo.save(item)

        const lstChild = (await a.childs).filter((c) => !c.isDeleted)

        if (lstChild && lstChild.length > 0) {
          for (const b of lstChild) {
            const itemChild = new BidTechEntity()
            itemChild.companyId = user.companyId
            itemChild.createdBy = user.id
            itemChild.bidId = bidItemId
            itemChild.sort = b.sort
            itemChild.name = b.name
            itemChild.isRequired = b.isRequired
            itemChild.type = b.type
            itemChild.percent = b.percent
            itemChild.percentRule = b.percentRule
            itemChild.isCalUp = b.isCalUp
            itemChild.percentDownRule = b.percentDownRule
            itemChild.level = b.level
            itemChild.description = b.description
            itemChild.parentId = bidTechEntity.id
            itemChild.scoreDLC = b.scoreDLC
            itemChild.requiredMin = b.requiredMin
            itemChild.isHighlight = b.isHighlight
            itemChild.hightlightValue = b.hightlightValue
            const bidTechChildEntity = await bidTechRepo.save(itemChild)

            const lstDataTypeList = (await b.serviceTechListDetails).filter((c) => !c.isDeleted)
            if (lstDataTypeList && lstDataTypeList.length > 0) {
              for (const c of lstDataTypeList) {
                const itemListDetail = new BidTechListDetailEntity()
                itemListDetail.companyId = user.companyId
                itemListDetail.createdBy = user.id
                itemListDetail.bidTechId = bidTechChildEntity.id
                itemListDetail.name = c.name
                itemListDetail.value = c.value
                await bidTechListDetailRepo.save(itemListDetail)
              }
            }
          }
        }

        const lstDataTypeList = (await a.serviceTechListDetails).filter((c) => !c.isDeleted)
        if (lstDataTypeList && lstDataTypeList.length > 0) {
          for (const c of lstDataTypeList) {
            const itemListDetail = new BidTechListDetailEntity()
            itemListDetail.companyId = user.companyId
            itemListDetail.createdBy = user.id
            itemListDetail.bidTechId = bidTechEntity.id
            itemListDetail.name = c.name
            itemListDetail.value = c.value
            await bidTechListDetailRepo.save(itemListDetail)
          }
        }
      }
    })

    // cập nhật statusTech => DangTao
    await this.creatingTech(user, bid.parentId || bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái kỹ thuật cho gói thầu thất bại.')
    })
  }

  /** Lưu template kỹ thuật cho gói thầu => Chuyển TechLead duyệt */
  async createTech(user: UserDto, bidId: string, data: { noteTech?: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionTechCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(bidId, {
      statusTech: enumData.BidTechStatus.DaTao.code,
      noteTech: data.noteTech,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.TaoKyThuat.code
    bidHistory.save()

    // gửi email
    await this.emailService.GuiTechLeadDuyetKyThuat(bidId)
    return { message: 'Thiết lập yêu cầu kỹ thuật cho gói thầu thành công' }
  }

  /** Lấy thiết lập yêu cầu kỹ thuật của gói thầu */
  async getTech(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res = await this.repo.getBid1(user, bidId)
    for (const item of res.listItem) {
      item.listTech = await this.bidTechRepo.getTech(user, item.id)

      item.sumPercent = 0
      for (const data1 of item.listTech) {
        if (data1.percent > 0) item.sumPercent += data1.percent

        const lstChild = data1.__childs__.filter((c) => c.type === enumData.DataType.List.code || c.type === enumData.DataType.Number.code)
        if (lstChild.length > 0) {
          data1.sumPercent = 0
          for (const child of lstChild) {
            if (child.percent > 0) data1.sumPercent += child.percent
          }
        }
      }
    }

    return res
  }

  /** Check quyền duyệt thiết lập yêu cầu kỹ thuật của gói thầu */
  async checkPermissionTechAccept(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const bid = await this.repo.findOne({ where: { id: bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false } })
    if (bid) {
      if (bid.status === enumData.BidStatus.DangCauHinhGoiThau.code && bid.statusTech === enumData.BidTechStatus.DaTao.code) {
        result = await this.bidEmployeeAccessRepo.isTechLeader(user, bid.id)
        if (!result) {
          message = 'Bạn không có quyền xét duyệt thiết lập kỹ thuật cho gói thầu.'
        }
      } else {
        result = false
        message = 'Gói thầu đã được xét duyệt thiết lập kỹ thuật.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Duyệt thiết lập yêu cầu kỹ thuật của gói thầu */
  async techAccept(user: UserDto, bidId: string, data: { noteTechLeader?: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionTechAccept(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId } })
    if (bid) {
      bid.statusTech = enumData.BidTechStatus.DaDuyet.code
      bid.noteTechLeader = data.noteTechLeader
      if (
        (bid.statusTrade === enumData.BidTradeStatus.DaTao.code || bid.statusTrade === enumData.BidTradeStatus.DaDuyet.code) &&
        (bid.statusPrice === enumData.BidPriceStatus.DaTao.code || bid.statusPrice === enumData.BidPriceStatus.DaDuyet.code)
      ) {
        bid.status = enumData.BidStatus.DangChonNCC.code
        // chưa chọn => đang chọn, để chọn Doanh nghiệp
        if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.ChuaChon.code) {
          bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DangChon.code
        }
        // đã duyệt => đã chọn, để duyệt lại
        if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaDuyet.code) {
          bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DaChon.code
        }
      }
      bid.updatedBy = user.id
      await this.repo.save(bid)
    }

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.DuyetTaoKyThuat.code
    bidHistory.save()

    // gửi email
    await this.emailService.ThongBaoDaDuyetKyThuat(bidId)

    return { message: 'Duyệt thiết lập yêu cầu kỹ thuật của gói thầu thành công.' }
  }

  /** Từ chối thiết lập yêu cầu kỹ thuật của gói thầu */
  async techReject(user: UserDto, bidId: string, data: { noteTechLeader?: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionTechAccept(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(bidId, {
      statusTech: enumData.BidTechStatus.TuChoi.code,
      noteTechLeader: data.noteTechLeader,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.TuChoiTaoKyThuat.code
    bidHistory.save()

    // gửi email
    this.emailService.GuiTechTuChoiKyThuat(bidId)

    return { message: 'Từ chối thiết lập yêu cầu kỹ thuật của gói thầu thành công.' }
  }

  /** Lấy data cbb tiêu chí cấp 1 */
  async techGetData(user: UserDto, bidId: string) {
    const result = await this.bidTechRepo.find({ where: { bidId, level: 1, companyId: user.companyId, isDeleted: false } })
    return result
  }

  async getItem(user: UserDto, bidId: string) {
    const bid = await this.repo.findOne({ where: { id: bidId, isDeleted: false } })
    const result: any = await this.bidItemRepo.find({ where: { bidId: bid.parentId, isDeleted: false }, relations: { item: true } })
    for (const item of result) {
      item.id = item.__item__.id
      item.name = item.__item__.name
    }
    return result
  }

  async techCreateData(user: UserDto, data: BidTechCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.repo.findOne({ where: { id: data.bidId, companyId: user.companyId, isDeleted: false } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionTechCreate(user, data.bidId)
    // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Tạo thêm ngoài lấy từ template
    await this.repo.manager.transaction(async (manager) => {
      const bidTechRepo = manager.getRepository(BidTechEntity)

      const item = new BidTechEntity()
      item.companyId = user.companyId
      item.createdBy = user.id
      item.bidId = data.bidId
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      // item.itemId = data.itemId
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.percentRule = data.percentRule
      item.isCalUp = data.isCalUp
      item.percentDownRule = data.percentDownRule
      item.level = data.level
      item.description = data.description
      item.parentId = data.parentId
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      item.isHighlight = data.isHighlight
      item.hightlightValue = data.hightlightValue
      await bidTechRepo.save(item)
    })

    // cập nhật statusTech => DangTao
    await this.creatingTech(user, bid.parentId || bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái kỹ thuật cho gói thầu thất bại.')
    })
  }

  async techUpdateData(user: UserDto, data: BidTechUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.repo.findOne({ where: { id: data.bidId, companyId: user.companyId, isDeleted: false } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionTechCreate(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Sửa
    await this.repo.manager.transaction(async (manager) => {
      const bidTechRepo = manager.getRepository(BidTechEntity)

      const item = await bidTechRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!item) throw new Error(ERROR_NOT_FOUND_DATA)
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.percentRule = data.percentRule
      item.isCalUp = data.isCalUp
      item.percentDownRule = data.percentDownRule
      item.level = data.level
      item.description = data.description
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      item.isHighlight = data.isHighlight
      item.hightlightValue = data.hightlightValue
      item.updatedBy = user.id
      await bidTechRepo.save(item)
    })

    // cập nhật statusTech => DangTao
    await this.creatingTech(user, bid.parentId || bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái kỹ thuật cho gói thầu thất bại.')
    })
  }

  /** Xóa */
  async techDeleteData(user: UserDto, bidTechId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bidTech = await this.bidTechRepo.findOne({ where: { id: bidTechId, companyId: user.companyId } })
    if (!bidTech) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidTech.bid
    const objPermission = await this.checkPermissionTechCreate(user, bidTech.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidTech.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Xóa
    await this.repo.manager.transaction(async (manager) => {
      const bidTechRepo = manager.getRepository(BidTechEntity)
      const bidTechListDetailRepo = manager.getRepository(BidTechListDetailEntity)

      const bidTech = await bidTechRepo.findOne({ where: { id: bidTechId, companyId: user.companyId } })
      if (!bidTech) throw new Error(ERROR_NOT_FOUND_DATA)

      const lstChild = await bidTech.childs
      for (const bidTechChild of lstChild) {
        await bidTechListDetailRepo.delete({ bidTechId: bidTechChild.id })
      }
      await bidTechListDetailRepo.delete({ bidTechId })
      await bidTechRepo.delete({ parentId: bidTechId })
      await bidTechRepo.delete(bidTechId)

      return { message: DELETE_SUCCESS }
    })

    // cập nhật statusTech => DangTao
    await this.creatingTech(user, bid.parentId || bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái kỹ thuật cho gói thầu thất bại.')
    })
  }

  /** Xoá tất cả */
  async techDeleteAllData(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionTechCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: bidId, isDeleted: false } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Xoá tất cả
    await this.repo.manager.transaction(async (manager) => {
      const bidTechRepo = manager.getRepository(BidTechEntity)
      const bidTechListDetailRepo = manager.getRepository(BidTechListDetailEntity)

      const lstBidTech = await bidTechRepo.find({ where: { bidId, companyId: user.companyId } })
      for (const bidTech of lstBidTech) {
        const lstBidTechChild = await bidTech.childs
        for (const bidTechChild of lstBidTechChild) {
          // xoá lst detail
          await bidTechListDetailRepo.delete({ bidTechId: bidTechChild.id })
        }
        // xoá lst
        await bidTechListDetailRepo.delete({ bidTechId: bidTech.id })
        // xoá con
        await bidTechRepo.delete({ parentId: bidTech.id })
        // xoá
        await bidTechRepo.delete(bidTech.id)
      }
    })

    // cập nhật statusTech => DangTao
    await this.creatingTech(user, bid.parentId || bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái kỹ thuật cho gói thầu thất bại.')
    })
  }

  /** Import excel kỹ thuật */
  public async tech_import(user: UserDto, bidId: string, data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    await this.techDeleteAllData(user, bidId)
    // import excel kỹ thuật
    await this.repo.manager.transaction(async (manager) => {
      const bidTechRepo = manager.getRepository(BidTechEntity)
      const bidTechListDetailRepo = manager.getRepository(BidTechListDetailEntity)

      // add lv1
      var lstDataLv1 = data.lstDataTable1.filter((c: any) => c.level == 1)
      for (const item of lstDataLv1) {
        const objBidTechNew = new BidTechEntity()
        objBidTechNew.companyId = user.companyId
        objBidTechNew.createdBy = user.id
        objBidTechNew.bidId = bidId
        objBidTechNew.level = 1
        objBidTechNew.sort = item.sort || 0
        objBidTechNew.name = item.name
        objBidTechNew.percent = item.percent
        objBidTechNew.percentRule = item.percentRule
        objBidTechNew.requiredMin = item.requiredMin
        objBidTechNew.type = item.type
        objBidTechNew.isRequired = item.isRequired
        objBidTechNew.isHighlight = item.isHighlight
        objBidTechNew.hightlightValue = item.hightlightValue
        objBidTechNew.isCalUp = item.isCalUp
        objBidTechNew.percentDownRule = item.percentDownRule

        const objBidTech = await bidTechRepo.save(objBidTechNew)
        item.id = objBidTech.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new BidTechListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.bidTechId = item.id
            detailNew.name = detail.nameList
            detailNew.value = detail.valueList
            await bidTechListDetailRepo.save(detailNew)
          }
        }
      }

      // add lv2
      var lstDataLv2 = data.lstDataTable1.filter((c: any) => c.level == 2)
      for (const item of lstDataLv2) {
        const objBidTechNew = new BidTechEntity()
        objBidTechNew.companyId = user.companyId
        objBidTechNew.createdBy = user.id
        objBidTechNew.bidId = bidId
        objBidTechNew.level = 2
        objBidTechNew.sort = item.sort || 0
        objBidTechNew.name = item.name
        objBidTechNew.percent = item.percent
        objBidTechNew.percentRule = item.percentRule
        objBidTechNew.requiredMin = item.requiredMin
        objBidTechNew.type = item.type
        objBidTechNew.isRequired = item.isRequired
        objBidTechNew.isHighlight = item.isHighlight
        objBidTechNew.hightlightValue = item.hightlightValue
        objBidTechNew.isCalUp = item.isCalUp
        objBidTechNew.percentDownRule = item.percentDownRule
        const parent = lstDataLv1.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objBidTechNew.parentId = parent.id

        const objBidTech = await bidTechRepo.save(objBidTechNew)
        item.id = objBidTech.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new BidTechListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.bidTechId = item.id
            detailNew.name = detail.nameList
            detailNew.value = detail.valueList
            await bidTechListDetailRepo.save(detailNew)
          }
        }
      }
    })

    return { message: IMPORT_SUCCESS }
  }

  public async bidTechListDetail_list(user: UserDto, bidTechId: string) {
    return await this.repo.manager.getRepository(BidTechListDetailEntity).find({
      where: { bidTechId, companyId: user.companyId },
      order: { value: 'DESC' },
    })
  }

  public async bidTechListDetail_create_data(user: UserDto, data: { bidTechId: string; name: string; value: number }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidTech = await this.bidTechRepo.findOne({ where: { id: data.bidTechId, companyId: user.companyId } })
    if (!bidTech) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidTech.bid
    const objPermission = await this.checkPermissionTechCreate(user, bidTech.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidTech.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusTech => DangTao
    await this.creatingTech(user, bid.parentId || bid.id)

    const entity = new BidTechListDetailEntity()
    entity.companyId = user.companyId
    entity.createdBy = user.id
    entity.name = data.name
    entity.value = data.value
    entity.bidTechId = data.bidTechId
    await entity.save()

    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async bidTechListDetail_update_data(user: UserDto, data: { id: string; bidTechId: string; name: string; value: number }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidTech = await this.bidTechRepo.findOne({ where: { id: data.bidTechId, companyId: user.companyId } })
    if (!bidTech) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidTech.bid
    const objPermission = await this.checkPermissionTechCreate(user, bidTech.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidTech.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusTech => DangTao
    await this.creatingTech(user, bid.parentId || bid.id)

    const entity = await this.repo.manager.getRepository(BidTechListDetailEntity).findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.value = data.value
    entity.updatedBy = user.id
    await entity.save()

    return { id: entity.id, message: UPDATE_SUCCESS }
  }

  public async bidTechListDetail_delete_data(user: UserDto, id: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidTechListDetail = await this.repo.manager.getRepository(BidTechListDetailEntity).findOne({ where: { id, companyId: user.companyId } })
    if (!bidTechListDetail) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bidTech = await bidTechListDetail.bidTech
    const bid = await bidTech.bid
    const objPermission = await this.checkPermissionTechCreate(user, bidTech.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidTech.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusTech => DangTao
    await this.creatingTech(user, bid.parentId || bid.id)

    await this.repo.manager.getRepository(BidTechListDetailEntity).delete(id)

    return { message: DELETE_SUCCESS }
  }

  //#endregion

  //#region  bidTrade

  /** Check quyền tạo thiết lập điều kiện thương mại cho gói thầu */
  async checkPermissionTradeCreate(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const lstStatusCanEdit = [
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    const bid = await this.repo.findOne({
      where: [{ id: bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false }, { childs: { id: bidId, isDeleted: false } }],
    })
    if (bid) {
      if (lstStatusCanEdit.includes(bid.status)) {
        result = await this.bidEmployeeAccessRepo.isMPO(user, bid.id)
        if (!result) message = 'Bạn không có quyền thiết lập điều kiện thương mại cho gói thầu.'
      } else {
        result = false
        message = 'Chỉ được phép thiết lập điều kiện thương mại khi chưa mở thầu.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Update statusTrade => DangTao */
  async creatingTrade(user: UserDto, bidId: string) {
    await this.repo.update(bidId, {
      statusTrade: enumData.BidTradeStatus.DangTao.code,
      status: enumData.BidStatus.DangCauHinhGoiThau.code,
      updatedBy: user.id,
    })
  }

  /** Lấy thông tin thiết lập điều kiện thương mại của lĩnh vực mời thầu */
  async loadTrade(user: UserDto, bidItemId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bid = await this.repo.findOne({ where: { id: bidItemId, companyId: user.companyId, isDeleted: false } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionTradeCreate(user, bidItemId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidItemId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    let lstServiceTrade = await this.serviceTradeRepo.find({
      where: { serviceId: bid.serviceId, parentId: IsNull(), companyId: user.companyId, isDeleted: false },
      relations: { childs: { serviceTradeListDetails: true }, serviceTradeListDetails: true },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })

    // Tạo danh sách điều kiện thương mại cho gói thầu
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(BidTradeEntity)
      const bidTradeListDetailRepo = manager.getRepository(BidTradeListDetailEntity)
      for (const a of lstServiceTrade) {
        const item = new BidTradeEntity()
        item.companyId = user.companyId
        item.createdBy = user.id
        item.bidId = bidItemId
        item.sort = a.sort
        item.name = a.name
        item.isRequired = a.isRequired
        item.type = a.type
        item.percent = a.percent
        item.percentRule = a.percentRule
        item.isCalUp = a.isCalUp
        item.percentDownRule = a.percentDownRule
        item.level = a.level
        item.description = a.description
        item.parentId = a.parentId
        item.scoreDLC = a.scoreDLC
        item.requiredMin = a.requiredMin
        const bidTradeEntity = await bidTradeRepo.save(item)

        const lstChild = (await a.childs).filter((c) => !c.isDeleted)
        if (lstChild && lstChild.length > 0) {
          for (const b of lstChild) {
            const itemChild = new BidTradeEntity()
            itemChild.companyId = user.companyId
            itemChild.createdBy = user.id
            itemChild.bidId = bidItemId
            itemChild.sort = b.sort
            itemChild.name = b.name
            itemChild.isRequired = b.isRequired
            itemChild.type = b.type
            itemChild.percent = b.percent
            itemChild.percentRule = b.percentRule
            itemChild.isCalUp = b.isCalUp
            itemChild.percentDownRule = b.percentDownRule
            itemChild.level = b.level
            itemChild.description = b.description
            itemChild.parentId = bidTradeEntity.id
            itemChild.scoreDLC = b.scoreDLC
            itemChild.requiredMin = b.requiredMin
            const bidTradeChildEntity = await bidTradeRepo.save(itemChild)

            const lstDataTypeList = (await b.serviceTradeListDetails).filter((c) => !c.isDeleted)
            if (lstDataTypeList && lstDataTypeList.length > 0) {
              for (const c of lstDataTypeList) {
                const itemListDetail = new BidTradeListDetailEntity()
                itemListDetail.companyId = user.companyId
                itemListDetail.createdBy = user.id
                itemListDetail.bidTradeId = bidTradeChildEntity.id
                itemListDetail.name = c.name
                itemListDetail.value = c.value
                await bidTradeListDetailRepo.save(itemListDetail)
              }
            }
          }
        }

        const lstDataTypeList = (await a.serviceTradeListDetails).filter((c) => !c.isDeleted)
        for (const c of lstDataTypeList) {
          const itemListDetail = new BidTradeListDetailEntity()
          itemListDetail.companyId = user.companyId
          itemListDetail.createdBy = user.id
          itemListDetail.bidTradeId = bidTradeEntity.id
          itemListDetail.name = c.name
          itemListDetail.value = c.value
          await bidTradeListDetailRepo.save(itemListDetail)
        }
      }
    })

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.parentId || bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái điều kiện thương mại cho gói thầu thất bại.')
    })
  }

  /** Tạo thiết lập điều kiện thương mại cho gói thầu */
  async createTrade(user: UserDto, bidId: string, data: { noteTrade: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionTradeCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId, isDeleted: false } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    bid.statusTrade = enumData.BidTradeStatus.DaTao.code
    bid.noteTrade = data.noteTrade
    if (
      bid.statusTech === enumData.BidTechStatus.DaDuyet.code &&
      (bid.statusPrice === enumData.BidPriceStatus.DaTao.code || bid.statusPrice === enumData.BidPriceStatus.DaDuyet.code)
    ) {
      bid.status = enumData.BidStatus.DangChonNCC.code
      // chưa chọn => đang chọn, để chọn Doanh nghiệp
      if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.ChuaChon.code) {
        bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DangChon.code
      }
      // đã duyệt => đã chọn, để duyệt lại
      if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaDuyet.code) {
        bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DaChon.code
      }
    }
    bid.updatedBy = user.id
    await this.repo.save(bid)

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.TaoThuongMai.code
    bidHistory.save()

    return { message: 'Thiết lập điều kiện thương mại cho gói thầu thành công.' }
  }

  /** Lấy thiết lập điều kiện thương mại của gói thầu */
  async getTrade(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res = await this.repo.getBid1(user, bidId)
    for (const item of res.listItem) {
      item.listTrade = await this.bidTradeRepo.getTrade(user, item.id)

      item.sumPercent = 0
      for (const data1 of item.listTrade) {
        if (data1.percent > 0) item.sumPercent += data1.percent

        const lstChild = data1.__childs__.filter((c) => c.type === enumData.DataType.List.code || c.type === enumData.DataType.Number.code)
        if (lstChild.length > 0) {
          data1.sumPercent = 0
          for (const child of lstChild) {
            if (child.percent > 0) data1.sumPercent += child.percent
          }
        }
      }
    }

    return res
  }

  /** Lấy data cbb tiêu chí cấp 1 */
  async tradeGetData(user: UserDto, bidId: string) {
    return await this.bidTradeRepo.find({ where: { bidId, level: 1, companyId: user.companyId, isDeleted: false } })
  }

  /** Tạo thêm ngoài lấy từ template */
  async tradeCreateData(user: UserDto, data: BidTradeCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.repo.findOne({ where: { id: data.bidId, companyId: user.companyId, isDeleted: false } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionTradeCreate(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Tạo thêm ngoài lấy từ template
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(BidTradeEntity)

      const item = new BidTradeEntity()
      item.companyId = user.companyId
      item.createdBy = user.id
      item.bidId = data.bidId
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.percentRule = data.percentRule
      item.isCalUp = data.isCalUp
      item.percentDownRule = data.percentDownRule
      item.level = data.level
      item.description = data.description
      item.parentId = data.parentId
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      await bidTradeRepo.save(item)
    })

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.parentId || bid.id)
  }

  /** Cập nhật */
  async tradeUpdateData(user: UserDto, data: BidTradeUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.repo.findOne({ where: { id: data.bidId, companyId: user.companyId, isDeleted: false } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionTradeCreate(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Cập nhật
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(BidTradeEntity)

      const item = await bidTradeRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!item) throw new Error(ERROR_NOT_FOUND_DATA)
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.percentRule = data.percentRule
      item.isCalUp = data.isCalUp
      item.percentDownRule = data.percentDownRule
      item.level = data.level
      item.description = data.description
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      item.updatedBy = user.id
      await bidTradeRepo.save(item)
    })

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.parentId || bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái điều kiện thương mại cho gói thầu thất bại.')
    })
  }

  /** Xóa */
  async tradeDeleteData(user: UserDto, bidTradeId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bidTrade = await this.bidTradeRepo.findOne({ where: { id: bidTradeId, companyId: user.companyId } })
    if (!bidTrade) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidTrade.bid
    const objPermission = await this.checkPermissionTradeCreate(user, bidTrade.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidTrade.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Xóa
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(BidTradeEntity)
      const bidTradeListDetailRepo = manager.getRepository(BidTradeListDetailEntity)

      const bidTrade = await bidTradeRepo.findOne({ where: { id: bidTradeId, companyId: user.companyId } })
      if (!bidTrade) throw new Error(ERROR_NOT_FOUND_DATA)

      const lstChild = await bidTrade.childs
      for (const bidTradeChild of lstChild) {
        await bidTradeListDetailRepo.delete({ bidTradeId: bidTradeChild.id })
      }
      await bidTradeListDetailRepo.delete({ bidTradeId })
      await bidTradeRepo.delete({ parentId: bidTradeId })
      await bidTradeRepo.delete(bidTradeId)

      return { message: DELETE_SUCCESS }
    })

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.parentId || bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái điều kiện thương mại cho gói thầu thất bại.')
    })
  }

  /** Xoá tất cả */
  async tradeDeleteAllData(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionTradeCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId, isDeleted: false } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const flag = await this.checkPermissionMpoEditTemplate(user, bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Xoá tất cả
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(BidTradeEntity)
      const bidTradeListDetailRepo = manager.getRepository(BidTradeListDetailEntity)

      const lstBidTrade = await bidTradeRepo.find({ where: { bidId, companyId: user.companyId } })
      for (const bidTrade of lstBidTrade) {
        const lstBidTradeChild = await bidTrade.childs
        for (const bidTradeChild of lstBidTradeChild) {
          // xoá lst detail
          await bidTradeListDetailRepo.delete({ bidTradeId: bidTradeChild.id })
        }
        // xoá lst
        await bidTradeListDetailRepo.delete({ bidTradeId: bidTrade.id })
        // xoá con
        await bidTradeRepo.delete({ parentId: bidTrade.id })
        // xoá
        await bidTradeRepo.delete(bidTrade.id)
      }

      return { message: DELETE_SUCCESS }
    })

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.parentId || bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái điều kiện thương mại cho gói thầu thất bại.')
    })
  }

  /** Import excel chào giá */
  public async trade_import(user: UserDto, bidId: string, data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    await this.tradeDeleteAllData(user, bidId)
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(BidTradeEntity)
      const bidTradeListDetailRepo = manager.getRepository(BidTradeListDetailEntity)

      // add lv1
      var lstDataLv1 = data.lstDataTable1.filter((c: any) => c.level == 1)
      for (const item of lstDataLv1) {
        const objBidTradeNew = new BidTradeEntity()
        objBidTradeNew.companyId = user.companyId
        objBidTradeNew.createdBy = user.id
        objBidTradeNew.bidId = bidId
        objBidTradeNew.level = 1
        objBidTradeNew.sort = item.sort || 0
        objBidTradeNew.name = item.name
        objBidTradeNew.percent = item.percent
        objBidTradeNew.percentRule = item.percentRule
        objBidTradeNew.type = item.type
        objBidTradeNew.isRequired = item.isRequired
        objBidTradeNew.isCalUp = item.isCalUp
        objBidTradeNew.percentDownRule = item.percentDownRule

        const objBidTrade = await bidTradeRepo.save(objBidTradeNew)
        item.id = objBidTrade.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new BidTradeListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.bidTradeId = item.id
            detailNew.name = detail.nameList
            detailNew.value = detail.valueList
            await bidTradeListDetailRepo.save(detailNew)
          }
        }
      }

      // add lv2
      var lstDataLv2 = data.lstDataTable1.filter((c: any) => c.level == 2)
      for (const item of lstDataLv2) {
        const objBidTradeNew = new BidTradeEntity()
        objBidTradeNew.companyId = user.companyId
        objBidTradeNew.createdBy = user.id
        objBidTradeNew.bidId = bidId
        objBidTradeNew.level = 2
        objBidTradeNew.sort = item.sort || 0
        objBidTradeNew.name = item.name
        objBidTradeNew.percent = item.percent
        objBidTradeNew.percentRule = item.percentRule
        objBidTradeNew.type = item.type
        objBidTradeNew.isRequired = item.isRequired
        objBidTradeNew.isCalUp = item.isCalUp
        objBidTradeNew.percentDownRule = item.percentDownRule

        const parent = lstDataLv1.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objBidTradeNew.parentId = parent.id

        const objBidTrade = await bidTradeRepo.save(objBidTradeNew)
        item.id = objBidTrade.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new BidTradeListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.bidTradeId = item.id
            detailNew.name = detail.nameList
            detailNew.value = detail.valueList
            await bidTradeListDetailRepo.save(detailNew)
          }
        }
      }
    })

    return { message: IMPORT_SUCCESS }
  }

  public async bidTradeListDetail_list(user: UserDto, bidTradeId: string) {
    return await this.repo.manager.getRepository(BidTradeListDetailEntity).find({
      where: { bidTradeId, companyId: user.companyId },
      order: { value: 'DESC' },
    })
  }

  public async bidTradeListDetail_create_data(user: UserDto, data: { bidTradeId: string; name: string; value: number }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidTrade = await this.bidTradeRepo.findOne({ where: { id: data.bidTradeId, companyId: user.companyId } })
    if (!bidTrade) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidTrade.bid
    const objPermission = await this.checkPermissionTradeCreate(user, bidTrade.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidTrade.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.parentId || bid.id)

    const entity = new BidTradeListDetailEntity()
    entity.companyId = user.companyId
    entity.createdBy = user.id
    entity.name = data.name
    entity.value = data.value
    entity.bidTradeId = data.bidTradeId
    await entity.save()

    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async bidTradeListDetail_update_data(user: UserDto, data: { id: string; bidTradeId: string; name: string; value: number }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidTrade = await this.bidTradeRepo.findOne({ where: { id: data.bidTradeId, companyId: user.companyId } })
    if (!bidTrade) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidTrade.bid
    const objPermission = await this.checkPermissionTradeCreate(user, bidTrade.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidTrade.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.parentId || bid.id)

    const entity = await this.repo.manager.getRepository(BidTradeListDetailEntity).findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    entity.name = data.name
    entity.value = data.value
    entity.updatedBy = user.id
    await entity.save()

    return { id: entity.id, message: UPDATE_SUCCESS }
  }

  public async bidTradeListDetail_delete_data(user: UserDto, id: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidTradeListDetail = await this.repo.manager.getRepository(BidTradeListDetailEntity).findOne({ where: { id, companyId: user.companyId } })
    if (!bidTradeListDetail) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bidTrade = await bidTradeListDetail.bidTrade
    const bid = await bidTrade.bid
    const objPermission = await this.checkPermissionTradeCreate(user, bidTrade.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidTrade.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.parentId || bid.id)

    await this.repo.manager.getRepository(BidTradeListDetailEntity).delete(id)

    return { message: DELETE_SUCCESS }
  }

  //#endregion

  //#region bidPrice

  /** Lấy các hạng mục giá */
  public async price_find(user: UserDto, data: { bidId: string }) {
    return await this.bidPriceRepo.find({
      where: { bidId: data.bidId, level: 1, companyId: user.companyId, isDeleted: false },
      relations: { childs: true },
    })
  }

  /** Check quyền tạo chào giá cho gói thầu */
  async checkPermissionPriceCreate(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const lstStatusCanEdit = [
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    const bid = await this.repo.findOne({
      where: [
        { id: bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false },
        { companyId: user.companyId, childs: { id: bidId, isDeleted: false } },
      ],
    })
    if (bid) {
      if (lstStatusCanEdit.includes(bid.status) || bid.statusResetPrice == enumData.BidResetPriceStatus.DangTao.code) {
        result = await this.bidEmployeeAccessRepo.isMPO(user, bid.id)
        if (!result) {
          message = 'Bạn không có quyền thiết lập các hạng mục chào giá cho gói thầu.'
        }
      } else {
        result = false
        message = 'Chỉ được phép thiết lập các hạng mục chào giá khi chưa mở thầu.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Update statusPrice => DangTao */
  async creatingPrice(user: UserDto, bidId: string) {
    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (!bid) return
    if (bid.statusResetPrice == enumData.BidResetPriceStatus.DangTao.code) return

    await this.repo.update(bidId, {
      statusPrice: enumData.BidPriceStatus.DangTao.code,
      status: enumData.BidStatus.DangCauHinhGoiThau.code,
      updatedBy: user.id,
    })
  }

  /** Lấy bảng chào giá của lĩnh vực mời thầu */
  async loadPrice(user: UserDto, bidItemId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.repo.findOne({ where: { id: bidItemId, companyId: user.companyId } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, bidItemId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidItemId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    const lstServicePrice = await this.servicePriceRepo.find({
      where: { serviceId: bid.serviceId, parentId: IsNull(), companyId: user.companyId, isDeleted: false },
      order: { sort: 'ASC', createdAt: 'ASC' },
    })

    const lstServicePriceCol = await this.repo.manager.getRepository(ServicePriceColEntity).find({
      where: { serviceId: bid.serviceId, companyId: user.companyId, isDeleted: false },
      order: { sort: 'ASC', createdAt: 'ASC' },
    })

    // Tạo danh sách yêu cầu giá cho gói thầu
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(BidPriceEntity)
      const bidPriceListDetailRepo = manager.getRepository(BidPriceListDetailEntity)
      const bidPriceColRepo = manager.getRepository(BidPriceColEntity)
      const bidPriceColValueRepo = manager.getRepository(BidPriceColValueEntity)

      const lstCol = []
      for (const col of lstServicePriceCol) {
        const item = new BidPriceColEntity()
        item.companyId = user.companyId
        item.createdBy = user.id
        item.bidId = bidItemId
        item.code = col.code
        item.name = col.name
        item.fomular = col.fomular
        item.type = col.type
        item.colType = col.colType
        item.isRequired = col.isRequired
        item.sort = col.sort
        const bidPriceColEntity = await bidPriceColRepo.save(item)
        const temp = { ...col, bidPriceColId: bidPriceColEntity.id }
        lstCol.push(temp)
      }

      for (const servicePrice1 of lstServicePrice) {
        //#region Tạo lv1
        const itemLv1 = new BidPriceEntity()
        itemLv1.companyId = user.companyId
        itemLv1.createdBy = user.id
        itemLv1.bidId = bidItemId
        itemLv1.sort = servicePrice1.sort
        itemLv1.name = servicePrice1.name
        itemLv1.isRequired = servicePrice1.isRequired
        itemLv1.type = servicePrice1.type
        itemLv1.percent = servicePrice1.percent
        itemLv1.level = servicePrice1.level
        itemLv1.description = servicePrice1.description
        itemLv1.parentId = servicePrice1.parentId
        itemLv1.scoreDLC = servicePrice1.scoreDLC
        itemLv1.requiredMin = servicePrice1.requiredMin
        itemLv1.unit = servicePrice1.unit
        itemLv1.currency = servicePrice1.currency
        itemLv1.isSetup = servicePrice1.isSetup
        itemLv1.isTemplate = servicePrice1.isTemplate
        itemLv1.number = servicePrice1.number
        itemLv1.servicePriceId = servicePrice1.id
        const bidPriceLv1 = await bidPriceRepo.save(itemLv1)
        //#endregion

        // Tạo thông tin bổ sung lv1
        const lstDetailLv1 = (await servicePrice1.servicePriceListDetails).filter((c) => !c.isDeleted)
        for (const detailLv1 of lstDetailLv1) {
          const itemDetail1 = new BidPriceListDetailEntity()
          itemDetail1.companyId = user.companyId
          itemDetail1.createdBy = user.id
          itemDetail1.bidPriceId = bidPriceLv1.id
          itemDetail1.name = detailLv1.name
          itemDetail1.type = detailLv1.type
          itemDetail1.value = detailLv1.value
          await bidPriceListDetailRepo.save(itemDetail1)
        }

        // Tạo giá trị cho các cột động lv1
        const lstValueLv1 = await servicePrice1.servicePriceColValues
        for (const itemValueLv1 of lstValueLv1) {
          const objCol = lstCol.find((c) => c.id === itemValueLv1.servicePriceColId)
          if (objCol) {
            const itemValueNew1 = new BidPriceColValueEntity()
            itemValueNew1.companyId = user.companyId
            itemValueNew1.createdBy = user.id
            itemValueNew1.bidPriceId = bidPriceLv1.id
            itemValueNew1.bidPriceColId = objCol.bidPriceColId
            itemValueNew1.value = itemValueLv1.value
            await bidPriceColValueRepo.save(itemValueNew1)
          }
        }

        // Tạo thông tin lv2
        const lstServicePrice2 = (await servicePrice1.childs).filter((c) => !c.isDeleted)
        if (lstServicePrice2.length > 0) {
          for (const servicePrice2 of lstServicePrice2) {
            //#region Tạo lv2
            const itemLv2 = new BidPriceEntity()
            itemLv2.companyId = user.companyId
            itemLv2.createdBy = user.id
            itemLv2.bidId = bidItemId
            itemLv2.sort = servicePrice2.sort
            itemLv2.name = servicePrice2.name
            itemLv2.isRequired = servicePrice2.isRequired
            itemLv2.type = servicePrice2.type
            itemLv2.percent = servicePrice2.percent
            itemLv2.level = servicePrice2.level
            itemLv2.description = servicePrice2.description
            itemLv2.parentId = bidPriceLv1.id
            itemLv2.scoreDLC = servicePrice2.scoreDLC
            itemLv2.requiredMin = servicePrice2.requiredMin
            itemLv2.unit = servicePrice2.unit
            itemLv2.currency = servicePrice2.currency
            itemLv2.number = servicePrice2.number
            itemLv2.isSetup = servicePrice2.isSetup
            itemLv2.servicePriceId = servicePrice2.id
            const bidPriceLv2 = await bidPriceRepo.save(itemLv2)
            //#endregion

            // Tạo thông tin bổ sung lv2
            const lstDetailLv2 = (await servicePrice2.servicePriceListDetails).filter((c) => !c.isDeleted)
            for (const detailLv2 of lstDetailLv2) {
              const itemDetail2 = new BidPriceListDetailEntity()
              itemDetail2.companyId = user.companyId
              itemDetail2.createdBy = user.id
              itemDetail2.bidPriceId = bidPriceLv2.id
              itemDetail2.name = detailLv2.name
              itemDetail2.type = detailLv2.type
              itemDetail2.value = detailLv2.value
              await bidPriceListDetailRepo.save(itemDetail2)
            }

            // Tạo giá trị cho các cột động lv2
            const lstValueLv2 = await servicePrice2.servicePriceColValues
            for (const itemValueLv2 of lstValueLv2) {
              const objCol = lstCol.find((c) => c.id === itemValueLv2.servicePriceColId)
              if (objCol) {
                const itemValueNew2 = new BidPriceColValueEntity()
                itemValueNew2.companyId = user.companyId
                itemValueNew2.createdBy = user.id
                itemValueNew2.bidPriceId = bidPriceLv2.id
                itemValueNew2.bidPriceColId = objCol.bidPriceColId
                itemValueNew2.value = itemValueLv2.value
                await bidPriceColValueRepo.save(itemValueNew2)
              }
            }

            // Tạo thông tin lv3
            const lstServicePrice3 = (await servicePrice2.childs).filter((c) => !c.isDeleted)
            if (lstServicePrice3.length > 0) {
              for (const servicePrice3 of lstServicePrice3) {
                //#region Tạo lv3
                const itemLv3 = new BidPriceEntity()
                itemLv3.companyId = user.companyId
                itemLv3.createdBy = user.id
                itemLv3.bidId = bidItemId
                itemLv3.sort = servicePrice3.sort
                itemLv3.name = servicePrice3.name
                itemLv3.isRequired = servicePrice3.isRequired
                itemLv3.type = servicePrice3.type
                itemLv3.percent = servicePrice3.percent
                itemLv3.level = servicePrice3.level
                itemLv3.description = servicePrice3.description
                itemLv3.parentId = bidPriceLv2.id
                itemLv3.scoreDLC = servicePrice3.scoreDLC
                itemLv3.requiredMin = servicePrice3.requiredMin
                itemLv3.unit = servicePrice3.unit
                itemLv3.currency = servicePrice3.currency
                itemLv3.number = servicePrice3.number
                itemLv3.isSetup = servicePrice3.isSetup
                itemLv3.servicePriceId = servicePrice3.id
                const bidPriceLv3 = await bidPriceRepo.save(itemLv3)
                //#endregion

                // Tạo thông tin bổ sung lv3
                const lstDetailLv3 = (await servicePrice3.servicePriceListDetails).filter((c) => !c.isDeleted)
                for (const detailLv3 of lstDetailLv3) {
                  const itemDetail3 = new BidPriceListDetailEntity()
                  itemDetail3.companyId = user.companyId
                  itemDetail3.createdBy = user.id
                  itemDetail3.bidPriceId = bidPriceLv3.id
                  itemDetail3.name = detailLv3.name
                  itemDetail3.type = detailLv3.type
                  itemDetail3.value = detailLv3.value
                  await bidPriceListDetailRepo.save(itemDetail3)
                }

                // Tạo giá trị cho các cột động lv3
                const lstValueLv3 = await servicePrice3.servicePriceColValues
                for (const itemValueLv3 of lstValueLv3) {
                  const objCol = lstCol.find((c) => c.id === itemValueLv3.servicePriceColId)
                  if (objCol) {
                    const itemValueNew3 = new BidPriceColValueEntity()
                    itemValueNew3.companyId = user.companyId
                    itemValueNew3.createdBy = user.id
                    itemValueNew3.bidPriceId = bidPriceLv3.id
                    itemValueNew3.bidPriceColId = objCol.bidPriceColId
                    itemValueNew3.value = itemValueLv3.value
                    await bidPriceColValueRepo.save(itemValueNew3)
                  }
                }
              }
            }
          }
        }
      }
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)
  }

  /** Lấy thông tin cơ cấu giá của lĩnh vực mời thầu */
  async loadCustomPrice(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    const lstServiceCustomPrice = await this.serviceCustomPriceRepo.find({
      where: { serviceId: bid.serviceId, companyId: user.companyId, isDeleted: false },
      order: { sort: 'ASC', createdAt: 'ASC' },
    })

    await this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(BidCustomPriceEntity)
      for (const a of lstServiceCustomPrice) {
        const item = new BidCustomPriceEntity()
        item.companyId = user.companyId
        item.createdBy = user.id
        item.bidId = bidId
        item.sort = a.sort
        item.name = a.name
        item.isRequired = a.isRequired
        item.type = a.type
        item.unit = a.unit
        item.currency = a.currency
        item.number = a.number
        await bidCustomPriceRepo.save(item)
      }
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)
  }

  /** Tạo chào giá cho gói thầu */
  async createPrice(user: UserDto, bidId: string, data: any) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionPriceCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceColValueRepo = manager.getRepository(BidPriceColValueEntity)
      const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId } })
      if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
      let lstPriceId = data.listItem[0].listPrice.map((x) => x.id)
      let lstPriceIdLv2 = data.listItem[0].listPrice.map((x: { __childs__: any }) => x.__childs__.map((y) => y.id))
      let lstPriceIdLv3 = data.listItem[0].listPrice.map((x: { __childs__: any }) =>
        x.__childs__.map((y: { __childs__ }) => y.__childs__.map((z) => z.id)),
      )
      lstPriceId = [...lstPriceId, ...lstPriceIdLv2, ...lstPriceIdLv3]
      lstPriceId = lstPriceId.flat()
      lstPriceId = lstPriceId.flat()
      let lstPrice = await this.bidPriceRepo.find({
        where: { id: In(lstPriceId) },
        relations: { bidPriceColValue: true },
        order: { bidPriceColValue: { createdAt: 'DESC' } },
      })
      let mapPrice = lstPrice.convertToMap((x) => x.id)
      const lstBidPriceCol = await this.bidPriceColRepo.find({
        where: { bidId, colType: enumData.ColType.MPO.code, companyId: user.companyId, isDeleted: false },
        order: { sort: 'ASC', createdAt: 'ASC' },
      })
      let mapBidPriceCol = lstBidPriceCol.convertToMap((x) => x.id)
      // add lv1
      for (const col of data.listItem[0].listPriceCol) {
        for (let item of data.listItem[0].listPrice) {
          if (item[col.id] != null) {
            let bidPriceCol = await mapPrice.get(item.id).bidPriceColValue
            let mapBidPriceColValue = bidPriceCol.convertToMap((x) => x.bidPriceColId)
            if (bidPriceCol.length != 0) {
              mapBidPriceColValue.get(col.id).value = item[col.id]
              await bidPriceColValueRepo.save(mapBidPriceColValue.get(col.id))
            } else {
              const objBidPriceColValueNew = new BidPriceColValueEntity()
              objBidPriceColValueNew.companyId = user.companyId
              objBidPriceColValueNew.createdBy = user.id
              objBidPriceColValueNew.bidPriceId = item.id
              objBidPriceColValueNew.bidPrice = Promise.resolve(mapPrice.get(item.id))
              objBidPriceColValueNew.bidPriceColId = col.id
              objBidPriceColValueNew.bidPriceCol = Promise.resolve(mapBidPriceCol.get(col.id))
              objBidPriceColValueNew.value = item[col.id]

              await bidPriceColValueRepo.save(objBidPriceColValueNew)
            }
          }
          for (let item2 of item.__childs__) {
            if (item2[col.id] != null) {
              let bidPriceCol = await mapPrice.get(item2.id).bidPriceColValue
              let mapBidPriceColValue = bidPriceCol.convertToMap((x) => x.bidPriceColId)
              if (bidPriceCol.length != 0) {
                mapBidPriceColValue.get(col.id).value = item2[col.id]
                await bidPriceColValueRepo.save(mapBidPriceColValue.get(col.id))
              } else {
                const objBidPriceColValueNew = new BidPriceColValueEntity()
                objBidPriceColValueNew.companyId = user.companyId
                objBidPriceColValueNew.createdBy = user.id
                objBidPriceColValueNew.bidPriceId = item2.id
                objBidPriceColValueNew.bidPrice = Promise.resolve(mapPrice.get(item2.id))
                objBidPriceColValueNew.bidPriceColId = col.id
                objBidPriceColValueNew.bidPriceCol = Promise.resolve(mapBidPriceCol.get(col.id))
                objBidPriceColValueNew.value = item2[col.id]

                await bidPriceColValueRepo.save(objBidPriceColValueNew)
              }
            }
            for (let item3 of item2.__childs__) {
              if (item3[col.id] != null) {
                let bidPriceCol = await mapPrice.get(item3.id).bidPriceColValue
                let mapBidPriceColValue = bidPriceCol.convertToMap((x) => x.bidPriceColId)
                if (bidPriceCol.length != 0) {
                  mapBidPriceColValue.get(col.id).value = item3[col.id]
                  await bidPriceColValueRepo.save(mapBidPriceColValue.get(col.id))
                } else {
                  const objBidPriceColValueNew = new BidPriceColValueEntity()
                  objBidPriceColValueNew.companyId = user.companyId
                  objBidPriceColValueNew.createdBy = user.id
                  objBidPriceColValueNew.bidPriceId = item3.id
                  objBidPriceColValueNew.bidPrice = Promise.resolve(mapPrice.get(item3.id))
                  objBidPriceColValueNew.bidPriceColId = col.id
                  objBidPriceColValueNew.bidPriceCol = Promise.resolve(mapBidPriceCol.get(col.id))
                  objBidPriceColValueNew.value = item3[col.id]

                  await bidPriceColValueRepo.save(objBidPriceColValueNew)
                }
              }
            }
          }
        }
      }
      bid.statusPrice = enumData.BidPriceStatus.DaTao.code
      bid.notePrice = data.notePrice
      if (
        bid.statusTech === enumData.BidTechStatus.DaDuyet.code &&
        (bid.statusTrade === enumData.BidTradeStatus.DaTao.code || bid.statusTrade === enumData.BidTradeStatus.DaDuyet.code)
      ) {
        bid.status = enumData.BidStatus.DangChonNCC.code
        // chưa chọn => đang chọn, để chọn Doanh nghiệp
        if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.ChuaChon.code) {
          bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DangChon.code
        }
        // đã duyệt => đã chọn, để duyệt lại
        if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaDuyet.code) {
          bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DaChon.code
        }
      }
      await this.repo.save(bid)

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = bidId
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.TaoGia.code
      bidHistory.save()
    })
    return { message: 'Cấu hình template chào giá cho gói thầu thành công.' }
  }

  /** Lấy chào giá của gói thầu */
  async getPrice(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const res = await this.repo.getBid1(user, bidId)
    const getDataCell = (row, col) => {
      row[col.id] = ''
      if (row.__bidPriceColValue__?.length > 0) {
        const cell = row.__bidPriceColValue__.find((c) => c.bidPriceColId === col.id)
        if (cell) row[col.id] = cell.value
      }
    }
    for (const item of res.listItem) {
      item.listPrice = await this.bidPriceRepo.getPrice(user, item.id)
      item.listPriceCol = await this.bidPriceColRepo.getBidPriceColMPO(user, item.id)
      item.listPriceColId = item.listPriceCol.map((c) => c.id)
      item.listCustomPrice = await this.bidCustomPriceRepo.find({
        where: { bidId: item.id, companyId: user.companyId },
        order: { sort: 'ASC', createdAt: 'ASC' },
      })

      if (item.listPriceCol.length == 0) continue
      for (const data1 of item.listPrice) {
        for (const col of item.listPriceCol) getDataCell(data1, col)
        for (const data2 of data1.__childs__) {
          for (const col of item.listPriceCol) getDataCell(data2, col)
          for (const data3 of data2.__childs__) {
            for (const col of item.listPriceCol) getDataCell(data3, col)
          }
        }
      }
    }

    return res
  }

  /** Kiểm tra quyền cấu hình lại bảng giá cho gói thầu */
  async checkPermissionResetBidPrice(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const lstStatus = [enumData.BidStatus.DongDamPhanGia.code, enumData.BidStatus.DongDauGia.code, enumData.BidStatus.HoanTatDanhGia.code]
    const bid = await this.repo.findOne({
      where: [{ id: bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false }, { childs: { id: bidId, isDeleted: false } }],
    })
    if (bid) {
      if (lstStatus.includes(bid.status)) {
        result = await this.bidEmployeeAccessRepo.isMPO_MPOLeader(user, bid.id)
        if (!result) message = 'Bạn không có quyền cấu hình lại bảng giá cho gói thầu.'
      } else message = 'Gói thầu đã thay đổi trạng thái, vui lòng kiểm tra lại.'
    }

    return { hasPermission: result, message }
  }

  /** MPO/MPOLeader cấu hình lại bảng giá cho gói thầu */
  async resetPrice(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionResetBidPrice(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    /** MPO/MPOLeader cấu hình lại bảng giá cho gói thầu */
    await this.repo.manager.transaction(async (manager) => {
      const bidRepo = manager.getRepository(BidEntity)
      const bidSupplierRepo = manager.getRepository(BidSupplierEntity)
      const bidPriceRepo = manager.getRepository(BidPriceEntity)
      const bidPriceColRepo = manager.getRepository(BidPriceColEntity)
      const priceValueRepo = manager.getRepository(BidSupplierPriceValueEntity)
      const priceColValueRepo = manager.getRepository(BidSupplierPriceColValueEntity)

      const bidDealRepo = manager.getRepository(BidDealEntity)
      const bidDealPriceRepo = manager.getRepository(BidDealPriceEntity)
      const bidDealSupplierRepo = manager.getRepository(BidDealSupplierEntity)
      const bidDealSupplierPriceValueRepo = manager.getRepository(BidDealSupplierPriceValueEntity)

      const bidAuctionRepo = manager.getRepository(BidAuctionEntity)
      const bidAuctionPriceRepo = manager.getRepository(BidAuctionPriceEntity)
      const bidAuctionSupplierRepo = manager.getRepository(BidAuctionSupplierEntity)
      const bidAuctionSupplierPriceValueRepo = manager.getRepository(BidAuctionSupplierPriceValueEntity)

      const bid = await bidRepo.findOne({ where: { id: bidId, companyId: user.companyId } })
      if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

      // Lấy những Doanh nghiệp đã nộp hồ sơ thầu
      const lstStatus = [
        enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
        enumData.BidSupplierStatus.DangDanhGia.code,
        enumData.BidSupplierStatus.DaDanhGia.code,
      ]
      const lstBidSupplier = await bidSupplierRepo.find({ where: { bidId, status: In(lstStatus), companyId: user.companyId } })

      const bidPrice = await bidPriceRepo.find({ where: { id: bidId, companyId: user.companyId } })
      const bidPriceCol = await bidPriceColRepo.find({ where: { id: bidId, companyId: user.companyId } })
      const bidDeal = await bidDealRepo.find({ where: { id: bidId, companyId: user.companyId } })
      const lstBidDealId = bidDeal.map((c) => c.id)
      const bidAuction = await bidAuctionRepo.find({ where: { id: bidId, companyId: user.companyId } })
      const lstBidAuctionId = bidAuction.map((c) => c.id)

      for (const bidSupplier of lstBidSupplier) {
        // Thông tin chào giá cũ của Doanh nghiệp
        const bidPriceValue = await bidSupplier.bidSupplierPriceValue
        const bidPriceColValue = await bidSupplier.bidSupplierPriceColValue

        // Thông tin các lần đàm phán của Doanh nghiệp
        let lstBidDealSupplier: BidDealSupplierEntity[] = []
        if (lstBidDealId.length > 0) {
          lstBidDealSupplier = await bidDealSupplierRepo.find({
            where: { bidDealId: In(lstBidDealId), supplierId: bidSupplier.supplierId, companyId: user.companyId },
            relations: { bidDealSupplierPriceValue: true, bidDeal: { bidDealPrices: true } },
          })
        }

        // Thông tin các lần đấ gái của Doanh nghiệp
        let lstBidAuctionSupplier: BidAuctionSupplierEntity[] = []
        if (lstBidAuctionId.length > 0) {
          lstBidAuctionSupplier = await bidAuctionSupplierRepo.find({
            where: { bidAuctionId: In(lstBidAuctionId), supplierId: bidSupplier.supplierId, companyId: user.companyId },
            relations: { bidAuctionSupplierPriceValue: true, bidAuction: { bidAuctionPrice: true } },
          })
        }

        // Data lưu log
        const dataSave = {
          bidPrice,
          bidPriceCol,
          bidPriceValue,
          bidPriceColValue,
          lstBidDealSupplier,
          lstBidAuctionSupplier,
        }

        bidSupplier.dataJson = JSON.stringify(dataSave)
        bidSupplier.updatedBy = user.id

        await bidSupplierRepo.save(bidSupplier)
      }

      await bidSupplierRepo.update(
        { bidId },
        {
          statusResetPrice: enumData.BidSupplierResetPriceStatus.KhongYeuCau.code,
          updatedBy: user.id,
        },
      )

      const lstBidPrice = await bidPriceRepo.find({ where: { bidId, companyId: user.companyId } })
      // Nếu chưa có cấu hình bảng giá trước đó thì không có gì để xóa
      if (lstBidPrice.length == 0) return

      // Xóa chào giá cũ
      const lstBidPriceId = lstBidPrice.map((c) => c.id)
      await priceValueRepo.delete({ bidPriceId: In(lstBidPriceId) })
      await priceColValueRepo.delete({ bidPriceId: In(lstBidPriceId) })

      // Xóa đàm phán giá
      if (lstBidDealId.length > 0) {
        await bidDealPriceRepo.delete({ bidDealId: In(lstBidDealId) })

        const lstBidDealSupplier = await bidDealSupplierRepo.find({ where: { bidDealId: In(lstBidDealId), companyId: user.companyId } })
        const lstBidDealSupplierId = lstBidDealSupplier.map((c) => c.id)
        if (lstBidDealSupplierId.length > 0) {
          await bidDealSupplierPriceValueRepo.delete({
            bidDealSupplierId: In(lstBidDealSupplierId),
          })
          await bidDealSupplierRepo.delete({
            bidDealId: In(lstBidDealId),
          })
        }

        await bidDealRepo.delete({ bidId })
      }

      // Xóa đấu giá
      if (lstBidAuctionId.length > 0) {
        await bidAuctionPriceRepo.delete({ bidAuctionId: In(lstBidAuctionId) })

        const lstBidAuctionSupplier = await bidAuctionSupplierRepo.find({ where: { bidAuctionId: In(lstBidAuctionId), companyId: user.companyId } })
        const lstBidAuctionSupplierId = lstBidAuctionSupplier.map((c) => c.id)
        if (lstBidAuctionSupplierId.length > 0) {
          await bidAuctionSupplierPriceValueRepo.delete({
            bidAuctionSupplierId: In(lstBidAuctionSupplierId),
          })
          await bidAuctionSupplierRepo.delete({
            bidAuctionId: In(lstBidAuctionId),
          })
        }

        await bidAuctionRepo.delete({ bidId })
      }
    })

    await this.repo.update(bidId, {
      // status: enumData.BidStatus.DangDanhGia.code,
      // statusRatePrice: enumData.BidPriceRateStatus.ChuaTao.code,
      statusResetPrice: enumData.BidResetPriceStatus.DangTao.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.HieuChinhBangGia.code
    bidHistory.save()

    return { message: 'Thao tác thành công! Gói thầu đang được cấu hình lại bảng giá.' }
  }

  /** MPO/MPOLeader lưu cấu hình lại bảng giá cho gói thầu */
  async saveResetPrice(
    user: UserDto,
    bidId: string,
    data: {
      lstSupplierId: string[]
      resetPriceEndDate: Date
      isRequireFilePriceDetail: boolean
      isRequireFileTechDetail: boolean
    },
  ) {
    if (!data.resetPriceEndDate) throw new BadRequestException('Vui lòng chọn thời điểm kết thúc nộp chào giá hiệu chỉnh.')
    if (data.lstSupplierId == null || data.lstSupplierId.length == 0)
      throw new BadRequestException('Vui lòng chọn doanh nghiệp nộp bảng giá hiệu chỉnh.')

    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionResetBidPrice(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.manager.transaction(async (manager) => {
      const bidRepo = manager.getRepository(BidEntity)
      const bidSupplierRepo = manager.getRepository(BidSupplierEntity)

      const bid = await bidRepo.findOne({ where: { id: bidId, companyId: user.companyId } })
      if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

      await bidRepo.update(bidId, {
        resetPriceEndDate: data.resetPriceEndDate,
        isRequireFilePriceDetail: data.isRequireFilePriceDetail,
        isRequireFileTechDetail: data.isRequireFileTechDetail,
        updatedBy: user.id,
      })

      // update lại các thông tin giá của các Doanh nghiệp tham gia gói thầu
      await bidSupplierRepo.update(
        { bidId, supplierId: In(data.lstSupplierId) },
        {
          statusPrice: enumData.BidSupplierPriceStatus.DangBoSung.code,
          scoreManualPrice: 0,
          scorePrice: 0,
          notePrice: '',
          isPriceValid: true,
          statusResetPrice: enumData.BidSupplierResetPriceStatus.YeuCauBoSung.code,
          updatedBy: user.id,
        },
      )
    })

    await this.repo.update(bidId, {
      status: enumData.BidStatus.DangDanhGia.code,
      statusRatePrice: enumData.BidPriceRateStatus.ChuaTao.code,
      statusResetPrice: enumData.BidResetPriceStatus.DaTao.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.HoanTatHieuChinhBangGia.code
    bidHistory.save()

    // Gửi email các Doanh nghiệp yêu cầu nộp bảng giá hiệu chỉnh
    this.emailService.GuiNCCNopLaiChaoGia(bidId)

    return { message: 'Gói thầu đã được cấu hình lại bảng giá thành công.' }
  }

  /** Kiểm tra quyền kết thúc nộp chào giá hiệu chỉnh cho gói thầu */
  async checkPermissionEndResetBidPrice(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const bid = await this.repo.findOne({
      where: [{ id: bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false }, { childs: { id: bidId, isDeleted: false } }],
    })
    if (bid) {
      if (bid.status == enumData.BidStatus.DangDanhGia.code && bid.statusResetPrice == enumData.BidResetPriceStatus.DaTao.code) {
        result = await this.bidEmployeeAccessRepo.isMPO_MPOLeader(user, bid.id)
        if (!result) {
          message = 'Bạn không có quyền kết thúc nộp chào giá hiệu chỉnh cho gói thầu.'
        }
      } else {
        result = false
        message = 'Gói thầu đã thay đổi trạng thái, vui lòng kiểm tra lại.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Danh sách Doanh nghiệp tham gia gói thầu */
  async bidSupplierJoinResetPrice(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const objPermission = await this.checkPermissionEndResetBidPrice(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    return await this.bidSupplierRepo.find({
      where: { bidId, statusResetPrice: enumData.BidSupplierResetPriceStatus.DaBoSung.code, companyId: user.companyId },
      relations: { supplier: true },
    })
  }

  /** MPO/MPOLeader kết thúc nộp chào giá hiệu chỉnh cho gói thầu */
  async endResetPrice(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionEndResetBidPrice(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(bidId, {
      // status: enumData.BidStatus.DangDanhGia.code,
      statusRatePrice: enumData.BidPriceRateStatus.DangTao.code,
      statusRateTrade: enumData.BidTradeRateStatus.DaTao.code, // update để hiện nút duyệt chung cho (chào giá & thương mại)
      statusResetPrice: enumData.BidResetPriceStatus.KetThuc.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.KetThucNopChaoGiaHieuChinh.code
    bidHistory.save()

    return { message: 'Thao tác thành công! Gói thầu đã kết thúc nộp chào giá hiệu chỉnh.' }
  }

  /** Lấy cơ cấu giá của gói thầu */
  async getCustomPrice(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const res = await this.repo.getBid1(user, bidId)
    for (const item of res.listItem) {
      item.listCustomPrice = await this.bidCustomPriceRepo.find({
        where: { bidId: item.id, companyId: user.companyId },
        order: { sort: 'ASC', createdAt: 'ASC' },
      })
    }

    return res
  }

  async priceCreateData(user: UserDto, data: BidPriceCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.repo.findOne({ where: { id: data.bidId, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Tạo thêm ngoài lấy từ template
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(BidPriceEntity)

      const item = new BidPriceEntity()
      item.companyId = user.companyId
      item.createdBy = user.id
      item.bidId = data.bidId
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.itemId = data.itemId
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.level = data.level
      item.description = data.description
      item.parentId = data.parentId
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      item.isSetup = data.isSetup
      if (data.isSetup) {
        item.isTemplate = data.isTemplate
      }
      item.unit = data.unit
      item.currency = data.currency
      item.number = data.number

      await bidPriceRepo.save(item)
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)
  }

  async priceUpdateData(user: UserDto, data: BidPriceUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.repo.findOne({ where: { id: data.bidId, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Update
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(BidPriceEntity)

      const item = await bidPriceRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!item) throw new Error(ERROR_NOT_FOUND_DATA)
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.itemId = data.itemId
      item.percent = data.percent
      item.level = data.level
      item.description = data.description
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      item.isSetup = data.isSetup
      if (data.isSetup) {
        item.isTemplate = data.isTemplate
      }
      item.unit = data.unit
      item.currency = data.currency
      item.number = data.number
      item.updatedBy = user.id

      await bidPriceRepo.save(item)
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)
  }

  async priceDeleteData(user: UserDto, bidPriceId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bidPrice = await this.bidPriceRepo.findOne({ where: { id: bidPriceId, companyId: user.companyId } })
    if (!bidPrice) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidPrice.bid
    const objPermission = await this.checkPermissionPriceCreate(user, bidPrice.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidPrice.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Xoá
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(BidPriceEntity)
      const bidPriceListDetailRepo = manager.getRepository(BidPriceListDetailEntity)
      const bidPriceColValueRepo = manager.getRepository(BidPriceColValueEntity)

      const bidPrice = await bidPriceRepo.findOne({ where: { id: bidPriceId, companyId: user.companyId } })
      if (!bidPrice) throw new Error(ERROR_NOT_FOUND_DATA)

      // lv2
      const lstData2 = await bidPrice.childs
      if (lstData2.length > 0) {
        for (const data2 of lstData2) {
          const lstData3 = await data2.childs
          if (lstData3.length > 0) {
            for (const data3 of lstData3) {
              // xóa thông tin mở rộng lv3
              await bidPriceListDetailRepo.delete({ bidPriceId: data3.id })
              // xóa giá trị cột động lv3
              await bidPriceColValueRepo.delete({ bidPriceId: data3.id })
            }

            // xóa lv3
            await bidPriceRepo.delete({ parentId: data2.id })
          }

          // xóa thông tin mở rộng lv2
          await bidPriceListDetailRepo.delete({ bidPriceId: data2.id })
          // xóa giá trị cột động lv2
          await bidPriceColValueRepo.delete({ bidPriceId: data2.id })
        }

        // xóa lv2
        await bidPriceRepo.delete({ parentId: bidPriceId })
      }

      // xóa thông tin mở rộng lv1
      await bidPriceListDetailRepo.delete({ bidPriceId: bidPriceId })
      // xóa giá trị cột động lv1
      await bidPriceColValueRepo.delete({ bidPriceId: bidPriceId })

      // xóa lv1
      await bidPriceRepo.delete(bidPriceId)
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)
  }

  async priceDeleteAllData(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionPriceCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId, isDeleted: false } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const flag = await this.checkPermissionMpoEditTemplate(user, bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Xóa tất cả
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(BidPriceEntity)
      const bidPriceListDetailRepo = manager.getRepository(BidPriceListDetailEntity)
      const bidPriceColValueRepo = manager.getRepository(BidPriceColValueEntity)

      const level = 1
      const lstBidPriceLv1 = await bidPriceRepo.find({ where: { bidId, level, companyId: user.companyId } })
      if (lstBidPriceLv1.length > 0) {
        for (const bidPriceLv1 of lstBidPriceLv1) {
          const lstBidPriceLv2 = await bidPriceLv1.childs
          if (lstBidPriceLv2.length > 0) {
            for (const bidPriceLv2 of lstBidPriceLv2) {
              const lstBidPriceLv3 = await bidPriceLv2.childs
              if (lstBidPriceLv3.length > 0) {
                for (const bidPriceLv3 of lstBidPriceLv3) {
                  // xóa thông tin mở rộng lv3
                  await bidPriceListDetailRepo.delete({ bidPriceId: bidPriceLv3.id })
                  // xóa giá trị cột động lv3
                  await bidPriceColValueRepo.delete({ bidPriceId: bidPriceLv3.id })
                }
                // xóa lv3
                await bidPriceRepo.delete({ parentId: bidPriceLv2.id })
              }

              // xoá thông tin mở rộng Lv2
              await bidPriceListDetailRepo.delete({ bidPriceId: bidPriceLv2.id })
              // xoá giá trị cột động Lv2
              await bidPriceColValueRepo.delete({ bidPriceId: bidPriceLv2.id })
            }
            // xoá Lv2
            await bidPriceRepo.delete({ parentId: bidPriceLv1.id })
          }

          // xoá thông tin mở rộng Lv1
          await bidPriceListDetailRepo.delete({ bidPriceId: bidPriceLv1.id })
          // xoá giá trị cột động Lv1
          await bidPriceColValueRepo.delete({ bidPriceId: bidPriceLv1.id })
        }
        // xoá Lv1
        await bidPriceRepo.delete({ bidId })
      }
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)
  }

  async setting_fomular(user: UserDto, data: { id: string; fomular: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionPriceCreate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.id)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    const lstField = await this.bidPriceColRepo.getBidPriceColAll(user, data.id)
    const isValidFomular = await coreHelper.checkFomular(data.fomular, lstField)
    if (!isValidFomular) {
      throw new NotAcceptableException(ERROR_INVALID_FOMULAR)
    }

    const bid = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId, isDeleted: false } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)

    bid.fomular = data.fomular
    bid.updatedBy = user.id
    const res = await this.repo.save(bid)
    return res
  }

  /** Setup cách tính điểm giá của Item gói thầu */
  async saveSettingPriceCalWay(user: UserDto, data: { id: string; wayCalScorePrice: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionPriceCreate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.id)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    await this.repo.update(data.id, { wayCalScorePrice: data.wayCalScorePrice, updatedBy: user.id })

    return { message: UPDATE_SUCCESS }
  }

  /** Import excel chào giá */
  public async price_import(user: UserDto, bidId: string, data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    await this.priceDeleteAllData(user, bidId)

    // Import excel chào giá
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceColValueRepo = manager.getRepository(BidPriceColValueEntity)
      const bidPriceRepo = manager.getRepository(BidPriceEntity)
      const bidPriceColRepo = manager.getRepository(BidPriceColEntity)
      const bidPriceListDetailRepo = manager.getRepository(BidPriceListDetailEntity)

      const lstBidPriceCol = await bidPriceColRepo.find({
        where: { bidId, colType: enumData.ColType.MPO.code, companyId: user.companyId, isDeleted: false },
        order: { sort: 'ASC', createdAt: 'ASC' },
      })

      // add lv1
      var lstDataLv1 = data.lstDataTable1.filter((c: any) => c.level == 1)
      for (const item of lstDataLv1) {
        const objBidPriceNew = new BidPriceEntity()
        objBidPriceNew.companyId = user.companyId
        objBidPriceNew.createdBy = user.id
        objBidPriceNew.bidId = bidId
        objBidPriceNew.name = item.name
        objBidPriceNew.sort = item.sort || 0
        objBidPriceNew.unit = item.unit
        objBidPriceNew.currency = item.currency
        objBidPriceNew.number = item.number
        objBidPriceNew.isRequired = item.isRequired
        objBidPriceNew.level = 1
        objBidPriceNew.type = enumData.DataType.Number.code
        objBidPriceNew.isTemplate = false
        objBidPriceNew.isSetup = false

        const objBidPrice = await bidPriceRepo.save(objBidPriceNew)
        item.id = objBidPrice.id

        for (const col of lstBidPriceCol) {
          if (item[col.id] != null) {
            const objBidPriceColValueNew = new BidPriceColValueEntity()
            objBidPriceColValueNew.companyId = user.companyId
            objBidPriceColValueNew.createdBy = user.id
            objBidPriceColValueNew.bidPriceId = item.id
            objBidPriceColValueNew.bidPriceColId = col.id
            objBidPriceColValueNew.value = item[col.id]

            await bidPriceColValueRepo.save(objBidPriceColValueNew)
          }
        }

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new BidPriceListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.bidPriceId = item.id
            detailNew.name = detail.nameList
            detailNew.type = detail.typeDataList
            detailNew.value = detail.valueList
            await bidPriceListDetailRepo.save(detailNew)
          }
        }
      }

      // add lv2
      var lstDataLv2 = data.lstDataTable1.filter((c: any) => c.level == 2)
      for (const item of lstDataLv2) {
        const objBidPriceNew = new BidPriceEntity()
        objBidPriceNew.companyId = user.companyId
        objBidPriceNew.createdBy = user.id
        objBidPriceNew.bidId = bidId
        objBidPriceNew.name = item.name
        objBidPriceNew.sort = item.sort || 0
        objBidPriceNew.unit = item.unit
        objBidPriceNew.currency = item.currency
        objBidPriceNew.number = item.number
        objBidPriceNew.isRequired = item.isRequired
        objBidPriceNew.level = 2
        objBidPriceNew.type = enumData.DataType.Number.code
        objBidPriceNew.isTemplate = false
        objBidPriceNew.isSetup = false
        const parent = lstDataLv1.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objBidPriceNew.parentId = parent.id

        const objBidPrice = await bidPriceRepo.save(objBidPriceNew)
        item.id = objBidPrice.id

        for (const col of lstBidPriceCol) {
          if (item[col.id] != null) {
            const objBidPriceColValueNew = new BidPriceColValueEntity()
            objBidPriceColValueNew.companyId = user.companyId
            objBidPriceColValueNew.createdBy = user.id
            objBidPriceColValueNew.bidPriceId = item.id
            objBidPriceColValueNew.bidPriceColId = col.id
            objBidPriceColValueNew.value = item[col.id]

            await bidPriceColValueRepo.save(objBidPriceColValueNew)
          }
        }

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new BidPriceListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.bidPriceId = item.id
            detailNew.name = detail.nameList
            detailNew.type = detail.typeDataList
            detailNew.value = detail.valueList
            await bidPriceListDetailRepo.save(detailNew)
          }
        }
      }

      // add lv3
      var lstDataLv3 = data.lstDataTable1.filter((c: any) => c.level == 3)
      for (const item of lstDataLv3) {
        const objBidPriceNew = new BidPriceEntity()
        objBidPriceNew.companyId = user.companyId
        objBidPriceNew.createdBy = user.id
        objBidPriceNew.bidId = bidId
        objBidPriceNew.name = item.name
        objBidPriceNew.sort = item.sort || 0
        objBidPriceNew.unit = item.unit
        objBidPriceNew.currency = item.currency
        objBidPriceNew.number = item.number
        objBidPriceNew.isRequired = item.isRequired
        objBidPriceNew.level = 3
        objBidPriceNew.type = enumData.DataType.Number.code
        objBidPriceNew.isTemplate = false
        objBidPriceNew.isSetup = false
        const parent = lstDataLv2.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objBidPriceNew.parentId = parent.id

        const objBidPrice = await bidPriceRepo.save(objBidPriceNew)
        item.id = objBidPrice.id

        for (const col of lstBidPriceCol) {
          if (item[col.id] != null) {
            const objBidPriceColValueNew = new BidPriceColValueEntity()
            objBidPriceColValueNew.companyId = user.companyId
            objBidPriceColValueNew.createdBy = user.id
            objBidPriceColValueNew.bidPriceId = item.id
            objBidPriceColValueNew.bidPriceColId = col.id
            objBidPriceColValueNew.value = item[col.id]

            await bidPriceColValueRepo.save(objBidPriceColValueNew)
          }
        }

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new BidPriceListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.bidPriceId = item.id
            detailNew.name = detail.nameList
            detailNew.type = detail.typeDataList
            detailNew.value = detail.valueList
            await bidPriceListDetailRepo.save(detailNew)
          }
        }
      }
    })

    return { message: IMPORT_SUCCESS }
  }

  async customPriceCreateData(user: UserDto, data: BidCustomPriceCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.repo.findOne({ where: { id: data.bidId, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    await this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(BidCustomPriceEntity)
      const item = new BidCustomPriceEntity()
      item.companyId = user.companyId
      item.createdBy = user.id
      item.bidId = data.bidId
      item.itemId = data.itemId
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.unit = data.unit
      item.currency = data.currency
      item.number = data.number
      await bidCustomPriceRepo.save(item)
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)
  }

  async customPriceUpdateData(user: UserDto, data: BidCustomPriceUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.repo.findOne({ where: { id: data.bidId, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    await this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(BidCustomPriceEntity)
      const item = await bidCustomPriceRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!item) throw new Error(ERROR_NOT_FOUND_DATA)
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.unit = data.unit
      item.itemId = data.itemId
      item.currency = data.currency
      item.number = data.number
      item.updatedBy = user.id
      await bidCustomPriceRepo.save(item)
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)
  }

  async customPriceDeleteData(user: UserDto, bidCustomPriceId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bidCustomPrice = await this.bidCustomPriceRepo.findOne({ where: { id: bidCustomPriceId, companyId: user.companyId } })
    if (!bidCustomPrice) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const objPermission = await this.checkPermissionPriceCreate(user, bidCustomPrice.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    const bid = await bidCustomPrice.bid
    const flag = await this.checkPermissionMpoEditTemplate(user, bidCustomPrice.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    await this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(BidCustomPriceEntity)
      const bidCustomPrice = await bidCustomPriceRepo.findOne({ where: { id: bidCustomPriceId, companyId: user.companyId } })
      if (!bidCustomPrice) throw new Error(ERROR_NOT_FOUND_DATA)
      await bidCustomPriceRepo.delete(bidCustomPriceId)
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)
  }

  async customPriceDeleteAllData(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionPriceCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const flag = await this.checkPermissionMpoEditTemplate(user, bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    await this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(BidCustomPriceEntity)
      await bidCustomPriceRepo.delete({ bidId })
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)
  }

  public async custompriceImport(user: UserDto, bidId: string, data: { lstData: any[] }) {
    await this.customPriceDeleteAllData(user, bidId)

    return this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(BidCustomPriceEntity)

      for (const item of data.lstData) {
        const objBidCustomPriceNew = new BidCustomPriceEntity()
        objBidCustomPriceNew.companyId = user.companyId
        objBidCustomPriceNew.createdBy = user.id
        objBidCustomPriceNew.bidId = bidId
        objBidCustomPriceNew.name = item.name
        objBidCustomPriceNew.sort = item.sort || 0
        objBidCustomPriceNew.unit = item.unit
        objBidCustomPriceNew.currency = item.currency
        objBidCustomPriceNew.number = item.number
        objBidCustomPriceNew.isRequired = item.isRequired
        objBidCustomPriceNew.type = enumData.DataType.Number.code

        await bidCustomPriceRepo.save(objBidCustomPriceNew)
      }
    })
  }

  public async bidPriceCol_list(user: UserDto, bidId: string) {
    const res: any[] = await this.bidPriceColRepo.getBidPriceColAll(user, bidId)
    for (const item of res) {
      item.typeName = enumData.DataType[item.type]?.name || ''
      item.colTypeName = enumData.ColType[item.colType]?.name || ''
    }

    return res
  }

  public async bidPriceCol_create_data(user: UserDto, data: BidPriceColCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.repo.findOne({ where: { id: data.bidId, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    if (data.fomular?.length > 0) {
      const lstField = await this.bidPriceColRepo.getBidPriceColAll(user, data.bidId)
      const isValidFomular = await coreHelper.checkFomular(data.fomular, lstField)
      if (!isValidFomular) throw new NotAcceptableException(ERROR_INVALID_FOMULAR)
    }

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)

    const entity = new BidPriceColEntity()
    entity.companyId = user.companyId
    entity.createdBy = user.id
    entity.code = data.code
    entity.name = data.name
    entity.fomular = data.fomular
    entity.type = data.type
    entity.colType = data.colType
    entity.isRequired = data.isRequired
    entity.sort = data.sort || 0
    entity.bidId = data.bidId
    return await entity.save()
  }

  public async bidPriceCol_update_data(user: UserDto, data: BidPriceColUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.repo.findOne({ where: { id: data.bidId, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)

    const entity = await this.bidPriceColRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (data.fomular !== entity.fomular) {
      // Nếu update cthuc thì kiểm tra lại
      if (data.fomular?.length > 0) {
        const lstField = await this.bidPriceColRepo.getBidPriceColAll(user, entity.bidId)
        const isValidFomular = await coreHelper.checkFomular(data.fomular, lstField)
        if (!isValidFomular) throw new Error(ERROR_INVALID_FOMULAR)
      }

      entity.fomular = data.fomular
    }
    entity.code = data.code
    entity.name = data.name
    entity.type = data.type
    entity.colType = data.colType
    entity.isRequired = data.isRequired
    entity.sort = data.sort || 0
    entity.updatedBy = user.id
    return await entity.save()
  }

  public async bidPriceCol_delete_data(user: UserDto, id: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidPriceCol = await this.bidPriceColRepo.findOne({ where: { id, companyId: user.companyId } })
    if (!bidPriceCol) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bid = await bidPriceCol.bid

    const objPermission = await this.checkPermissionPriceCreate(user, bidPriceCol.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidPriceCol.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)

    return this.repo.manager.transaction(async (manager) => {
      const bidPriceColValueRepo = manager.getRepository(BidPriceColValueEntity)
      const bidPriceColRepo = manager.getRepository(BidPriceColEntity)
      await bidPriceColValueRepo.delete({ bidPriceColId: id })
      await bidPriceColRepo.delete(id)
      return { message: DELETE_SUCCESS }
    })
  }

  public async bidPriceCol_delete_all_data(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const objPermission = await this.checkPermissionPriceCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)

    return this.repo.manager.transaction(async (manager) => {
      const bidPriceColValueRepo = manager.getRepository(BidPriceColValueEntity)
      const bidPriceColRepo = manager.getRepository(BidPriceColEntity)

      const lstBidPriceCol = await bidPriceColRepo.find({ where: { bidId, companyId: user.companyId } })
      for (const bidPriceCol of lstBidPriceCol) {
        await bidPriceColValueRepo.delete({ bidPriceColId: bidPriceCol.id })
      }

      await bidPriceColRepo.delete({ bidId })

      return { message: DELETE_SUCCESS }
    })
  }

  public async bidPriceListDetail_list(user: UserDto, bidPriceId: string) {
    return await this.repo.manager.getRepository(BidPriceListDetailEntity).find({
      where: { bidPriceId, companyId: user.companyId },
      order: { createdAt: 'ASC' },
    })
  }

  public async bidPriceListDetail_create_data(user: UserDto, data: { bidPriceId: string; name: string; type: string; value: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidPrice = await this.bidPriceRepo.findOne({ where: { id: data.bidPriceId, companyId: user.companyId } })
    if (!bidPrice) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bid = await bidPrice.bid

    const objPermission = await this.checkPermissionPriceCreate(user, bidPrice.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidPrice.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)

    const entity = new BidPriceListDetailEntity()
    entity.companyId = user.companyId
    entity.createdBy = user.id
    entity.name = data.name
    entity.type = data.type
    entity.value = data.value
    entity.bidPriceId = data.bidPriceId
    await entity.save()

    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async bidPriceListDetail_update_data(user: UserDto, data: { id: string; bidPriceId: string; name: string; type: string; value: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidPrice = await this.bidPriceRepo.findOne({ where: { id: data.bidPriceId, companyId: user.companyId } })
    if (!bidPrice) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bid = await bidPrice.bid

    const objPermission = await this.checkPermissionPriceCreate(user, bidPrice.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidPrice.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)

    const entity = await this.repo.manager.getRepository(BidPriceListDetailEntity).findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    entity.name = data.name
    entity.type = data.type
    entity.value = data.value
    entity.updatedBy = user.id
    await entity.save()

    return { id: entity.id, message: UPDATE_SUCCESS }
  }

  public async bidPriceListDetail_delete_data(user: UserDto, id: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidPriceListDetail = await this.repo.manager.getRepository(BidPriceListDetailEntity).findOne({ where: { id, companyId: user.companyId } })
    if (!bidPriceListDetail) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bidPrice = await bidPriceListDetail.bidPrice
    const bid = await bidPrice.bid

    const objPermission = await this.checkPermissionPriceCreate(user, bidPrice.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidPrice.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.parentId || bid.id)

    await this.repo.manager.getRepository(BidPriceListDetailEntity).delete(id)

    return { message: DELETE_SUCCESS }
  }

  //#endregion

  //#region bidChooseSupplier

  /** Kiểm tra quyền mời thầu */
  async checkPermissionChooseSupplier(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const lstStatusCanEdit = [
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    const bid = await this.repo.findOne({
      where: [
        { id: bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false },
        { companyId: user.companyId, childs: { id: bidId, isDeleted: false } },
      ],
    })
    if (bid) {
      if (lstStatusCanEdit.includes(bid.status)) {
        result = await this.bidEmployeeAccessRepo.isMPO(user, bid.id)
        if (!result) {
          message = 'Bạn không có quyền chọn nhà cung cấp tham gia cho gói thầu.'
        }
      } else {
        result = false
        message = 'Chỉ được phép chọn nhà cung cấp tham gia khi chưa mở thầu.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Danh sách nhà cung cấp mời thầu */
  async loadSupplierInvite(user: UserDto, data: { bidId: string; supplierName?: string; lstStatus?: string[]; typeGetData: number }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new BadRequestException(`Vui lòng chọn gói thầu trước`)

    const bid: any = await this.repo.findOne({
      where: { id: data.bidId, companyId: user.companyId, isDeleted: false },
      relations: { childs: true, employeeAccess: true },
      select: {
        id: true,
        name: true,
        status: true,
        statusChooseSupplier: true,
        childs: { id: true, serviceId: true },
        employeeAccess: { id: true, employeeId: true, type: true },
      },
    })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const lstServiceId = bid.__childs__.map((c) => c.serviceId).filter((value, index, self) => self.indexOf(value) === index)
    if (lstServiceId.length == 0) throw new Error('Gói thầu chưa thiết lập danh sách Item')

    let whereCon: any = { companyId: user.companyId, status: enumData.SupplierStatus.DaDuyet.code, isDeleted: false }
    whereCon.supplierServices = { serviceId: In(lstServiceId), isDeleted: false }
    if (data.lstStatus?.length > 0) whereCon.supplierServices.statusExpertise = In(data.lstStatus)

    if (data.supplierName) {
      whereCon = [
        { ...whereCon, code: Like(`%${data.supplierName}%`) },
        { ...whereCon, name: Like(`%${data.supplierName}%`) },
      ]
    }

    let lstData: any[] = await this.supplierRepo.find({
      where: whereCon,
      order: { code: 'ASC' },
    })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.SupplierStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }

    let res: any[] = []
    for (const item of lstData) {
      const temp: any = {}
      temp.supplierId = item.id
      temp.supplierCode = item.code
      temp.supplierName = item.name
      temp.isChosen = false
      temp.statusName = dicStatus[item.status]
      temp.createdAt = item.createdAt
      res.push(temp)
    }

    if (bid.statusChooseSupplier != enumData.BidChooseSupplierStatus.ChuaChon.code) {
      const lstBidSupplier = await this.bidSupplierRepo.find({
        where: { bidId: data.bidId, companyId: user.companyId },
        select: { id: true, supplierId: true },
      })
      const lstSupplierInvitedID = lstBidSupplier.map((c) => c.supplierId)
      for (const item of res) item.isChosen = lstSupplierInvitedID.includes(item.supplierId)
    }

    // nếu 'Chọn cả doanh nghiệp ngoài LVMH' (không xét isChosen vì có isChosen thì đã gen data supplierService & bidSupplier)
    if (data.typeGetData == 2) {
      let lstSupplier = await this.supplierRepo.find({
        where: { companyId: user.companyId, isDeleted: false },
        select: { id: true, code: true, name: true },
      })

      if (lstSupplier.length > 0) {
        const lstSupplierId = lstData.map((c) => c.supplierId)
        if (lstSupplierId.length > 0) {
          lstSupplier = lstSupplier.filter((c) => lstSupplierId.indexOf(c.id) == -1)
        }
        for (const supplier of lstSupplier) {
          const temp: any = {}
          temp.supplierId = supplier.id
          temp.supplierCode = supplier.code
          temp.supplierName = supplier.name
          temp.isChosen = false
          temp.statusName = enumData.SupplierServiceStatus.ChuaDangKy.code
          temp.createdAt = supplier.createdAt
          res.push(temp)
        }
      }
    }

    bid.isMPO = bid.__employeeAccess__.some((p) => p.type === enumData.BidRuleType.MPO.code && p.employeeId === user.employeeId)
    bid.isMPOLeader = bid.__employeeAccess__.some((p) => p.type === enumData.BidRuleType.MPOLeader.code && p.employeeId === user.employeeId)

    const enumStatus = enumData.BidChooseSupplierStatus
    const status = bid.statusChooseSupplier
    const lstStatus = [enumStatus.DangChon.code, enumStatus.TuChoi.code]
    const isShowChooseSupplier = lstStatus.includes(status) && bid.isMPO
    const isShowSendMPOLeaderCheck = status === enumStatus.DangChon.code && bid.isMPO
    const isShowAcceptChooseSupplier = bid.status === enumData.BidStatus.DangDuyetGoiThau.code && status === enumStatus.DaChon.code && bid.isMPOLeader
    if (status === enumStatus.DaChon.code || status === enumStatus.DaDuyet.code || (bid.isMPOLeader && !bid.isMPO)) {
      res = res.filter((c) => c.isChosen)
    }

    return { isShowChooseSupplier, isShowSendMPOLeaderCheck, isShowAcceptChooseSupplier, bidName: bid.name, lstData: res }
  }

  /** Mời thầu */
  async bidChooseSupplier(user: UserDto, data: { bidId: string; lstData: any[] }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionChooseSupplier(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid: any = await this.repo.findOne({
      where: { id: data.bidId, companyId: user.companyId },
      relations: { childs: true },
      select: { id: true, childs: { id: true, serviceId: true } },
    })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    // xoá bidsupplier cũ của gói thầu
    await this.bidSupplierRepo.delete({ bidId: bid.id })
    const lstItem = bid.__childs__ || []
    const lstItemId = lstItem.map((c) => c.id)
    // xoá bidsupplier cũ của các Item
    await this.bidSupplierRepo.delete({ bidId: In(lstItemId) })

    const lstSupplierChooseId = data.lstData.filter((item: any) => item.isChosen).map((item: any) => item.supplierId)

    for (const supplierId of lstSupplierChooseId) {
      const newBidSupplier = new BidSupplierEntity()
      newBidSupplier.companyId = user.companyId
      newBidSupplier.createdBy = user.id
      newBidSupplier.supplierId = supplierId
      newBidSupplier.bidId = data.bidId
      newBidSupplier.status = enumData.BidSupplierStatus.DaDuocChon.code
      newBidSupplier.statusFile = enumData.BidSupplierFileStatus.ChuaKiemTra.code
      newBidSupplier.statusTech = enumData.BidSupplierTechStatus.ChuaXacNhan.code
      newBidSupplier.statusTrade = enumData.BidSupplierTradeStatus.ChuaXacNhan.code
      newBidSupplier.statusPrice = enumData.BidSupplierPriceStatus.ChuaXacNhan.code
      newBidSupplier.scoreTech = newBidSupplier.scoreTrade = newBidSupplier.scorePrice = 0
      await newBidSupplier.save()

      // Tạo cho các item
      for (const item of lstItem) {
        // Nếu supplier chưa có supplierService thì tự tạo mới trạng thái 'ChuaDangKy'
        const supplierService = await this.supplierServiceRepo.findOne({
          where: { serviceId: item.serviceId, supplierId: supplierId, companyId: user.companyId },
        })
        if (!supplierService) {
          const newSupplierService = new SupplierServiceEntity()
          newSupplierService.companyId = user.companyId
          newSupplierService.createdBy = user.id
          newSupplierService.supplierId = supplierId
          newSupplierService.serviceId = item.serviceId
          newSupplierService.status = enumData.SupplierServiceStatus.ChuaDangKy.code
          newSupplierService.statusExpertise = enumData.SupplierServiceExpertiseStatus.ChuaDangKy.code
          await newSupplierService.save()
        }

        const newBidSupplierItem = new BidSupplierEntity()
        newBidSupplierItem.companyId = user.companyId
        newBidSupplierItem.createdBy = user.id
        newBidSupplierItem.supplierId = supplierId
        newBidSupplierItem.bidId = item.id
        newBidSupplierItem.serviceId = item.serviceId
        newBidSupplierItem.status = enumData.BidSupplierStatus.DaDuocChon.code
        newBidSupplierItem.statusFile = enumData.BidSupplierFileStatus.ChuaKiemTra.code
        newBidSupplierItem.statusTech = enumData.BidSupplierTechStatus.ChuaXacNhan.code
        newBidSupplierItem.statusTrade = enumData.BidSupplierTradeStatus.ChuaXacNhan.code
        newBidSupplierItem.statusPrice = enumData.BidSupplierPriceStatus.ChuaXacNhan.code
        newBidSupplierItem.scoreTech = newBidSupplierItem.scoreTrade = newBidSupplierItem.scorePrice = 0
        await newBidSupplierItem.save()
      }
    }

    await this.repo.update(data.bidId, {
      statusChooseSupplier: enumData.BidChooseSupplierStatus.DangChon.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.ChonNCC.code
    bidHistory.save()

    return { message: UPDATE_SUCCESS }
  }

  /** Chọn lại danh sách nhà cung cấp mời thầu */
  async bidRechooseSupplier(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionChooseSupplier(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const objCheck = await this.bidSupplierRepo.findOne({
      where: { bid: [{ id: bidId }, { parentId: bidId }], status: enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code, companyId: user.companyId },
    })
    if (objCheck) throw new MethodNotAllowedException('Chỉ được phép chọn lại Doanh nghiệp tham gia khi chưa có Doanh nghiệp nộp hồ sơ thầu.')

    await this.repo.update(bidId, {
      statusChooseSupplier: enumData.BidChooseSupplierStatus.DangChon.code,
      status: enumData.BidStatus.DangChonNCC.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.ChonLaiNCC.code
    bidHistory.save()
  }

  /** Gửi yêu cầu phê duyệt bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu */
  async bidSendMpoleaderCheck(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionChooseSupplier(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(bidId, {
      statusChooseSupplier: enumData.BidChooseSupplierStatus.DaChon.code,
      status: enumData.BidStatus.DangDuyetGoiThau.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.GuiMPOLeader.code
    bidHistory.save()

    this.emailService.GuiMpoLeadDuyetGia(bidId)
  }

  //#endregion

  //#region Accept all & send email invite supplier

  /** Kiểm tra quyền duyệt tất cả */
  async checkPermissionMpoAcceptAll(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const bid = await this.repo.findOne({
      where: [
        { id: bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false },
        { companyId: user.companyId, childs: { id: bidId, isDeleted: false } },
      ],
    })
    if (bid) {
      if (bid.status === enumData.BidStatus.DangDuyetGoiThau.code) {
        result = await this.bidEmployeeAccessRepo.isMPOLeader(user, bid.id)
        if (!result) {
          message = 'Bạn không có quyền xét duyệt bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu.'
        }
      } else {
        result = false
        message =
          'Gói thầu đã được xét duyệt bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu hoặc chưa khởi tạo xong yêu cầu kỹ thuật, bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Duyệt tất cả */
  async acceptAll(user: UserDto, data: { id: string; noteMPOLeader: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    const objPermission = await this.checkPermissionMpoAcceptAll(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const status = enumData.BidSupplierStatus.DaThongBaoMoiThau.code
    await this.bidSupplierRepo.update({ bidId: data.id }, { status, updatedBy: user.id })

    await this.repo.update(data.id, {
      status: enumData.BidStatus.DangNhanBaoGia.code,
      statusTrade: enumData.BidTradeStatus.DaDuyet.code,
      statusPrice: enumData.BidPriceStatus.DaDuyet.code,
      statusChooseSupplier: enumData.BidChooseSupplierStatus.DaDuyet.code,
      noteMPOLeader: data.noteMPOLeader || '',
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.DuyetGoiThau.code
    bidHistory.save()

    // gửi email
    this.emailService.ThongBaoDaDuyetGia(data.id)
    if (bid.isSendEmailInviteBid) {
      this.emailService.GuiNccThongBaoMoiThau(user, data.id)
    }

    return { message: UPDATE_SUCCESS }
  }

  /** Từ chối tất cả */
  async rejectAll(user: UserDto, data: { id: string; noteMPOLeader: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionMpoAcceptAll(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(data.id, {
      status: enumData.BidStatus.TuChoiGoiThau.code,
      statusTrade: enumData.BidTradeStatus.TuChoi.code,
      statusPrice: enumData.BidPriceStatus.TuChoi.code,
      statusChooseSupplier: enumData.BidChooseSupplierStatus.TuChoi.code,
      noteMPOLeader: data.noteMPOLeader || '',
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.TuChoiGoiThau.code
    bidHistory.save()

    // gửi email
    this.emailService.GuiMpoTuChoiGia(data.id)

    return { message: UPDATE_SUCCESS }
  }

  //#endregion

  //#region Mở thầu

  /** Kiểm tra quyền mở thầu */
  async checkPermissionOpenBid(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const bid = await this.repo.findOne({
      where: [
        { id: bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false },
        { companyId: user.companyId, childs: { id: bidId, isDeleted: false } },
      ],
    })
    if (bid) {
      if (bid.status === enumData.BidStatus.DangNhanBaoGia.code) {
        result = await this.bidEmployeeAccessRepo.isMPO_MPOLeader(user, bid.id)
        if (!result) {
          message = 'Bạn không có quyền mở thầu.'
        }
      } else {
        result = false
        message = 'Gói thầu đã được mở.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Load ds Doanh nghiệp khi mở thầu */
  async loadBidSupplierOpenBid(user: UserDto, data: { bidId: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const isGetListAccess = true
    const res: any = await this.repo.getBid1(user, data.bidId, isGetListAccess)
    res.bidOpenDate = new Date()
    if (res.status != enumData.BidStatus.DangNhanBaoGia.code) {
      res.bidOpenDate = res.bidOpenDate || res.startBidDate
    }
    // DS Doanh nghiệp được mời thầu
    res.listBidSupplier = await this.bidSupplierRepo.find({
      where: { bidId: data.bidId, companyId: user.companyId, isDeleted: false },
      relations: { supplier: true },
      select: { id: true, supplierId: true, status: true, supplier: { id: true, code: true, name: true } },
    })
    for (const item of res.listItem) {
      // Trạng thái nộp thầu các Doanh nghiệp theo từng Item
      item.listBidSupplier = await this.bidSupplierRepo.find({
        where: { bidId: item.id, companyId: user.companyId, isDeleted: false },
        select: { id: true, supplierId: true, status: true, isSuccessBid: true, statusFile: true },
      })
      for (const bidSupplierItem of item.listBidSupplier) {
        bidSupplierItem.isSubmitBid = false
        bidSupplierItem.statusName = 'Chưa bổ sung hồ sơ thầu'
        bidSupplierItem.isValid = bidSupplierItem.statusFile == enumData.BidSupplierFileStatus.HopLe.code
        if (
          bidSupplierItem.status === enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code ||
          bidSupplierItem.status === enumData.BidSupplierStatus.DangDanhGia.code ||
          bidSupplierItem.status === enumData.BidSupplierStatus.DaDanhGia.code
        ) {
          bidSupplierItem.isSubmitBid = true
          bidSupplierItem.statusName = 'Đã bổ sung hồ sơ thầu'
        }
      }
    }

    res.numOfSupplier = res.listBidSupplier.length
    res.numOfSupplierConfirmJoin = 0
    res.numOfSupplierSubmitBid = 0
    for (const bidSupplier of res.listBidSupplier) {
      bidSupplier.supplierCode = bidSupplier.__supplier__.code
      bidSupplier.supplierName = bidSupplier.__supplier__.name
      delete bidSupplier.__supplier__

      // Trạng thái nộp thầu các Item theo từng Doanh nghiệp
      bidSupplier.listItem = []
      for (const item of res.listItem) {
        const bidSupplierItem = item.listBidSupplier.find((c: any) => c.supplierId == bidSupplier.supplierId)
        if (bidSupplierItem) {
          item.statusName = bidSupplierItem.statusName
          item.isSubmitBid = bidSupplierItem.isSubmitBid
          item.isValid = bidSupplierItem.isValid
          item.bidSupplierId = bidSupplierItem.id

          bidSupplier.listItem.push({
            id: item.id,
            itemName: item.itemName,
            statusName: bidSupplierItem.statusName,
            isSubmitBid: bidSupplierItem.isSubmitBid,
            isValid: bidSupplierItem.isValid,
            bidSupplierId: bidSupplierItem.id,
          })
        }
      }

      // Lấy "Trạng thái nộp thầu Doanh nghiệp"
      const numItem = bidSupplier.listItem.length
      const numSubmitBid = bidSupplier.listItem.filter((c: any) => c.isSubmitBid).length
      const numValid = bidSupplier.listItem.filter((c: any) => c.isValid).length
      if (numSubmitBid == 0) {
        bidSupplier.statusSubmitBid = `Chưa nộp hồ sơ thầu`
      } else {
        res.numOfSupplierSubmitBid++
        bidSupplier.statusSubmitBid = `Đã nộp hồ sơ ${numSubmitBid}/${numItem} Item`
      }
      bidSupplier.statusSubmitValid = `${numValid}/${numSubmitBid} Hồ sơ`

      bidSupplier.statusConfirmJoinBid = ''
      if (bidSupplier.status == enumData.BidSupplierStatus.DaThongBaoMoiThau.code) {
        bidSupplier.statusConfirmJoinBid = 'Chưa xác nhận'
      } else if (bidSupplier.status == enumData.BidSupplierStatus.DaXacNhanKhongThamGiaThau.code) {
        bidSupplier.statusConfirmJoinBid = 'Từ chối tham gia'
      } else {
        res.numOfSupplierConfirmJoin++
        bidSupplier.statusConfirmJoinBid = 'Đồng ý tham gia'
      }
    }

    delete res.listItem
    return res
  }

  /** Xác nhận mở thầu */
  async openBid(user: UserDto, data: { bidId: string; lstData: any[]; lstEmployeeId: string[] }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionOpenBid(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    for (const bidSupplier of data.lstData) {
      for (const item of bidSupplier.listItem) {
        const bidSupplierItem = await this.bidSupplierRepo.findOne({
          where: { id: item.bidSupplierId, companyId: user.companyId },
          select: { id: true, status: true },
        })
        if (bidSupplierItem) {
          let status = bidSupplierItem.status
          let statusFile = enumData.BidSupplierFileStatus.KhongHopLe.code
          if (item.isValid) {
            status = enumData.BidSupplierStatus.DangDanhGia.code
            statusFile = enumData.BidSupplierFileStatus.HopLe.code
          }
          await this.bidSupplierRepo.update(item.bidSupplierId, {
            status,
            statusFile,
            note: item.note,
            updatedBy: user.id,
          })
        }
      }
    }

    await this.repo.update(data.bidId, {
      status: enumData.BidStatus.DangDanhGia.code,
      statusRateTech: enumData.BidTechRateStatus.DangTao.code,
      statusRateTrade: enumData.BidTradeRateStatus.DangTao.code,
      statusRatePrice: enumData.BidPriceRateStatus.DangTao.code,
      bidOpenDate: new Date(),
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.MoThau.code
    bidHistory.save()

    // gửi email nội bộ
    this.emailService.ThongBaoMoThau(data.bidId)

    // gửi email Doanh nghiệp
    this.emailService.ThongBaoMoThauNCC(data.bidId)

    return { message: 'Mở thầu thành công.' }
  }

  //#endregion

  //#region gửi email thông báo

  /** Kiểm tra quyền gửi email thông báo */
  async checkPermissionSendEmailBid(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const bid = await this.repo.findOne({
      where: [
        { id: bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false },
        { companyId: user.companyId, childs: { id: bidId, isDeleted: false } },
      ],
    })
    if (bid) {
      result = await this.bidEmployeeAccessRepo.isMPO_MPOLeader(user, bid.id)
      if (!result) {
        message = 'Bạn không có quyền gửi email thông báo nội bộ và Doanh nghiệp.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Lấy ds Doanh nghiệp được mời thầu */
  async findBidSupplier(user: UserDto, data: { bidId: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const lstData: any[] = await this.bidSupplierRepo.find({
      where: { bidId: data.bidId, companyId: user.companyId, isDeleted: false },
      relations: { supplier: true },
      select: { id: true, bidId: true, supplierId: true, supplier: { code: true, name: true } },
    })

    const res: any[] = []
    for (const item of lstData) {
      res.push({
        id: item.supplierId,
        code: item.__supplier__.code,
        name: item.__supplier__.name,
      })
    }

    return res
  }

  /** Lấy ds Doanh nghiệp khi nộp thầu trang admin */
  async loadBidSupplier(user: UserDto, data: { bidId: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res: any = {}
    // DS Doanh nghiệp được mời thầu
    res.listBidSupplier = await this.bidSupplierRepo.find({
      where: { bidId: data.bidId, companyId: user.companyId, isDeleted: false },
      relations: { supplier: true },
      select: { id: true, bidId: true, supplierId: true, supplier: { code: true, name: true } },
    })
    const setStatus = new Set()
    setStatus.add(enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code)
    setStatus.add(enumData.BidSupplierStatus.DangDanhGia.code)
    setStatus.add(enumData.BidSupplierStatus.DaDanhGia.code)

    for (const bidSupplier of res.listBidSupplier) {
      bidSupplier.supplierCode = bidSupplier.__supplier__.code
      bidSupplier.supplierName = bidSupplier.__supplier__.name
      delete bidSupplier.__supplier__

      // Trạng thái nộp thầu các Item theo từng Doanh nghiệp
      bidSupplier.listItem = await this.bidSupplierRepo.find({
        where: { bid: { parentId: bidSupplier.bidId, isDeleted: false }, supplierId: bidSupplier.supplierId, isDeleted: false },
        relations: { bid: { service: true } },
        select: {
          id: true,
          bidId: true,
          supplierId: true,
          status: true,
          isSuccessBid: true,
          statusFile: true,
          bid: { id: true, service: { code: true, name: true } },
        },
      })
      for (const item of bidSupplier.listItem) {
        item.itemName = item.__bid__.__service__.code + ' - ' + item.__bid__.__service__.name
        delete item.__bid__

        item.isValid = item.statusFile == enumData.BidSupplierFileStatus.HopLe.code
        item.isSubmitBid = false
        item.statusName = 'Chưa bổ sung hồ sơ thầu'
        if (setStatus.has(item.status)) {
          item.isSubmitBid = true
          item.statusName = 'Đã bổ sung hồ sơ thầu'
        }
      }

      // Lấy "Trạng thái nộp thầu Doanh nghiệp"
      const numItem = bidSupplier.listItem.length
      const numSubmitBid = bidSupplier.listItem.filter((c: any) => c.isSubmitBid).length
      const numValid = bidSupplier.listItem.filter((c: any) => c.isValid).length
      if (numSubmitBid == 0) {
        bidSupplier.statusSubmitBid = `Chưa nộp hồ sơ thầu`
      } else {
        bidSupplier.statusSubmitBid = `Đã nộp hồ sơ ${numSubmitBid}/${numItem} Item`
      }
      bidSupplier.statusSubmitValid = `${numValid}/${numSubmitBid} Hồ sơ`
    }

    return res
  }

  /** Gửi email nội bộ và NCC được mời tham gia thầu */
  async sendEmailBid(user: UserDto, data: { bidId: string; lstEmployeeId: string[]; lstSupplierId: string[]; emailContent: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionSendEmailBid(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    this.emailService.sendEmailBid(data)
  }

  //#endregion

  //#region Trang Client

  async paginationHomePage(req: Request, data: PaginationDto) {
    const companyId = await apeAuthApiHelper.getCompanyId(req)
    return await this.paginationHomePageHadToken({ companyId }, data)
  }

  async paginationHomePageHadToken(user: any, data: PaginationDto) {
    const lstBidStatus = [
      enumData.BidStatus.DangNhanBaoGia.code, // Đang nhận  báo giá
      enumData.BidStatus.DangDanhGia.code, // Đanh đánh giá
      enumData.BidStatus.DangDuyetDanhGia.code, // Đang duyệt đánh giá
      enumData.BidStatus.HoanTatDanhGia.code, // Hoàn tất đánh giá thầu
      enumData.BidStatus.DangDamPhanGia.code, // Đang đàm phán giá
      enumData.BidStatus.DongDauGia.code, // Đóng đấu giá
      enumData.BidStatus.DangDauGia.code, // Đang đấu giá
      enumData.BidStatus.DongDamPhanGia.code, // Đóng đàm phán giá
      enumData.BidStatus.DongThau.code, // Đang duyệt kết quả
      enumData.BidStatus.DuyetNCCThangThau.code, // Đã duyệt Doanh nghiệp thắng thầu
      enumData.BidStatus.DangDuyetKetThucThau.code, // Đang duyệt kết thúc thầu
      enumData.BidStatus.HoanTat.code, // Hoàn tất
      enumData.BidStatus.Huy.code, // Huỷ
    ]
    const whereCommon: any = { companyId: user.companyId, parentId: IsNull(), status: In(lstBidStatus), isDeleted: false }
    if (data.where.serviceId) {
      const whereCommonS: any = { companyId: user.companyId, isLast: true, isDeleted: false }
      const lstService = await this.serviceRepo.find({
        where: [
          { ...whereCommonS, id: data.where.serviceId },
          { ...whereCommonS, parentId: data.where.serviceId },
          { ...whereCommonS, parent: { parentId: data.where.serviceId } },
          { ...whereCommonS, parent: { parent: { parentId: data.where.serviceId } } },
        ],
        select: { id: true },
      })
      if (lstService.length == 0) return [[], 0]
      const lstServiceId = lstService.map((c) => c.id)
      whereCommon.childs = { serviceId: In(lstServiceId) }
    }
    let whereCon = whereCommon
    if (user.supplierId) {
      whereCon = [
        { ...whereCommon, isShowHomePage: true },
        { ...whereCommon, isShowHomePage: false, bidSuppliers: { supplierId: user.supplierId, isDeleted: false } },
      ]
    } else whereCon.isShowHomePage = true
    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip > 0 ? data.skip : 0,
      take: data.take > 1000 ? 10 : data.take,
      order: { startBidDate: 'DESC' },
      select: {
        id: true,
        code: true,
        name: true,
        status: true,
        submitEndDate: true,
        startBidDate: true,
        companyInvite: true,

        isShowHomePage: true,
      },
    })
    if (res[0].length == 0) return res

    // Đang phát hành
    // Mặc định
    // Đã mở thầu
    const lstStatus1 = [
      enumData.BidStatus.DangDanhGia.code,
      enumData.BidStatus.DangDuyetDanhGia.code,
      enumData.BidStatus.HoanTatDanhGia.code,
      enumData.BidStatus.DangDamPhanGia.code,
      enumData.BidStatus.DongDamPhanGia.code,
      enumData.BidStatus.DangDauGia.code,
      enumData.BidStatus.DongDauGia.code,
      enumData.BidStatus.DongThau.code,
      enumData.BidStatus.DuyetNCCThangThau.code,
      enumData.BidStatus.DangDuyetKetThucThau.code,
    ]
    // Đóng thầu
    const lstStatus2 = [enumData.BidStatus.HoanTat.code, enumData.BidStatus.Huy.code]
    let lstBidSupplier: any[] = []
    if (user.supplierId) {
      const lstId = res[0].map((c) => c.id)
      lstBidSupplier = await this.bidSupplierRepo.find({
        where: { bidId: In(lstId), supplierId: user.supplierId, status: Not(In(lstStatus2)), isDeleted: false },
        select: { id: true, bidId: true },
      })
    }

    for (const item of res[0]) {
      if (user.supplierId) {
        item.isAllowViewDetail = lstBidSupplier.some((c) => c.bidId == item.id)
      }

      const date = new Date(item.startBidDate)
      item.day = date.getDate()
      item.month = date.getMonth() + 1
      item.begin_time = new Date(item.submitEndDate)
      item.end_time = new Date(item.startBidDate)

      item.submit = 0
      if (lstStatus1.includes(item.status)) item.submit = 1
      if (lstStatus2.includes(item.status)) item.submit = 2
    }

    return res
  }

  /** Lấy data để bidding */
  async loadDataBidding(data: { bidId: string }, user: UserDto) {
    if (!user.supplierId) throw new UnauthorizedException('Phiên đăng nhập hết hạn, vui lòng đăng nhập và thử lại!')

    const res: any = await this.bidSupplierRepo.findOne({
      where: { bidId: data.bidId, supplierId: user.supplierId, companyId: user.companyId },
      relations: { bid: { childs: { service: true }, bidItems: true } },
    })
    if (!res) throw new Error('Bạn không được mời tham gia gói thầu, vui lòng kiểm tra lại!')

    const setType = new Set()
    setType.add(enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code)
    setType.add(enumData.BidSupplierStatus.DangDanhGia.code)
    setType.add(enumData.BidSupplierStatus.DaDanhGia.code)

    const bid = res.__bid__
    delete res.__bid__
    const lstChild = bid.__childs__.filter((c) => !c.isDeleted)
    res.bidCode = bid.code
    res.bidName = bid.name
    res.listItem = []
    const resBid: any = await this.repo.findOne({
      where: { id: data.bidId, companyId: user.companyId, isDeleted: false },
      relations: { bidType: true, masterBidGuarantee: true, childs: { service: true }, bidItems: true },
    })
    const bidItem = resBid.__bidItems__
    const totalQuantity = bidItem.reduce((sum, item) => sum + (item.quantityItem || 0), 0)

    for (const item of lstChild) {
      const bidSupplierItem = await this.bidSupplierRepo.findOne({
        where: { bidId: item.bidId, supplierId: user.supplierId, companyId: user.companyId },
        select: { id: true, status: true },
      })
      if (!bidSupplierItem) continue

      const dataItem: any = {
        id: item.id,
        itemName: item.__service__.name,
        quantityItem: totalQuantity,
        bidSupplierId: bidSupplierItem.id,
        isSubmitBid: false,
        statusName: 'Chưa bổ sung hồ sơ thầu',
      }
      const valuePrice = await bidSupplierItem.bidSupplierPriceValue
      if (setType.has(bidSupplierItem.status) || valuePrice) {
        dataItem.isSubmitBid = true
        dataItem.statusName = 'Đã bổ sung hồ sơ thầu'
      }

      res.listItem.push(dataItem)
    }

    return res
  }

  async bidDetailHadToken(data: { bidId: string }, user: UserDto) {
    if (!user.supplierId) throw new UnauthorizedException('Phiên đăng nhập hết hạn, vui lòng đăng nhập và thử lại!')

    const res: any = await this.repo.findOne({
      where: { id: data.bidId, companyId: user.companyId, isDeleted: false },
      relations: { bidType: true, masterBidGuarantee: true, childs: { service: true }, bidItems: true },
    })
    if (!res) throw new Error('Gói thầu không tồn tại, vui lòng kiểm tra lại link mời thầu!')

    // if (res.status == enumData.BidStatus.HoanTat.code || res.status == enumData.BidStatus.Huy.code) {
    //   throw new Error('Gói thầu đã đóng!')
    // }

    const bidSupplier = await this.bidSupplierRepo.findOne({
      where: { supplierId: user.supplierId, bidId: res.id, companyId: user.companyId },
      select: { id: true },
    })
    if (!bidSupplier) throw new Error('Bạn không được mời tham gia gói thầu, vui lòng kiểm tra lại!')

    res.bidTypeName = res.__bidType__?.name
    res.masterBidGuaranteeName = res.__masterBidGuarantee__?.name

    delete res.__bidType__
    delete res.__masterBidGuarantee__

    res.__bidItems__ = res.__bidItems__ || []
    res.listItem = []
    for (const item of res.__bidItems__) {
      res.listItem.push({
        itemName: item.productName,
        quantityItem: item.quantityItem,
      })
    }
    delete res.__bidItems__

    return res
  }

  /** Hàm kiểm tra xem có hiển thị nút bổ sung hồ sơ không */
  async isDisplayBtnBid(bidId: string = '', user: UserDto) {
    // Kiểm tra gói thầu có đang nhận giá hay không
    const bid = await this.repo.findOne({
      where: { id: bidId, status: enumData.BidStatus.DangNhanBaoGia.code, companyId: user.companyId },
      select: { id: true, submitEndDate: true },
    })
    // Nếu không thì không hiện nút
    if (!bid) return false

    // Nếu hết hạn gửi hồ sơ thì không hiện nút
    const today = new Date()
    if (today > bid.submitEndDate) return false

    // Kiểm tra đã xác nhận tham gia thầu chưa
    const bidSupplier = await this.bidSupplierRepo.findOne({
      where: {
        supplierId: user.supplierId,
        bidId: bid.id,
        status: enumData.BidSupplierStatus.DaXacNhanThamGiaThau.code,
        companyId: user.companyId,
      },
      select: { id: true },
    })
    if (!bidSupplier) return false

    return true
  }

  async isDisplayBtnAcceptBid(bidId: string = '', user: UserDto) {
    const bid = await this.repo.findOne({ where: { id: bidId, status: enumData.BidStatus.DangNhanBaoGia.code, companyId: user.companyId } })
    if (!bid) return false

    const today = new Date()
    if (today > bid.acceptEndDate) return false

    // Nếu gói thầu không công khai thì kiểm tra thêm Doanh nghiệp có được mời không!, nếu không thì không hiện
    if (!bid.isShowHomePage) {
      const bidSupplier = await this.bidSupplierRepo.findOne({
        where: { supplierId: user.supplierId, bidId: bid.id, status: enumData.BidSupplierStatus.DaThongBaoMoiThau.code, companyId: user.companyId },
      })
      if (!bidSupplier) return false
    }

    const lstStatus = [
      enumData.BidSupplierStatus.DaDuocChon.code,
      enumData.BidSupplierStatus.DaXacNhanKhongThamGiaThau.code,
      enumData.BidSupplierStatus.DaXacNhanThamGiaThau.code,
      enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
      enumData.BidSupplierStatus.DangDanhGia.code,
      enumData.BidSupplierStatus.DaDanhGia.code,
    ]
    // Kiểm tra Doanh nghiệp đã xác nhận hoặc từ chối chưa, nếu có thì không hiện btn
    const bidSupplier = await this.bidSupplierRepo.findOne({
      where: { supplierId: user.supplierId, bidId: bid.id, status: In(lstStatus), companyId: user.companyId },
    })
    if (bidSupplier) return false

    return true
  }

  async acceptBid(bidId: string = '', user: UserDto) {
    const bid = await this.repo.findOne({ where: { id: bidId, status: In([enumData.BidStatus.DangNhanBaoGia.code]), companyId: user.companyId } })

    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    const today = new Date()
    if (today > bid.acceptEndDate) {
      throw new Error('Hết hạn xác nhận tham gia đấu thầu.')
    }

    const check = await this.isDisplayBtnAcceptBid(bidId, user)

    if (!check) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    // Nếu gói thầu hạn chế thì cập nhật trạng thái
    if (!bid.isShowHomePage) {
      await this.bidSupplierRepo.update(
        { bidId, supplierId: user.supplierId },
        {
          status: enumData.BidSupplierStatus.DaXacNhanThamGiaThau.code,
          statusTech: enumData.BidSupplierTechStatus.DangBoSung.code,
          statusTrade: enumData.BidSupplierTradeStatus.DangBoSung.code,
          statusPrice: enumData.BidSupplierPriceStatus.DangBoSung.code,
          updatedBy: user.id,
        },
      )
    } else {
      // Nếu gói thầu công khai thì kiểm tra xem đã được mời chưa
      const check = await this.bidSupplierRepo.findOne({ where: { bidId, supplierId: user.supplierId, companyId: user.companyId } })

      // Nếu đã được mời thì cập nhật
      if (check) {
        await this.bidSupplierRepo.update(
          { bidId, supplierId: user.supplierId },
          {
            status: enumData.BidSupplierStatus.DaXacNhanThamGiaThau.code,
            statusTech: enumData.BidSupplierTechStatus.DangBoSung.code,
            statusTrade: enumData.BidSupplierTradeStatus.DangBoSung.code,
            statusPrice: enumData.BidSupplierPriceStatus.DangBoSung.code,
            updatedBy: user.id,
          },
        )
      } else {
        // Nếu chưa được mời thì thêm mới
        const entity = this.bidSupplierRepo.create({
          companyId: user.companyId,
          createdBy: user.id,
          bidId,
          supplierId: user.supplierId,
          status: enumData.BidSupplierStatus.DaXacNhanThamGiaThau.code,
          statusFile: enumData.BidSupplierFileStatus.ChuaKiemTra.code,
          statusTech: enumData.BidSupplierTechStatus.DangBoSung.code,
          statusTrade: enumData.BidSupplierTradeStatus.DangBoSung.code,
          statusPrice: enumData.BidSupplierPriceStatus.DangBoSung.code,
        })
        await this.bidSupplierRepo.save(entity)
      }
    }

    await this.emailService.GuiMpoNccXacNhanThamGiaThau(bidId, user.supplierId)

    return { message: UPDATE_SUCCESS }
  }

  async rejectBid(bidId: string = '', user: UserDto) {
    const bid = await this.repo.findOne({ where: { id: bidId, status: In([enumData.BidStatus.DangNhanBaoGia.code]), companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    const today = new Date()
    if (today > bid.acceptEndDate) {
      throw new Error('Hết hạn xác nhận tham gia đấu thầu.')
    }

    const check = await this.isDisplayBtnAcceptBid(bidId, user)

    if (!check) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    // Nếu gói thầu hạn chế thì cập nhật trạng thái
    if (!bid.isShowHomePage) {
      await this.bidSupplierRepo.update(
        { bidId, supplierId: user.supplierId },
        {
          status: enumData.BidSupplierStatus.DaXacNhanKhongThamGiaThau.code,
          statusTech: enumData.BidSupplierTechStatus.KhongXacNhan.code,
          statusTrade: enumData.BidSupplierTradeStatus.KhongXacNhan.code,
          statusPrice: enumData.BidSupplierPriceStatus.KhongXacNhan.code,
          updatedBy: user.id,
        },
      )
    } else {
      // Nếu gói thầu công khai thì kiểm tra xem đã được mời chưa
      const check = await this.bidSupplierRepo.findOne({ where: { bidId, supplierId: user.supplierId, companyId: user.companyId } })

      // Nếu đã được mời thì cập nhật
      if (check) {
        await this.bidSupplierRepo.update(
          { bidId, supplierId: user.supplierId },
          {
            status: enumData.BidSupplierStatus.DaXacNhanKhongThamGiaThau.code,
            statusTech: enumData.BidSupplierTechStatus.KhongXacNhan.code,
            statusTrade: enumData.BidSupplierTradeStatus.KhongXacNhan.code,
            statusPrice: enumData.BidSupplierPriceStatus.KhongXacNhan.code,
            updatedBy: user.id,
          },
        )
      } else {
        // Nếu chưa được mời thì thêm mới
        const entity = this.bidSupplierRepo.create({
          companyId: user.companyId,
          createdBy: user.id,
          bidId,
          supplierId: user.supplierId,
          status: enumData.BidSupplierStatus.DaXacNhanKhongThamGiaThau.code,
          statusTech: enumData.BidSupplierTechStatus.KhongXacNhan.code,
          statusTrade: enumData.BidSupplierTradeStatus.KhongXacNhan.code,
          statusPrice: enumData.BidSupplierPriceStatus.KhongXacNhan.code,
        })
        await this.bidSupplierRepo.save(entity)
      }
    }

    await this.emailService.GuiMpoNccTuChoiThamGiaThau(bidId, user.supplierId)

    return { message: UPDATE_SUCCESS }
  }

  async createBidSupplier(
    user: UserDto,
    data: {
      bidId: string
      techInfo: SupplierCreateTechItemDto[]
      tradeInfo: SupplierCreateTradeItemDto[]
      priceInfo: SupplierCreatePriceItemDto[]
      customPriceInfo: SupplierCreateCustomPriceItemDto[]
    },
  ) {
    if (!user.supplierId) throw new UnauthorizedException('Phiên đăng nhập hết hạn, vui lòng đăng nhập và thử lại!')

    await this.createBidSupplierFromAdmin(user, { ...data, supplierId: user.supplierId })
  }

  async createBidSupplierFromAdmin(
    user: UserDto,
    data: {
      bidId: string
      supplierId: string
      techInfo: SupplierCreateTechItemDto[]
      tradeInfo: SupplierCreateTradeItemDto[]
      priceInfo: SupplierCreatePriceItemDto[]
      customPriceInfo: SupplierCreateCustomPriceItemDto[]
    },
  ) {
    const bidItem = await this.repo.findOne({ where: { id: data.bidId, companyId: user.companyId, isDeleted: false } })
    if (!bidItem) throw new Error('Item không còn tồn tại!')

    const bid = await bidItem.parent

    const today = new Date()
    // if (today > bid.submitEndDate) throw new Error('Hết hạn nộp hồ sơ thầu.')

    await this.repo.manager.transaction(async (manager) => {
      const bidSupplierRepo = manager.getRepository(BidSupplierEntity)
      const bidSupplier = await bidSupplierRepo.findOne({
        where: { bidId: data.bidId, supplierId: data.supplierId, companyId: user.companyId, isDeleted: false },
      })
      if (!bidSupplier) throw new Error(ERROR_NOT_FOUND_DATA)

      await bidSupplierRepo.update(bidSupplier.id, {
        status: enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
        statusTech: enumData.BidSupplierTechStatus.DangDanhGia.code,
        statusTrade: enumData.BidSupplierTradeStatus.DangDanhGia.code,
        statusPrice: enumData.BidSupplierPriceStatus.DangDanhGia.code,
        updatedBy: user.id,
      })

      //#region Save Tech
      const bidSupplierTechValueRepo = manager.getRepository(BidSupplierTechValueEntity)
      // Xóa hồ sơ nộp thầu cũ
      await bidSupplierTechValueRepo.delete({ bidSupplierId: bidSupplier.id })
      for (let index = 0; index < data.techInfo.length; index++) {
        const item = data.techInfo[index]

        if (item.value || item.__childs__?.length > 0) {
          const bidTechValue = new BidSupplierTechValueEntity()
          bidTechValue.companyId = user.companyId
          bidTechValue.createdBy = user.id
          bidTechValue.bidSupplierId = bidSupplier.id
          bidTechValue.bidTechId = item.bidTechId
          bidTechValue.value = item.value
          await bidSupplierTechValueRepo.save(bidTechValue)

          const lengthC = item.__childs__.length
          for (let i = 0; i < lengthC; i++) {
            const itemC = item.__childs__[i]
            if (itemC.value) {
              let bidTechValueC = new BidSupplierTechValueEntity()
              bidTechValueC.companyId = user.companyId
              bidTechValueC.createdBy = user.id
              bidTechValueC.bidSupplierId = bidSupplier.id
              bidTechValueC.bidTechId = itemC.bidTechId
              bidTechValueC.value = itemC.value
              await bidSupplierTechValueRepo.save(bidTechValueC)
            }
          }
        }
      }
      //#endregion

      //#region Save Trade
      const bidSupplierTradeValueRepo = manager.getRepository(BidSupplierTradeValueEntity)
      // Xóa hồ sơ nộp thầu cũ
      await bidSupplierTradeValueRepo.delete({ bidSupplierId: bidSupplier.id })

      for (let index = 0; index < data.tradeInfo.length; index++) {
        const item = data.tradeInfo[index]
        if (item.value || item.__childs__?.length > 0) {
          const bidTradeValue = new BidSupplierTradeValueEntity()
          bidTradeValue.companyId = user.companyId
          bidTradeValue.createdBy = user.id
          bidTradeValue.bidSupplierId = bidSupplier.id
          bidTradeValue.bidTradeId = item.bidTradeId
          bidTradeValue.value = item.value
          await bidSupplierTradeValueRepo.save(bidTradeValue)

          const lengthC = item.__childs__.length
          for (let i = 0; i < lengthC; i++) {
            const itemC = item.__childs__[i]
            if (itemC.value) {
              let bidTradeValueC = new BidSupplierTradeValueEntity()
              bidTradeValueC.companyId = user.companyId
              bidTradeValueC.createdBy = user.id
              bidTradeValueC.bidSupplierId = bidSupplier.id
              bidTradeValueC.bidTradeId = itemC.bidTradeId
              bidTradeValueC.value = itemC.value
              await bidSupplierTradeValueRepo.save(bidTradeValueC)
            }
          }
        }
      }
      //#endregion

      //#region Save Price

      const bidSupplierPriceValueRepo = manager.getRepository(BidSupplierPriceValueEntity)
      const bidSupplierPriceColValueRepo = manager.getRepository(BidSupplierPriceColValueEntity)
      const bidSupplierPriceRepo = new BidSupplierPriceRepository(BidSupplierPriceEntity, manager)
      const lstBidPriceCol = (await bidItem.bidPriceCols).filter((c) => !c.isDeleted)
      // Xóa lần nộp giá cuối
      await bidSupplierPriceRepo.delete({ bidSupplierId: bidSupplier.id })
      // Xóa hồ sơ nộp thầu cũ
      await bidSupplierPriceValueRepo.delete({ bidSupplierId: bidSupplier.id })
      // Xóa hồ sơ nộp thầu cũ
      await bidSupplierPriceColValueRepo.delete({ bidSupplierId: bidSupplier.id })

      // lv1
      for (let index = 0; index < data.priceInfo.length; index++) {
        const item = data.priceInfo[index] as any
        item.submitDate = today
        item.submitType = 0
        item.level = 1

        for (const col of lstBidPriceCol) {
          if (col.type === enumData.DataType.Number.code && col.fomular?.length > 0) {
            const value = await coreHelper.calFomular(col.fomular, lstBidPriceCol, item)
            if (value != null) {
              const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
              bidSupplierPriceColValue.companyId = user.companyId
              bidSupplierPriceColValue.createdBy = user.id
              bidSupplierPriceColValue.value = value
              bidSupplierPriceColValue.bidPriceId = item.bidPriceId
              bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
              bidSupplierPriceColValue.bidPriceColId = col.id
              await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
              item[col.id] = value
            }
          } else {
            if (item[col.id]) {
              const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
              bidSupplierPriceColValue.companyId = user.companyId
              bidSupplierPriceColValue.createdBy = user.id
              bidSupplierPriceColValue.value = item[col.id]
              bidSupplierPriceColValue.bidPriceId = item.bidPriceId
              bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
              bidSupplierPriceColValue.bidPriceColId = col.id
              await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
            }
          }
        }

        if (bidItem.fomular && bidItem.fomular.length > 0) {
          item.value = await coreHelper.calFomular(bidItem.fomular, lstBidPriceCol, item)
        }

        if (item.value) {
          const bidPriceValue = new BidSupplierPriceValueEntity()
          bidPriceValue.companyId = user.companyId
          bidPriceValue.createdBy = user.id
          bidPriceValue.bidSupplierId = bidSupplier.id
          bidPriceValue.value = item.value
          bidPriceValue.bidPriceId = item.bidPriceId
          bidPriceValue.name = item.name
          bidPriceValue.unit = item.unit
          bidPriceValue.currency = item.currency
          bidPriceValue.number = item.number
          await bidSupplierPriceValueRepo.save(bidPriceValue)

          await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplier, item)
        }

        // lv2
        if (item.__childs__?.length > 0) {
          var priceInfoLv2 = item.__childs__
          for (let index2 = 0; index2 < priceInfoLv2.length; index2++) {
            const itemLv2 = priceInfoLv2[index2] as any
            itemLv2.submitDate = today
            itemLv2.submitType = 0
            itemLv2.level = 2

            for (const col of lstBidPriceCol) {
              if (col.type === enumData.DataType.Number.code && col.fomular?.length > 0) {
                const value = await coreHelper.calFomular(col.fomular, lstBidPriceCol, itemLv2)
                if (value != null) {
                  const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                  bidSupplierPriceColValue.companyId = user.companyId
                  bidSupplierPriceColValue.createdBy = user.id
                  bidSupplierPriceColValue.value = value
                  bidSupplierPriceColValue.bidPriceId = itemLv2.bidPriceId
                  bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                  bidSupplierPriceColValue.bidPriceColId = col.id
                  await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                  itemLv2[col.id] = value
                }
              } else {
                if (itemLv2[col.id]) {
                  const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                  bidSupplierPriceColValue.companyId = user.companyId
                  bidSupplierPriceColValue.createdBy = user.id
                  bidSupplierPriceColValue.value = itemLv2[col.id]
                  bidSupplierPriceColValue.bidPriceId = itemLv2.bidPriceId
                  bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                  bidSupplierPriceColValue.bidPriceColId = col.id
                  await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                }
              }
            }

            if (bidItem.fomular && bidItem.fomular.length > 0) {
              itemLv2.value = await coreHelper.calFomular(bidItem.fomular, lstBidPriceCol, itemLv2)
            }

            if (itemLv2.value) {
              const bidPriceValue = new BidSupplierPriceValueEntity()
              bidPriceValue.companyId = user.companyId
              bidPriceValue.createdBy = user.id
              bidPriceValue.bidSupplierId = bidSupplier.id
              bidPriceValue.value = itemLv2.value
              bidPriceValue.bidPriceId = itemLv2.bidPriceId
              bidPriceValue.name = itemLv2.name
              bidPriceValue.unit = itemLv2.unit
              bidPriceValue.currency = itemLv2.currency
              bidPriceValue.number = itemLv2.number
              await bidSupplierPriceValueRepo.save(bidPriceValue)

              await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplier, itemLv2)
            }

            // lv3
            if (itemLv2.__childs__?.length > 0) {
              var priceInfoLv3 = itemLv2.__childs__
              for (let index3 = 0; index3 < priceInfoLv3.length; index3++) {
                const itemLv3 = priceInfoLv3[index3] as any
                itemLv3.submitDate = today
                itemLv3.submitType = 0
                itemLv3.level = 3

                for (const col of lstBidPriceCol) {
                  if (col.type === enumData.DataType.Number.code && col.fomular?.length > 0) {
                    const value = await coreHelper.calFomular(col.fomular, lstBidPriceCol, itemLv3)
                    if (value != null) {
                      const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                      bidSupplierPriceColValue.companyId = user.companyId
                      bidSupplierPriceColValue.createdBy = user.id
                      bidSupplierPriceColValue.value = value
                      bidSupplierPriceColValue.bidPriceId = itemLv3.bidPriceId
                      bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                      bidSupplierPriceColValue.bidPriceColId = col.id
                      await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                      itemLv3[col.id] = value
                    }
                  } else {
                    if (itemLv3[col.id]) {
                      const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                      bidSupplierPriceColValue.companyId = user.companyId
                      bidSupplierPriceColValue.createdBy = user.id
                      bidSupplierPriceColValue.value = itemLv3[col.id]
                      bidSupplierPriceColValue.bidPriceId = itemLv3.bidPriceId
                      bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                      bidSupplierPriceColValue.bidPriceColId = col.id
                      await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                    }
                  }
                }

                if (bidItem.fomular && bidItem.fomular.length > 0) {
                  itemLv3.value = await coreHelper.calFomular(bidItem.fomular, lstBidPriceCol, itemLv3)
                }

                if (itemLv3.value) {
                  const bidPriceValue = new BidSupplierPriceValueEntity()
                  bidPriceValue.companyId = user.companyId
                  bidPriceValue.createdBy = user.id
                  bidPriceValue.bidSupplierId = bidSupplier.id
                  bidPriceValue.value = itemLv3.value
                  bidPriceValue.bidPriceId = itemLv3.bidPriceId
                  bidPriceValue.name = itemLv3.name
                  bidPriceValue.unit = itemLv3.unit
                  bidPriceValue.currency = itemLv3.currency
                  bidPriceValue.number = itemLv3.number
                  await bidSupplierPriceValueRepo.save(bidPriceValue)

                  await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplier, itemLv3)
                }
              }
            }
          }
        }
      }
      //#endregion

      //#region Save CustomPrice

      const bidSupplierCustomPriceValueRepo = manager.getRepository(BidSupplierCustomPriceValueEntity)
      // Xóa hồ sơ nộp thầu cũ
      await bidSupplierCustomPriceValueRepo.delete({ bidSupplierId: bidSupplier.id })

      for (let index = 0; index < data.customPriceInfo.length; index++) {
        const item = data.customPriceInfo[index]
        if (item.value) {
          let bidCustomPriceValue = new BidSupplierCustomPriceValueEntity()
          bidCustomPriceValue.companyId = user.companyId
          bidCustomPriceValue.createdBy = user.id
          bidCustomPriceValue.bidSupplierId = bidSupplier.id
          bidCustomPriceValue.value = item.value
          bidCustomPriceValue.name = item.name
          bidCustomPriceValue.unit = item.unit
          bidCustomPriceValue.currency = item.currency
          bidCustomPriceValue.number = item.number
          bidCustomPriceValue.sort = item.sort
          await bidSupplierCustomPriceValueRepo.save(bidCustomPriceValue)
        }
      }
      //#endregion
    })

    await this.emailService.GuiMpoNccNopHoSoThau(data.bidId, data.supplierId)

    return { message: UPDATE_SUCCESS }
  }

  async checkPermissionLoadDataBid(bidId: string, user: UserDto) {
    return await this.isDisplayBtnBid(bidId, user)
  }

  async loadDataBidTech(user: UserDto, data: { bidId: string; supplierId?: string }) {
    const bidSupplierTechValueRepo = this.repo.manager.getRepository(BidSupplierTechValueEntity)
    const listTech: any[] = await this.bidTechRepo.find({
      where: { bidId: data.bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false },
      relations: { bidTechListDetails: true, childs: { bidTechListDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })

    const supplierId = user.supplierId || data.supplierId
    if (supplierId && listTech.length > 0) {
      const bidSupplier = await this.bidSupplierRepo.findOne({
        where: { bidId: data.bidId, supplierId, companyId: user.companyId, isDeleted: false },
        select: { id: true, status: true },
      })
      if (!bidSupplier) return listTech

      // nếu đã nộp thầu => show các data đã nộp
      if (bidSupplier.status == enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code) {
        const dicValue: any = {}
        {
          const lstTechValue = await bidSupplierTechValueRepo.find({
            where: { bidSupplierId: bidSupplier.id, companyId: user.companyId, isDeleted: false },
          })
          lstTechValue.forEach((c) => (dicValue[c.bidTechId] = c.value))
        }

        for (const data1 of listTech) {
          data1.value = dicValue[data1.id] || ''
          for (const data2 of data1.__childs__) {
            data2.value = dicValue[data2.id] || ''
          }
        }
      }
    }

    return listTech
  }

  async loadDataBidTrade(user: UserDto, data: { bidId: string; supplierId?: string }) {
    const bidSupplierTradeValueRepo = this.repo.manager.getRepository(BidSupplierTradeValueEntity)

    const listTrade: any[] = await this.bidTradeRepo.find({
      where: { bidId: data.bidId, isDeleted: false, companyId: user.companyId, parentId: IsNull() },
      relations: { bidTradeListDetails: true, childs: { bidTradeListDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })

    const supplierId = user.supplierId || data.supplierId
    if (supplierId && listTrade.length > 0) {
      const bidSupplier = await this.bidSupplierRepo.findOne({
        where: { bidId: data.bidId, supplierId, companyId: user.companyId, isDeleted: false },
        select: { id: true, status: true },
      })
      if (!bidSupplier) return listTrade

      // nếu đã nộp thầu => show các data đã nộp
      if (bidSupplier.status == enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code) {
        const dicValue: any = {}
        {
          const lstTradeValue = await bidSupplierTradeValueRepo.find({
            where: { bidSupplierId: bidSupplier.id, companyId: user.companyId, isDeleted: false },
          })
          lstTradeValue.forEach((c) => (dicValue[c.bidTradeId] = c.value))
        }

        for (const data1 of listTrade) {
          data1.value = dicValue[data1.id] || ''
          for (const data2 of data1.__childs__) {
            data2.value = dicValue[data2.id] || ''
          }
        }
      }
    }

    return listTrade
  }

  async loadDataBidPrice(user: UserDto, data: { bidId: string; supplierId?: string }) {
    const res1: any[] = await this.bidPriceRepo.find({
      where: { bidId: data.bidId, parentId: IsNull(), companyId: user.companyId },
      relations: {
        bidPriceListDetails: true,
        bidPriceColValue: true,
        childs: { bidPriceListDetails: true, bidPriceColValue: true, childs: { bidPriceListDetails: true, bidPriceColValue: true } },
      },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } } },
    })
    const res2 = await this.bidPriceColRepo.getBidPriceColAll(user, data.bidId)

    const supplierId = user.supplierId || data.supplierId
    if (supplierId && res1.length > 0) {
      const bidSupplier = await this.bidSupplierRepo.findOne({
        where: { bidId: data.bidId, supplierId, companyId: user.companyId, isDeleted: false },
        select: { id: true, status: true },
      })
      if (!bidSupplier) return res1

      const getDataCell = (row: any, col: any, lstValue: any[] = []) => {
        row[col.id] = ''
        if (col.colType === enumData.ColType.MPO.code) {
          if (row.__bidPriceColValue__?.length > 0) {
            const cell = row.__bidPriceColValue__.find((c: any) => c.bidPriceColId === col.id)
            if (cell) row[col.id] = cell.value
          }
        } else {
          const cell = lstValue.find((c) => c.bidPriceColId === col.id && c.bidPriceId === row.id)
          if (cell) row[col.id] = cell.value
        }
      }

      // Show các data đã nộp
      const lstBidSupplierPriceColValue = await bidSupplier.bidSupplierPriceColValue
      const dicValue: any = {}
      {
        const lstPriceValue = await bidSupplier.bidSupplierPriceValue
        lstPriceValue.forEach((c) => (dicValue[c.bidPriceId] = c.value))
      }

      for (const data1 of res1) {
        data1.value = dicValue[data1.id] || ''
        for (const col of res2) {
          getDataCell(data1, col, lstBidSupplierPriceColValue)
        }
        for (const data2 of data1.__childs__) {
          data2.value = dicValue[data2.id] || ''
          for (const col of res2) {
            getDataCell(data2, col, lstBidSupplierPriceColValue)
          }
          for (const data3 of data2.__childs__) {
            data3.value = dicValue[data3.id] || ''
            for (const col of res2) {
              getDataCell(data3, col, lstBidSupplierPriceColValue)
            }
          }
        }
      }
    }

    const res3 = await this.repo.findOne({ where: { id: data.bidId, companyId: user.companyId }, select: { id: true, fomular: true } })
    return [res1, res2, res3]
  }

  async loadDataBidCustomPrice(user: UserDto, data: { bidId: string; supplierId?: string }) {
    const bidSupplierCustomPriceValueRepo = this.repo.manager.getRepository(BidSupplierCustomPriceValueEntity)

    let res: any[] = await this.bidCustomPriceRepo.find({
      where: { bidId: data.bidId, companyId: user.companyId },
      order: { sort: 'ASC', createdAt: 'ASC' },
    })

    const supplierId = user.supplierId || data.supplierId
    if (supplierId) {
      const bidSupplier = await this.bidSupplierRepo.findOne({
        where: { bidId: data.bidId, supplierId, companyId: user.companyId, isDeleted: false },
        select: { id: true, status: true },
      })
      if (!bidSupplier) return res

      // nếu đã nộp thầu => show các data đã nộp
      if (bidSupplier.status == enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code) {
        res = await bidSupplierCustomPriceValueRepo.find({
          where: { bidSupplierId: bidSupplier.id, companyId: user.companyId, isDeleted: false },
          order: { sort: 'ASC', createdAt: 'ASC' },
        })
      }
    }

    return res
  }

  /** Lịch sử đấu thầu Doanh nghiệp */
  async paginationBidHistory(user: UserDto, data: PaginationDto) {
    const whereCon: any = { supplierId: user.supplierId, companyId: user.companyId, isDeleted: false, bid: { parentId: IsNull() } }
    if (data.where.textFilter) {
      whereCon.bid = [
        { code: Like(`%${data.where.textFilter}%`), parentId: IsNull() },
        { name: Like(`%${data.where.textFilter}%`), parentId: IsNull() },
      ]
    }
    const res: any[] = await this.bidSupplierRepo.findAndCount({
      where: whereCon,
      relations: { supplier: true, bid: true },
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    })

    if (res[0].length == 0) return res

    // Đang phát hành
    const lstStatus1 = [
      enumData.BidStatus.DangDanhGia.code,
      enumData.BidStatus.DangDuyetDanhGia.code,
      enumData.BidStatus.HoanTatDanhGia.code,
      enumData.BidStatus.DangDamPhanGia.code,
      enumData.BidStatus.DongDamPhanGia.code,
      enumData.BidStatus.DangDauGia.code,
      enumData.BidStatus.DongDauGia.code,
      enumData.BidStatus.DongThau.code,
      enumData.BidStatus.DuyetNCCThangThau.code,
      enumData.BidStatus.DangDuyetKetThucThau.code,
    ]
    // Đóng thầu
    const lstStatus2 = [enumData.BidStatus.HoanTat.code, enumData.BidStatus.Huy.code]
    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }
    for (const item of res[0]) {
      item.bidCode = item.__bid__.code
      item.bidName = item.__bid__.name
      item.bidStatus = item.__bid__.status
      item.companyInvite = item.__bid__.companyInvite
      item.addressSubmit = item.__bid__.addressSubmit
      let bid: any
      if (item.__bid__.parentId) {
        bid = await this.repo.findOne({ where: { id: item.__bid__.parentId }, relations: { bidItems: true } })
      } else {
        bid = await this.repo.findOne({ where: { id: item.__bid__.id }, relations: { bidItems: true } })
      }

      delete item.__bid__

      item.supplierName = item.__supplier__.name
      delete item.__supplier__

      item.statusName = dicStatus[item.status]
      item.statusCode = item.status
      item.bidStatusName = 'Đang phát hành'
      if (lstStatus1.includes(item.bidStatus)) {
        item.bidStatusName = 'Đã mở thầu'
      }
      if (lstStatus2.includes(item.bidStatus)) {
        item.bidStatusName = 'Đóng thầu'
      }
      if (item.bidStatus == enumData.BidStatus.HoanTat.code) {
        item.isShowResult = true
      }

      item.lstItemSuccess = await this.bidSupplierRepo.find({
        where: { supplierId: item.supplierId, bid: { parentId: item.bidId, isDeleted: false }, isDeleted: false, isSuccessBid: true },
        relations: { bid: { service: true } },
        select: { id: true, bidId: true, bid: { id: true, quantityItem: true, service: { code: true, name: true } } },
      })
      if (item.lstItemSuccess.length > 0) item.isSuccessBid = true
      if (bid) {
        for (const item of bid.__bidItems__) {
          item.itemName = item.productName
          item.quantityItem = item.quantityItem
        }
      }
      item.lstItems = bid.__bidItems__
      for (const itemS of item.lstItemSuccess) {
        itemS.itemName = itemS.__bid__.__service__.code + ' - ' + itemS.__bid__.__service__.name
        itemS.quantityItem = itemS.__bid__.quantityItem
        delete itemS.__bid__
      }
    }

    return res
  }

  async checkPermissionJoinResetPrice(bidId: string, user: UserDto) {
    const bidSupplier: any = await this.bidSupplierRepo.findOne({
      where: { bidId, supplierId: user.supplierId, companyId: user.companyId },
      relations: { bid: true },
      select: { id: true, statusResetPrice: true, bid: { id: true, statusResetPrice: true, resetPriceEndDate: true } },
    })
    if (!bidSupplier) return false

    const bid = bidSupplier.__bid__
    const todate = new Date()
    if (bid.resetPriceEndDate && bid.resetPriceEndDate < todate) return false

    if (
      bid.statusResetPrice == enumData.BidResetPriceStatus.DaTao.code &&
      bidSupplier.statusResetPrice == enumData.BidSupplierResetPriceStatus.YeuCauBoSung.code
    ) {
      return true
    }

    return false
  }

  /** Doanh nghiệp nộp chào giá bổ sung cho gói thầu */
  async supplierSaveResetPrice(
    user: UserDto,
    data: {
      bidId: string
      dataInfo: { filePriceDetail?: string; fileTechDetail?: string }
      priceInfo: SupplierCreatePriceItemDto[]
    },
  ) {
    if (!user.supplierId) throw new NotFoundException('Không có quyền truy cập')

    const bid = await this.repo.findOne({ where: { id: data.bidId, companyId: user.companyId } })
    if (!bid) throw new Error('Gói thầu không tồn tại, vui lòng kiểm tra lại link yêu cầu nộp chào giá bổ sung!')

    const today = new Date()
    if (bid.resetPriceEndDate && bid.resetPriceEndDate < today) {
      throw new Error('Hết hạn nộp chào giá bổ sung cho gói thầu!')
    }
    const check = await this.checkPermissionJoinResetPrice(data.bidId, user)
    if (!check) throw new Error('Bạn không có quyền truy cập hoặc đã ngưng nộp giá bổ sung cho gói thầu')

    await this.repo.manager.transaction(async (manager) => {
      const bidSupplierRepo = manager.getRepository(BidSupplierEntity)
      const bidSupplier = await bidSupplierRepo.findOne({
        where: { bidId: data.bidId, supplierId: user.supplierId, companyId: user.companyId },
        relations: { bid: true },
      })
      if (!bidSupplier) throw new Error(ERROR_NOT_FOUND_DATA)

      await bidSupplierRepo.update(bidSupplier.id, {
        statusResetPrice: enumData.BidSupplierResetPriceStatus.DaBoSung.code,
        filePriceDetail: data.dataInfo.filePriceDetail,
        fileTechDetail: data.dataInfo.fileTechDetail,
        updatedBy: user.id,
      })

      //#region Save Price

      const bidSupplierPriceValueRepo = manager.getRepository(BidSupplierPriceValueEntity)
      const bidSupplierPriceColValueRepo = manager.getRepository(BidSupplierPriceColValueEntity)
      const bidSupplierPriceRepo = new BidSupplierPriceRepository(BidSupplierPriceEntity, manager)
      const lstBidPriceCol = (await bid.bidPriceCols).filter((c) => !c.isDeleted)
      await bidSupplierPriceRepo.delete({ bidSupplierId: bidSupplier.id })

      // lv1
      for (let index = 0; index < data.priceInfo.length; index++) {
        const item = data.priceInfo[index] as any
        item.submitDate = today
        item.submitType = 0
        item.level = 1

        for (const col of lstBidPriceCol) {
          if (col.type === enumData.DataType.Number.code && col.fomular?.length > 0) {
            const value = await coreHelper.calFomular(col.fomular, lstBidPriceCol, item)
            if (value != null) {
              const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
              bidSupplierPriceColValue.companyId = user.companyId
              bidSupplierPriceColValue.createdBy = user.id
              bidSupplierPriceColValue.value = value
              bidSupplierPriceColValue.bidPriceId = item.bidPriceId
              bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
              bidSupplierPriceColValue.bidPriceColId = col.id
              await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
              item[col.id] = value
            }
          } else {
            if (item[col.id]) {
              const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
              bidSupplierPriceColValue.companyId = user.companyId
              bidSupplierPriceColValue.createdBy = user.id
              bidSupplierPriceColValue.value = item[col.id]
              bidSupplierPriceColValue.bidPriceId = item.bidPriceId
              bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
              bidSupplierPriceColValue.bidPriceColId = col.id
              await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
            }
          }
        }

        if (bid.fomular && bid.fomular.length > 0) {
          item.value = await coreHelper.calFomular(bid.fomular, lstBidPriceCol, item)
        }

        if (item.value) {
          const bidPriceValue = new BidSupplierPriceValueEntity()
          bidPriceValue.companyId = user.companyId
          bidPriceValue.createdBy = user.id
          bidPriceValue.bidSupplierId = bidSupplier.id
          bidPriceValue.value = item.value
          bidPriceValue.bidPriceId = item.bidPriceId
          bidPriceValue.name = item.name
          bidPriceValue.unit = item.unit
          bidPriceValue.currency = item.currency
          bidPriceValue.number = item.number
          await bidSupplierPriceValueRepo.save(bidPriceValue)

          await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplier, item)
        }

        // lv2
        if (item.__childs__?.length > 0) {
          var priceInfoLv2 = item.__childs__
          for (let index2 = 0; index2 < priceInfoLv2.length; index2++) {
            const itemLv2 = priceInfoLv2[index2] as any
            itemLv2.submitDate = today
            itemLv2.submitType = 0
            itemLv2.level = 2

            for (const col of lstBidPriceCol) {
              if (col.type === enumData.DataType.Number.code && col.fomular?.length > 0) {
                const value = await coreHelper.calFomular(col.fomular, lstBidPriceCol, itemLv2)
                if (value != null) {
                  const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                  bidSupplierPriceColValue.companyId = user.companyId
                  bidSupplierPriceColValue.createdBy = user.id
                  bidSupplierPriceColValue.value = value
                  bidSupplierPriceColValue.bidPriceId = itemLv2.bidPriceId
                  bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                  bidSupplierPriceColValue.bidPriceColId = col.id
                  await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                  itemLv2[col.id] = value
                }
              } else {
                if (itemLv2[col.id]) {
                  const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                  bidSupplierPriceColValue.companyId = user.companyId
                  bidSupplierPriceColValue.createdBy = user.id
                  bidSupplierPriceColValue.value = itemLv2[col.id]
                  bidSupplierPriceColValue.bidPriceId = itemLv2.bidPriceId
                  bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                  bidSupplierPriceColValue.bidPriceColId = col.id
                  await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                }
              }
            }

            if (bid.fomular && bid.fomular.length > 0) {
              itemLv2.value = await coreHelper.calFomular(bid.fomular, lstBidPriceCol, itemLv2)
            }

            if (itemLv2.value) {
              const bidPriceValue = new BidSupplierPriceValueEntity()
              bidPriceValue.companyId = user.companyId
              bidPriceValue.createdBy = user.id
              bidPriceValue.bidSupplierId = bidSupplier.id
              bidPriceValue.value = itemLv2.value
              bidPriceValue.bidPriceId = itemLv2.bidPriceId
              bidPriceValue.name = itemLv2.name
              bidPriceValue.unit = itemLv2.unit
              bidPriceValue.currency = itemLv2.currency
              bidPriceValue.number = itemLv2.number
              await bidSupplierPriceValueRepo.save(bidPriceValue)

              await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplier, itemLv2)
            }

            // lv3
            if (itemLv2.__childs__?.length > 0) {
              var priceInfoLv3 = itemLv2.__childs__
              for (let index3 = 0; index3 < priceInfoLv3.length; index3++) {
                const itemLv3 = priceInfoLv3[index3] as any
                itemLv3.submitDate = today
                itemLv3.submitType = 0
                itemLv3.level = 3

                for (const col of lstBidPriceCol) {
                  if (col.type === enumData.DataType.Number.code && col.fomular?.length > 0) {
                    const value = await coreHelper.calFomular(col.fomular, lstBidPriceCol, itemLv3)
                    if (value != null) {
                      const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                      bidSupplierPriceColValue.companyId = user.companyId
                      bidSupplierPriceColValue.createdBy = user.id
                      bidSupplierPriceColValue.value = value
                      bidSupplierPriceColValue.bidPriceId = itemLv3.bidPriceId
                      bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                      bidSupplierPriceColValue.bidPriceColId = col.id
                      await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                      itemLv3[col.id] = value
                    }
                  } else {
                    if (itemLv3[col.id]) {
                      const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                      bidSupplierPriceColValue.companyId = user.companyId
                      bidSupplierPriceColValue.createdBy = user.id
                      bidSupplierPriceColValue.value = itemLv3[col.id]
                      bidSupplierPriceColValue.bidPriceId = itemLv3.bidPriceId
                      bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                      bidSupplierPriceColValue.bidPriceColId = col.id
                      await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                    }
                  }
                }

                if (bid.fomular && bid.fomular.length > 0) {
                  itemLv3.value = await coreHelper.calFomular(bid.fomular, lstBidPriceCol, itemLv3)
                }

                if (itemLv3.value) {
                  const bidPriceValue = new BidSupplierPriceValueEntity()
                  bidPriceValue.companyId = user.companyId
                  bidPriceValue.createdBy = user.id
                  bidPriceValue.bidSupplierId = bidSupplier.id
                  bidPriceValue.value = itemLv3.value
                  bidPriceValue.bidPriceId = itemLv3.bidPriceId
                  bidPriceValue.name = itemLv3.name
                  bidPriceValue.unit = itemLv3.unit
                  bidPriceValue.currency = itemLv3.currency
                  bidPriceValue.number = itemLv3.number
                  await bidSupplierPriceValueRepo.save(bidPriceValue)

                  await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplier, itemLv3)
                }
              }
            }
          }
        }
      }
      //#endregion
    })

    // email gửi cho nhân viên phụ trách, MPOLeader mua hàng khi có nhà cung cấp nộp chào giá bổ sung
    await this.emailService.GuiMpoNccNopChaoGiaBosung(data.bidId, user.supplierId)

    return { message: UPDATE_SUCCESS }
  }

  /** Cảnh báo khi gần hết hạn thiết lập và đánh giá Gói Thầu */
  public async autoCreateWarningComingExpirySettingEvalution() {
    return this.repo.manager.transaction(async (manager) => {
      try {
        const dataType = enumData.DataWarningType.Bid.code
        const warningType = enumData.WarningType.Bid_Expiry_Setting_Evalution
        await manager.getRepository(EmployeeWarningEntity).delete({
          dataId: Not(IsNull()),
          dataType: dataType,
          warningType: warningType.code,
        })

        const dFrom = new Date(new Date().setHours(0, 0, 0, 0))
        let dTo = new Date(new Date().setDate(new Date().getDate() + 7))
        dTo = new Date(new Date(dTo).setHours(23, 59, 59, 59))

        const condition: any = new Object()
        condition.isDeleted = false
        condition.status = Not(In([enumData.BidStatus.Huy.code, enumData.BidStatus.HoanTat.code, enumData.BidStatus.DongThau.code]))

        const where1 = {
          ...condition,
          ...{
            timeTechDate: Raw(
              (alias) =>
                `DATE(${alias}) BETWEEN DATE("${moment(dFrom).format('YYYY-MM-DD HH:MM:SS')}") AND DATE("${moment(dTo).format(
                  'YYYY-MM-DD HH:MM:SS',
                )}")`,
            ),
          },
        }
        const where2 = {
          ...condition,
          ...{
            timePriceDate: Raw(
              (alias) =>
                `DATE(${alias}) BETWEEN DATE("${moment(dFrom).format('YYYY-MM-DD HH:MM:SS')}") AND DATE("${moment(dTo).format(
                  'YYYY-MM-DD HH:MM:SS',
                )}")`,
            ),
          },
        }
        const where3 = {
          ...condition,
          ...{
            timeCheckTechDate: Raw(
              (alias) =>
                `DATE(${alias}) BETWEEN DATE("${moment(dFrom).format('YYYY-MM-DD HH:MM:SS')}") AND DATE("${moment(dTo).format(
                  'YYYY-MM-DD HH:MM:SS',
                )}")`,
            ),
          },
        }
        const where4 = {
          ...condition,
          ...{
            timeCheckPriceDate: Raw(
              (alias) =>
                `DATE(${alias}) BETWEEN DATE("${moment(dFrom).format('YYYY-MM-DD HH:MM:SS')}") AND DATE("${moment(dTo).format(
                  'YYYY-MM-DD HH:MM:SS',
                )}")`,
            ),
          },
        }

        const lstBid = await manager.getRepository(BidEntity).find({
          where: [where1, where2, where3, where4],
        })
        if (lstBid.length > 0) {
          let html = warningType.default
          let subject = warningType.name
          const template = await manager.getRepository(EmailTemplateEntity).findOne({ where: { code: warningType.code, isDeleted: false } })
          if (template) {
            html = template.description
            subject = template.name
          }
          let lstUserId = lstBid.map((s: any) => s.createdBy)
          lstUserId = Array.from(new Set(lstUserId))
          const lstUser = await manager.getRepository(UserEntity).find({
            where: { id: In(lstUserId), isDeleted: false },
            relations: ['employee'],
          })

          for await (const bid of lstBid) {
            let createdBy = lstUser.find((s: any) => s.id == bid.createdBy)
            if (createdBy && createdBy.employeeId) {
              let emp = await createdBy.employee
              let title = ''
              if (dFrom.getTime() <= new Date(bid.timeTechDate).getTime() && new Date(bid.timeTechDate).getTime() <= dTo.getTime()) {
                title = 'Thiết Lập Yêu Cầu Kỹ Thuật Và Năng Lực'
              } else if (dFrom.getTime() <= new Date(bid.timePriceDate).getTime() && new Date(bid.timePriceDate).getTime() <= dTo.getTime()) {
                title = 'Thiết Lập Các Hạng Mục Báo Giá, Cơ Cấu Giá Và Điều Kiện Thương Mại'
              } else if (dFrom.getTime() <= new Date(bid.timeCheckTechDate).getTime() && new Date(bid.timeCheckTechDate).getTime() <= dTo.getTime()) {
                title = 'Đánh Giá Yêu Cầu Kỹ Thuật Và Năng Lực'
              } else if (
                dFrom.getTime() <= new Date(bid.timeCheckPriceDate).getTime() &&
                new Date(bid.timeCheckPriceDate).getTime() <= dTo.getTime()
              ) {
                title = 'Đánh Giá Các Hạng Mục Báo Giá, Cơ Cấu Giá Và Điều Kiện Thương Mại'
              }

              const subject_text = coreHelper.stringInject(subject, [title, bid.code])
              // let link = `&nbsp; <button onclick="showDataDetail('${dataType}', '${pp.id}')">Xem Chi Tiết</button>`

              let content = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [emp.name, bid.name, bid.code])

              const w1 = new EmployeeWarningEntity()
              w1.warningType = warningType.code
              w1.dataType = dataType
              w1.dataId = bid.id
              w1.message = subject_text || ''
              w1.messageFull = content || ''
              w1.employeeId = createdBy.employeeId
              await manager.getRepository(EmployeeWarningEntity).save(w1)
            }
          }
        }
      } catch (error) {
        throw error
      }
    })
  }

  //#endregion
}
