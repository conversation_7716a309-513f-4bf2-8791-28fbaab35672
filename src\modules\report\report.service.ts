import { Injectable, NotFoundException, NotAcceptableException, BadRequestException } from '@nestjs/common'
import {
  BidEmployeeAccessRepository,
  BidPriceRepository,
  BidRepository,
  BidSupplierRepository,
  ServiceRepository,
  SupplierRepository,
  SupplierServiceRepository,
} from '../../repositories'
import { In, IsNull, Like, Raw } from 'typeorm'
import { enumData, ERROR_YOU_DO_NOT_HAVE_PERMISSION } from '../../constants'
import { UserDto } from '../../dto'
import * as moment from 'moment'
import {
  SupplierExpertiseEntity,
  ServiceAccessEntity,
  BidSupplierPriceValueEntity,
  BidDealSupplierEntity,
  BidAuctionSupplierEntity,
  EmployeeEntity,
} from '../../entities'
import { coreHelper } from '../../helpers'
import { PaginationDto } from '../../dto'

@Injectable()
export class ReportService {
  constructor(
    private readonly bidRepo: BidRepository,
    private readonly supplierRepo: SupplierRepository,
    private readonly bidSupplierRepo: BidSupplierRepository,
    private readonly bidPriceRepo: BidPriceRepository,
    private readonly bidEmployeeAccessRepo: BidEmployeeAccessRepository,
    private readonly serviceRepo: ServiceRepository,
    private readonly supplierServiceRepo: SupplierServiceRepository,
  ) {}

  async getReportSuppliers(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data) throw new NotFoundException()

    let whereCon: any = { companyId: user.companyId }
    if (data.where.serviceId) {
      whereCon.serviceId = data.where.serviceId
    }

    if (data.where.statusExpertise) {
      whereCon.statusExpertise = data.where.statusExpertise
    }

    if (data.where.status) {
      whereCon.status = data.where.status
    } else {
      whereCon.status = In([
        enumData.SupplierServiceStatus.MoiDangKy.code,
        enumData.SupplierServiceStatus.CapNhatThongTin.code,
        enumData.SupplierServiceStatus.PhuTrachDuyet.code,
        enumData.SupplierServiceStatus.PhuTrachKhongDuyet.code,
        enumData.SupplierServiceStatus.KhongDuyet.code,
        enumData.SupplierServiceStatus.DaDuyet.code,
        enumData.SupplierServiceStatus.NgungHoatDong.code,
      ])
    }

    if (data.where.fromDate && data.where.toDate) {
      whereCon.createdAt = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.fromDate).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.toDate).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.fromDate && !data.where.toDate) {
      whereCon.createdAt = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.fromDate).format('YYYY-MM-DD')}")`)
    } else if (data.where.toDate && !data.where.fromDate) {
      whereCon.createdAt = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.toDate).format('YYYY-MM-DD')}")`)
    }

    if (data.where.supplierName)
      whereCon.supplier = [
        {
          code: Like(`%${data.where.supplierName}%`),
        },
        {
          name: Like(`%${data.where.supplierName}%`),
        },
      ]
    const res: any[] = await this.supplierServiceRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { supplier: true, service: true },
      order: { createdAt: 'DESC' },
    })
    if (res[0].length == 0) return res
    const dicStatus: any = {}
    const dicStatusColor: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.SupplierServiceStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
      lstStatus.forEach((c) => (dicStatusColor[c.code] = c.color))
    }
    const lstServiceId = res[0].map((c) => c.serviceId).filter((value, index, self) => self.indexOf(value) === index)
    const lstApproveById = res[0].map((c) => c.__service__.approveById).filter((value, index, self) => value && self.indexOf(value) === index)
    const dicServiceAccessName: any = {}
    const dicServiceApproveName: any = {}
    {
      const lstServiceAccess: any[] = await this.serviceRepo.manager.getRepository(ServiceAccessEntity).find({
        where: {
          serviceId: In(lstServiceId),
          companyId: user.companyId,
          isDeleted: false,
        },
        relations: { employee: true },
        select: { id: true, serviceId: true, employee: { id: true, name: true } },
      })
      lstServiceAccess.forEach((c) => {
        if (!dicServiceAccessName[c.serviceId]) dicServiceAccessName[c.serviceId] = []
        dicServiceAccessName[c.serviceId].push(c.__employee__.name)
      })

      if (lstApproveById.length > 0) {
        const lstApprove = await this.serviceRepo.manager
          .getRepository(EmployeeEntity)
          .find({ where: { id: In(lstApproveById) }, select: { id: true, name: true } })
        lstApprove.forEach((c) => (dicServiceApproveName[c.id] = c.name))
      }
    }
    for (const item of res[0]) {
      const expertise = await this.supplierRepo.manager.getRepository(SupplierExpertiseEntity).findOne({
        where: { supplierServiceId: item.id, companyId: user.companyId, isDeleted: false },
        order: { createdAt: 'DESC' },
        select: { changeDate: true },
      })
      item.supplierName = item.__supplier__.name
      item.itemName = item.__service__.code + ' - ' + item.__service__.name
      const lstAccess = dicServiceAccessName[item.serviceId]
      if (lstAccess?.length > 0) item.acceptEmployeeName = lstAccess.join(', ')
      item.approveByName = ''
      if (item.__service__.approveById) {
        item.approveByName = dicServiceApproveName[item.__service__.approveById]
      }
      item.lastUpdateExpertise = expertise?.changeDate
      item.statusName = dicStatus[item.status]
      item.statusColor = dicStatusColor[item.status]
      delete item.__supplier__
      delete item.__service__
    }

    return res
  }

  async getReportExpertise(user: UserDto, data: PaginationDto) {
    let whereCon: any = { companyId: user.companyId }
    if (data.where.supplierName) whereCon.supplier = [{ code: Like(`%${data.where.supplierName}%`) }, { name: Like(`%${data.where.supplierName}%`) }]
    if (data.where.serviceId) whereCon.serviceId = data.where.serviceId
    if (data.where.expertiseStatus && data.where.expertiseStatus.length > 0) whereCon.status = In(data.where.expertiseStatus)
    if (data.where.lawStatus) whereCon.statusLaw = data.where.lawStatus
    if (data.where.capacityStatus) whereCon.statusCapacity = data.where.capacityStatus
    if (data.where.fromDate && data.where.toDate) {
      whereCon.changeDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.fromDate).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.toDate).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.fromDate && !data.where.toDate) {
      whereCon.changeDate = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.fromDate).format('YYYY-MM-DD')}")`)
    } else if (data.where.toDate && !data.where.fromDate) {
      whereCon.changeDate = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.toDate).format('YYYY-MM-DD')}")`)
    }

    const res: any[] = await this.supplierRepo.manager.getRepository(SupplierExpertiseEntity).findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { supplier: true, service: { serviceAccess: { employee: true } }, approvedLaw: true },
    })

    for (let item of res[0]) {
      item.supplierName = item.__supplier__.name
      item.statusLawName = enumData.SupplierExpertiseLawStatus[item.statusLaw]?.name || ''
      item.statusCapacityName = enumData.SupplierExpertiseCapacityStatus[item.statusCapacity]?.name || ''
      delete item.__supplier__
      item.itemName = item.__service__.code + ' - ' + item.__service__.name
      const lstAccess = item.__service__.__serviceAccess__
      delete item.__service__
      item.finish = true
      item.isApproved = false
      if (item.isCheckLaw && item.statusLaw !== enumData.SupplierExpertiseLawStatus.DaThamDinh.code) {
        item.finish = false
      }
      if (item.isCheckCapacity && item.statusCapacity !== enumData.SupplierExpertiseCapacityStatus.DaThamDinh.code) {
        item.finish = false
      }
      if (lstAccess.some((c) => c.employeeId === user.employeeId)) {
        item.isApproved = true
      }

      if (item.approvedLawId) {
        item.approvedLawName = item.__approvedLaw__.name
      }
      delete item.__approvedLaw__

      item.approvedCapacityName = lstAccess.map((c) => c.__employee__.name).join(', ')
    }

    return res
  }

  /** Báo cáo gói thầu */
  async getReportBid(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    let whereCon: any = { parentId: IsNull(), companyId: user.companyId, isDeleted: false }

    if (data.where.bidIds && data.where.bidIds.length === 0) {
      whereCon.id = In(data.where.bidIds)
    }

    if (data.where.serviceId) whereCon.childs = { serviceId: data.where.serviceId }

    let listBidStatus = [enumData.BidStatus.HoanTat.code]
    if (data.where.status?.length > 0) listBidStatus = data.where.status
    whereCon.status = In(listBidStatus)

    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.publicDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.dateFrom) {
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}")`)
    } else if (data.where.dateTo) {
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.dateTo).format('YYYY-MM-DD')}")`)
    }

    // Tìm theo mã số hoặc tên gói thầu
    if (data.where.name) {
      whereCon = [
        { ...whereCon, code: Like(`%${data.where.name}%`) },
        { ...whereCon, name: Like(`%${data.where.name}%`) },
      ]
    }

    const result: any = await this.bidRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { service: { parent: true }, employeeAccess: { employee: true } },
      order: { createdAt: 'DESC' },
    })
    if (result[0].length == 0) return result

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }
    for (const item of result[0]) {
      item.statusName = dicStatus[item.status]

      const objTech = item.__employeeAccess__.find((c) => c.type === enumData.BidRuleType.Tech.code)
      item.techName = objTech?.__employee__?.name || ''
      const objMpo = item.__employeeAccess__.find((c) => c.type === enumData.BidRuleType.MPO.code)
      item.mpoName = objMpo?.__employee__?.name || ''

      delete item.__employeeAccess__
    }

    return result
  }

  //#region Báo cáo lịch sử giá theo các tiêu chí

  /** Lấy giá mới nhất của Doanh nghiệp */
  async getBidHistoryPrice(user: UserDto, bidSupplierId: string) {
    const bidSupplier = await this.bidSupplierRepo.findOne({ where: { id: bidSupplierId, companyId: user.companyId } })
    if (!bidSupplier) return {}
    const bid = await bidSupplier.bid

    // Lấy template hồ sơ giá của gói thầu
    const lstBidPrice = await this.bidPriceRepo.getPrice(user, bidSupplier.bidId)

    //#region Đấu giá
    const lstBidAuction = await bid.bidAuctions
    if (lstBidAuction && lstBidAuction.length > 0) {
      const bidAuction = lstBidAuction[0]
      // check xem Doanh nghiệp tham gia đấu thầu không
      const bidAuctionSupplier = await this.bidRepo.manager.getRepository(BidAuctionSupplierEntity).findOne({
        where: {
          bidAuctionId: bidAuction.id,
          supplierId: bidSupplier.supplierId,
          status: enumData.BidAuctionSupplierStatus.DaDauGia.code,
          companyId: user.companyId,
        },
      })
      if (bidAuctionSupplier) {
        const res: any = {}
        res.name = `Đấu giá ${coreHelper.dateToString(bidAuction.createdAt)}`
        res.date = bidAuction.createdAt
        res.type = 2
        res.price = 0
        res.lstPrice = lstBidPrice.map((object) => ({ ...object }))
        const lstPriceValue = await bidAuctionSupplier.bidAuctionSupplierPriceValue
        for (const data1 of res.lstPrice) {
          const priceValue = lstPriceValue.find((c) => c.bidPriceId == data1.id)
          if (priceValue) {
            data1.value = +priceValue.value
            res.price += data1.value * data1.number
          }
          for (const data2 of data1.__childs__) {
            const priceValue2 = lstPriceValue.find((c) => c.bidPriceId == data2.id)
            if (priceValue2) {
              data2.value = +priceValue2.value
            }
            for (const data3 of data2.__childs__) {
              const priceValue3 = lstPriceValue.find((c) => c.bidPriceId == data3.id)
              if (priceValue3) {
                data3.value = +priceValue3.value
              }
            }
          }
        }

        return res
      }
    }
    //#endregion

    //#region Đàm phán giá
    const lstBidDeal = await bid.bidDeals
    if (lstBidDeal && lstBidDeal.length > 0) {
      const lstBidDealId = lstBidDeal.map((c) => c.id)
      // check xem Doanh nghiệp tham gia đàm phán không
      const bidDealSupplierLast = await this.bidRepo.manager.getRepository(BidDealSupplierEntity).findOne({
        where: {
          bidDealId: In(lstBidDealId),
          supplierId: bidSupplier.supplierId,
          status: enumData.BidDealSupplierStatus.DaGuiGiaMoi.code,
          companyId: user.companyId,
        },
        order: { createdAt: 'DESC' },
      })
      if (bidDealSupplierLast) {
        const bidDeal = lstBidDeal.find((c) => c.id == bidDealSupplierLast.bidDealId)
        if (!bidDeal) return {}
        const bidDealPrices = await bidDeal.bidDealPrices

        const res: any = {}
        res.name = `Đàm phán giá ${coreHelper.dateToString(bidDeal.createdAt)}`
        res.date = bidDeal.createdAt
        res.type = 1
        res.price = 0
        res.lstPrice = lstBidPrice.map((object) => ({ ...object }))
        for (const price of res.lstPrice) {
          const bidDealPrice = bidDealPrices.find((c) => c.bidPriceId == price.id)
          if (bidDealPrice && bidDealPrice.number > 0) {
            price.number = bidDealPrice.number
          }
        }
        const lstPriceValue = await bidDealSupplierLast.bidDealSupplierPriceValue
        for (const data1 of res.lstPrice) {
          const priceValue = lstPriceValue.find((c) => c.bidPriceId == data1.id)
          if (priceValue) {
            data1.value = +priceValue.value
            res.price += data1.value * data1.number
          }
          for (const data2 of data1.__childs__) {
            const priceValue2 = lstPriceValue.find((c) => c.bidPriceId == data2.id)
            if (priceValue2) {
              data2.value = +priceValue2.value
            }
            for (const data3 of data2.__childs__) {
              const priceValue3 = lstPriceValue.find((c) => c.bidPriceId == data3.id)
              if (priceValue3) {
                data3.value = +priceValue3.value
              }
            }
          }
        }

        return res
      }
    }
    //#endregion

    //#region Bảng chào giá

    const res: any = {}
    res.name = `Chào giá ${coreHelper.dateToString(bidSupplier.createdAt)}`
    res.date = bidSupplier.createdAt
    res.type = 0
    res.price = 0
    res.lstPrice = lstBidPrice.map((object) => ({ ...object }))
    const lstPriceValue = await bidSupplier.bidSupplierPriceValue
    for (const data1 of res.lstPrice) {
      const priceValue = lstPriceValue.find((c) => c.bidPriceId == data1.id)
      if (priceValue) {
        data1.value = +priceValue.value
        res.price += data1.value * data1.number
      }
      for (const data2 of data1.__childs__) {
        const priceValue2 = lstPriceValue.find((c) => c.bidPriceId == data2.id)
        if (priceValue2) {
          data2.value = +priceValue2.value
        }
        for (const data3 of data2.__childs__) {
          const priceValue3 = lstPriceValue.find((c) => c.bidPriceId == data3.id)
          if (priceValue3) {
            data3.value = +priceValue3.value
          }
        }
      }
    }

    return res
  }

  /** Lấy giá mới nhất của Doanh nghiệp theo 1 hạng mục */
  async getBidHistoryPriceByCategory(user: UserDto, bidSupplierId: string, bidPriceId: string) {
    const bidSupplier = await this.bidSupplierRepo.findOne({ where: { id: bidSupplierId, companyId: user.companyId } })
    if (!bidSupplier) return {}
    const bid = await bidSupplier.bid

    // Lấy template hạng mục chào giá của gói thầu
    const bidPrice = await this.bidPriceRepo.findOne({ where: { id: bidPriceId, companyId: user.companyId } })
    if (!bidPrice) return null

    //#region Đấu giá
    const lstBidAuction = await bid.bidAuctions
    if (lstBidAuction && lstBidAuction.length > 0) {
      const bidAuction = lstBidAuction[0]
      // check xem Doanh nghiệp tham gia đấu thầu không
      const bidAuctionSupplier = await this.bidRepo.manager.getRepository(BidAuctionSupplierEntity).findOne({
        where: {
          bidAuctionId: bidAuction.id,
          supplierId: bidSupplier.supplierId,
          status: enumData.BidAuctionSupplierStatus.DaDauGia.code,
          companyId: user.companyId,
        },
      })
      if (bidAuctionSupplier) {
        const res: any = {}
        res.name = `Đấu giá ${coreHelper.dateToString(bidAuction.createdAt)}`
        res.date = bidAuction.createdAt
        res.type = 2
        res.price = 0
        res.number = bidPrice.number
        res.name = bidPrice.name
        res.objPrice = { ...bidPrice }

        const lstPriceValue = await bidAuctionSupplier.bidAuctionSupplierPriceValue
        const priceValue = lstPriceValue.find((c) => c.bidPriceId == bidPrice.id)
        if (priceValue) {
          res.value = +priceValue.value
          res.price += res.value * res.number
        }

        return res
      }
    }
    //#endregion

    //#region Đàm phán giá
    const lstBidDeal = await bid.bidDeals
    if (lstBidDeal && lstBidDeal.length > 0) {
      const lstBidDealId = lstBidDeal.map((c) => c.id)
      // check xem Doanh nghiệp tham gia đàm phán không
      const bidDealSupplierLast = await this.bidRepo.manager.getRepository(BidDealSupplierEntity).findOne({
        where: {
          bidDealId: In(lstBidDealId),
          supplierId: bidSupplier.supplierId,
          status: enumData.BidDealSupplierStatus.DaGuiGiaMoi.code,
          companyId: user.companyId,
        },
        order: { createdAt: 'DESC' },
      })
      if (bidDealSupplierLast) {
        const bidDeal = lstBidDeal.find((c) => c.id == bidDealSupplierLast.bidDealId)
        if (!bidDeal) return {}
        const bidDealPrices = await bidDeal.bidDealPrices

        const res: any = {}
        res.name = `Đàm phán giá ${coreHelper.dateToString(bidDeal.createdAt)}`
        res.date = bidDeal.createdAt
        res.type = 1
        res.price = 0
        res.number = bidPrice.number
        res.name = bidPrice.name
        res.objPrice = { ...bidPrice }
        const bidDealPrice = bidDealPrices.find((c) => c.bidPriceId == bidPrice.id)
        if (bidDealPrice && bidDealPrice.number > 0) {
          res.number = bidDealPrice.number
          res.objPrice.number = bidDealPrice.number
        }
        const lstPriceValue = await bidDealSupplierLast.bidDealSupplierPriceValue
        const priceValue = lstPriceValue.find((c) => c.bidPriceId == bidPrice.id)
        if (priceValue) {
          res.value = +priceValue.value
          res.price += res.value * res.number
        }

        return res
      }
    }
    //#endregion

    //#region Bảng chào giá

    const res: any = {}
    res.name = `Chào giá ${coreHelper.dateToString(bidSupplier.createdAt)}`
    res.date = bidSupplier.createdAt
    res.type = 0
    res.price = 0
    res.number = bidPrice.number
    res.name = bidPrice.name
    res.objPrice = { ...bidPrice }
    const lstPriceValue = await bidSupplier.bidSupplierPriceValue
    const priceValue = lstPriceValue.find((c) => c.bidPriceId == bidPrice.id)
    if (priceValue) {
      res.value = +priceValue.value
      res.price += res.value * res.number
    }

    return res

    //#endregion
  }

  /** Báo cáo lịch sử giá theo Doanh nghiệp */
  async getReportHistoryPriceSupplier(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.where.supplierId) throw new BadRequestException('Vui lòng chọn NCC trước!')

    // Lấy các gói thầu mà Doanh nghiệp đã nộp giá
    const lstBidSupplier = await this.bidSupplierRepo.find({
      where: {
        supplierId: data.where.supplierId,
        status: In([
          enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
          enumData.BidSupplierStatus.DangDanhGia.code,
          enumData.BidSupplierStatus.DaDanhGia.code,
        ]),
        companyId: user.companyId,
        isDeleted: false,
      },
      select: { id: true, bidId: true },
    })
    if (lstBidSupplier.length == 0) return [[], 0]

    const lstBidId = lstBidSupplier.map((c) => c.bidId).filter((value, idx, self) => self.indexOf(value) == idx)

    // Lấy top các gói thầu mới nhất theo phân trang
    let whereCon: any = { companyId: user.companyId }
    whereCon.id = In(lstBidId)
    whereCon.isDeleted = false
    if (data.where.serviceId) whereCon.serviceId = data.where.serviceId
    if (data.where.name) {
      if (!whereCon.parent) whereCon.parent = {}
      whereCon.parent.name = Like(`%${data.where.name}%`)
    }
    if (data.where.dateFrom && data.where.dateTo) {
      if (!whereCon.parent) whereCon.parent = {}
      whereCon.parent.publicDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.dateFrom) {
      if (!whereCon.parent) whereCon.parent = {}
      whereCon.parent.publicDate = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}")`)
    } else if (data.where.dateTo) {
      if (!whereCon.parent) whereCon.parent = {}
      whereCon.parent.publicDate = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.dateTo).format('YYYY-MM-DD')}")`)
    }

    const result: any[] = await this.bidRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      relations: { service: true, parent: true },
      select: { id: true, createdAt: true, service: { code: true, name: true }, parent: { code: true, name: true } },
    })

    const temp = []
    for (const item of result[0]) {
      const bidSupplier = lstBidSupplier.find((c) => c.bidId == item.id)
      if (!bidSupplier) continue

      // Lấy giá mới nhất của Doanh nghiệp
      const priceLast = await this.getBidHistoryPrice(user, bidSupplier.id)
      if (priceLast) {
        temp.push({
          bidCode: item.__parent__?.code,
          bidName: item.__parent__?.name,
          itemName: item.__service__?.code + ' - ' + item.__service__?.name,
          price: priceLast.price,
          dateSubmitPrice: priceLast.date,
          priceLast,
        })
      }
    }

    return [temp, result[1]]
  }

  /** Báo cáo lịch sử giá theo LVMH */
  async getReportHistoryPriceService(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const whereCon: any = { companyId: user.companyId, isDeleted: false }
    if (data.where.name && data.where.name.trim() !== '') whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.serviceId) whereCon.serviceId = data.where.serviceId
    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.publicDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.dateFrom)
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}")`)
    else if (data.where.dateTo) whereCon.publicDate = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.dateTo).format('YYYY-MM-DD')}")`)

    const lstBid = await this.bidRepo.find({
      where: whereCon,
      order: { createdAt: 'DESC' },
    })

    if (lstBid.length == 0) return [[], 0]
    const lstBidId = lstBid.map((c) => c.id)

    // Lấy các Doanh nghiệp tham gia thầu và đã chào giá (theo phân trang)
    const result = await this.bidSupplierRepo.findAndCount({
      where: {
        bidId: In(lstBidId),
        status: In([
          enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
          enumData.BidSupplierStatus.DangDanhGia.code,
          enumData.BidSupplierStatus.DaDanhGia.code,
        ]),
        companyId: user.companyId,
        isDeleted: false,
      },
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    })

    let temp = []
    for (const item of result[0]) {
      const bid = lstBid.find((c) => c.id == item.bidId)
      if (!bid) continue
      const supplier = await item.supplier

      // Lấy giá mới nhất của Doanh nghiệp
      const priceLast = await this.getBidHistoryPrice(user, item.id)
      if (priceLast) {
        temp.push({
          bidCode: bid.code,
          bidName: bid.name,
          supplierCode: supplier.code,
          supplierName: supplier.name,
          price: priceLast.price,
          dateSubmitPrice: priceLast.date,
          priceLast,
        })
      }
    }

    return [temp, result[1]]
  }

  /** Báo cáo lịch sử giá theo hạng mục giá */
  async getReportHistoryPriceCategory(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    // Tìm các gói thầu thoả bộ lọc
    const whereCon: any = { companyId: user.companyId, isDeleted: false }
    if (data.where.serviceId) whereCon.serviceId = data.where.serviceId
    if (data.where.name) {
      if (!whereCon.parent) whereCon.parent = {}
      whereCon.parent.name = Like(`%${data.where.name}%`)
    }
    if (data.where.dateFrom && data.where.dateTo) {
      if (!whereCon.parent) whereCon.parent = {}
      whereCon.parent.publicDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.dateFrom) {
      if (!whereCon.parent) whereCon.parent = {}
      whereCon.parent.publicDate = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}")`)
    } else if (data.where.dateTo) {
      if (!whereCon.parent) whereCon.parent = {}
      whereCon.parent.publicDate = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.dateTo).format('YYYY-MM-DD')}")`)
    }

    const lstBidItem = await this.bidRepo.find({
      where: whereCon,
      relations: { parent: true, service: true },
      select: { id: true, serviceId: true, parent: { code: true, name: true }, service: { code: true, name: true } },
    })
    if (lstBidItem.length == 0) return [[], 0]

    const lstBidId = lstBidItem.map((c) => c.id)
    const lstBidSupplier = await this.bidSupplierRepo.find({
      where: {
        bidId: In(lstBidId),
        status: In([
          enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
          enumData.BidSupplierStatus.DangDanhGia.code,
          enumData.BidSupplierStatus.DaDanhGia.code,
        ]),
        companyId: user.companyId,
        isDeleted: false,
      },
      relations: { supplier: true },
      select: { id: true, bidId: true, supplier: { code: true, name: true } },
    })
    if (lstBidSupplier.length == 0) return [[], 0]
    const lstBidSupplierId = lstBidSupplier.map((c) => c.id)

    // Lấy các gói thầu có hạng mục chào giá [category] đã đc Doanh nghiệp chào giá
    const result = await this.bidRepo.manager.getRepository(BidSupplierPriceValueEntity).findAndCount({
      where: {
        bidSupplierId: In(lstBidSupplierId),
        name: Like(`%${data.where.category}%`),
        companyId: user.companyId,
        isDeleted: false,
      },
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })

    let temp = []
    for (const item of result[0]) {
      const bidSupplier: any = lstBidSupplier.find((c) => c.id == item.bidSupplierId)
      if (!bidSupplier) continue

      const bidItem: any = lstBidItem.find((c) => c.id == bidSupplier.bidId)
      if (!bidItem) continue

      // Lấy giá mới nhất của Doanh nghiệp
      const priceLast = await this.getBidHistoryPriceByCategory(user, bidSupplier.id, item.bidPriceId)
      if (priceLast) {
        temp.push({
          bidCode: bidItem.__parent__?.code,
          bidName: bidItem.__parent__?.name,
          itemName: bidItem.serviceId ? bidItem.__service__.code + ' - ' + bidItem.__service__.name : '',
          supplierCode: bidSupplier.__supplier__.code,
          supplierName: bidSupplier.__supplier__.name,
          number: priceLast.number,
          price: priceLast.price,
          unitPrice: priceLast.value,
          dateSubmitPrice: priceLast.date,
          name: priceLast.name,
          priceLast,
        })
      }
    }

    return [temp, result[1]]
  }

  /** Báo cáo lịch sử đấu thầu Doanh nghiệp */
  async getReportHistoryBidSupplier(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    // Lấy các gói thầu mà Doanh nghiệp đã nộp giá
    const lstBidSupplier = await this.bidSupplierRepo.find({
      where: {
        supplierId: data.where.supplierId,
        status: In([
          enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
          enumData.BidSupplierStatus.DangDanhGia.code,
          enumData.BidSupplierStatus.DaDanhGia.code,
        ]),
        companyId: user.companyId,
        isDeleted: false,
      },
    })
    if (lstBidSupplier.length == 0) return [[], 0]
    const lstBidId = lstBidSupplier.map((c) => c.bidId).filter((value, idx, self) => self.indexOf(value) == idx)

    // Lấy top các gói thầu mới nhất theo phân trang
    const whereCon: any = { companyId: user.companyId, id: In(lstBidId), isDeleted: false }
    if (data.where.name && data.where.name.trim() !== '') whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code && data.where.code.trim() !== '') whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.serviceId) whereCon.serviceId = data.where.serviceId
    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.publicDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.dateFrom)
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}")`)
    else if (data.where.dateTo) whereCon.publicDate = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.dateTo).format('YYYY-MM-DD')}")`)

    const result = await this.bidRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    })

    const lstServiceId = result[0].map((c) => c.serviceId)
    if (lstServiceId.length == 0) return [[], 0]
    const lstSupplierService = await this.supplierServiceRepo.find({
      where: {
        supplierId: data.where.supplierId,
        serviceId: In(lstServiceId),
        companyId: user.companyId,
        isDeleted: false,
      },
    })
    const lstService = await this.serviceRepo.find({
      where: { id: In(lstServiceId), companyId: user.companyId, isDeleted: false },
      relations: { parent: { parent: true } },
    })

    let temp = []
    let sort = 0
    const bidStatus: any = enumData.BidStatus
    for (const item of result[0]) {
      sort++
      let service: any = lstService.find((c: any) => c.id == item.serviceId)
      const arrServiceCode = []
      while (service) {
        arrServiceCode.unshift(service.code)
        service = service.__parent__
      }

      const bidSupplier = lstBidSupplier.find((c) => c.bidId == item.id)
      if (!bidSupplier) continue
      const supplierService = lstSupplierService.find((c: any) => c.serviceId == item.serviceId)

      // Lấy giá mới nhất của từng Doanh nghiệp
      const priceLast = await this.getBidHistoryPrice(user, bidSupplier.id)
      let successBidStatus = ''
      if (item.status == enumData.BidStatus.HoanTat.code) {
        if (bidSupplier.isSuccessBid) successBidStatus = 'Duyệt chọn thầu'
        else successBidStatus = 'Không duyệt chọn thầu'
      }
      if (priceLast) {
        temp.push({
          sort,
          bid: item,
          bidCode: item.code,
          bidName: item.name,
          bidStatus: bidStatus[item.status].name,
          successBidStatus,
          scoreCapacity: supplierService?.score,
          scoreTech: bidSupplier.scoreTech,
          scorePrice: bidSupplier.scorePrice,
          scoreTrade: bidSupplier.scoreTrade,
          serviceLv1Code: arrServiceCode[0],
          serviceLv2Code: arrServiceCode[1],
          serviceLv3Code: arrServiceCode[2],
          price: priceLast.price,
          dateSubmitPrice: priceLast.date,
          priceLast,
        })
      }
    }

    return [temp, result[1]]
  }

  /** Báo cáo lịch sử mua hàng buyer */
  async getReportHistoryBuyer(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.where.employeeId) throw new BadRequestException('Vui lòng chọn Buyer trước!')

    const whereCon: any = { employeeAccess: { employeeId: data.where.employeeId }, companyId: user.companyId, isDeleted: false }
    if (data.where.serviceId) whereCon.serviceId = data.where.serviceId

    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.publicDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.dateFrom)
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}")`)
    else if (data.where.dateTo) whereCon.publicDate = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.dateTo).format('YYYY-MM-DD')}")`)

    const lstBid = await this.bidRepo.find({
      where: whereCon,
      select: { id: true, code: true, name: true, status: true, startBidDate: true, bidCloseDate: true },
    })
    if (lstBid.length == 0) return [[], 0]

    const lstBidId = lstBid.map((c) => c.id)
    const lstBidSupplier = await this.bidSupplierRepo.find({
      where: {
        bid: { parentId: In(lstBidId) },
        status: In([
          enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
          enumData.BidSupplierStatus.DangDanhGia.code,
          enumData.BidSupplierStatus.DaDanhGia.code,
        ]),
        isDeleted: false,
      },
      relations: { supplier: true, bid: { service: true } },
      select: {
        id: true,
        bidId: true,
        isSuccessBid: true,
        supplier: { code: true, name: true },
        bid: { id: true, parentId: true, service: { code: true, name: true } },
      },
    })
    if (lstBidSupplier.length == 0) return [[], 0]
    const lstBidSupplierId = lstBidSupplier.map((c) => c.id)

    // Lấy các gói thầu có hạng mục chào giá [category] đã đc Doanh nghiệp chào giá
    const where2: any = { bidSupplierId: In(lstBidSupplierId), companyId: user.companyId, isDeleted: false }
    if (data.where.category && data.where.category.trim() !== '') where2.name = Like(`%${data.where.category}%`)

    const result = await this.bidRepo.manager.getRepository(BidSupplierPriceValueEntity).findAndCount({
      where: where2,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })

    let temp = []
    const bidStatus: any = enumData.BidStatus
    for (const item of result[0]) {
      const bidSupplier: any = lstBidSupplier.find((c) => c.id == item.bidSupplierId)
      if (!bidSupplier) continue

      const bid = lstBid.find((c) => c.id == bidSupplier.__bid__.parentId)
      if (!bid) continue

      // Lấy giá mới nhất của Doanh nghiệp
      const priceLast = await this.getBidHistoryPriceByCategory(user, bidSupplier.id, item.bidPriceId)
      let successBidStatus = ''
      if (bid.status == enumData.BidStatus.HoanTat.code) {
        if (bidSupplier.isSuccessBid) successBidStatus = 'Duyệt chọn thầu'
        else successBidStatus = 'Không duyệt chọn thầu'
      }
      if (priceLast) {
        temp.push({
          startBidDate: bid.startBidDate,
          bidCloseDate: bid.bidCloseDate,
          bidCode: bid.code,
          bidName: bid.name,
          bidStatus: bidStatus[bid.status].name,
          itemName: bidSupplier.__bid__.__service__.code + ' - ' + bidSupplier.__bid__.__service__.name,
          supplierCode: bidSupplier.__supplier__.code,
          supplierName: bidSupplier.__supplier__.name,
          successBidStatus,
          priceLast,
          number: priceLast.number,
          price: priceLast.price,
          unitPrice: priceLast.value,
          dateSubmitPrice: priceLast.date,
          name: priceLast.name,
        })
      }
    }

    return [temp, result[1]]
  }

  //#endregion
}
