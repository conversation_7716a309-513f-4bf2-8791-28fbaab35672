import { Body, Controller, Post } from '@nestjs/common'
import { AiService } from './ai.service'

@Controller('ai')
export class AiController {
  constructor(private readonly service: AiService) {}

  @Post('openAi')
  public async callOpenAI(@Body() data: any) {
    return await this.service.callOpenAI(data.text)
  }

  @Post('gemini')
  public async callGeminiAI(@Body() data: any) {
    return await this.service.callGeminiAI(data.text)
  }
  @Post('getProductPrice')
  public async getProductPrice(@Body() data: { param: string }) {
    return await this.service.getProductPrice(data.param)
  }
}
