import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, Join<PERSON><PERSON>umn } from 'typeorm'
import { BidPriceEntity } from './bidPrice.entity'
import { BidPriceColEntity } from './bidPriceCol.entity'

@Entity('bid_price_col_value')
export class BidPriceColValueEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  value: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidPriceId: string
  @ManyToOne(() => BidPriceEntity, (p) => p.bidPriceColValue)
  @JoinColumn({ name: 'bidPriceId', referencedColumnName: 'id' })
  bidPrice: Promise<BidPriceEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidPriceColId: string
  @ManyToOne(() => BidPriceColEntity, (p) => p.bidPriceColValue)
  @JoinColumn({ name: 'bidPriceColId', referencedColumnName: 'id' })
  bidPriceCol: Promise<BidPriceColEntity>
}
