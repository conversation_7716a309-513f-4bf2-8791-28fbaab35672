import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { enumProject } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { DepartmentService } from './department.service'
import { DepartmentCreateDto, DepartmentUpdateDto } from './dto'

@ApiBearerAuth()
@ApiTags('Department')
@Controller('departments')
export class DepartmentController {
  constructor(private readonly service: DepartmentService) {}

  @ApiOperation({ summary: 'Lấy danh sách phòng ban' })
  @UseGuards(ApeAuthGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: { branchId?: string }) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'L<PERSON>y danh sách phòng ban phân trang' })
  @Roles(enumProject.Features.SETTING_004.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo phòng ban' })
  @Roles(enumProject.Features.SETTING_004.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: DepartmentCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Chỉnh sửa phòng ban' })
  @Roles(enumProject.Features.SETTING_004.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: DepartmentUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động phòng ban' })
  @Roles(enumProject.Features.SETTING_004.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateIsDelete(user, data)
  }
}
