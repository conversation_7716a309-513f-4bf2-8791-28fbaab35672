import { Module } from '@nestjs/common'
import { EmailService } from './email.service'
import { TypeOrmExModule } from '../../typeorm'
import {
  SupplierServiceRepository,
  BidEmployeeAccessRepository,
  BidRepository,
  SupplierRepository,
  BidSupplierRepository,
  SupplierExpertiseRepository,
  EmailHistoryRepository,
  BidDealRepository,
  BidAuctionRepository,
  EmployeeRepository,
  EmailTemplateRepository,
  SupplierNotifyRepository,
  EmployeeNotifyRepository,
  EmployeeWarningRepository,
  AuctionRepository,
  PORepository,
} from '../../repositories'
import { SQSModule } from '../common/sqs/sqs.module'
import { EmailController } from './email.controller'
import { EmailNodeService } from './emailNodeMailer.service'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      BidRepository,
      BidAuctionRepository,
      BidEmployeeAccessRepository,
      BidDealRepository,
      BidSupplierRepository,
      EmailTemplateRepository,
      EmailHistoryRepository,
      EmployeeRepository,
      EmployeeNotifyRepository,
      EmployeeWarningRepository,
      SupplierRepository,
      SupplierServiceRepository,
      SupplierNotifyRepository,
      SupplierExpertiseRepository,
      AuctionRepository,
      PORepository,
    ]),

    SQSModule,
  ],
  controllers: [EmailController],
  providers: [EmailService, EmailNodeService],
  exports: [EmailService, EmailNodeService],
})
export class EmailModule {}
