import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { enumProject } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { ContractAppendixService } from './contractAppendix.service'
import { ContractAppendixCreate, ContractAppendixUpdate } from './dto'

/** <PERSON>u<PERSON>n lý <PERSON> lụ<PERSON> hợp đồng */
@ApiBearerAuth()
@ApiTags('Contract')
@Controller('contractAppendix')
export class ContractAppendixController {
  constructor(private readonly service: ContractAppendixService) {}

  @ApiOperation({ summary: 'Tạo phụ lục' })
  @Roles(enumProject.Features.CONTRACT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@Body() data: ContractAppendixCreate, @CurrentUser() user: UserDto) {
    return await this.service.createData(data, user)
  }

  @ApiOperation({ summary: 'Chỉnh sửa phụ lục' })
  @Roles(enumProject.Features.CONTRACT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@Body() data: ContractAppendixUpdate, @CurrentUser() user: UserDto) {
    return await this.service.updateData(data, user)
  }

  @ApiOperation({ summary: 'Danh sách phụ lục phân trang' })
  @Roles(enumProject.Features.CONTRACT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@Body() data: PaginationDto, @CurrentUser() user: UserDto) {
    return await this.service.pagination(data, user)
  }
}
