import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { SupplierEntity } from './supplier.entity'
import { BidDealEntity } from './bidDeal.entity'
import { BidDealSupplierPriceValueEntity } from './bidDealSupplierPriceValue.entity'

@Entity('bid_deal_supplier')
export class BidDealSupplierEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidDealId: string
  @ManyToOne(() => BidDealEntity, (p) => p.bidDealSupplier)
  @JoinColumn({ name: 'bidDealId', referencedColumnName: 'id' })
  bidDeal: Promise<BidDealEntity>

  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  score: number

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.bidDealSupplier)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /** File chi tiết giá (Nếu có) */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  filePriceDetail: string

  /** File chi tiết kỹ thuật (Nếu có) */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileTechDetail: string

  /** Link driver các file bổ sung (Nếu có) */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  linkDriver: string

  /** Ngày nộp đàm phán giá */
  @Column({
    nullable: true,
  })
  submitDate: Date

  /** Danh sách giá trị dữ liệu nhà cung cấp đàm phán */
  @OneToMany(() => BidDealSupplierPriceValueEntity, (p) => p.bidDealSupplier)
  bidDealSupplierPriceValue: Promise<BidDealSupplierPriceValueEntity[]>
}
