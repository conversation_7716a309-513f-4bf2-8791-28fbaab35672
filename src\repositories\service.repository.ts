import { Repository } from 'typeorm'
import {
  ServiceAccessEntity,
  ServiceCapacityEntity,
  ServiceCustomPriceEntity,
  ServiceEntity,
  ServicePriceColEntity,
  ServicePriceEntity,
  ServiceTechEntity,
  ServiceTradeEntity,
} from '../entities'
import { CustomRepository } from '../typeorm'

@CustomRepository(ServiceEntity)
export class ServiceRepository extends Repository<ServiceEntity> {}

@CustomRepository(ServiceAccessEntity)
export class ServiceAccessRepository extends Repository<ServiceAccessEntity> {}

@CustomRepository(ServiceCapacityEntity)
export class ServiceCapacityRepository extends Repository<ServiceCapacityEntity> {}

@CustomRepository(ServiceTechEntity)
export class ServiceTechRepository extends Repository<ServiceTechEntity> {}

@CustomRepository(ServiceTradeEntity)
export class ServiceTradeRepository extends Repository<ServiceTradeEntity> {}

@CustomRepository(ServicePriceEntity)
export class ServicePriceRepository extends Repository<ServicePriceEntity> {}

@CustomRepository(ServiceCustomPriceEntity)
export class ServiceCustomPriceRepository extends Repository<ServiceCustomPriceEntity> {}

@CustomRepository(ServicePriceColEntity)
export class ServicePriceColRepository extends Repository<ServicePriceColEntity> {}
