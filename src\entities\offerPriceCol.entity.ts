import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, Join<PERSON><PERSON>umn, OneToMany } from 'typeorm'
import { OfferEntity } from './offer.entity'
import { OfferServiceEntity } from './offerService.entity'
import { OfferPriceColValueEntity } from './offerPriceColValue.entity'
import { OfferSupplierPriceColValueEntity } from './offerSupplierPriceColValue.entity'

@Entity('offer_price_col')
export class OfferPriceColEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'text',
    nullable: true,
  })
  fomular: string

  /** Có bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  @Column({
    length: 50,
    nullable: false,
  })
  type: string

  @Column({
    length: 50,
    nullable: false,
  })
  colType: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerId: string

  @ManyToOne(() => OfferEntity, (p) => p.offerPriceCols)
  @JoinColumn({ name: 'offerId', referencedColumnName: 'id' })
  offer: Promise<OfferEntity>

  @OneToMany(() => OfferPriceColValueEntity, (p) => p.offerPriceCol)
  offerPriceColValue: Promise<OfferPriceColValueEntity[]>

  @OneToMany(() => OfferSupplierPriceColValueEntity, (p) => p.offerPriceCol)
  offerSupplierPriceColValue: Promise<OfferSupplierPriceColValueEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerServiceId: string
  @ManyToOne(() => OfferServiceEntity, (p) => p.offerPriceCol)
  @JoinColumn({ name: 'offerServiceId', referencedColumnName: 'id' })
  offerService: Promise<OfferServiceEntity>
}
