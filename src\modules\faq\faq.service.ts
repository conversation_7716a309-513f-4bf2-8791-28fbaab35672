import { Injectable } from '@nestjs/common'
import { Like } from 'typeorm'
import { CREATE_SUCCESS, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { UserDto } from '../../dto'
import { ape<PERSON>uthApiHelper } from '../../helpers'
import { FaqRepository } from '../../repositories'
import { FaqCreateDto, FaqUpdateDto } from './dto'

@Injectable()
export class FaqService {
  constructor(private readonly repo: FaqRepository) {}

  public async createData(user: UserDto, data: FaqCreateDto) {
    const newEntity = this.repo.create(data)
    newEntity.companyId = user.companyId
    newEntity.createdBy = user.id
    await this.repo.save(newEntity)

    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: FaqUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.title = data.title
    entity.description = data.description
    entity.categoryId = data.categoryId
    entity.updatedBy = user.id
    await this.repo.save(entity)

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: any) {
    const whereCon: any = { companyId: user.companyId }

    if (data.where.title) whereCon.title = Like(`%${data.where.title}%`)
    if (data.where.categoryId) whereCon.categoryId = data.where.categoryId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { category: true },
      order: { title: 'ASC' },
    })

    for (const item of res[0]) {
      if (item.categoryId) item.categoryName = item.__category__.name
      delete item.__category__
    }

    return res
  }

  public async updateIsDelete(user: UserDto, data: { id: string }) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async getFAQHomePage(req: Request, id: string) {
    const companyId = await apeAuthApiHelper.getCompanyId(req)
    return await this.repo.findOne({
      where: { id, companyId },
      relations: { category: true },
    })
  }
}
