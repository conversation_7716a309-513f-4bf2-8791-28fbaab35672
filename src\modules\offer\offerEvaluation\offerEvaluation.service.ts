import { Injectable, MethodNotAllowedException, NotAcceptableException } from '@nestjs/common'
import { EmailService } from '../../email/email.service'
import { enumData, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION } from '../../../constants'
import { UserDto } from '../../../dto'
import { In, IsNull, Not } from 'typeorm'
import { coreHelper } from '../../../helpers'
import { OfferPrItemRepository, OfferRepository, OfferSupplierRepository } from '../../../repositories/offer.repository'
import { BidEntity, OfferEntity } from '../../../entities'

@Injectable()
export class OfferEvaluationService {
  constructor(
    private readonly repo: OfferRepository,
    private readonly bidPrItemRepository: OfferPrItemRepository,
    private readonly bidSupplierRepo: OfferSupplierRepository,
    private readonly emailService: EmailService,
  ) {}

  /** Tự động chọn NCC thắng thầu và kết thúc thầu */

  /** Load ds Doanh nghiệp để chọn trúng thầu */
  async loadSupplierData(user: UserDto, data: { bidId: string; isMobile?: boolean }) {
    // const bidItem = await this.bidPrItemRepository.find({where: { id: data.bidId}})
    const res: any = await this.repo.getBid3(user, data.bidId)
    const dicStatusFile: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierFileStatus)
      lstStatus.forEach((c) => (dicStatusFile[c.code] = c.name))
    }
    for (const item of res.listItem) {
      item.lstBidSupplier = await this.bidSupplierRepo.find({
        // where: { bidId: item.id, companyId: user.companyId, isDeleted: false },
        where: { offerId: item.offerId, isDeleted: false, parentId: Not(IsNull()) },
        relations: { supplier: true },
      })
      if (item.lstBidSupplier.length == 0) continue

      const lstAll = await this.bidSupplierRepo.find({
        where: { offerId: item.id },
        select: { id: true, scorePrice: true, scoreManualPrice: true },
      })
      const lstValue = lstAll.map((c) => c.scorePrice)
      const maxValue = Math.max(...lstValue)
      const dlc = coreHelper.calDLC(lstValue)

      const lstValueManual = lstAll.map((c) => c.scoreManualPrice)
      const maxValueManual = Math.max(...lstValueManual)
      const dlcManual = coreHelper.calDLC(lstValueManual)

      for (const itemBidSupplier of item.lstBidSupplier) {
        itemBidSupplier.supplierName = itemBidSupplier.__supplier__.name
        delete itemBidSupplier.__supplier__
        itemBidSupplier.statusFileName = dicStatusFile[itemBidSupplier.statusFile]
        itemBidSupplier.isChoose = itemBidSupplier.isSuccessBid == true
        let isHasTotal = false
        itemBidSupplier.scoreTotal = 0
        itemBidSupplier.scoreManualTotal = 0
        if (
          itemBidSupplier.statusTech === enumData.BidSupplierTechStatus.DaXacNhan.code ||
          itemBidSupplier.statusTech === enumData.BidSupplierTechStatus.DaDuyet.code
        ) {
          isHasTotal = true
          itemBidSupplier.scoreTotal += (itemBidSupplier.scoreTech * item.percentTech) / 100
          itemBidSupplier.scoreManualTotal += (itemBidSupplier.scoreManualTech * item.percentTech) / 100
        } else {
          itemBidSupplier.scoreTech = -1
          itemBidSupplier.scoreManualTech = -1
        }

        if (
          itemBidSupplier.statusTrade === enumData.BidSupplierTradeStatus.DaXacNhan.code ||
          itemBidSupplier.statusTrade === enumData.BidSupplierTradeStatus.DaDuyet.code
        ) {
          isHasTotal = true
          itemBidSupplier.scoreTotal += (itemBidSupplier.scoreTrade * item.percentTrade) / 100
          itemBidSupplier.scoreManualTotal += (itemBidSupplier.scoreManualTrade * item.percentTrade) / 100
        } else {
          itemBidSupplier.scoreTrade = -1
          itemBidSupplier.scoreManualTrade = -1
        }

        if (
          itemBidSupplier.statusPrice === enumData.BidSupplierPriceStatus.DaXacNhan.code ||
          itemBidSupplier.statusPrice === enumData.BidSupplierPriceStatus.DaDuyet.code
        ) {
          isHasTotal = true
          let priceScore = 0
          if (dlc > 0) {
            priceScore = item.percentPrice - (maxValue - itemBidSupplier.scorePrice) / dlc
          } else {
            priceScore = item.percentPrice
          }
          itemBidSupplier.scoreTotal += priceScore

          let priceManualScore = 0
          if (dlcManual > 0) {
            priceManualScore = item.percentPrice - (maxValueManual - itemBidSupplier.scoreManualPrice) / dlcManual
          } else {
            priceManualScore = item.percentPrice
          }
          itemBidSupplier.scoreManualTotal += priceManualScore
        } else {
          itemBidSupplier.scorePrice = -1
          itemBidSupplier.scoreManualPrice = -1
        }

        if (!isHasTotal) {
          itemBidSupplier.scoreTotal = -1
          itemBidSupplier.scoreManualTotal = -1
        }
      }

      item.lstBidSupplier.sort((a, b) => b.scoreTotal - a.scoreTotal)
      let rank = 1
      const total = item.lstBidSupplier.length
      item.lstBidSupplier.forEach((itemBidSupplier) => {
        itemBidSupplier.rank = rank + '/' + total
        rank++
      })
    }

    if (data.isMobile) {
      const listContractor = []
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierFileStatus)
      for (const item of res.listItem) {
        item.lstBidSupplier = await this.bidSupplierRepo.find({
          // where: { bidId: item.id, companyId: user.companyId, isDeleted: false },
          where: { offerId: item.bidId, isDeleted: false },
          relations: { supplier: true },
        })
        if (item.lstBidSupplier.length == 0) continue
        let rank = 1
        for (const itemBidSupplier of item.lstBidSupplier) {
          const lstAll = await this.bidSupplierRepo.find({
            where: { offerId: item.id },
            select: { id: true, scorePrice: true, scoreManualPrice: true },
          })
          const lstValue = lstAll.map((c) => c.scorePrice)
          const maxValue = Math.max(...lstValue)
          const dlc = coreHelper.calDLC(lstValue)

          const lstValueManual = lstAll.map((c) => c.scoreManualPrice)
          const maxValueManual = Math.max(...lstValueManual)
          const dlcManual = coreHelper.calDLC(lstValueManual)
          const supDetail: any = {}
          supDetail.supplierName = itemBidSupplier.__supplier__.name

          //   điểm kỹ thuật
          supDetail.scoreTech = itemBidSupplier.scoreTech
          //   điểm HĐXT kỹ thuật
          supDetail.scoreManualTotal = itemBidSupplier.scoreManualTotal
          //   điểm báo giá
          let priceScore = 0
          if (dlc > 0) {
            priceScore = item.percentPrice - (maxValue - itemBidSupplier.scorePrice) / dlc
          } else {
            priceScore = item.percentPrice
          }
          supDetail.scoreTotal += priceScore
          //   điểm HĐXT BG

          let priceManualScore = 0
          if (dlcManual > 0) {
            priceManualScore = item.percentPrice - (maxValueManual - itemBidSupplier.scoreManualPrice) / dlcManual
          } else {
            priceManualScore = item.percentPrice
          }
          supDetail.scoreManualTotal += priceManualScore
          //   điểm ĐKTM
          supDetail.scoreTrade = itemBidSupplier.scoreTrade
          //   Điểm HĐXT ĐKTM
          supDetail.scoreManualTrade = itemBidSupplier.scoreManualTrade
          //   Trạng thái hồ sơ
          supDetail.statusFileName = dicStatusFile[itemBidSupplier.statusFile]
          //   Tổng điểm hồ sơ
          supDetail.scoreTotal = 0
          supDetail.scoreManualTotal = 0
          //   Tổng điểm hệ thống
          supDetail.scoreTotal += (itemBidSupplier.scoreTech * item.percentTech) / 100
          supDetail.scoreTotal += (itemBidSupplier.scoreTrade * item.percentTrade) / 100
          supDetail.scoreTotal += priceScore
          //   Tổng điểm đánh giá
          supDetail.scoreManualTotal += (itemBidSupplier.scoreManualTech * item.percentTech) / 100
          supDetail.scoreManualTotal += (itemBidSupplier.scoreManualTrade * item.percentTrade) / 100
          supDetail.scoreManualTotal += priceManualScore
          //   Thứ hạng
          supDetail.rank = rank
          rank++
          listContractor.push(supDetail)
        }
      }
      return listContractor
    }

    res.canApprove = true || false

    /* Tìm ra danh sách cấp duyệt và comment và người duyệt */
    res.haveProgress = false

    res.showComment = false
    /* nếu như nhân viên hiện tại chưa duyệt và nằm trong luồng duyệt thì toggle bật showComment = true */
    // for (const item of res.approvalProgress) {
    //   if (!item.approved) {
    //     res.showComment = true
    //     break
    //   }
    // }
    // if (res.approvalProgress.length > 0) res.haveProgress = true

    return res
  }

  /** Hàm kiểm tra quyền xác nhận Doanh nghiệp trúng thầu */
  async checkPermissionEvaluation(user: UserDto, bidId: string) {
    let result = true
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId }, select: { id: true, status: true } })
    if (!bid) return { hasPermission: result, message }
    if (
      bid.status === enumData.BidStatus.HoanTatDanhGia.code ||
      bid.status === enumData.BidStatus.DongDamPhanGia.code ||
      bid.status === enumData.BidStatus.DongDauGia.code
    ) {
      result = true
      if (!result) message = 'Bạn không có quyền xác nhận Doanh nghiệp trúng thầu cho gói thầu.'
    } else message = 'Gói thầu đã được xác nhận Doanh nghiệp trúng thầu.'

    return { hasPermission: result, message }
  }

  /** Chọn Doanh nghiệp trúng thầu, trượt thầu theo từng Item */
  async evaluationBidSupplier(user: UserDto, data: { bidId: string; listItem: any[]; comment: string }) {
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionEvaluation(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const lstPromise = []
    for (const item of data.listItem) {
      const lstSupplierChoose = item.lstBidSupplier.filter((c) => c.isChoose)
      // Cập nhật trạng thái cho các Doanh nghiệp thắng thầu
      for (const supplierChoose of lstSupplierChoose) {
        lstPromise.push(this.bidSupplierRepo.update(supplierChoose.id, { isSuccessBid: true, updatedBy: user.id }))
      }
      const lstId = lstSupplierChoose.map((c) => c.id)
      lstPromise.push(this.bidSupplierRepo.update({ offerId: item.id, id: Not(In(lstId)) }, { isSuccessBid: false, updatedBy: user.id }))
    }
    await Promise.all(lstPromise)

    await this.repo.update(data.bidId, {
      status: enumData.BidStatus.DongThau.code,

      updatedBy: user.id,
    })

    // gửi email mpo leader duyệt
    this.emailService.GuiMpoDuyetNCCThangThau(data.bidId)

    // tạo quyền duyệt nhà cung cấp thắng thầu
    let flowType: string

    return { message: 'Chọn doanh nghiệp thắng thầu thành công.' }
  }

  /** Phê duyệt Doanh nghiệp thắng thầu */
  async approveSupplierWinBid(user: UserDto, data: { bidId: string; comment: string }) {
    // kiểm tra quyền
    // if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionApproveSupplierWinBid(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const todate = new Date()
    await this.repo.update(data.bidId, {
      status: enumData.BidStatus.DuyetNCCThangThau.code,
      approveChooseSupplierWinDate: todate,
      updatedBy: user.id,
    })

    // Gửi email thông báo nội bộ: tạm khóa
    // await this.emailService.ThongBaoNCCThangThauDuocDuyet(bidId)

    return { message: 'Phê duyệt Doanh nghiệp thắng thầu thành công.' }
    // }
  }

  /** Yêu cầu đánh giá và chọn lại Doanh nghiệp thắng thầu */
  async rejectSupplierWinBid(user: UserDto, data: { bidId: string; comment: string; recheck: any[] }) {
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    // const objPermission = await this.checkPermissionApproveSupplierWinBid(user, data.bidId)
    // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const isCheck1 = data.recheck[0].checked
    const isCheck2 = data.recheck[1].checked
    const dataUpdate: any = {
      status: enumData.BidStatus.DangDanhGia.code,
      // statusRateTech: enumData.BidTechRateStatus.DangTao.code,
      // statusRateTrade: enumData.BidTradeRateStatus.DangTao.code,
      // statusRatePrice: enumData.BidPriceRateStatus.DangTao.code,
      noteCloseBidMPOLeader: data.comment,
      updatedBy: user.id,
    }
    //nếu như có 1 thì reset Đánh giá NL, KT
    if (isCheck1) {
      dataUpdate.statusRateTech = enumData.BidTechRateStatus.DangTao.code
    }
    //nếu như có 2 Đánh giá bảng CG, CCG & Đánh giá DKTM
    if (isCheck2) {
      dataUpdate.statusRateTrade = enumData.BidTradeRateStatus.DangTao.code
      dataUpdate.statusRatePrice = enumData.BidTradeRateStatus.DangTao.code
    }
    // throw new Error('')
    await this.repo.update(data.bidId, dataUpdate)

    // // Bid History
    // const bidHistory = new BidHistoryEntity()
    // bidHistory.companyId = user.companyId
    // bidHistory.createdBy = user.id
    // bidHistory.bidId = data.bidId
    // bidHistory.employeeId = user.employeeId
    // bidHistory.status = enumData.BidHistoryStatus.YeuCauKiemTraLai.code
    // bidHistory.save()

    // Gửi email
    this.emailService.ThongBaoNCCThangThauBiTuChoi(data.bidId)

    return { message: 'Yêu cầu chọn lại Doanh nghiệp thắng thầu thành công.' }
  }

  /** Kiểm tra quyền Phê duyệt/Từ chối Doanh nghiệp thắng thầu */
  async checkPermissionApproveSupplierWinBid(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId }, select: { id: true, status: true } })
    if (!bid) return { hasPermission: result, message }

    if (bid.status === enumData.BidStatus.DongThau.code) {
      // result = await this.bidEmployeeAccessRepo.isMPOLeader(user, bidId)
      result = true
      if (!result) message = 'Bạn không có quyền phê duyệt Doanh nghiệp thắng thầu.'
    } else message = 'Gói thầu đã được phê duyệt Doanh nghiệp thắng thầu.'

    return { hasPermission: result, message }
  }
}
