import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class BranchCreateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiPropertyOptional()
  description: string

  @ApiPropertyOptional()
  level: number

  @ApiPropertyOptional()
  @IsOptional()
  parent1?: string

  @ApiPropertyOptional()
  @IsOptional()
  parent2?: string

  @ApiPropertyOptional()
  type: string
}
