import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsOptional, IsArray, IsNumber, IsBoolean } from 'class-validator'
export class BidCreateDto {
  /** C<PERSON> hiển thị ở trang chủ không */
  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isShowHomePage: boolean

  /** Có gửi thông báo mởi thầu Doanh nghiệp qua email không */
  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isSendEmailInviteBid: boolean

  /** Tự động chọn NCC thắng thầu và kết thúc thầu */
  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isAutoBid: boolean

  hiddenScore: boolean
  isSkipEnd: boolean

  isNotImportFromAdmin: boolean
  watchProfile: boolean

  /** Tên gói thầu */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  /** YCMH */
  @ApiPropertyOptional()
  prId?: string

  /** Mã hệ thống tự sinh */
  @ApiPropertyOptional()
  code: string

  /** Tỉ trọng điểm đánh giá kỹ thuật */
  @ApiPropertyOptional()
  percentTech: number

  /** Tỉ trọng điểm đánh giá ĐKTM */
  @ApiPropertyOptional()
  percentTrade: number

  /** Tỉ trọng điểm đánh giá bảng giá */
  @ApiPropertyOptional()
  percentPrice: number

  /** Bản vẽ kỹ thuật hoặc hình ảnh minh hoạ */
  @ApiPropertyOptional()
  fileDrawing: string

  serviceId: string

  /** Phạm vi công việc */
  @ApiPropertyOptional()
  fileJD: string

  /** Tiêu chuẩn đánh giá KPI */
  @ApiPropertyOptional()
  fileKPI: string

  /** Các quy định về nội quy gói thầu: an toàn, an ninh, VSTP,... */
  @ApiPropertyOptional()
  fileRule: string

  /** Tài liệu mẫu (mẫu báo giá, mẫu hợp đồng,...) */
  @ApiPropertyOptional()
  fileDocument: string

  /** Khác */
  @ApiPropertyOptional()
  fileAnother: string

  /** Mô tả nội dung mời thầu */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  serviceInvite: string

  /** Ngày hết hạn xác nhận tham gia đấu thầu */
  @ApiProperty()
  @IsNotEmpty()
  acceptEndDate: Date

  /** Ngày hết hạn nộp hồ sơ thầu */
  @ApiProperty()
  @IsNotEmpty()
  submitEndDate: Date

  /** Địa chỉ nộp hồ sơ thầu */
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  addressSubmit?: string

  /** Công ty mời thầu */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  companyInvite: string

  /** Các địa điểm thực hiện gói thầu */
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  listAddress?: string

  /** Thời gian đăng tải hệ thống tự sinh */
  @ApiPropertyOptional()
  publicDate: Date

  /** Hình thức đấu thầu */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  bidTypeId: string

  /** Hiệu lực hợp đồng (tháng) */
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  timeserving: number

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @ApiPropertyOptional()
  scoreDLC: number

  /** Thời điểm mở thầu hệ thống tự sinh */
  @ApiPropertyOptional()
  startBidDate: Date

  /** Số tiền bảo lãnh dự thầu (VNĐ) */
  @ApiPropertyOptional()
  moneyGuarantee: number

  /** Thời hạn bảo lãnh dự thầu (tháng) */
  @ApiPropertyOptional()
  timeGuarantee: number

  /** Hình thức bảo lãnh dự thầu */
  @ApiPropertyOptional()
  masterBidGuaranteeId: string

  /** Thời hạn thiết lập yêu cầu kỹ thuật, năng lực */
  @ApiProperty()
  @IsNotEmpty()
  timeTechDate: Date

  /** Thời hạn thiết lập các hạng mục báo giá, cơ cấu giá và điều kiện thương mại */
  @ApiProperty()
  @IsNotEmpty()
  timePriceDate: Date

  /** Thời hạn đánh giá yêu cầu kỹ thuật, năng lực */
  @ApiProperty()
  @IsNotEmpty()
  timeCheckTechDate: Date

  /** Thời hạn đánh giá các hạng mục báo giá, cơ cấu giá và điều kiện thương mại */
  @ApiProperty()
  @IsNotEmpty()
  timeCheckPriceDate: Date

  /** Danh sách nhân viên có quyền */

  /** Nhân viên MPO */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  mpoId: string

  /** Admin MPO */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  mpoLeadId: string

  /** Nhân viên Kỹ thuật */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  techId: string

  /** Admin kỹ thuật */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  techLeadId: string

  /** Nhân viên trong hội đồng */
  @ApiProperty()
  @IsNotEmpty()
  @IsArray()
  anotherRoleIds: string[]

  /** Các thành viên khác */
  @ApiPropertyOptional()
  otherRoleIds: string[]

  /** Danh sách Item gói thầu */
  @ApiProperty()
  listItem: any[]
}
