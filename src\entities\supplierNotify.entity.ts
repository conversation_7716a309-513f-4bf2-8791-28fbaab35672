import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { SupplierEntity } from './supplier.entity'
@Entity('supplier_notify')
export class SupplierNotifyEntity extends BaseEntity {
  @Column({
    type: 'text',
    nullable: true,
  })
  message: string

  @Column({
    type: 'text',
    nullable: true,
  })
  messageFull: string

  @Column({
    type: 'text',
    nullable: true,
  })
  url: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.supplierNotifys)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>
}
