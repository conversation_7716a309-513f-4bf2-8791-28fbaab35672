import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { ReportService } from './report.service'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { CurrentUser, Roles } from '../common/decorators'
import { PaginationDto, UserDto } from '../../dto'
import { enumProject } from '../../constants'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Report')
@Controller('reports')
export class ReportController {
  constructor(private readonly service: ReportService) {}

  @ApiOperation({ summary: 'Báo cáo Nhà cung cấp' })
  @Roles(enumProject.Features.REPORT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('report_suppliers')
  public async getReportSuppliers(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.getReportSuppliers(user, data)
  }

  @ApiOperation({ summary: 'Báo cáo Thẩm định' })
  @Roles(enumProject.Features.REPORT_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('report_expertise')
  public async getReportExpertise(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.getReportExpertise(user, data)
  }

  @ApiOperation({ summary: 'Báo cáo Trạng thái gói thầu' })
  @Roles(enumProject.Features.REPORT_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('report_bid')
  public async getReportBid(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.getReportBid(user, data)
  }

  @ApiOperation({ summary: 'Báo cáo lịch sử giá theo NCC' })
  @Roles(enumProject.Features.REPORT_004.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('report_history_price_supplier')
  public async getReportHistoryPriceSupplier(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.getReportHistoryPriceSupplier(user, data)
  }

  @ApiOperation({ summary: 'Báo cáo lịch sử giá theo LVMH' })
  @Roles(enumProject.Features.REPORT_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('report_history_price_service')
  public async getReportHistoryPriceService(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.getReportHistoryPriceService(user, data)
  }

  @ApiOperation({ summary: 'Báo cáo lịch sử giá theo hạng mục' })
  @Roles(enumProject.Features.REPORT_006.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('report_history_price_category')
  public async getReportHistoryPriceCategory(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.getReportHistoryPriceCategory(user, data)
  }

  @ApiOperation({ summary: 'Báo cáo lịch sử đấu thầu NCC' })
  @Roles(enumProject.Features.REPORT_007.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('report_history_bid_supplier')
  public async getReportHistoryBidSupplier(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.getReportHistoryBidSupplier(user, data)
  }

  @ApiOperation({ summary: 'Báo cáo lịch sử mua hàng Buyer' })
  @Roles(enumProject.Features.REPORT_008.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('report_history_buyer')
  public async getReportHistoryBuyer(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.getReportHistoryBuyer(user, data)
  }
}
