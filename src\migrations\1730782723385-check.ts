import { MigrationInterface, QueryRunner } from 'typeorm'

export class check1730782723385 implements MigrationInterface {
  name = 'check1730782723385'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`asn\` DROP COLUMN \`quantityFail\``)
    await queryRunner.query(`ALTER TABLE \`asn\` DROP COLUMN \`quantityPass\``)
    await queryRunner.query(`ALTER TABLE \`asn\` DROP COLUMN \`quantityQC\``)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`asn\` ADD \`quantityQC\` float NULL DEFAULT '0'`)
    await queryRunner.query(`ALTER TABLE \`asn\` ADD \`quantityPass\` float NULL DEFAULT '0'`)
    await queryRunner.query(`ALTER TABLE \`asn\` ADD \`quantityFail\` float NULL DEFAULT '0'`)
  }
}
