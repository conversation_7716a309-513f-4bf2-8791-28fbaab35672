import { Entity, Column, Jo<PERSON><PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'
import { InvoiceSuggestEntity } from './invoiceSuggest.entity'

@Entity('invoice_suggest_history')
export class InvoiceSuggestHistoryEntity extends BaseEntity {
  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /** Mô tả */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  invoiceSuggestId: string
  @ManyToOne(() => InvoiceSuggestEntity, (p) => p.histories)
  @JoinColumn({ name: 'invoiceSuggestId', referencedColumnName: 'id' })
  invoiceSuggest: Promise<InvoiceSuggestEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.invoiceSuggestHistories)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>
}
