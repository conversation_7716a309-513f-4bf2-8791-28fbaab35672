import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, ManyToOne, JoinC<PERSON>umn } from 'typeorm'
import { OfferPriceEntity } from './offerPrice.entity'
import { OfferPriceColEntity } from './offerPriceCol.entity'

@Entity('offer_price_col_value')
export class OfferPriceColValueEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  offerPriceId: string
  @ManyToOne(() => OfferPriceEntity, (p) => p.offerPriceColValue)
  @JoinColumn({ name: 'offerPriceId', referencedColumnName: 'id' })
  offerPrice: Promise<OfferPriceEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  offerPriceColId: string
  @ManyToOne(() => OfferPriceColEntity, (p) => p.offerPriceColValue)
  @JoinColumn({ name: 'offerPriceColId', referencedColumnName: 'id' })
  offerPriceCol: Promise<OfferPriceColEntity>
}
