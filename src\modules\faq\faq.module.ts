import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { FaqService } from './faq.service'
import { FaqController } from './faq.controller'
import { FaqRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([FaqRepository])],
  controllers: [FaqController],
  providers: [FaqService],
  exports: [FaqService],
})
export class FaqModule {}
