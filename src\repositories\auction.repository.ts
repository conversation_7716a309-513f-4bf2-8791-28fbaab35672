import { Repository } from 'typeorm'
import { AuctionEntity, AuctionHistoryEntity, AuctionSupplierEntity } from '../entities'
import { CustomRepository } from '../typeorm'

@CustomRepository(AuctionEntity)
export class AuctionRepository extends Repository<AuctionEntity> {}

@CustomRepository(AuctionSupplierEntity)
export class AuctionSupplierRepository extends Repository<AuctionSupplierEntity> {}

@CustomRepository(AuctionHistoryEntity)
export class AuctionHistoryRepository extends Repository<AuctionHistoryEntity> {}
