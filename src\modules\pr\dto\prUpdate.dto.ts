import { IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { PrItemDto } from './prItem.dto'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'

export class PrUpdateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  branchId: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  objectId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiPropertyOptional()
  @IsOptional()
  code: string

  @ApiPropertyOptional()
  @IsOptional()
  purchasePlanId: string

  @ApiPropertyOptional()
  @IsOptional()
  serviceId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  empProposerId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  empInChargeId: string

  @ApiProperty()
  @IsNotEmpty()
  deliveryDate: Date

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  deliveryAddress: string

  @ApiPropertyOptional()
  @IsOptional()
  status: string

  @ApiPropertyOptional()
  @IsOptional()
  prType: string

  @ApiPropertyOptional()
  employeeId: string

  @ApiPropertyOptional()
  lstEmployee: []

  @ApiPropertyOptional()
  lstItem: PrItemDto[]

  @ApiPropertyOptional()
  lstImage: any[]

  @ApiPropertyOptional()
  lstFile: any[]

  @ApiPropertyOptional()
  description: string

  @ApiPropertyOptional()
  isChangePrItem?: boolean
  @ApiPropertyOptional()
  isChangePrApprover?: boolean
}
