import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsBoolean, IsNumber } from 'class-validator'

export class BidTechUpdateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  bidId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isRequired: boolean

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  type: string

  @ApiPropertyOptional()
  isHighlight: boolean
  @ApiPropertyOptional()
  hightlightValue: number
  @ApiPropertyOptional()
  sort: number

  @ApiPropertyOptional()
  percent: number
  @ApiPropertyOptional()
  percentRule: number

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  level: number

  @ApiPropertyOptional()
  description: string

  @ApiPropertyOptional()
  parentId: string

  @ApiPropertyOptional()
  scoreDLC: number
  @ApiPropertyOptional()
  requiredMin: number

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isCalUp: boolean

  @ApiPropertyOptional()
  percentDownRule: number
}
