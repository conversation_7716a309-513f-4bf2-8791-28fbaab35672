import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { AsnEntity } from './asn.entity'
import { BaseEntity } from './base.entity'
import { BidEntity } from './bid.entity'
import { BranchEntity } from './branch.entity'
import { ContractEntity } from './contract.entity'
import { InvoiceSuggestEntity } from './invoiceSuggest.entity'
import { ObjectEntity } from './object.entity'
import { POHistoryEntity } from './poHistory.entity'
import { POMemberEntity } from './poMember.entity'
import { PaymentProgressEntity } from './paymentProgress.entity'
import { POProductEntity } from './poProduct.entity'
import { PrEntity } from './pr.entity'
import { SupplierEntity } from './supplier.entity'
import { BillEntity } from './bill.entity'
import { PaymentPoEntity } from './paymentPo.entity'
import { InboundEntity } from './inbound.entity'

/** PO */
@Entity({ name: 'po' })
export class POEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  title: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.pos)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.pos)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  prId: string
  @ManyToOne(() => PrEntity, (p) => p.pos)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>

  /** PO theo Hợp đồng */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  contractId: string
  @ManyToOne(() => ContractEntity, (p) => p.pos)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  /** PO theo Hợp đồng thì có chọn thêm tiến độ thanh toán theo HĐ */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  contractPaymentPlanId: string
  @ManyToOne(() => PaymentProgressEntity, (p) => p.pos)
  @JoinColumn({ name: 'contractPaymentPlanId', referencedColumnName: 'id' })
  contractPaymentPlan: Promise<PaymentProgressEntity>

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  //trạng thái đơn hàng
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  orderStatus: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  operator: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  type: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  region: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  currency: string

  /** Số tiền cần thanh toán của PO */
  @Column({
    type: 'bigint',
    default: 0,
  })
  money: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  company: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  email: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  phone: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Hình thức, enum ContractTypePo */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  paymentPlanType: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  branchId: string
  @ManyToOne(() => BranchEntity, (p) => p.pos)
  @JoinColumn({ name: 'branchId', referencedColumnName: 'id' })
  branch: Promise<BranchEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  createdBy: string

  /** Đối tượng */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  objectId: string
  @ManyToOne(() => ObjectEntity, (p) => p.pos)
  @JoinColumn({ name: 'objectId', referencedColumnName: 'id' })
  object: Promise<ObjectEntity>

  /** Ghi chú từ chối */
  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  reason: string

  /** Ngày cần giao */
  @Column({
    nullable: true,
  })
  deliveryDate: Date

  /** Tiến độ thanh toán */
  @OneToMany(() => PaymentProgressEntity, (p) => p.po)
  paymentPlan: Promise<PaymentProgressEntity[]>

  @OneToMany(() => POProductEntity, (p) => p.po)
  products: Promise<POProductEntity[]>

  @OneToMany(() => POMemberEntity, (p) => p.po)
  members: Promise<POMemberEntity[]>

  @OneToMany(() => POHistoryEntity, (p) => p.po)
  poHistorys: Promise<POHistoryEntity[]>

  /** Đề nghị thanh toán */
  @OneToMany(() => InvoiceSuggestEntity, (p) => p.po)
  invoiceSuggests: Promise<InvoiceSuggestEntity[]>

  @OneToMany(() => AsnEntity, (p) => p.po)
  asns: Promise<AsnEntity[]>

  /** Là PO con */
  @Column({
    nullable: true,
    default: false,
  })
  isChild: boolean

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  parentId: string
  // 1 PO có thể tách thành nhiều PO con
  @ManyToOne(() => POEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: POEntity
  @OneToMany(() => POEntity, (p) => p.parent)
  childs: Promise<POEntity[]>

  @OneToMany(() => BillEntity, (p) => p.po)
  bills: Promise<BillEntity[]>

  @OneToMany(() => PaymentPoEntity, (p) => p.po)
  paymentPos: Promise<PaymentPoEntity[]>

  /** DS inbounds */
  @OneToMany(() => InboundEntity, (p) => p.po)
  inbounds: Promise<InboundEntity[]>
}
