import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { enumProject } from '../../constants'
import { UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { DashboardService } from './dashboard.service'

@ApiBearerAuth()
@ApiTags('Dashboard')
@Controller('dashboard')
export class DashboardController {
  constructor(private readonly service: DashboardService) {}

  @ApiOperation({ summary: 'Lấy data cho các dashboard tổng quan' })
  @Roles(enumProject.Features.DASHBOARD_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_data_dashboard')
  public async loadDataDashboard(@CurrentUser() user: UserDto) {
    return await this.service.loadDashBoard(user)
  }

  @ApiOperation({ summary: 'Lấy data cho các dashboard tổng quan theo NCC' })
  @Roles(enumProject.Features.DASHBOARD_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_dashboard_supplier')
  public async loadDashboardSupplier(
    @CurrentUser() user: UserDto,
    @Body() data: { serviceId?: string[]; dateFrom?: Date; dateTo?: Date; isDeleted?: boolean; listChart?: number[]; category?: string },
  ) {
    return await this.service.loadDashBoardSupplier(user, data)
  }
  @ApiOperation({ summary: 'Lấy data cho dashboard top10 NCC' })
  @Roles(enumProject.Features.DASHBOARD_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_dashboard_top10_supplier')
  public async loadDashboardTop10Supplier(@CurrentUser() user: UserDto) {
    return await this.service.loadDashBoardTop10Supplier(user)
  }
}
