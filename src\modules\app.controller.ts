import { Get, Controller } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger'
import { AppService } from './app.service'

@ApiBearerAuth()
@ApiTags('')
@Controller()
export class AppController {
  constructor(private readonly service: AppService) {}

  @ApiOperation({ summary: 'Kiểm tra tình trạng server' })
  @Get('healthcheck')
  healthCheck() {
    return this.service.healthCheck()
  }

  @ApiOperation({ summary: 'Kiểm tra thời gian theo timezone của server' })
  @ApiResponse({ status: 200, description: 'Giờ server & giờ ở múi giờ 7' })
  @Get('timezone')
  checkTimeZone() {
    return this.service.checkTimeZone()
  }
}
