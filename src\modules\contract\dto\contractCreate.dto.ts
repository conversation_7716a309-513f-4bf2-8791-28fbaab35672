import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class ContractCreate {
  @ApiPropertyOptional()
  @IsOptional()
  code: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  supplierId: string

  /** Người quản lý hợp đồng */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  manageContractId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  bidId: string

  /** <PERSON><PERSON>i tượng */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  objectId: string

  @ApiPropertyOptional()
  @IsOptional()
  fileAttach: string

  @ApiPropertyOptional()
  @IsOptional()
  effectiveDate: Date

  @ApiPropertyOptional()
  @IsOptional()
  expiredDate: Date

  @ApiPropertyOptional()
  @IsOptional()
  status: string

  @ApiPropertyOptional()
  @IsOptional()
  reason: string

  @IsOptional()
  contractMirrorId: string

  @ApiPropertyOptional()
  @IsOptional()
  description: string

  @ApiPropertyOptional()
  @IsOptional()
  confirmContractId: string

  @ApiPropertyOptional()
  @IsOptional()
  anotherRoleIds: string[]

  @ApiPropertyOptional()
  @IsOptional()
  editContractId: string

  @ApiPropertyOptional()
  @IsOptional()
  branchId: string

  @ApiPropertyOptional()
  @IsOptional()
  createdBy: string

  @IsOptional()
  isGenChild: boolean

  @IsOptional()
  isPaymentProgress: boolean

  @ApiPropertyOptional()
  value: number

  /** DS tiến độ thanh toán */
  @ApiProperty()
  lstPaymentProgress: any[]
}
