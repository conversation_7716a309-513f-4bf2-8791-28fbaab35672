import { MigrationInterface, QueryRunner } from 'typeorm'

export class removeColSupplierHistory1680165677340 implements MigrationInterface {
  name = 'removeColSupplierHistory1680165677340'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`supplier_history\` DROP COLUMN \`status\``)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`supplier_history\` ADD \`status\` varchar(50) NOT NULL`)
  }
}
