import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import {
  SupplierRepository,
  ServiceRepository,
  SupplierServiceRepository,
  ServiceAccessRepository,
  DataHistoryRepository,
  SupplierExpertiseRepository,
} from '../../repositories'
import { SupplierReviewalController } from './supplierReviewal.controller'
import { SupplierReviewalService } from './supplierReviewal.service'
import { EmailModule } from '../email/email.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      SupplierRepository,
      SupplierServiceRepository,
      SupplierExpertiseRepository,
      ServiceRepository,
      ServiceAccessRepository,
      DataHistoryRepository,
    ]),

    EmailModule,
  ],
  controllers: [SupplierReviewalController],
  providers: [SupplierReviewalService],
  exports: [SupplierReviewalService],
})
export class SupplierReviewalModule {}
