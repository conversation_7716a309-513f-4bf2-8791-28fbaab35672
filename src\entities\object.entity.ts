import { Entity, Column, OneToMany } from 'typeorm'
import { AsnEntity } from './asn.entity'
import { BaseEntity } from './base.entity'
import { ContractEntity } from './contract.entity'
import { ContractAppendixEntity } from './contractAppendix.entity'
import { PrEntity } from './pr.entity'
import { POEntity } from './po.entity'

@Entity('object')
export class ObjectEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 1000,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  code: string

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  @OneToMany(() => PrEntity, (p) => p.object)
  prs: Promise<PrEntity[]>

  @OneToMany(() => ContractEntity, (p) => p.object)
  contracts: Promise<ContractEntity[]>

  @OneToMany(() => AsnEntity, (p) => p.object)
  asn: Promise<AsnEntity[]>

  @OneToMany(() => POEntity, (p) => p.object)
  pos: Promise<POEntity[]>

  @OneToMany(() => ContractAppendixEntity, (p) => p.object)
  contractAppendix: Promise<ContractAppendixEntity[]>
}
