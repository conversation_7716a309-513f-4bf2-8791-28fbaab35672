import { MigrationInterface, QueryRunner } from "typeorm";

export class migrationMaterial1730260700929 implements MigrationInterface {
    name = 'migrationMaterial1730260700929'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`material\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(1000) NOT NULL, \`code\` varchar(100) NULL, \`description\` varchar(550) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE \`material\``);
    }

}
