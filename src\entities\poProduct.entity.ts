import { Column, <PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { POEntity } from './po.entity'
import { ServiceEntity } from './service.entity'
import { MaterialEntity } from './material.entity'

//** PO detail */
@Entity({ name: 'po_product' })
export class POProductEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.products)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  type: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  group: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  note: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  unit: string

  @Column({
    nullable: false,
  })
  quantity: number

  @Column({
    nullable: false,
  })
  price: number

  @Column({
    nullable: false,
    type: 'float',
  })
  money: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  itemCode: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  itemId: string

  @ManyToOne(() => MaterialEntity, (p) => p.poProducts)
  @JoinColumn({ name: 'itemId', referencedColumnName: 'id' })
  item: Promise<MaterialEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  serviceId: string
  @ManyToOne(() => ServiceEntity, (p) => p.poProducts)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>
}
