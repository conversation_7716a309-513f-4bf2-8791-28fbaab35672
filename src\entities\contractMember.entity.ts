import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ContractEntity } from './contract.entity'
import { EmployeeEntity } from './employee.entity'

/** Bảng thành viên trong hd */
@Entity({ name: 'contract_member' })
export class ContractMemberEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.contractMembers)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  contractId: string
  @ManyToOne(() => ContractEntity, (p) => p.contractMembers)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  contractRoleCode: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string
}
