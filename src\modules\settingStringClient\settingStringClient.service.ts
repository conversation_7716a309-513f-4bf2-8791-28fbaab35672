import { Injectable, ConflictException } from '@nestjs/common'
import { SettingStringClientRepository } from '../../repositories'
import { CREATE_SUCCESS, enumData, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { In, Like } from 'typeorm'
import { PaginationDto, UserDto } from '../../dto'
import { SettingStringClientCreateDto, SettingStringClientUpdateDto } from './dto'

@Injectable()
export class SettingStringClientService {
  constructor(private readonly repo: SettingStringClientRepository) {}

  public async createData(user: UserDto, data: SettingStringClientCreateDto) {
    const objCheckType = await this.repo.findOne({ where: { type: data.type, companyId: user.companyId } })
    if (objCheckType) throw new ConflictException('Footer chỉ được tạo một duy nhất cho mỗi loại!')

    const newEntity = this.repo.create(data)
    newEntity.companyId = user.companyId
    newEntity.createdBy = user.id
    await this.repo.save(newEntity)

    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: SettingStringClientUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.description = data.description
    entity.updatedBy = user.id
    await this.repo.save(entity)

    return { message: UPDATE_SUCCESS }
  }

  public async paginationFooter(user: UserDto, data: PaginationDto) {
    const whereCon: any = {
      type: In([
        enumData.SettingStringClientType.Footer1.code,
        enumData.SettingStringClientType.Footer2.code,
        enumData.SettingStringClientType.Footer3.code,
      ]),
      companyId: user.companyId,
    }
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { type: 'ASC' },
    })
  }

  public async paginationHeader(user: UserDto, data: PaginationDto) {
    const whereCon: any = { type: enumData.SettingStringClientType.BannerName.code, companyId: user.companyId }
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { name: 'ASC' },
    })
  }

  public async updateIsDelete(user: UserDto, data: { id: string }) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }
}
