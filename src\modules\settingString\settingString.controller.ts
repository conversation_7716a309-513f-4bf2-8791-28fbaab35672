import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { enumProject } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { SettingStringCreateDto, SettingStringUpdateDto } from './dto'
import { SettingStringService } from './settingString.service'

@ApiBearerAuth()
@ApiTags('SettingString')
@Controller('settingStrings')
export class SettingStringController {
  constructor(private readonly service: SettingStringService) {}

  @ApiOperation({ summary: 'Danh sách các settingString' })
  @UseGuards(ApeAuthGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: { type?: string }) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Danh sách các settingString phân trang' })
  @Roles(
    enumProject.Features.SETTING_007.code,
    enumProject.Features.SETTING_008.code,
    enumProject.Features.SETTING_009.code,
    enumProject.Features.SETTING_010.code,
    enumProject.Features.SETTING_016.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo các settingString' })
  @Roles(
    enumProject.Features.SETTING_007.code,
    enumProject.Features.SETTING_008.code,
    enumProject.Features.SETTING_009.code,
    enumProject.Features.SETTING_010.code,
    enumProject.Features.SETTING_016.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: SettingStringCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật các settingString' })
  @Roles(
    enumProject.Features.SETTING_007.code,
    enumProject.Features.SETTING_008.code,
    enumProject.Features.SETTING_009.code,
    enumProject.Features.SETTING_010.code,
    enumProject.Features.SETTING_016.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: SettingStringUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động các settingString' })
  @Roles(
    enumProject.Features.SETTING_007.code,
    enumProject.Features.SETTING_008.code,
    enumProject.Features.SETTING_009.code,
    enumProject.Features.SETTING_010.code,
    enumProject.Features.SETTING_016.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateIsDelete(user, data)
  }
}
