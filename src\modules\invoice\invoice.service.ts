import { Injectable, NotAcceptableException } from '@nestjs/common'
import { CREATE_SUCCESS, enumData, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION } from '../../constants'
import { UserDto } from '../../dto'
import { ContractMemberEntity, EmployeeEntity, InvoiceEntity, InvoiceFileEntity, InvoiceSuggestHistoryEntity, POMemberEntity } from '../../entities'
import { InvoiceRepository, InvoiceSuggestRepository } from '../../repositories'
import { InvoiceCreate } from './dto'

@Injectable()
export class InvoiceService {
  constructor(private repo: InvoiceRepository, private invoiceSuggestRepo: InvoiceSuggestRepository) {}

  /** Thanh toán */
  public async createData(data: InvoiceCreate, user: UserDto) {
    const poMemberRepo = this.repo.manager.getRepository(POMemberEntity)
    const contractMemberRepo = this.repo.manager.getRepository(ContractMemberEntity)
    const employeeRepo = this.repo.manager.getRepository(EmployeeEntity)

    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const employee = await employeeRepo.findOne({ where: { id: user.employeeId, companyId: user.companyId } })
    if (!employee) throw new NotAcceptableException(ERROR_NOT_FOUND_DATA)

    const invoiceSuggest = await this.invoiceSuggestRepo.findOne({ where: { id: data.invoiceSuggestId, companyId: user.companyId } })
    if (!invoiceSuggest) throw new Error('Đề nghị thanh toán không tồn tại')
    if (+invoiceSuggest.money - +invoiceSuggest.moneyPaid != +data.moneyNeedPaid) {
      throw new Error('Đề nghị thanh toán đã thay đổi số tiền còn lại cần thanh toán, vui lòng thử lại.')
    }

    if (invoiceSuggest.poId) {
      const isPaymentPO = await poMemberRepo.findOne({
        where: {
          employeeId: user.employeeId,
          poId: invoiceSuggest.poId,
          companyId: user.companyId,
          poRoleCode: enumData.PORoleCode.PurchaseOrderPayMent.code,
        },
        select: { id: true },
      })
      if (!isPaymentPO) throw new Error('Bạn không có quyền thanh toán PO')

      const po = await invoiceSuggest.po
      if (po.status === enumData.PurchaseOrderStatus.Cancel.code) {
        throw new Error(`PO có mã [${po.code}] - Có trạng thái là [${enumData.PurchaseOrderStatus.Cancel.name}] - Không thể thanh toán`)
      }
      if (po.status === enumData.PurchaseOrderStatus.DeliveryRefuse.code) {
        throw new Error(`PO có mã [${po.code}] - Có trạng thái là [${enumData.PurchaseOrderStatus.DeliveryRefuse.name}] - Không thể thanh toán`)
      }
    }
    if (invoiceSuggest.contractId) {
      const isPaymentPO = await contractMemberRepo.findOne({
        where: {
          employeeId: user.employeeId,
          contractId: invoiceSuggest.contractId,
          companyId: user.companyId,
          contractRoleCode: enumData.ContractRoleCode.Management.code,
        },
        select: { id: true },
      })
      if (!isPaymentPO) throw new Error('Bạn không có quyền quản lý HĐ')
    }

    if (invoiceSuggest.status == enumData.InvoiceSuggestStatus.Paid.code) throw new Error('Đề nghị thanh toán đã hoàn thành, vui lòng kiểm tra lại.')

    const invoiceNew = new InvoiceEntity()
    invoiceNew.companyId = user.companyId
    invoiceNew.createdBy = user.id
    invoiceNew.invoiceSuggestId = data.invoiceSuggestId
    invoiceNew.description = data.description
    invoiceNew.invoiceDate = data.invoiceDate
    invoiceNew.employeeName = employee.name
    invoiceNew.money = +data.money
    const invoice = await this.repo.save(invoiceNew)

    //#region Cập nhật ĐNTT
    invoiceSuggest.moneyPaid = +invoiceSuggest.moneyPaid + +data.money

    let invoiceSuggestStatus = enumData.InvoiceSuggestStatus.Unpaid.code
    if (+invoiceSuggest.moneyPaid > 0) {
      invoiceSuggestStatus = enumData.InvoiceSuggestStatus.Partial.code
      if (+invoiceSuggest.moneyPaid >= +invoiceSuggest.money) {
        invoiceSuggestStatus = enumData.InvoiceSuggestStatus.Paid.code
      }
    }

    await this.invoiceSuggestRepo.update(invoiceSuggest.id, {
      moneyPaid: +invoiceSuggest.moneyPaid,
      status: invoiceSuggestStatus,
      updatedBy: user.id,
      description: data.description,
    })
    //#endregion

    let moneyStr = `${Intl.NumberFormat('en-US').format(+invoiceNew.money)}`
    const description = `${user.username} thanh toán ĐNTT [${invoiceSuggest.code}] với số tiền ${moneyStr}`
    const historyNew = new InvoiceSuggestHistoryEntity()
    historyNew.companyId = user.companyId
    historyNew.createdBy = user.id
    historyNew.invoiceSuggestId = invoiceSuggest.id
    historyNew.employeeId = user.employeeId
    historyNew.status = invoiceSuggestStatus
    historyNew.description = description
    await historyNew.save()

    for (const file of data.fileList) {
      const newFile = new InvoiceFileEntity()
      newFile.companyId = user.companyId
      newFile.createdBy = user.id
      newFile.invoiceId = invoice.id
      newFile.fileName = file.name
      newFile.fileUrl = file.url
      await newFile.save()
    }

    return { message: CREATE_SUCCESS }
  }
}
