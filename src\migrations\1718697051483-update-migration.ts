import { MigrationInterface, QueryRunner } from 'typeorm'

export class updateMigration1718697051483 implements MigrationInterface {
  name = 'updateMigration1718697051483'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`pr_child\` ADD \`isCreateChild\` tinyint NULL DEFAULT 0`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`pr_child\` DROP COLUMN \`isCreateChild\``)
  }
}
