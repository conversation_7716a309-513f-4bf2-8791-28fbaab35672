import { Injectable, ConflictException } from '@nestjs/common'
import { EmailTemplateCreateDto } from './dto/emailTemplateCreate.dto'
import { EmailTemplateRepository } from '../../repositories'
import { CREATE_SUCCESS, enumEmailType, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { EmailTemplateUpdateDto } from './dto'
import { Like } from 'typeorm'
import { PaginationDto, UserDto } from '../../dto'
import { coreHelper } from '../../helpers'

@Injectable()
export class EmailTemplateService {
  constructor(private readonly repo: EmailTemplateRepository) {}

  public async createData(user: UserDto, data: EmailTemplateCreateDto) {
    const objCheckCode = await this.repo.findOne({ where: { code: data.code, companyId: user.companyId }, select: { id: true } })
    if (objCheckCode) throw new ConflictException('Loại template đã có thiết lập template.')

    const newEntity = this.repo.create(data)

    newEntity.companyId = user.companyId
    newEntity.createdBy = user.id
    await this.repo.save(newEntity)

    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: EmailTemplateUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.description = data.description
    entity.updatedBy = user.id
    await this.repo.save(entity)

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { companyId: user.companyId }

    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { name: 'ASC' },
    })

    const dicTypeName: any = {}
    {
      const lst = coreHelper.convertObjToArray(enumEmailType)
      lst.forEach((c) => (dicTypeName[c.code] = c.default))
    }

    for (const item of res[0]) {
      item.typeName = dicTypeName[item.code]
    }

    return res
  }

  public async updateIsDelete(user: UserDto, data: { id: string }) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }
}
