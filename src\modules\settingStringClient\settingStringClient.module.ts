import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { SettingStringClientService } from './settingStringClient.service'
import { SettingStringClientController } from './settingStringClient.controller'
import { SettingStringClientRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([SettingStringClientRepository])],
  controllers: [SettingStringClientController],
  providers: [SettingStringClientService],
})
export class SettingStringClientModule {}
