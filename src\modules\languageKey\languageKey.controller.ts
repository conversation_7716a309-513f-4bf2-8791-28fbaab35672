import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'

import { LanguageKeyService } from './languageKey.service'
import { LanguageFilterDto, LanguageUpdateDto } from './dto'
import { CurrentUser } from '../common/decorators'
import { UserDto } from '../../dto'
import { ApeAuthGuard } from '../common/guards'

@ApiBearerAuth()
@ApiTags('Language_key')
@Controller('language_key')
export class LanguageKeyController {
  constructor(private readonly service: LanguageKeyService) { }

  @UseGuards(ApeAuthGuard)
  @ApiOperation({ summary: 'Cập nhật thông tin của một ngôn ngữ với dữ liệu được cung cấp.' })
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: LanguageUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds ngôn ngữ' })
  @Post('load_data')
  public async loadLanguage(@Body() data: LanguageFilterDto) {
    return await this.service.loadData(data)
  }
  @UseGuards(ApeAuthGuard)
  @ApiOperation({ summary: 'Lấy ds ngôn ngữ' })
  @Post('load_data_by_tenant')
  public async loadLanguageByTenant(@CurrentUser() user: UserDto, @Body() data: LanguageFilterDto) {
    return await this.service.loadLanguageByTenant(user, data)
  }

  @UseGuards(ApeAuthGuard)
  @ApiOperation({ summary: 'Lấy ds ngôn ngữ ở trang tenant' })
  @Post('load_language')
  public async loadLanguageTe(@Body() data: LanguageFilterDto) {
    return await this.service.loadData(data)
  }

  @UseGuards(ApeAuthGuard)
  @ApiOperation({ summary: 'Lấy ds key ngôn ngữ ở trang tenant' })
  @Post('load_list_language_key')
  public async loadListLanguageKey() {
    return await this.service.loadListLanguageKey()
  }

  @UseGuards(ApeAuthGuard)
  @ApiOperation({ summary: 'Cập nhật thông tin của dữ liệu bằng excel' })
  @Post('import_data')
  public async importExcel(@CurrentUser() user: UserDto, @Body() data: LanguageUpdateDto) {
    return await this.service.importExcel(user, data)
  }
}
