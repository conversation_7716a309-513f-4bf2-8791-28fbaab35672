import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, ManyToOne, Join<PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm'
import { BidEntity } from './bid.entity'
import { BidSupplierPriceValueEntity } from './bidSupplierPriceValue.entity'
import { BidPriceListDetailEntity } from './bidPriceListDetail.entity'
import { BidDealPriceEntity } from './bidDealPrice.entity'
import { ServicePriceEntity } from './servicePrice.entity'
import { BidDealSupplierPriceValueEntity } from './bidDealSupplierPriceValue.entity'
import { BidAuctionSupplierPriceValueEntity } from './bidAuctionSupplierPriceValue.entity'
import { BidAuctionPriceEntity } from './bidAuctionPrice.entity'
import { BidSupplierPriceColValueEntity } from './bidSupplierPriceColValue.entity'
import { BidPriceColValueEntity } from './bidPriceColValue.entity'
import { BidSupplierPriceEntity } from './bidSupplierPrice.entity'
import { MaterialEntity } from './material.entity'

@Entity('bid_price')
export class BidPriceEntity extends BaseEntity {
  /** Số lượng */
  @Column({
    nullable: false,
    default: 0,
  })
  number: number

  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  /** Tên  */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** Có bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  /** Có cấu hình giá hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isSetup: boolean

  /** Có Theo template cơ cấu giá hay không */
  @Column({
    nullable: false,
    default: true,
  })
  isTemplate: boolean

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  value: string

  /** Kiểu dữ liệu: string - number - cal. Nếu cal thì cho phép tạo công thức con*/
  @Column({
    nullable: false,
    default: 'string',
  })
  type: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  unit: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  currency: string

  /** % tỉ trọng */
  @Column({
    type: 'float',
    nullable: true,
    default: 100,
  })
  percent: number

  /** Cấp độ */
  @Column({
    nullable: false,
    default: 1,
  })
  level: number

  /** Mô tả */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  itemId: string

  @ManyToOne(() => MaterialEntity, (p) => p.prices)
  @JoinColumn({ name: 'itemId', referencedColumnName: 'id' })
  item: Promise<MaterialEntity>

  /** Id của công thức cha */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  parentId: string
  /** Cha */
  @ManyToOne(() => BidPriceEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: BidPriceEntity

  /** Con - 1 công thức sẽ có thể có nhiều con */
  @OneToMany(() => BidPriceEntity, (p) => p.parent)
  childs: Promise<BidPriceEntity[]>

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @Column({
    nullable: true,
  })
  scoreDLC: number

  /** Giá trị nhỏ nhất */
  @Column({
    nullable: true,
  })
  requiredMin: number

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidId: string
  /** 1 công thức chỉ có 1 dịch vụ cha */
  @ManyToOne(() => BidEntity, (p) => p.prices)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  servicePriceId: string
  @ManyToOne(() => ServicePriceEntity, (p) => p.bidPrices)
  @JoinColumn({ name: 'servicePriceId', referencedColumnName: 'id' })
  servicePrice: Promise<ServicePriceEntity>

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => BidSupplierPriceValueEntity, (p) => p.bidPrice)
  bidSupplierPriceValue: Promise<BidSupplierPriceValueEntity[]>

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => BidDealSupplierPriceValueEntity, (p) => p.bidPrice)
  bidDealSupplierPriceValue: Promise<BidDealSupplierPriceValueEntity[]>

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => BidAuctionSupplierPriceValueEntity, (p) => p.bidPrice)
  bidAuctionSupplierPriceValue: Promise<BidAuctionSupplierPriceValueEntity[]>

  /** Con - 1 công thức sẽ có thể có nhiều detail list */
  @OneToMany(() => BidPriceListDetailEntity, (p) => p.bidPrice)
  bidPriceListDetails: Promise<BidPriceListDetailEntity[]>

  @OneToMany(() => BidDealPriceEntity, (p) => p.bidPrice)
  bidDealPrices: Promise<BidDealPriceEntity[]>

  @OneToMany(() => BidAuctionPriceEntity, (p) => p.bidPrice)
  bidAuctionPrices: Promise<BidAuctionPriceEntity[]>

  @OneToMany(() => BidPriceColValueEntity, (p) => p.bidPrice)
  bidPriceColValue: Promise<BidPriceColValueEntity[]>

  @OneToMany(() => BidSupplierPriceColValueEntity, (p) => p.bidPrice)
  bidSupplierPriceColValue: Promise<BidSupplierPriceColValueEntity[]>

  /** Giá Doanh nghiệp chào */
  @OneToMany(() => BidSupplierPriceEntity, (p) => p.bidPrice)
  bidSupplierPrices: Promise<BidSupplierPriceEntity[]>
}
