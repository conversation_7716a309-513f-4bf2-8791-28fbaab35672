import { Injectable, NotFoundException, NotAcceptableException, BadRequestException } from '@nestjs/common'
import { PrCreateDto, PrUpdateDto } from './dto'
import {
  CREATE_SUCCESS,
  DELETE_SUCCESS,
  enumData,
  ERROR_CODE_TAKEN,
  ERROR_NOT_FOUND_DATA,
  ERROR_YOU_DO_NOT_HAVE_PERMISSION,
  UPDATE_ACTIVE_SUCCESS,
  UPDATE_SUCCESS,
} from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { EmployeeRepository, PrApproverRepository, PrItemRepository, PrRepository, ServiceTechRepository } from '../../repositories'
import { Code, In, IsNull, Like, Not, Raw } from 'typeorm'
import * as moment from 'moment'
import { coreHelper } from '../../helpers'
import {
  BidEntity,
  EmailTemplateEntity,
  EmployeeEntity,
  EmployeeWarningEntity,
  ItemTechEntity,
  ItemTechListDetailEntity,
  ObjectEntity,
  PrApproveEntity,
  PrApproverEntity,
  PrEntity,
  PrHistoryEntity,
  PrItemEntity,
  PurchasePlanEntity,
  ServiceEntity,
} from '../../entities'
import { ConflictException } from '@nestjs/common/exceptions'

@Injectable()
export class PrService {
  constructor(
    private readonly repo: PrRepository,
    private readonly prItemRepo: PrItemRepository,
    private readonly serviceTechRepo: ServiceTechRepository,
    private readonly prApproverRepo: PrApproverRepository,
    private readonly employeeRepo: EmployeeRepository,
  ) {}

  public async find(user: UserDto, data: { status?: string }) {
    const whereCon: any = {}
    whereCon.isDeleted = false
    if (data.status) {
      if (typeof data.status == 'string') {
        whereCon.status = data.status
      } else {
        whereCon.status = In(data.status)
      }
    }
    whereCon.companyId = user.companyId
    const rsData: any = await this.repo.find({ where: whereCon, relations: { prItems: true } })
    // console.log(rsData.__prItems__)
    rsData.lstItem = rsData.__prItems__ || []
    return rsData
  }

  // load các pr đã duyệt theo phân quyền và có thể tạo thầu
  public async loadPrCreateBid(user: UserDto, data: { bidId?: string }) {
    if (!user.employeeId) throw new Error(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const employee: any = await this.employeeRepo.findOne({ where: { id: user.employeeId, companyId: user.companyId, isDeleted: false } })
    if (!employee) throw new Error(ERROR_NOT_FOUND_DATA)

    // lấy những pr đã duyệt và cho phép tạo gói thầu
    const whereCommon: any = {
      status: enumData.PRStatus.Approved.code,
      isAllowBid: true,
      companyId: user.companyId,
      isDeleted: false,
      prItems: { isDeleted: false },
      // user là Admin thì không xét phân quyền
      createdBy: user.type != enumData.UserType.Admin.code ? user.employeeId : undefined,
    }

    let whereCon: any = whereCommon
    if (data.bidId) {
      const where3: any = { bids: { id: data.bidId }, prItems: { isDeleted: false } }
      whereCon = [whereCommon, where3]
    }

    const res: any[] = await this.repo.find({
      where: whereCon,
      relations: { prItems: { service: true } },
      order: { code: 'ASC', prItems: { code: 'ASC' } },
      select: {
        id: true,
        code: true,
        name: true,
        prItems: {
          id: true,
          productName: true,
          quantity: true,
          quantityBid: true,
          serviceId: true,
          service: { id: true, code: true, name: true, percentTech: true, percentTrade: true, percentPrice: true },
        },
      },
    })

    for (const item of res) {
      item.lstItem = item.__prItems__.filter((c: any) => c.quantityBid < c.quantity)
      delete item.__prItems__
      for (const dataItem of item.lstItem) {
        dataItem.prItemId = dataItem.id
        dataItem.itemName = dataItem.__service__.code + ' - ' + dataItem.__service__.name
        dataItem.percentTech = dataItem.__service__.percentTech
        dataItem.percentTrade = dataItem.__service__.percentTrade
        dataItem.percentPrice = dataItem.__service__.percentPrice
        dataItem.quantityItem = 0
        dataItem.quantityCreatedBid = dataItem.quantity - dataItem.quantityBid

        delete dataItem.__service__
      }
    }

    return res
  }

  public async findDetail(user: UserDto, data: { id: string }) {
    const res: any = await this.repo.findOne({
      where: { id: data.id, companyId: user.companyId },
      relations: {
        histories: true,
        prItems: { service: true, purchasePlan: true },
        empProposer: { department: { branch: true } },
        empInCharge: { department: { branch: true } },
        prApprovers: { employee: true },
        branch: true,
        object: true,
      },
      order: { histories: { createdAt: 'ASC' }, prApprovers: { level: 'ASC' } },
    })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    res.lstItem = res.__prItems__ || []
    console.log(res.lstItem)
    for (const item of res.lstItem) {
      item.materialCode = item.productName
      item.categoryName = item.suggestReason
      item.unitName = item.unit
    }
    delete res.__prItems__
    res.lstPermission = res.__prApprovers__ || []
    delete res.__prApprovers__

    if (user.id != res.createdBy && !res.lstPermission.some((s) => s.employeeId === user.employeeId))
      res.message = 'Nhân viên không có quyền duyệt trên PR này!'

    if (user.employeeId) {
      res.isApprover = await this.checkPermissionApprovePR(user, res.id) // Quyền duyệt PR
      res.isCloser = await this.checkPermissionClosePR(user, res.id) // Quyền đóng
      res.isCanceler = await this.checkPermissionCancelPR(user, res.id) // Quyền từ chối
      res.isDeleter = await this.checkPermissionDeletePR(user, res.id) // Quyền xoá
      res.isProcess = await this.checkPermissionProcessPR(user, res.id) // THỰC HIỆN PR
      res.isEdit = await this.checkPermissionEditPR(user, res.id) // Chỉnh sửa PR
      res.isRecheck = await this.checkPermissionRecheckPR(user, res.id) // Yêu cầu kiểm tra lại PR
    }

    return res
  }

  // quyền chỉnh sửa PR nếu không tạo thì ko đc chỉnh sửa
  public async checkPermissionEditPR(user: UserDto, prId: string) {
    if (!user.employeeId) {
      return false
    }

    const pr = await this.repo.findOne({ where: { id: prId, companyId: user.companyId } })
    if (!pr || pr.status !== enumData.PRStatus.New.code) return false

    // Nếu user đã duyệt rồi thì k hiện button nữa. Ở đây nói là cấp 1. VÌ cấp 2 thì đã đổi trạng thái rồi
    const historyprApproves = await pr.prApproves
    const check = historyprApproves.find((p) => p.employeeId === user.employeeId)
    if (check) {
      return false
    }
    // Danh sách lịch sử duyệt PR
    const historyApprove = await pr.prApproves
    // Nếu user bị user người cùng cấp 1 duyệt rồi cũng không hiện nữa. Ở đây nói là cấp 1. VÌ cấp 2 thì đã đổi trạng thái rồi
    const checkAnother = historyApprove.find((p) => p.level === 1)
    if (checkAnother) {
      return false
    }

    // Nếu không phải người tạo thì được chỉnh sửa
    if (!pr.createdBy || pr.createdBy !== user.employeeId) {
      return false
    }

    return true
  }

  // quyền yêu cầu kiểm tra lạim chỉ cấp 2 mới thấy nút yêu cầu kiểm tra lại
  public async checkPermissionRecheckPR(user: UserDto, prId: string) {
    if (!user.employeeId) {
      return false
    }

    const pr = await this.repo.findOne({ where: { id: prId, companyId: user.companyId } })
    if (!pr || pr.status !== enumData.PRStatus.New.code) return false

    if (pr.status !== enumData.PRStatus.New.code) {
      return false
    }

    const checkEmp = await this.prApproverRepo.findOne({
      where: {
        prId: pr.id,
        employeeId: user.employeeId,
        companyId: user.companyId,
        isDeleted: false,
        level: 2,
      },
    })

    if (checkEmp) {
      return true
    } else {
      return false
    }
  }

  public async createData(user: UserDto, data: PrCreateDto) {
    if (!user.employeeId) throw new Error(ERROR_NOT_FOUND_DATA)

    return this.repo.manager.transaction(async (manager) => {
      try {
        //#region code default

        const employee: any = await manager.getRepository(EmployeeEntity).findOne({
          where: { id: user.employeeId, companyId: user.companyId },
          relations: { branch: true },
        })
        const object = await manager.getRepository(ObjectEntity).findOne({ where: { id: data.objectId, companyId: user.companyId } })
        const serviceLevel1 = await manager.getRepository(ServiceEntity).findOne({ where: { id: data.serviceLevel1, companyId: user.companyId } })

        const prLast = await manager.getRepository(PrEntity).findOne({
          where: { companyId: user.companyId },
          order: { code: 'DESC' },
        })
        let sortString = '0'
        if (prLast) sortString = prLast.code.substring(0, 4)

        let lastSort = +sortString
        if (!lastSort || isNaN(lastSort)) lastSort = 0
        sortString = ('000' + (lastSort + 1)).slice(-4)
        let code = sortString // STT
        code += '/' + new Date().getFullYear().toString().slice(-2) // YY
        if (employee && employee.branchId) code += '/' + employee.__branch__.code // XXX
        if (object) code += '.' + object.code // ZZZ
        if (serviceLevel1) code += '.' + serviceLevel1.code // AAA
        code += '-' + data.prType // BBB

        //#endregion

        const entity = new PrEntity()
        entity.companyId = user.companyId
        entity.createdBy = user.id
        entity.branchId = data.branchId
        entity.objectId = data.objectId
        entity.code = code
        entity.name = data.name
        entity.empProposerId = data.empProposerId
        entity.empInChargeId = data.empInChargeId
        entity.deliveryDate = data.deliveryDate
        entity.description = data.description
        entity.deliveryAddress = data.deliveryAddress
        entity.status = enumData.PRStatus.New.code
        entity.prType = data.prType
        let sum = data.lstItem.reduce((sum: any, current: any) => +sum + +current.quantity, 0)
        entity.quantity = sum
        if (user.employeeId) entity.createdBy = user.employeeId
        if (data.lstFile) {
          entity.fileUrl = data.lstFile[0]?.response[0]
        }
        if (data.lstImage) {
          entity.imgUrl = data.lstImage[0]?.response[0]
        }
        const prEntity = await manager.getRepository(PrEntity).save(entity)
        // nếu như có danh sách file

        // nếu như có danh sách ảnh

        for (let item of data.lstItem) {
          const itemCode = coreHelper.codeDefaultItem()
          const prItem = new PrItemEntity()
          prItem.companyId = user.companyId
          prItem.createdBy = user.id
          prItem.prId = prEntity.id
          prItem.itemId = item.itemId
          if (item.purchasePlanId) {
            prItem.purchasePlanId = item.purchasePlanId
            const purchasePlan = await manager.getRepository(PurchasePlanEntity).findOne({
              where: { id: item.purchasePlanId, companyId: user.companyId, isDeleted: false },
            })
            if (purchasePlan) {
              prItem.serviceId = purchasePlan.serviceId
              if (sum > purchasePlan.quantity) {
                throw new Error(`Số lượng vượt quá số lượng của kế hoạch ${purchasePlan.code}. Vui lòng kiểm tra lại`)
              }
              prItem.quantity = item.quantity
            }
          } else {
            prItem.serviceId = item.serviceId
            prItem.quantity = item.quantity
          }
          prItem.productName = item.productName
          prItem.description = item.description
          prItem.suggestReason = item.suggestReason
          prItem.code = itemCode
          prItem.unit = item.unit
          await manager.getRepository(PrItemEntity).save(prItem)
        }

        if (data.employeeId) {
          // Nhân viên duyệt bước PR bước 1
          const prApproverNew = new PrApproverEntity()
          prApproverNew.companyId = user.companyId
          prApproverNew.createdBy = user.id
          prApproverNew.employeeId = data.employeeId
          prApproverNew.level = 1
          prApproverNew.prId = prEntity.id
          await manager.getRepository(PrApproverEntity).save(prApproverNew)
        }
        if (data.lstEmployee) {
          for (const item of data.lstEmployee) {
            const prApproverNew = new PrApproverEntity()
            prApproverNew.companyId = user.companyId
            prApproverNew.createdBy = user.id
            prApproverNew.employeeId = item
            prApproverNew.level = 2
            prApproverNew.prId = prEntity.id
            await manager.getRepository(PrApproverEntity).save(prApproverNew)
          }
        }

        const historyNew = new PrHistoryEntity()
        historyNew.companyId = user.companyId
        historyNew.createdBy = user.id
        historyNew.prId = prEntity.id
        historyNew.statusCurrent = prEntity.status
        historyNew.statusConvert = prEntity.status
        historyNew.description = 'Thêm mới yêu cầu mua hàng'
        historyNew.createdByName = user.username
        await manager.getRepository(PrHistoryEntity).save(historyNew)

        return { message: UPDATE_ACTIVE_SUCCESS }
      } catch (error) {
        throw error
      }
    })
  }

  public async updateData(user: UserDto, data: PrUpdateDto) {
    return this.repo.manager.transaction(async (manager) => {
      try {
        const prRepo = manager.getRepository(PrEntity)
        const prItemRepo = manager.getRepository(PrItemEntity)
        const itemTechRepo = manager.getRepository(ItemTechEntity)
        const prApproverRepo = manager.getRepository(PrApproverEntity)
        const bidRepo = manager.getRepository(BidEntity)

        const entity = await prRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
        if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
        if (data.code != entity.code) {
          const objCheckCode = await prRepo.findOne({ where: { code: data.code, companyId: user.companyId }, select: { id: true } })
          if (objCheckCode) throw new ConflictException(ERROR_CODE_TAKEN)
        }

        entity.branchId = data.branchId
        entity.objectId = data.objectId
        entity.code = data.code
        entity.name = data.name
        entity.empProposerId = data.empProposerId
        entity.empInChargeId = data.empInChargeId
        entity.deliveryDate = data.deliveryDate
        entity.deliveryAddress = data.deliveryAddress
        entity.status = data.status
        entity.prType = data.prType
        const sum = data.lstItem.reduce((sum: any, current: any) => +sum + +current.quantity, 0)
        entity.quantity = sum
        entity.description = data.description
        entity.updatedBy = user.id
        if (data.lstFile) {
          if (data.lstFile[0]?.response) entity.fileUrl = data.lstFile[0]?.response[0]
          if (data.lstFile[0]?.url) entity.fileUrl = data.lstFile[0]?.url
        }
        if (data.lstImage) {
          if (data.lstImage[0]?.response) entity.imgUrl = data.lstImage[0]?.response[0]
          if (data.lstImage[0]?.url) entity.imgUrl = data.lstImage[0]?.url
        }
        const updatedEntity = await prRepo.save(entity)

        if (data.isChangePrItem) {
          try {
            await prItemRepo.delete({ prId: entity.id })
          } catch {
            // check xem có thiết lập kỹ thuật không
            const objCheck1 = await itemTechRepo.findOne({ where: { prItem: { prId: entity.id } }, select: { id: true } })
            if (objCheck1) throw new Error('Vui lòng xóa Thiết lập yêu cầu kỹ thuật của Item trước')

            // check xem có gói thầu PR không
            const objCheck2 = await bidRepo.findOne({ where: { prItem: { prId: entity.id } }, select: { id: true } })
            if (objCheck2) throw new Error('PR đã được tạo gói thầu, không thể xóa')
          }

          for (let item of data.lstItem) {
            const itemCode = coreHelper.codeDefaultItem()
            const prItemNew = new PrItemEntity()
            prItemNew.companyId = user.companyId
            prItemNew.createdBy = user.id
            prItemNew.prId = entity.id
            prItemNew.itemId = item.itemId
            if (item.purchasePlanId) {
              prItemNew.purchasePlanId = item.purchasePlanId
              const purchasePlan = await manager.getRepository(PurchasePlanEntity).findOne({
                where: { id: item.purchasePlanId, companyId: user.companyId, isDeleted: false },
              })
              if (purchasePlan) {
                prItemNew.serviceId = purchasePlan.serviceId
                if (sum > purchasePlan.quantity) {
                  throw new Error(`Số lượng vượt quá số lượng của kế hoạch ${purchasePlan.code}. Vui lòng kiểm tra lại`)
                }
                prItemNew.quantity = item.quantity
              }
            } else {
              prItemNew.serviceId = item.serviceId
              prItemNew.quantity = item.quantity
            }
            prItemNew.purchasePlanId = item.purchasePlanId
            prItemNew.productName = item.productName
            prItemNew.description = item.description
            prItemNew.suggestReason = item.suggestReason
            prItemNew.code = itemCode
            prItemNew.unit = item.unit
            await prItemRepo.save(prItemNew)
          }
        }

        if (data.isChangePrApprover) {
          await prApproverRepo.delete({ prId: entity.id })
          if (data.employeeId) {
            // Nhân viên duyệt bước PR bước 1
            const prApproverNew = new PrApproverEntity()
            prApproverNew.companyId = user.companyId
            prApproverNew.createdBy = user.id
            prApproverNew.employeeId = data.employeeId
            prApproverNew.level = 1
            prApproverNew.prId = entity.id
            await prApproverRepo.save(prApproverNew)
          }
          if (data.lstEmployee) {
            for (const item of data.lstEmployee) {
              const prApproverNew = new PrApproverEntity()
              prApproverNew.companyId = user.companyId
              prApproverNew.createdBy = user.id
              prApproverNew.employeeId = item
              prApproverNew.level = 2
              prApproverNew.prId = entity.id
              await prApproverRepo.save(prApproverNew)
            }
          }
        }

        const historyNew = new PrHistoryEntity()
        historyNew.companyId = user.companyId
        historyNew.createdBy = user.id
        historyNew.prId = updatedEntity.id
        historyNew.statusCurrent = updatedEntity.status
        historyNew.statusConvert = updatedEntity.status
        historyNew.description = 'Chỉnh sửa yêu cầu mua hàng'
        historyNew.createdByName = user.username
        await manager.getRepository(PrHistoryEntity).save(historyNew)

        return { message: UPDATE_ACTIVE_SUCCESS, updatedEntity }
      } catch (error) {
        throw error
      }
    })
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new Error(ERROR_NOT_FOUND_DATA)
    let whereCon: any = { companyId: user.companyId, isDeleted: false }

    const lstBranch = await this.employeeRepo.getListBranchView(user)
    if (lstBranch && lstBranch.length > 0 && user.type !== enumData.UserType.Admin.code) {
      whereCon.branchId = In(lstBranch)
    }

    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)

    if (data.where.prType) whereCon.prType = Like(`%${data.where.prType}%`)

    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)

    if (data.where.status) whereCon.status = data.where.status

    if (data.where.empProposerId) whereCon.empProposerId = data.where.empProposerId

    if (data.where.empInChargeId) whereCon.empInChargeId = data.where.empInChargeId

    if (data.where.deliveryDate) {
      whereCon.deliveryDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.deliveryDate[0]).format('YYYY-MM-DD')}") AND DATE("${moment(
            data.where.deliveryDate[1],
          ).format('YYYY-MM-DD')}")`,
      )
    }

    if (data.where.purchasePlanId || data.where.serviceId) {
      const whereItem: any = { companyId: user.companyId, isDeleted: false }
      if (data.where.purchasePlanId) whereItem.purchasePlanId = data.where.purchasePlanId
      if (data.where.serviceId) whereItem.serviceId = data.where.serviceId
      const lstItem = await this.prItemRepo.find({ where: whereItem, select: { id: true, prId: true } })
      if (lstItem.length == 0) return [[], 0]

      const lstPrId = lstItem.map((s) => s.prId).filter((value, index, self) => self.indexOf(value) === index)
      whereCon.id = In(lstPrId)
    }

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      relations: { empProposer: true, empInCharge: true },
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })

    const dicStatus: any = {}
    const dicStatusColor: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.PRStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
      lstStatus.forEach((c) => (dicStatusColor[c.code] = c.color))
    }

    for (let item of res[0]) {
      item.empProposerName = item.__empProposer__?.name || ''
      item.empInChargeName = item.__empInCharge__?.name || ''
      delete item.__empProposer__
      delete item.__empInCharge__
      item.statusName = dicStatus[item.status]
      item.statusColor = dicStatusColor[item.status]
      if (user.employeeId) {
        item.isApprover = await this.checkPermissionApprovePR(user, item.id) // Quyền duyệt PR
        item.isCloser = await this.checkPermissionClosePR(user, item.id) // Quyền đóng
        item.isCanceler = await this.checkPermissionCancelPR(user, item.id) // Quyền từ chối
        item.isDeleter = await this.checkPermissionDeletePR(user, item.id) // Quyền xoá
        item.isProcess = await this.checkPermissionProcessPR(user, item.id) // THỰC HIỆN PR
        item.isEdit = await this.checkPermissionEditPR(user, item.id) // Chỉnh sửa PR
        item.isRecheck = await this.checkPermissionRecheckPR(user, item.id) // Yêu cầu kiểm tra lại PR
      }
    }

    return res
  }

  public async approvePR(user: UserDto, data: { id: string }) {
    if (!user.employeeId) throw new Error(ERROR_NOT_FOUND_DATA)

    return this.repo.manager.transaction(async (manager) => {
      try {
        const prRepo = await manager.getRepository(PrEntity)
        const prApproveRepo = await manager.getRepository(PrApproveEntity)
        const prApproverRepo = await manager.getRepository(PrApproverEntity)

        const pr = await prRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
        if (!pr) throw new Error(ERROR_NOT_FOUND_DATA)

        let lstStatus = coreHelper.convertObjToArray(enumData.PRStatus)
        const statusCurrent = lstStatus.find((s) => s.code === pr.status)
        const statusConvert = enumData.PRStatus.Approved

        const prApproveNew = new PrApproveEntity()
        prApproveNew.companyId = user.companyId
        prApproveNew.createdBy = user.id
        prApproveNew.prId = pr.id
        prApproveNew.level = 1
        // Kiểm tra xem cấu hình duyệt ở mấy level.
        // Tìm list quyền level 2 nếu mình có quyền level2 thì đổi trạng thái
        const objApproverLV2 = await prApproverRepo.findOne({
          where: {
            prId: pr.id,
            employeeId: user.employeeId,
            level: 2,
            companyId: user.companyId,
            isDeleted: false,
          },
        })

        if (objApproverLV2) {
          prApproveNew.level = 2
          pr.status = enumData.PRStatus.Approved.code
          pr.updatedBy = user.id
          await prRepo.save(pr)
        } else {
          // Tìm xem có ai tồn tại quyền vl2 không. Nếu k có thì chuyển trạng thái luôn. VÌ case này chỉ cần duyệt 1 cấp
          const checkApproverLV2 = await prApproverRepo.findOne({
            where: {
              prId: pr.id,
              level: 2,
              companyId: user.companyId,
              isDeleted: false,
            },
          })
          if (!checkApproverLV2) {
            prApproveNew.level = 1
            pr.status = enumData.PRStatus.Approved.code
            pr.updatedBy = user.id
            await prRepo.save(pr)
          }
        }

        prApproveNew.employeeId = user.employeeId
        prApproveNew.status = pr.status
        prApproveNew.description = pr.status
        const prApproveEntity = await prApproveRepo.save(prApproveNew)

        //#region history
        const description = `Tài khoản [${user.username}] vừa cập nhật PR có mã [${pr.code}] -  Trạng thái Cũ [${statusCurrent.name}] - Trạng thái Mới [${statusConvert.name}] - Cấp duyệt [${prApproveEntity.level}]`

        const historyNew = new PrHistoryEntity()
        historyNew.companyId = user.companyId
        historyNew.createdBy = user.id
        historyNew.prId = pr.id
        historyNew.statusCurrent = statusCurrent.code
        historyNew.statusConvert = statusConvert.code
        historyNew.description = description
        historyNew.createdByName = user.username
        await manager.getRepository(PrHistoryEntity).save(historyNew)

        //#endregion

        return { message: UPDATE_ACTIVE_SUCCESS }
      } catch (error) {
        throw error
      }
    })
  }

  public async cancelPR(user: UserDto, data: { id: string; reason?: string }) {
    if (!user.employeeId) throw new Error(ERROR_NOT_FOUND_DATA)
    return this.repo.manager.transaction(async (manager) => {
      try {
        const prRepo = await manager.getRepository(PrEntity)

        const pr = await prRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
        if (!pr) throw new Error(ERROR_NOT_FOUND_DATA)
        //#region history

        const lstStatus = coreHelper.convertObjToArray(enumData.PRStatus)
        const statusCurrent = lstStatus.find((s) => s.code === pr.status)
        const statusConvert = enumData.PRStatus.Cancel
        let description = `Tài khoản [${user.username}] vừa cập nhật [Hủy PR] - Trạng thái Cũ [${statusCurrent?.name}] - Trạng thái Mới [${statusConvert.name}]`
        if (data.reason) description = `${description} - Lý do: ${data.reason}`

        const historyNew = new PrHistoryEntity()
        historyNew.companyId = user.companyId
        historyNew.createdBy = user.id
        historyNew.prId = pr.id
        historyNew.statusCurrent = statusCurrent.code
        historyNew.statusConvert = statusConvert.code
        historyNew.description = description
        historyNew.createdByName = user.username
        await manager.getRepository(PrHistoryEntity).save(historyNew)

        //#endregion

        pr.status = statusConvert.code
        pr.updatedBy = user.id
        if (data.reason) pr.reason = data.reason

        await prRepo.save(pr)

        return { message: UPDATE_ACTIVE_SUCCESS }
      } catch (error) {
        throw error
      }
    })
  }

  public async closePR(user: UserDto, data: { id: string }) {
    if (!user.employeeId) throw new Error(ERROR_NOT_FOUND_DATA)
    return this.repo.manager.transaction(async (manager) => {
      try {
        const prRepo = manager.getRepository(PrEntity)

        const pr = await prRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
        if (!pr) throw new Error(ERROR_NOT_FOUND_DATA)
        //#region history

        const lstStatus = coreHelper.convertObjToArray(enumData.PRStatus)
        const statusCurrent = lstStatus.find((s) => s.code === pr.status)
        const statusConvert = enumData.PRStatus.Close
        const description = `Tài khoản [${user.username}] vừa cập nhật PR có mã [${pr.code}] - Trạng thái Cũ [${statusCurrent?.name}] - Trạng thái Mới [${statusConvert.name}]`

        const historyNew = new PrHistoryEntity()
        historyNew.companyId = user.companyId
        historyNew.createdBy = user.id
        historyNew.prId = pr.id
        historyNew.statusCurrent = statusCurrent.code
        historyNew.statusConvert = statusConvert.code
        historyNew.description = description
        historyNew.createdByName = user.username
        await manager.getRepository(PrHistoryEntity).save(historyNew)

        //#endregion

        pr.status = statusConvert.code
        pr.updatedBy = user.id
        await prRepo.save(pr)

        return { message: UPDATE_ACTIVE_SUCCESS }
      } catch (error) {
        throw error
      }
    })
  }

  public async processPR(user: UserDto, data: { id: string }) {
    if (!user.employeeId) throw new Error(ERROR_NOT_FOUND_DATA)

    return this.repo.manager.transaction(async (manager) => {
      try {
        const prRepo = manager.getRepository(PrEntity)
        const pr = await prRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
        if (!pr) throw new Error(ERROR_NOT_FOUND_DATA)
        //#region history

        const lstStatus = coreHelper.convertObjToArray(enumData.PRStatus)
        const statusCurrent = lstStatus.find((s) => s.code === pr.status)
        const statusConvert = enumData.PRStatus.Processing
        const description = `Tài khoản [${user.username}] vừa cập nhật PR có mã [${pr.code}] - Trạng thái Cũ [${statusCurrent.name}] - Trạng thái Mới [${statusConvert.name}]`

        const historyNew = new PrHistoryEntity()
        historyNew.companyId = user.companyId
        historyNew.createdBy = user.id
        historyNew.prId = pr.id
        historyNew.statusCurrent = statusCurrent.code
        historyNew.statusConvert = statusConvert.code
        historyNew.description = description
        historyNew.createdByName = user.username
        await manager.getRepository(PrHistoryEntity).save(historyNew)

        //#endregion

        pr.status = statusConvert.code
        pr.updatedBy = user.id
        await prRepo.save(pr)

        return { message: UPDATE_ACTIVE_SUCCESS }
      } catch (error) {
        throw error
      }
    })
  }

  public async contractPR(user: UserDto, data: { id: string }) {
    if (!user.employeeId) throw new Error(ERROR_NOT_FOUND_DATA)

    return this.repo.manager.transaction(async (manager) => {
      try {
        const prRepo = manager.getRepository(PrEntity)

        const pr = await prRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
        if (!pr) throw new Error(ERROR_NOT_FOUND_DATA)
        //#region history

        const lstStatus = coreHelper.convertObjToArray(enumData.PRStatus)
        const statusCurrent = lstStatus.find((s) => s.code === pr.status)
        const statusConvert = enumData.PRStatus.Processing
        const description = `Trạng thái Cũ [${statusCurrent.name}] - Trạng thái Mới [${statusConvert.name}]`

        const historyNew = new PrHistoryEntity()
        historyNew.companyId = user.companyId
        historyNew.createdBy = user.id
        historyNew.prId = pr.id
        historyNew.statusCurrent = statusCurrent.code
        historyNew.statusConvert = statusConvert.code
        historyNew.description = description
        historyNew.createdByName = user.username
        await manager.getRepository(PrHistoryEntity).save(historyNew)

        //#endregion

        pr.status = statusConvert.code
        pr.updatedBy = user.id
        await prRepo.save(pr)

        return { message: UPDATE_ACTIVE_SUCCESS }
      } catch (error) {
        throw error
      }
    })
  }

  /** Quyền duyệt PR */
  public async checkPermissionApprovePR(user: UserDto, prId: string) {
    const pr = await this.repo.findOne({ where: { id: prId, companyId: user.companyId } })
    if (!pr || pr.status !== enumData.PRStatus.New.code) return false

    // Danh sách những người có quyền duyệt PR. Có 2 trường hợp.
    // 1- TRả về 1 dòng dữ liêu. Tức là user hiện tại có 1 quyền
    // 2- Trả về 2 dòng dữ liệu. Tức là user hiện tại có 2 quyền
    const listEmp = await this.prApproverRepo.find({
      where: {
        prId: pr.id,
        employeeId: user.employeeId,
        companyId: user.companyId,
        isDeleted: false,
      },
    })

    // Nếu k có quyền thì không cần kiểm tra nữa
    if (listEmp.length == 0) return false

    // Danh sách lịch sử duyệt PR
    const historyApprove = await pr.prApprovers
    let lstLevel = listEmp.find((x) => x.level === 2)
    if (lstLevel) {
      if (historyApprove && historyApprove.length > 0) {
        return true
      } else {
        return false
      }
    }

    // Nếu user đã duyệt rồi thì k hiện button nữa. Ở đây nói là cấp 1. VÌ cấp 2 thì đã đổi trạng thái rồi
    const historyprApproves = await pr.prApproves
    const check = historyprApproves.find((p) => p.employeeId === user.employeeId)
    if (check) {
      return false
    }

    // Nếu user bị user người cùng cấp 1 duyệt rồi cũng không hiện nữa. Ở đây nói là cấp 1. VÌ cấp 2 thì đã đổi trạng thái rồi
    const checkAnother = historyprApproves.find((p) => p.level === 1)
    if (checkAnother) {
      return false
    }

    return true
  }

  /** Quyền đóng */
  public async checkPermissionClosePR(user: UserDto, prId: string) {
    const pr = await this.repo.findOne({ where: { id: prId, companyId: user.companyId } })
    if (!pr || pr.status !== enumData.PRStatus.Processing.code) return false

    // Nếu không phải nhân viên phụ trách thì ko thể đóng
    if (!pr.empInChargeId || pr.empInChargeId !== user.employeeId) return false

    return true
  }

  /** Quyền xóa PR */
  public async checkPermissionDeletePR(user: UserDto, prId: string) {
    const pr = await this.repo.findOne({ where: { id: prId, companyId: user.companyId } })
    if (!pr) return false

    if (pr.status === enumData.PRStatus.Close.code || pr.status === enumData.PRStatus.Cancel.code) return false

    // Nếu không phải người tạo thì không được gửi
    if (!pr.empInChargeId || pr.empInChargeId !== user.employeeId) return false

    return true
  }

  /** Quyền từ chối PR */
  public async checkPermissionCancelPR(user: UserDto, prId: string) {
    const pr = await this.repo.findOne({ where: { id: prId, companyId: user.companyId } })
    if (!pr) return false

    if (pr.status === enumData.PRStatus.Close.code) return false

    if (pr.status === enumData.PRStatus.Cancel.code) return false

    if (pr.status === enumData.PRStatus.Approved.code) return false

    if (pr.status === enumData.PRStatus.Processing.code) return false

    // Danh sách những người có quyền duyệt PR. Có 2 trường hợp.
    // 1- TRả về 1 dòng dữ liêu. Tức là user hiện tại có 1 quyền
    // 2- Trả về 2 dòng dữ liệu. Tức là user hiện tại có 2 quyền
    const listEmp = await this.prApproverRepo.find({
      where: {
        prId: pr.id,
        employeeId: user.employeeId,
        companyId: user.companyId,
        isDeleted: false,
      },
    })
    // Nếu k có quyền thì không cần kiểm tra nữa
    if (listEmp.length == 0) return false

    // Danh sách lịch sử duyệt PR
    const historyApprove = await pr.prApproves

    // Nếu user đã duyệt rồi thì k hiện button nữa. Ở đây nói là cấp 1. VÌ cấp 2 thì đã đổi trạng thái rồi
    const check = historyApprove.find((p) => p.employeeId === user.employeeId && p.level === 1)
    if (check) return false

    return true
  }

  /** Quyền thực hiện PR */
  public async checkPermissionProcessPR(user: UserDto, prId: string) {
    const pr = await this.repo.findOne({ where: { id: prId, companyId: user.companyId } })
    if (!pr) return false

    if (pr.status !== enumData.PRStatus.Approved.code) return false

    // Nếu không phải người tạo thì không được gửi
    if (!pr.empInChargeId || pr.empInChargeId !== user.employeeId) return false

    return true
  }

  /** Quyền ký HĐ */
  public async checkPermissionContractPR(user: UserDto, prId: string) {
    const pr = await this.repo.findOne({ where: { id: prId, companyId: user.companyId } })
    if (!pr) return false

    if (pr.status !== enumData.PRStatus.Processing.code) return false

    // Nếu không phải người tạo thì không được gửi
    if (!pr.empInChargeId || pr.empInChargeId !== user.employeeId) return false

    return true
  }

  //#region itemTech

  /** Lấy thông tin thiết lập yêu cầu kỹ thuật của vật tư */
  async loadTech(user: UserDto, prItemId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const prItem = await this.prItemRepo.findOne({ where: { id: prItemId, companyId: user.companyId } })
    if (!prItem) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const lstServiceTech = await this.serviceTechRepo.find({
      where: { serviceId: prItem.serviceId, parentId: IsNull(), companyId: user.companyId, isDeleted: false },
      relations: { serviceTechListDetails: true, childs: { serviceTechListDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })

    return await this.repo.manager.transaction(async (manager) => {
      const itemTechRepo = manager.getRepository(ItemTechEntity)
      const itemTechListDetailRepo = manager.getRepository(ItemTechListDetailEntity)
      for (const a of lstServiceTech) {
        const item = new ItemTechEntity()
        item.companyId = user.companyId
        item.createdBy = user.id
        item.prItemId = prItemId
        item.sort = a.sort
        item.name = a.name
        item.isRequired = a.isRequired
        item.type = a.type
        item.percent = a.percent
        item.percentRule = a.percentRule
        item.isCalUp = a.isCalUp
        item.percentDownRule = a.percentDownRule
        item.level = a.level
        item.description = a.description
        item.parentId = a.parentId
        item.scoreDLC = a.scoreDLC
        item.requiredMin = a.requiredMin
        item.isHighlight = a.isHighlight
        item.hightlightValue = a.hightlightValue
        const itemTechEntity = await itemTechRepo.save(item)

        const lstChild = (await a.childs).filter((c) => !c.isDeleted)

        for (const b of lstChild) {
          const itemChild = new ItemTechEntity()
          itemChild.companyId = user.companyId
          itemChild.createdBy = user.id
          itemChild.prItemId = prItemId
          itemChild.sort = b.sort
          itemChild.name = b.name
          itemChild.isRequired = b.isRequired
          itemChild.type = b.type
          itemChild.percent = b.percent
          itemChild.percentRule = b.percentRule
          itemChild.isCalUp = b.isCalUp
          itemChild.percentDownRule = b.percentDownRule
          itemChild.level = b.level
          itemChild.description = b.description
          itemChild.parentId = itemTechEntity.id
          itemChild.scoreDLC = b.scoreDLC
          itemChild.requiredMin = b.requiredMin
          itemChild.isHighlight = b.isHighlight
          itemChild.hightlightValue = b.hightlightValue
          const itemTechChildEntity = await itemTechRepo.save(itemChild)

          const lstDataTypeList = (await b.serviceTechListDetails).filter((c) => !c.isDeleted)
          for (const c of lstDataTypeList) {
            const itemListDetail = new ItemTechListDetailEntity()
            itemListDetail.companyId = user.companyId
            itemListDetail.createdBy = user.id
            itemListDetail.itemTechId = itemTechChildEntity.id
            itemListDetail.name = c.name
            itemListDetail.value = c.value
            await itemTechListDetailRepo.save(itemListDetail)
          }
        }

        const lstDataTypeList = (await a.serviceTechListDetails).filter((c) => !c.isDeleted)
        if (lstDataTypeList && lstDataTypeList.length > 0) {
          for (const c of lstDataTypeList) {
            const itemListDetail = new ItemTechListDetailEntity()
            itemListDetail.companyId = user.companyId
            itemListDetail.createdBy = user.id
            itemListDetail.itemTechId = itemTechEntity.id
            itemListDetail.name = c.name
            itemListDetail.value = c.value
            await itemTechListDetailRepo.save(itemListDetail)
          }
        }
      }
    })
  }

  async techDeleteData(user: UserDto, itemTechId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    return this.repo.manager.transaction(async (manager) => {
      const itemTechRepo = manager.getRepository(ItemTechEntity)
      const itemTechListDetailRepo = manager.getRepository(ItemTechListDetailEntity)

      const itemTech = await itemTechRepo.findOne({ where: { id: itemTechId, companyId: user.companyId } })
      if (!itemTech) throw new Error(ERROR_NOT_FOUND_DATA)

      const lstChild = await itemTech.childs
      for (const itemTechChild of lstChild) {
        await itemTechListDetailRepo.delete({ itemTechId: itemTechChild.id })
      }
      await itemTechListDetailRepo.delete({ itemTechId: itemTechId })
      await itemTechRepo.delete({ parentId: itemTechId })
      await itemTechRepo.delete(itemTechId)

      return { message: DELETE_SUCCESS }
    })
  }

  async techDeleteAllData(user: UserDto, prItemId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    return this.repo.manager.transaction(async (manager) => {
      const itemTechRepo = manager.getRepository(ItemTechEntity)
      const itemTechListDetailRepo = manager.getRepository(ItemTechListDetailEntity)

      const lstItemTech = await itemTechRepo.find({ where: { prItemId, companyId: user.companyId } })
      for (const itemTech of lstItemTech) {
        const lstItemTechChild = await itemTech.childs
        for (const itemTechChild of lstItemTechChild) {
          // xoá lst detail
          await itemTechListDetailRepo.delete({ itemTechId: itemTechChild.id })
        }
        // xoá lst
        await itemTechListDetailRepo.delete({ itemTechId: itemTech.id })
        // xoá con
        await itemTechRepo.delete({ parentId: itemTech.id })
        // xoá
        await itemTechRepo.delete(itemTech.id)
      }

      return { message: DELETE_SUCCESS }
    })
  }

  public async tech_import(user: UserDto, prItemId: string, data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    await this.techDeleteAllData(user, prItemId)

    return this.repo.manager.transaction(async (manager) => {
      const itemTechRepo = manager.getRepository(ItemTechEntity)
      const itemTechListDetailRepo = manager.getRepository(ItemTechListDetailEntity)

      // add lv1
      var lstDataLv1 = data.lstDataTable1.filter((c: any) => c.level == 1)
      for (const item of lstDataLv1) {
        const objItemTechNew = new ItemTechEntity()
        objItemTechNew.companyId = user.companyId
        objItemTechNew.createdBy = user.id
        objItemTechNew.prItemId = prItemId
        objItemTechNew.level = 1
        objItemTechNew.sort = item.sort || 0
        objItemTechNew.name = item.name
        objItemTechNew.percent = item.percent
        objItemTechNew.percentRule = item.percentRule
        objItemTechNew.requiredMin = item.requiredMin
        objItemTechNew.type = item.type
        objItemTechNew.isRequired = item.isRequired
        objItemTechNew.isHighlight = item.isHighlight
        objItemTechNew.hightlightValue = item.hightlightValue
        objItemTechNew.isCalUp = item.isCalUp
        objItemTechNew.percentDownRule = item.percentDownRule

        const objItemTech = await itemTechRepo.save(objItemTechNew)
        item.id = objItemTech.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        for (const detail of lstDetail) {
          const detailNew = new ItemTechListDetailEntity()
          detailNew.companyId = user.companyId
          detailNew.createdBy = user.id
          detailNew.itemTechId = item.id
          detailNew.name = detail.nameList
          detailNew.value = detail.valueList
          await itemTechListDetailRepo.save(detailNew)
        }
      }

      // add lv2
      var lstDataLv2 = data.lstDataTable1.filter((c: any) => c.level == 2)
      for (const item of lstDataLv2) {
        const objItemTechNew = new ItemTechEntity()
        objItemTechNew.companyId = user.companyId
        objItemTechNew.createdBy = user.id
        objItemTechNew.prItemId = prItemId
        objItemTechNew.level = 2
        objItemTechNew.sort = item.sort || 0
        objItemTechNew.name = item.name
        objItemTechNew.percent = item.percent
        objItemTechNew.percentRule = item.percentRule
        objItemTechNew.requiredMin = item.requiredMin
        objItemTechNew.type = item.type
        objItemTechNew.isRequired = item.isRequired
        objItemTechNew.isHighlight = item.isHighlight
        objItemTechNew.hightlightValue = item.hightlightValue
        objItemTechNew.isCalUp = item.isCalUp
        objItemTechNew.percentDownRule = item.percentDownRule
        const parent = lstDataLv1.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objItemTechNew.parentId = parent.id

        const objItemTech = await itemTechRepo.save(objItemTechNew)
        item.id = objItemTech.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        for (const detail of lstDetail) {
          const detailNew = new ItemTechListDetailEntity()
          detailNew.companyId = user.companyId
          detailNew.createdBy = user.id
          detailNew.itemTechId = item.id
          detailNew.name = detail.nameList
          detailNew.value = detail.valueList
          await itemTechListDetailRepo.save(detailNew)
        }
      }
    })
  }

  /** Lấy thiết lập yêu cầu kỹ thuật của vật tư */
  async getTech(user: UserDto, prItemId: string) {
    return await this.repo.manager.getRepository(ItemTechEntity).find({
      where: { prItemId, parentId: IsNull(), companyId: user.companyId, isDeleted: false },
      relations: { itemTechListDetails: true, childs: { itemTechListDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })
  }

  /** Lấy data cbb tiêu chí cấp 1 */
  async techGetData(user: UserDto, prItemId: string) {
    return await this.repo.manager.getRepository(ItemTechEntity).find({ where: { prItemId, level: 1, companyId: user.companyId, isDeleted: false } })
  }

  async techCreateData(user: UserDto, data: any) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.prItemId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    return this.repo.manager.transaction(async (manager) => {
      const itemTechRepo = manager.getRepository(ItemTechEntity)

      const item = new ItemTechEntity()
      item.companyId = user.companyId
      item.createdBy = user.id
      item.prItemId = data.prItemId
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.percentRule = data.percentRule
      item.isCalUp = data.isCalUp
      item.percentDownRule = data.percentDownRule
      item.level = data.level
      item.description = data.description
      item.parentId = data.parentId
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      item.isHighlight = data.isHighlight
      item.hightlightValue = data.hightlightValue
      await itemTechRepo.save(item)
    })
  }

  async techUpdateData(user: UserDto, data: any) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.prItemId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    return this.repo.manager.transaction(async (manager) => {
      const itemTechRepo = manager.getRepository(ItemTechEntity)

      const item = await itemTechRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!item) throw new Error(ERROR_NOT_FOUND_DATA)
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.percentRule = data.percentRule
      item.isCalUp = data.isCalUp
      item.percentDownRule = data.percentDownRule
      item.level = data.level
      item.description = data.description
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      item.isHighlight = data.isHighlight
      item.hightlightValue = data.hightlightValue
      item.updatedBy = user.id
      await itemTechRepo.save(item)
    })
  }

  public async itemTechListDetail_list(user: UserDto, itemTechId: string) {
    return await this.repo.manager.getRepository(ItemTechListDetailEntity).find({
      where: { itemTechId, companyId: user.companyId },
      order: { value: 'DESC' },
    })
  }

  public async itemTechListDetail_create_data(user: UserDto, data: { itemTechId: string; value: number; name: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const itemTech = await this.repo.manager.getRepository(ItemTechEntity).findOne({ where: { id: data.itemTechId, companyId: user.companyId } })
    if (!itemTech) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const entity = new ItemTechListDetailEntity()
    entity.companyId = user.companyId
    entity.createdBy = user.id
    entity.name = data.name
    entity.value = data.value
    entity.itemTechId = data.itemTechId
    await entity.save()

    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async itemTechListDetail_update_data(user: UserDto, data: { id: string; itemTechId: string; value: number; name: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const itemTech = await this.repo.manager.getRepository(ItemTechEntity).findOne({ where: { id: data.itemTechId, companyId: user.companyId } })
    if (!itemTech) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const entity = await this.repo.manager.getRepository(ItemTechListDetailEntity).findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.value = data.value
    entity.updatedBy = user.id

    await entity.save()

    return { id: entity.id, message: UPDATE_SUCCESS }
  }

  public async itemTechListDetail_delete_data(user: UserDto, id: string) {
    const itemTechListRepo = this.repo.manager.getRepository(ItemTechListDetailEntity)
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const itemTechListDetail = await itemTechListRepo.findOne({ where: { id, companyId: user.companyId } })
    if (!itemTechListDetail) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    await itemTechListRepo.delete(id)

    return { message: DELETE_SUCCESS }
  }

  public async findItemDetail(user: UserDto, data: { id: string }) {
    const res = await this.prItemRepo.find({
      where: { prId: data.id, companyId: user.companyId, isDeleted: false },
      relations: { service: { parent: true }, purchasePlan: true, pr: true },
    })

    return res
  }

  //#endregion

  /** Cảnh báo khi đến hạn mua hàng theo kế hoạch đã đề ra */
  public async autoCreateWarningExpiry() {
    return this.repo.manager.transaction(async (manager) => {
      try {
        const warningType = enumData.WarningType.Purchare_Plan_Expiry
        await manager.getRepository(EmployeeWarningEntity).delete({
          dataId: Not(IsNull()),
          dataType: In([enumData.DataWarningType.PR_Plan.code, enumData.DataWarningType.PR_Aries.code]),
          warningType: warningType.code,
        })

        const lstExpiry = await manager.getRepository(PrEntity).find({
          where: {
            isDeleted: false,
            status: Not(In([enumData.PurchaseOrderStatus.Cancel.code, enumData.PurchaseOrderStatus.Complete.code])),
            deliveryDate: Raw((alias) => `DATE(${alias}) < DATE("${moment(new Date()).format('YYYY-MM-DD')}")`),
          },
          relations: ['empProposer', 'empInCharge'],
        })
        if (lstExpiry.length > 0) {
          let html = warningType.default
          let subject = warningType.name
          const template = await manager.getRepository(EmailTemplateEntity).findOne({
            where: { code: warningType.code, isDeleted: false },
          })
          if (template) {
            html = template.description
            subject = template.name
          }
          for await (const pr of lstExpiry) {
            let dataType = enumData.DataWarningType.PR_Plan.code
            if (pr.prType == enumData.PRType.Arise.code) {
              dataType = enumData.DataWarningType.PR_Aries.code
            }

            const subject_text = coreHelper.stringInject(subject, [pr.code])
            // let link = `&nbsp; <button onclick="showDataDetail('${dataType}', '${pr.id}')">Xem Chi Tiết</button>`

            let empProposer = await pr.empProposer
            let empInCharge = await pr.empInCharge
            let content1 = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [empProposer.name, pr.name, pr.code])
            let content2 = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [empInCharge.name, pr.name, pr.code])

            const w1 = new EmployeeWarningEntity()
            w1.warningType = warningType.code
            w1.dataType = dataType
            w1.dataId = pr.id
            w1.message = subject_text || ''
            w1.messageFull = content1 || ''
            w1.employeeId = empProposer.id
            await manager.getRepository(EmployeeWarningEntity).save(w1)

            if (empProposer.id != empInCharge.id) {
              const w2 = new EmployeeWarningEntity()
              w1.warningType = warningType.code
              w2.dataType = dataType
              w2.dataId = pr.id
              w2.message = subject_text || ''
              w2.messageFull = content2 || ''
              w2.employeeId = empInCharge.id
              await manager.getRepository(EmployeeWarningEntity).save(w2)
            }
          }
        }
      } catch (error) {
        throw error
      }
    })
  }

  public async updateRechekPR(user: UserDto, data: { id: string; noteReCheck?: string }) {
    if (!user.employeeId) throw new Error(ERROR_NOT_FOUND_DATA)
    return this.repo.manager.transaction(async (manager) => {
      try {
        const prRepo = await manager.getRepository(PrEntity)

        const prApproveRepo = await manager.getRepository(PrApproveEntity)

        const pr = await prRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
        if (!pr) throw new Error(ERROR_NOT_FOUND_DATA)
        //#region history
        await prApproveRepo.delete({ prId: pr.id, level: 1 })
        const lstStatus = coreHelper.convertObjToArray(enumData.PRStatus)
        const statusCurrent = lstStatus.find((s) => s.code === pr.status)
        const statusConvert = lstStatus.find((s) => s.code === pr.status)
        let description = `Tài khoản [${user.username}] vừa cập nhật [Kiểm tra lại PR] - Trạng thái Cũ [${statusCurrent?.name}] - Trạng thái Mới [${enumData.PRStatus.Approved.name}]`
        if (data.noteReCheck) description = `${description} - Lý do kiểm tra lại: ${data.noteReCheck} - Quy trình duyệt PR bắt đầu lại`

        const historyNew = new PrHistoryEntity()
        historyNew.companyId = user.companyId
        historyNew.createdBy = user.id
        historyNew.prId = pr.id
        historyNew.statusCurrent = statusConvert.code
        historyNew.statusConvert = enumData.PRStatus.Approved.code
        historyNew.description = description
        historyNew.createdByName = user.username
        await manager.getRepository(PrHistoryEntity).save(historyNew)

        pr.updatedBy = user.id
        if (data.noteReCheck) pr.noteReCheck = data.noteReCheck

        await prRepo.save(pr)

        return { message: UPDATE_ACTIVE_SUCCESS }
      } catch (error) {
        throw error
      }
    })
  }
}
