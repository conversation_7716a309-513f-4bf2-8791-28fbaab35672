import { MigrationInterface, QueryRunner } from 'typeorm'

export class customBid1712675897344 implements MigrationInterface {
  name = 'customBid1712675897344'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`bid\` CHANGE \`timeserving\` \`timeserving\` float NOT NULL DEFAULT '0'`)
    await queryRunner.query(`ALTER TABLE \`bid\` CHANGE \`timeGuarantee\` \`timeGuarantee\` float NULL`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`bid\` CHANGE \`timeGuarantee\` \`timeGuarantee\` int NULL`)
    await queryRunner.query(`ALTER TABLE \`bid\` CHANGE \`timeserving\` \`timeserving\` int NOT NULL DEFAULT '0'`)
  }
}
