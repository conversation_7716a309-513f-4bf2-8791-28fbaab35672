import { Controller, UseGuards, Post, Body, Get } from '@nestjs/common'
import { CurrentUser } from '../common/decorators'
import { PaginationDto, UserDto } from '../../dto'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { BillService } from './bill.service'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { BillCreateDto, BillUpdateDto } from './dto'

@ApiBearerAuth()
@ApiTags('BILL')
@Controller('bill')
export class BillController {
  constructor(private readonly service: BillService) {}

  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách hóa đơn' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: '<PERSON>h sách hóa đơn phân trang' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo hóa đơn' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: BillCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật hóa đơn' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: BillUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động hóa đơn' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@Body() data: { id: string }, @CurrentUser() user: UserDto) {
    const warehouse = await this.service.updateIsDelete(data, user)
    return warehouse
  }

  @ApiOperation({ summary: 'Chi tiết hóa đơn' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetail(user, data)
  }

  @ApiOperation({ summary: 'API Cập nhật hủy Hóa đơn' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_cancel')
  public async updateCancel(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateCancel(user, data)
  }

  @ApiOperation({ summary: 'API Cập nhật gửi  Hóa đơn' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_send')
  public async updateSend(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateSend(user, data)
  }

  @ApiOperation({ summary: 'API Cập nhật duyệt Hóa đơn' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_confirmed')
  public async updateConfirmed(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateConfirmed(user, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách poId của các hóa đơn' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_po')
  public async findPOId(@CurrentUser() user: UserDto, @Body() data: { listBillId: string[] }) {
    return await this.service.findPOId(user, data)
  }
  @ApiOperation({ summary: 'Lấy danh sách contractId của các hóa đơn' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_contract')
  public async findContractId(@CurrentUser() user: UserDto, @Body() data: { listBillId: string[] }) {
    return await this.service.findContractId(user, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách hóa đơn theo trạng thái Đã xác nhận và chưa thanh toán' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_bill')
  public async findBillByStatus(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.findBillByStatus(user, data)
  }

  @ApiOperation({ summary: 'Hàm lấy danh sách công ty trong hệ thống bizzi' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('load_companies_from_bizzi')
  public async loadCompany(@CurrentUser() user: UserDto) {
    return await this.service.loadCompany(user)
  }

  @ApiOperation({ summary: 'Lấy danh sách hóa đơn theo trạng thái Đã xác nhận và chưa thanh toán client' })
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_bill_by_supplier')
  public async findBillSupplier(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.findBillSupplier(user, data)
  }
}
