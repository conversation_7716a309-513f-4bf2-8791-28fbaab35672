import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { BidSupplierEntity } from './bidSupplier.entity'
import { BidPriceEntity } from './bidPrice.entity'

/** <PERSON><PERSON>n nộp giá cuối của <PERSON> nghiệ<PERSON> */
@Entity('bid_supplier_price')
export class BidSupplierPriceEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidSupplierId: string
  @ManyToOne(() => BidSupplierEntity, (p) => p.bidSupplierPrices)
  @JoinColumn({ name: 'bidSupplierId', referencedColumnName: 'id' })
  bidSupplier: Promise<BidSupplierEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidPriceId: string
  @ManyToOne(() => BidPriceEntity, (p) => p.bidSupplierPrices)
  @JoinColumn({ name: 'bidPriceId', referencedColumnName: 'id' })
  bidPrice: Promise<BidPriceEntity>

  /** Tên hạng mục */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  bidPriceName: string

  /** Cấp độ */
  @Column({
    nullable: false,
    default: 1,
  })
  bidPriceLevel: number

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidId: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  serviceId: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  supplierId: string

  /** Ngày nộp chào giá */
  @Column({
    nullable: false,
  })
  submitDate: Date

  /** Hình thức nộp giá: 0,1,2 tương ứng chào giá, đàm phán, đấu giá */
  @Column({
    nullable: false,
  })
  submitType: number

  /** Số lượng */
  @Column({
    nullable: false,
  })
  number: number

  /** Đơn giá */
  @Column({
    type: 'bigint',
    nullable: false,
  })
  unitPrice: number

  /** Thành tiền (doanh thu theo hạng mục = Số lượng*Đơn giá ) */
  @Column({
    type: 'bigint',
    nullable: false,
  })
  price: number
}
