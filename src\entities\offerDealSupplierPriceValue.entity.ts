import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { OfferPriceEntity } from './offerPrice.entity'
import { OfferDealSupplierEntity } from './offerDealSupplier.entity'

@Entity('offer_deal_supplier_price_value')
export class OfferDealSupplierPriceValueEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: true,
  })
  score: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  offerDealSupplierId: string
  @ManyToOne(() => OfferDealSupplierEntity, (p) => p.offerDealSupplierPriceValue)
  @JoinColumn({ name: 'offerDealSupplierId', referencedColumnName: 'id' })
  offerDealSupplier: Promise<OfferDealSupplierEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  offerPriceId: string
  @ManyToOne(() => OfferPriceEntity, (p) => p.offerDealSupplierPriceValue)
  @JoinColumn({ name: 'offerPriceId', referencedColumnName: 'id' })
  offerPrice: Promise<OfferPriceEntity>
}
