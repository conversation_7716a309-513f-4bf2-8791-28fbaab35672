import { Injectable, BadRequestException, NotFoundException, UnauthorizedException } from '@nestjs/common'
import { SupplierRepository, UserRepository } from '../../repositories'
import { In, MoreThanOrEqual } from 'typeorm'
import { EmailService } from '../email/email.service'
import { enumData, enumProject, ERROR_VALIDATE, lstRoleDefault, PWD_SALT_ROUNDS, UPDATE_SUCCESS } from '../../constants'
import { UpdatePasswordDto, UpdateUsernameDto, ForgotPasswordDto } from './dto'
import { UserConfirmCodeEntity, UserEntity } from '../../entities'
import { JwtService } from '@nestjs/jwt'
import { Request as IRequest } from 'express'
import { apeAuthApiHelper, coreHelper, enumApeAuth, UserAuthDto } from '../../helpers'
import { nanoid } from 'nanoid'
import { UserDto } from '../../dto'
import { hash } from 'bcrypt'

@Injectable()
export class AuthService {
  constructor(
    private readonly supplierRepo: SupplierRepository,
    private readonly jwtService: JwtService,
    private readonly userRepo: UserRepository,
    private readonly emailService: EmailService,
  ) {}

  /** Xác thực user cho On-premise */
  public async validateUserOnPremise(username: string, password: string) {
    // check user
    const user = await this.userRepo.findOne({
      where: { username },
      select: {
        id: true,
        supplierId: true,
        employeeId: true,
        username: true,
        type: true,
        roles: true,
        companyId: true,
        password: true,
        isDeleted: true,
      },
    })
    if (!user) throw new UnauthorizedException(`Tài khoản không tồn tại.`)
    if (user.isDeleted) {
      if (user.supplierId) {
        const supplier = await this.supplierRepo.findOne({ where: { id: user.supplierId }, select: { id: true, status: true } })
        if (supplier?.status == enumData.SupplierStatus.MoiDangKy.code) {
          throw new UnauthorizedException('Tài khoản đang chờ xét duyệt, vui lòng liên hệ bộ phận mua hàng nếu cần kiểm tra lại!')
        }
        if (supplier?.status == enumData.SupplierStatus.Huy.code) {
          throw new UnauthorizedException('Tài khoản không được xét duyệt, vui lòng liên hệ bộ phận mua hàng nếu cần kiểm tra lại!')
        }
      }
      throw new UnauthorizedException('Tài khoản đã ngưng hoạt động!')
    }

    // check pw
    const isPasswordMatch = await user.comparePassword(password)
    if (!isPasswordMatch) throw new UnauthorizedException(`Mật khẩu không đúng. Vui lòng thử lại.`)
    delete user.password

    return user
  }

  /** Xác thực user */
  public async validateUserProduct(userType: string, companyId: string, username: string, accessToken: string) {
    let userEntity = await this.userRepo.findOne({
      where: { username, companyId },
      select: {
        id: true,
        supplierId: true,
        employeeId: true,
        username: true,
        type: true,
        roles: true,
        companyId: true,
        isDeleted: true,
      },
    })

    // Nếu là user AdminCompanyPackage thì tự tạo user nếu chưa có
    if (!userEntity) {
      if (userType != enumApeAuth.UserType.CompanyPackageAdmin) throw new UnauthorizedException('Tài khoản không tồn tại, vui lòng kiểm tra lại!')

      const userNew = new UserEntity()
      userNew.username = username
      userNew.type = enumData.UserType.AdminCompany.code
      userNew.companyId = companyId
      userNew.roles = JSON.stringify(lstRoleDefault)
      userEntity = await this.userRepo.save(userNew)

      // xóa các trường không cần thiết
      delete userEntity.createdAt
      delete userEntity.createdBy
      delete userEntity.updatedAt
      delete userEntity.updatedBy
      delete userEntity.password
      delete userEntity.isDeleted
    }
    if (userEntity.isDeleted) {
      if (userEntity.supplierId) {
        const supplier = await this.supplierRepo.findOne({ where: { id: userEntity.supplierId }, select: { id: true, status: true } })
        if (supplier?.status == enumData.SupplierStatus.MoiDangKy.code) {
          throw new UnauthorizedException('Tài khoản đang chờ xét duyệt, vui lòng liên hệ bộ phận mua hàng nếu cần kiểm tra lại!')
        }
        if (supplier?.status == enumData.SupplierStatus.Huy.code) {
          throw new UnauthorizedException('Tài khoản không được xét duyệt, vui lòng liên hệ bộ phận mua hàng nếu cần kiểm tra lại!')
        }
      }
      throw new UnauthorizedException('Tài khoản đã ngưng hoạt động!')
    }

    return { ...userEntity, accessToken }
  }

  /** Đăng nhập cho nhân viên */
  public async login(user: UserAuthDto) {
    const lstType = [enumData.UserType.Employee.code, enumData.UserType.Admin.code, enumData.UserType.AdminCompany.code]
    const userEntity: any = await this.userRepo.findOne({
      where: { id: user.id, type: In(lstType) },
      select: {
        id: true,
        type: true,
        isDeleted: true,
        employeeId: true,
        employee: { branchId: true },
      },
      relations: {
        employee: { branch: true },
      },
    })
    if (!userEntity) throw new UnauthorizedException('Tài khoản không tồn tại!')
    if (userEntity.isDeleted) throw new UnauthorizedException('Tài khoản đã ngưng hoạt động!')

    const isProduct = process.env.IS_PRODUCT == 'true'
    const accessToken = isProduct ? user.accessToken : this.jwtService.sign({ uid: userEntity.id })

    return {
      accessToken,
      employeeId: userEntity.employeeId,
      branchId: userEntity.employee?.branchId,
      userId: userEntity.id,
      enumData: enumData,
      enumProject: enumProject,
    }
  }

  /** Đăng nhập cho Doanh nghiệp */
  public async loginClient(user: UserAuthDto) {
    const userEntity = await this.userRepo.findOne({
      where: { id: user.id },
      select: { id: true, isDeleted: true, supplierId: true, type: true },
    })
    if (!userEntity) throw new UnauthorizedException('Tài khoản không tồn tại!')
    if (userEntity.type != enumData.UserType.Supplier.code) throw new UnauthorizedException('Vui lòng đăng nhập với tài khoản nhà cung cấp!')
    if (userEntity.isDeleted) throw new UnauthorizedException('Tài khoản đã ngưng hoạt động!')

    const isProduct = process.env.IS_PRODUCT == 'true'
    const accessToken = isProduct ? user.accessToken : this.jwtService.sign({ uid: userEntity.id })

    return {
      enumData: enumData,
      accessToken,
      supplierId: userEntity.supplierId,
    }
  }

  /** Lấy thông tin authorization Doanh nghiệp */
  public async authorizationClient(user: UserDto) {
    const userEntity = await this.userRepo.findOne({
      where: {
        id: user.id,
        type: enumData.UserType.Supplier.code,
        isDeleted: false,
      },
      relations: { supplier: true },
      select: {
        id: true,
        username: true,
        supplierId: true,
        supplier: { id: true, code: true, name: true },
      },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')

    return {
      id: userEntity.id,
      enumData: enumData,
      username: userEntity.username,
      supplierId: userEntity.supplierId,
      supplierCode: userEntity.supplier.code,
      supplierName: userEntity.supplier.name,
    }
  }

  /** Đổi mật khẩu */
  public async updatePassword(info: UpdatePasswordDto, user: UserDto, req: IRequest) {
    if (info.newPassword === info.currentPassword) throw new BadRequestException('Trùng mật khẩu cũ.')

    const userEntity = await this.userRepo.findOne({ where: { id: user.id }, select: { id: true } })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')

    // Update user bên auth
    const isProduct = process.env.IS_PRODUCT == 'true'
    if (isProduct) {
      if (!req || !req.headers || !req.headers.authorization) throw new UnauthorizedException('Không có quyền truy cập! (code: BEARER_TOKEN_ERROR)')
      await apeAuthApiHelper.updatePassword(req.headers.authorization, info.currentPassword, info.newPassword)
    } else {
      const hashedPassword = await hash(info.newPassword, PWD_SALT_ROUNDS)
      await this.userRepo.update(user.id, { password: hashedPassword, updatedBy: user.id })
    }

    return { message: 'Đổi mật khẩu thành công.' }
  }

  /** Đổi tên tài khoản supplier */
  public async updateUsername(info: UpdateUsernameDto, user: UserDto, req: IRequest) {
    const newUsername = info.newUsername.trim()
    if (!user.supplierId) throw new NotFoundException('Chức năng chỉ áp dụng cho tài khoản nhà cung cấp!')
    if (!newUsername || newUsername.length < 3) throw new NotFoundException('Tài khoản mới không hợp lệ')

    const userEntity = await this.userRepo.findOne({
      where: { id: user.id },
      select: { id: true, username: true, password: true },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')

    if (newUsername === userEntity.username) throw new BadRequestException('Trùng tên đăng nhập cũ.')

    const existUserEntity = await this.userRepo.findOne({ where: { username: newUsername, companyId: user.companyId }, select: { id: true } })
    if (existUserEntity) throw new Error(`Tài khoản [${newUsername}] đã tồn tại.`)

    const isCurrentPasswordMatch = await userEntity.comparePassword(info.currentPassword)
    if (!isCurrentPasswordMatch) throw new BadRequestException('Sai mật khẩu cũ.')

    // Update user bên auth
    const isProduct = process.env.IS_PRODUCT == 'true'
    if (isProduct) {
      if (!req || !req.headers || !req.headers.authorization) throw new UnauthorizedException('Không có quyền truy cập! (code: BEARER_TOKEN_ERROR)')
      await apeAuthApiHelper.updateUsername(req.headers.authorization, info.currentPassword, info.newUsername)
    } else {
      await this.userRepo.update(user.id, { username: newUsername, updatedBy: user.id })
    }

    return { message: 'Đổi tên đăng nhập thành công.' }
  }

  /** Gửi mã xác nhận khi quên mật khẩu */
  public async sendConfirmCode(req: Request, data: { email: string }) {
    const companyId = await apeAuthApiHelper.getCompanyId(req)
    const userConfirmCodeRepo = this.userRepo.manager.getRepository(UserConfirmCodeEntity)
    let email = data.email ? data.email.trim().toLowerCase() : ''
    if (!email) throw new Error(ERROR_VALIDATE)

    const user = await this.supplierRepo.findOne({ where: { email, companyId, isDeleted: false }, select: { id: true, email: true, userId: true } })
    if (!user || user.email.trim().toLowerCase() !== email) throw new Error('User không còn tồn tại!')

    const dtNow = new Date()
    const entity = await userConfirmCodeRepo.findOne({
      where: {
        userId: user.userId,
        exDate: MoreThanOrEqual(dtNow),
        companyId,
        isDeleted: false,
      },
      select: { id: true },
    })
    if (entity) throw new Error('Mã xác nhận đã được gửi, vui lòng kiểm tra lại email!')

    const expireDate = new Date()
    expireDate.setHours(expireDate.getHours() + 8)
    const confirmCode = nanoid(8)

    const createdData = userConfirmCodeRepo.create({
      userId: user.userId,
      exDate: expireDate,
      code: confirmCode,
      companyId,
      createdBy: user.userId,
    })
    await userConfirmCodeRepo.save(createdData)

    this.emailService.sendConfirmCode({ companyId, email, confirmCode })

    return { message: 'Mã đã gửi, vui lòng kiểm tra email đăng ký tài khoản.' }
  }

  /** Nhập mã xác nhận khi supplier quên mật khẩu */
  public async forgotPassword(req: Request, { email, confirmCode, newPassword }: ForgotPasswordDto) {
    const companyId = await apeAuthApiHelper.getCompanyId(req)

    const emailStr = email.trim() ? email.trim().toLowerCase() : ''
    if (!emailStr) throw new Error(ERROR_VALIDATE)

    const supplier: any = await this.supplierRepo.findOne({
      where: { email: emailStr, companyId, isDeleted: false },
      relations: { user: true },
      select: { id: true, email: true, userId: true, user: { id: true, username: true } },
    })
    if (!supplier || supplier.email.trim().toLowerCase() !== emailStr) throw new Error('User không còn tồn tại')
    const user = supplier.__user__

    const dtNow = new Date()
    const confirm = await this.userRepo.manager.getRepository(UserConfirmCodeEntity).findOne({
      where: {
        userId: supplier.userId,
        exDate: MoreThanOrEqual(dtNow),
        code: confirmCode,
        companyId,
        isDeleted: false,
      },
      select: { id: true, userId: true },
    })
    if (!confirm) throw new NotFoundException('Mã xác nhận không hợp lệ!')

    const userEntity = await this.userRepo.findOne({ where: { id: confirm.userId, companyId }, select: { id: true } })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')

    // Update user bên auth
    const isProduct = process.env.IS_PRODUCT == 'true'
    if (isProduct) {
      const req1: any = req
      if (!req1 || !req1.headers || !req1.headers.authorization)
        throw new UnauthorizedException('Không có quyền truy cập! (code: BEARER_TOKEN_ERROR)')
      await apeAuthApiHelper.updatePasswordSecret(companyId, user.username, newPassword)
    } else {
      const hashedPassword = await hash(newPassword, PWD_SALT_ROUNDS)
      await this.userRepo.update(confirm.userId, { password: hashedPassword, updatedBy: user.userId })
    }

    return { message: 'Thay đổi mật khẩu thành công.' }
  }

  /** Load phân quyền user */
  public async loadPermissionUser(user: UserDto) {
    const userEntity = await this.userRepo.findOne({
      where: { id: user.id },
      select: { id: true, isDeleted: true, type: true, roles: true },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')
    if (userEntity.isDeleted) throw new NotFoundException('Tài khoản đã ngưng hoạt động.')
    const lstUserType = [enumData.UserType.Admin.code, enumData.UserType.AdminCompany.code, enumData.UserType.Employee.code]
    if (!lstUserType.includes(userEntity.type)) throw new NotFoundException('Loại User không hợp lệ.')

    let lstFeature = coreHelper.convertObjToArray(enumProject.Features)
    let lstRole = []
    if (userEntity.roles) {
      try {
        lstRole = JSON.parse(userEntity.roles)
      } catch (err) {
        console.log(err)
      }
    }
    for (const feature of lstFeature) {
      delete feature.module
      delete feature.name
      const role = lstRole.find((d) => d.code == feature.code)
      if (role) {
        if (feature.action.View && role.isView) feature.isView = true
        if (feature.action.Create && role.isCreate) feature.isCreate = true
        if (feature.action.Update && role.isUpdate) feature.isUpdate = true
        if (feature.action.Delete && role.isDelete) feature.isDelete = true
        if (feature.action.Print && role.isPrint) feature.isPrint = true
        if (feature.action.Export && role.isExport) feature.isExport = true
        if (feature.action.Import && role.isImport) feature.isImport = true
        if (feature.action.Active && role.isActive) feature.isActive = true
      }
      delete feature.action
    }

    return lstFeature.filter((c) => c.isView || c.isCreate || c.isUpdate || c.isDelete || c.isPrint || c.isExport || c.isImport || c.isActive)
  }

  /** Lấy ds phân quyền nhân viên */
  public async loadPermissionEmployee(user: UserDto, data: { userId: string }) {
    if (!data.userId) throw new Error(`Vui lòng chọn nhân viên trước.`)

    const userEntity = await this.userRepo.findOne({
      where: { id: data.userId, companyId: user.companyId },
      select: { id: true, isDeleted: true, type: true, roles: true },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')
    if (userEntity.isDeleted) throw new NotFoundException('Tài khoản đã ngưng hoạt động.')

    const lstUserType = [enumData.UserType.Admin.code, enumData.UserType.AdminCompany.code, enumData.UserType.Employee.code]
    if (!lstUserType.includes(userEntity.type)) throw new NotFoundException('Loại User không hợp lệ.')

    const res = coreHelper.convertObjToArray(enumProject.Features)
    let lstRole = []
    if (userEntity.roles) {
      try {
        lstRole = JSON.parse(userEntity.roles)
      } catch (err) {
        console.log(err)
      }
    }
    res.forEach((c) => {
      c.showView = c.action.View || false
      c.showCreate = c.action.Create || false
      c.showUpdate = c.action.Update || false
      c.showDelete = c.action.Delete || false
      c.showPrint = c.action.Print || false
      c.showExport = c.action.Export || false
      c.showImport = c.action.Import || false
      c.showActive = c.action.Active || false

      const role = lstRole.find((d) => d.code == c.code)
      if (role) {
        c.isView = c.showView && role.isView
        c.isCreate = c.showCreate && role.isCreate
        c.isUpdate = c.showUpdate && role.isUpdate
        c.isDelete = c.showDelete && role.isDelete
        c.isPrint = c.showPrint && role.isPrint
        c.isExport = c.showExport && role.isExport
        c.isImport = c.showImport && role.isImport
        c.isActive = c.showActive && role.isActive
      }
    })

    return res
  }

  /** Lưu phân quyền nhân viên */
  public async savePermissionEmployee(user: UserDto, data: { userId: string; lstData: any[] }) {
    if (!data.userId) throw new Error(`Vui lòng chọn nhân viên trước.`)

    const userEntity = await this.userRepo.findOne({
      where: { id: data.userId, companyId: user.companyId },
      select: { id: true, isDeleted: true, type: true },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')
    if (userEntity.isDeleted) throw new NotFoundException('Tài khoản đã ngưng hoạt động.')
    const lstUserType = [enumData.UserType.Admin.code, enumData.UserType.AdminCompany.code, enumData.UserType.Employee.code]
    if (!lstUserType.includes(userEntity.type)) throw new NotFoundException('Loại User không hợp lệ.')

    const roles = JSON.stringify(data.lstData)

    await this.userRepo.update(userEntity.id, { roles, updatedBy: user.id })

    return { message: UPDATE_SUCCESS }
  }
}
