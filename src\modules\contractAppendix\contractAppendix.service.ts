import * as moment from 'moment'
import { ConflictException, Injectable, NotAcceptableException } from '@nestjs/common'
import { Like, Raw } from 'typeorm'
import { CREATE_SUCCESS, ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION, UPDATE_SUCCESS } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { BidEntity, ContractAppendixEntity, ContractEntity, EmployeeEntity, ObjectEntity, ServiceEntity } from '../../entities'
import { ContractAppendixRepository } from '../../repositories'
import { ContractAppendixCreate, ContractAppendixUpdate } from './dto'

@Injectable()
export class ContractAppendixService {
  constructor(private readonly repo: ContractAppendixRepository) {}

  public async pagination(data: PaginationDto, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const whereCon: any = { companyId: user.companyId }
    if (data.where.code && data.where.code.trim() !== '') whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.type) whereCon.type = data.where.type
    if (data.where.contractId) whereCon.contractId = data.where.contractId

    if (data.where.createdAtFrom && data.where.createdAtTo) {
      whereCon.createdAt = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.createdAtFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.createdAtTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    }
    if (data.where.effectiveDateFrom && data.where.effectiveDateTo) {
      whereCon.effectiveDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.effectiveDateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(
            data.where.effectiveDateTo,
          ).format('YYYY-MM-DD')}")`,
      )
    }

    return await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })
  }

  public async createData(data: ContractAppendixCreate, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const employee = await this.repo.manager.getRepository(EmployeeEntity).findOne({
      where: { id: user.employeeId, companyId: user.companyId },
      relations: { branch: true },
    })
    const object = await this.repo.manager.getRepository(ObjectEntity).findOne({ where: { id: data.objectId, companyId: user.companyId } })

    const contract = await this.repo.manager.getRepository(ContractEntity).findOne({
      where: {
        id: data.contractId,
        companyId: user.companyId,
        isDeleted: false,
      },
    })
    if (!contract) throw new Error(ERROR_NOT_FOUND_DATA)

    const bid = await this.repo.manager.getRepository(BidEntity).findOne({
      where: { id: contract.bidId, companyId: user.companyId, isDeleted: false },
    })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    const service: any = await this.repo.manager.getRepository(ServiceEntity).findOne({
      where: { id: bid.serviceId, companyId: user.companyId, isDeleted: false },
      relations: { parent: { parent: { parent: true } } },
    })
    if (!service) throw new Error(ERROR_NOT_FOUND_DATA)

    let serviceLevel1: any
    if (service.parentId && service.__parent__.parentId && service.__parent__.__parent__.parentId && service.__parent__.__parent__.__parent__) {
      serviceLevel1 = service.__parent__.__parent__.__parent__.code
    } else if (service.parentId && service.__parent__.parentId && service.__parent__.__parent__) {
      serviceLevel1 = service.__parent__.__parent__.code
    } else if (service.parentId && service.__parent__) {
      serviceLevel1 = service.__parent__.code
    } else {
      serviceLevel1 = service.code
    }

    let codeSC = '00'
    const contractLast = await this.repo.findOne({
      where: {
        code: Like(`%${codeSC}%`),
        companyId: user.companyId,
      },
      order: { code: 'DESC' },
    })
    let sortString = '0'
    if (contractLast) {
      sortString = contractLast.code.substring(0, 4)
    }
    const lastSort = +sortString
    sortString = ('000' + (lastSort + 1)).slice(-4)
    let code = sortString // STT
    code += '/' + new Date().getFullYear().toString().slice(-2) // YY
    if (employee && (await employee.branch)) {
      code += '/' + (await employee.branch).code // XXX
    }
    if (object) {
      code += '.' + object.code // ZZZ
    }
    if (serviceLevel1) {
      code += '.' + serviceLevel1 // AAA
    }
    code += '-' + 'PL'

    const contractAppendix = new ContractAppendixEntity()
    contractAppendix.companyId = user.companyId
    contractAppendix.createdBy = user.id
    contractAppendix.contractId = data.contractId
    contractAppendix.code = code
    contractAppendix.title = data.title
    contractAppendix.fileAttach = data.fileAttach
    contractAppendix.description = data.description
    contractAppendix.effectiveDate = data.effectiveDate
    contractAppendix.expiredDate = data.expiredDate
    contractAppendix.type = data.type
    contractAppendix.objectId = data.objectId
    await contractAppendix.save()

    return { message: CREATE_SUCCESS }
  }

  public async updateData(data: ContractAppendixUpdate, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    if (data.code != entity.code) {
      const objCheckCode = await this.repo.findOne({
        where: { code: data.code, contractId: entity.contractId, companyId: user.companyId },
        select: { id: true },
      })
      if (objCheckCode) throw new ConflictException(ERROR_CODE_TAKEN)
    }

    entity.contractId = data.contractId
    entity.code = data.code
    entity.title = data.title
    entity.fileAttach = data.fileAttach
    entity.effectiveDate = data.effectiveDate
    entity.type = data.type
    entity.description = data.description
    entity.objectId = data.objectId
    entity.title = data.title
    entity.updatedBy = user.id
    await entity.save()

    return { message: UPDATE_SUCCESS }
  }
}
