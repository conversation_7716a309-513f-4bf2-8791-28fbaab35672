import { Repository, In } from 'typeorm'
import { BidEmployeeAccessEntity } from '../entities'
import { enumData } from '../constants/enumData'
import { ERROR_NOT_FOUND_DATA } from '../constants'
import { CustomRepository } from '../typeorm'
import { UserDto } from '../dto'

@CustomRepository(BidEmployeeAccessEntity)
export class BidEmployeeAccessRepository extends Repository<BidEmployeeAccessEntity> {
  //#region Lấy nhân viên

  /** Lấy danh sách mail các thành viên liên quan của gói thầu */
  async getListEmailByBidId(bidId: string) {
    const lstType = [enumData.BidRuleType.Memmber.code, enumData.BidRuleType.Other.code]
    const lstAccess: any[] = await this.find({
      where: { bidId: bidId, type: In(lstType) },
      relations: { employee: true },
      select: { id: true, employee: { id: true, email: true } },
    })

    return lstAccess.map((c) => c.__employee__.email).filter((value, index, self) => self.indexOf(value) === index)
  }

  /** Lấy nhân viên mua hàng của gói thầu */
  async getMPO(bidId: string) {
    const access: any = await this.findOne({
      where: { bidId: bidId, type: enumData.BidRuleType.MPO.code },
      relations: { employee: true },
      select: { id: true, employee: { id: true, name: true, email: true } },
    })
    if (!access) throw new Error(ERROR_NOT_FOUND_DATA)

    return { name: access.__employee__.name, email: access.__employee__.email }
  }

  /** Lấy trưởng bộ phận mua hàng của gói thầu */
  async getMPOLead(bidId: string) {
    const access: any = await this.findOne({
      where: { bidId: bidId, type: enumData.BidRuleType.MPOLeader.code },
      relations: { employee: true },
      select: { id: true, employee: { id: true, name: true, email: true } },
    })
    if (!access) throw new Error(ERROR_NOT_FOUND_DATA)

    return { name: access.__employee__.name, email: access.__employee__.email }
  }

  //#endregion

  //#region Check quyền gói thầu

  /** Check quyền tạo đánh giá kỹ thuật cho gói thầu */
  async isTech(user: UserDto, bidId: string) {
    // MPO có quyền như Tech
    const lstType = [enumData.BidRuleType.Tech.code, enumData.BidRuleType.MPO.code]
    const objCheck = await this.findOne({
      where: { bidId, employeeId: user.employeeId, companyId: user.companyId, type: In(lstType) },
      select: { id: true },
    })

    return !!objCheck
  }

  async isTechLeader(user: UserDto, bidId: string) {
    // MPOLeader có quyền như TechLeader
    const lstType = [enumData.BidRuleType.TechLeader.code, enumData.BidRuleType.MPOLeader.code]
    const objCheck = await this.findOne({
      where: { bidId, employeeId: user.employeeId, companyId: user.companyId, type: In(lstType) },
      select: { id: true },
    })

    return !!objCheck
  }

  async isMPO_MPOLeader(user: UserDto, bidId: string) {
    const lstType = [enumData.BidRuleType.MPO.code, enumData.BidRuleType.MPOLeader.code]
    const objCheck = await this.findOne({
      where: { bidId, employeeId: user.employeeId, companyId: user.companyId, type: In(lstType) },
      select: { id: true },
    })

    return !!objCheck
  }

  async isMPO(user: UserDto, bidId: string) {
    const objCheck = await this.findOne({
      where: { bidId, employeeId: user.employeeId, companyId: user.companyId, type: enumData.BidRuleType.MPO.code },
      select: { id: true },
    })

    return !!objCheck
  }

  async isMPOLeader(user: UserDto, bidId: string) {
    const objCheck = await this.findOne({
      where: { bidId, employeeId: user.employeeId, companyId: user.companyId, type: enumData.BidRuleType.MPOLeader.code },
      select: { id: true },
    })

    return !!objCheck
  }

  //#endregion
}
