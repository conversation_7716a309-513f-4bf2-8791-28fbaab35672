import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>um<PERSON>, OneToMany } from 'typeorm'
import { BidPriceEntity } from './bidPrice.entity'
import { BidAuctionSupplierEntity } from './bidAuctionSupplier.entity'

@Entity('bid_auction_supplier_price_value')
export class BidAuctionSupplierPriceValueEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: true,
  })
  score: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  value: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidAuctionSupplierId: string
  @ManyToOne(() => BidAuctionSupplierEntity, (p) => p.bidAuctionSupplierPriceValue)
  @JoinColumn({ name: 'bidAuctionSupplierId', referencedColumnName: 'id' })
  bidAuctionSupplier: BidAuctionSupplierEntity

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidPriceId: string
  @ManyToOne(() => BidPriceEntity, (p) => p.bidAuctionSupplierPriceValue)
  @JoinColumn({ name: 'bidPriceId', referencedColumnName: 'id' })
  bidPrice: Promise<BidPriceEntity>
}
