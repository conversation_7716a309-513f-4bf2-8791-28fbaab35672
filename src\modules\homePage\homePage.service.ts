import { Injectable, NotAcceptableException } from '@nestjs/common'
import { ERROR_VALIDATE } from '../../constants'
import {
  BannerClientRepository,
  LinkClientRepository,
  ServiceRepository,
  SettingStringClientRepository,
  SupplierNotifyRepository,
} from '../../repositories'
import { enumData } from '../../constants/enumData'
import { UserDto } from '../../dto'
import { apeAuthApiHelper } from '../../helpers'

@Injectable()
export class HomePageService {
  constructor(
    private readonly bannerRepo: BannerClientRepository,
    private readonly linkClientRepo: LinkClientRepository,
    private readonly settingStringClientRepo: SettingStringClientRepository,
    private readonly serviceRepo: ServiceRepository,
    private readonly supplierNotifyRepo: SupplierNotifyRepository,
  ) {}

  public async getBannerClient(req: Request, data: { position: string }) {
    const companyId = await apeAuthApiHelper.getCompanyId(req)
    const position = data.position || ''
    const acceptPosition = [enumData.BannerClientPosition.Left.code, enumData.BannerClientPosition.Right.code, enumData.BannerClientPosition.Top.code]

    if (!acceptPosition.includes(position)) throw new NotAcceptableException(ERROR_VALIDATE)
    return await this.bannerRepo.find({ where: { position, companyId, isDeleted: false } })
  }

  public async getLinkClient(req: Request) {
    const companyId = await apeAuthApiHelper.getCompanyId(req)
    return await this.linkClientRepo.find({ where: { companyId, isDeleted: false } })
  }
  public async getSettingStringClient(req: Request) {
    const companyId = await apeAuthApiHelper.getCompanyId(req)
    return await this.settingStringClientRepo.find({ where: { companyId, isDeleted: false } })
  }

  /** Lấy lĩnh vực mời thầu menu trái trang Doanh nghiệp */
  public async getServicesClient(req: Request) {
    const companyId = await apeAuthApiHelper.getCompanyId(req)
    const res: any[] = await this.serviceRepo.find({
      where: { level: 1, companyId, isDeleted: false },
      order: { code: 'ASC', childs: { code: 'ASC', childs: { code: 'ASC' } } },
      relations: { childs: { childs: true } },
    })

    return res
  }

  public async getNotifys(user: UserDto, num: number) {
    if (user && user.supplierId) {
      const res = await this.supplierNotifyRepo.findAndCount({
        where: { supplierId: user.supplierId, companyId: user.companyId },
        order: { status: 'ASC', createdAt: 'DESC' },
        skip: 0,
        take: num,
      })
      const lstData = []
      for (const item of res[0]) {
        const isNew = item.status === enumData.NotifyStatus.ChuaDoc.code
        lstData.push({ id: item.id, message: item.message, isNew })
      }

      return lstData
    }
  }

  public async getNotify(user: UserDto, id: string) {
    if (user && user.supplierId) {
      const objNotify = await this.supplierNotifyRepo.findOne({ where: { supplierId: user.supplierId, id: id, companyId: user.companyId } })
      if (objNotify) {
        objNotify.status = enumData.NotifyStatus.DaDoc.code
        await this.supplierNotifyRepo.update(objNotify.id, { status: objNotify.status, updatedBy: user.id })
      }

      return objNotify
    }
  }
}
