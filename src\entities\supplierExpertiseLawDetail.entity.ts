import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { SupplierExpertiseEntity } from './supplierExpertise.entity'

/** Chi tiết yêu cầu điều chỉnh pháp lý trong lần thẩm định */
@Entity('supplier_expertise_law_detail')
export class SupplierExpertiseLawDetailEntity extends BaseEntity {
  /** Mã số doanh nghiệp */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  /** Tên chính thức */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  /** Tên giao dịch */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  dealName: string

  /** Địa chỉ trụ sở */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  address: string

  /** Địa chỉ giao dịch */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  dealAddress: string

  /** Gi<PERSON>y phép đăng ký kinh doanh/Mã số thuế --> URL */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileMST: string

  /** Người đại diện pháp luật */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  represen: string

  /** Tên giám đốc */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  chief: string

  /** Số tài khoản ngân hàng */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  bankNumber: string

  /** Tên ngân hàng */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  bankname: string

  /** Tên chi nhánh ngân hàng */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  bankBrand: string

  /** File đính kèm thông báo mở tài khoản/mẫu 08 -->URL */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileAccount: string

  /** Người liên hệ */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  contactName: string

  /** Email */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  email: string

  /** Điện thoại */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  phone: string

  /** Năm thành lập công ty */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  createYear: string

  /** Vốn điều lệ (tỷ đồng) */
  @Column({
    nullable: true,
  })
  capital: number

  /** Tài sản cố định (tỷ đồng) */
  @Column({
    nullable: true,
  })
  assets: number

  /** File đính kèm hóa đơn mẫu/phiếu thu/biên lai --> URL */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileBill: string

  /** File đính kèm thông tin phát hành hóa đơn --> URL */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileInfoBill: string

  /** Lần thẩm định */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  supplierExpertiseId: string
  @ManyToOne(() => SupplierExpertiseEntity, (p) => p.supplierExpertiseLawDetails)
  @JoinColumn({ name: 'supplierExpertiseId', referencedColumnName: 'id' })
  supplierExpertise: Promise<SupplierExpertiseEntity>
}
