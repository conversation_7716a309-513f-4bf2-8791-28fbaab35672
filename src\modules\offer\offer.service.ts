import { Injectable, NotAcceptableException, NotFoundException } from '@nestjs/common'
import { v4 as uuidv4 } from 'uuid'
import { PaginationDto, UserDto } from '../../dto'
import { CREATE_SUCCESS, DELETE_SUCCESS, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION, UPDATE_SUCCESS } from '../../constants'
import { In, IsNull, Like, Not } from 'typeorm'
import { ServiceEntity, SupplierEntity } from '../../entities'
import { SupplierRepository } from '../../repositories'
import {
  OfferDealRepository,
  OfferPriceRepository,
  OfferRepository,
  OfferServiceRepository,
  OfferSupplierRepository,
  OfferSupplierShipmentValueRepository,
} from '../../repositories/offer.repository'
import { OfferEntity } from '../../entities/offer.entity'
import { OfferPriceEntity } from '../../entities/offerPrice.entity'
import { OfferSupplierEntity } from '../../entities/offerSupplier.entity'
import { OfferServiceEntity } from '../../entities/offerService.entity'
import { enumData } from '../../constants/enumData'
import { OfferSupplierServiceEntity } from '../../entities/offerSupplierService.entity'
import { OfferTradeService } from './offerTrade.service'
import { OfferPriceService } from './offerPrice.service'
import { coreHelper } from '../../helpers'
import { OfferDealSupplierEntity } from '../../entities/offerDealSupplier.entity'
import { OfferDealSupplierPriceValueEntity } from '../../entities/offerDealSupplierPriceValue.entity'
import { nanoid } from 'nanoid'
import { OfferShipmentPriceEntity } from '../../entities/offerShipmentPrice.entity'
import { OfferSupplierShipmentValueEntity } from '../../entities/offerSupplierShipmentValue.entity'
@Injectable()
export class OfferService {
  constructor(
    private readonly repo: OfferRepository,
    private readonly bidDealRepo: OfferDealRepository,
    private readonly serviceRepo: OfferServiceRepository,
    private readonly priceRepo: OfferPriceRepository,
    private readonly offerSupplierRepo: OfferSupplierRepository,
    private readonly bidPriceRepo: OfferPriceRepository,
    private readonly supplierRepository: SupplierRepository,
    private readonly offerTradeService: OfferTradeService,
    private readonly offerPriceService: OfferPriceService,
    private readonly offerSupplierShipmentValueRepo: OfferSupplierShipmentValueRepository,
  ) {}

  async findOne(user: UserDto, data: any) {
    const thisOne: any = await this.repo.findOne({ where: { id: data.id }, relations: { offerService: true, offerSupplier: { supplier: true } } })
    if (!thisOne) throw new Error(`Không tìm thấy dữ liệu!`)
    /* Lấy danh sách Item(lstItem)  */
    thisOne.lstItem = []
    for (const item of thisOne.__offerService__) {
      if (!item.isExGr) thisOne.lstItem.push(item)
    }

    /* Lấy ra danh sách NCC (lstSup) */
    for (const item of thisOne.__offerSupplier__) {
      item.supplierCode = item.__supplier__.code
      item.supplierName = item.__supplier__.name
      item.isChosen = true
      item.createdAt = item?.__supplier__?.createdAt
    }
    thisOne.lstSup = thisOne.__offerSupplier__
    for (const item of thisOne.lstSup) {
    }
    /* Lấy ra cấu hình điều kiện thương mại */
    /* Lấy ra cấu hình giá */
    /* Trả về data */
    return thisOne
  }

  async find(user: UserDto, data: { status?: string; offerTypeCode: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const whereCon: any = { isDeleted: false }
    if (data.status) whereCon.status = data.status
    if (data.offerTypeCode) whereCon.offerTypeCode = data.offerTypeCode
    const res: any = await this.repo.find({
      where: whereCon,
      relations: { offerService: true },
    })

    return res
  }

  async loadOfferBySupplier(user: UserDto, data: { offerId: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const ltsAutionSupplier: any = await this.repo.find({
      where: { id: data.offerId, isDeleted: false },
      // relations: { pr: true, offerSupplier: { supplier: true } },
    })

    const res = []
    for (const item of ltsAutionSupplier) {
      for (const item1 of item.__offerSupplier__) {
        if (item1.__supplier__) {
          res.push(item1.__supplier__)
        }
      }
    }
    /** 1 nhà cung cấp có thể báo giá nhiều lần */
    const uniqueSuppliers = Array.from(new Set(res.map((supplier) => supplier.id))).map((id) => res.find((supplier) => supplier.id === id))

    return uniqueSuppliers
  }

  async createData(user: UserDto, data: any) {
    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(OfferEntity)
      const offerServicerepo = trans.getRepository(OfferServiceEntity)
      const offerSuprepo = trans.getRepository(OfferSupplierEntity)
      const supRepo = trans.getRepository(SupplierEntity)
      const offerShipmentPriceRepo = trans.getRepository(OfferShipmentPriceEntity)
      const serviceRepo = trans.getRepository(ServiceEntity)
      const offerSupplierService = trans.getRepository(OfferSupplierServiceEntity)

      //tạo mới offer
      const offer = new OfferEntity()
      offer.code = nanoid()
      offer.title = data.title
      offer.refType = data.refType
      offer.createdAt = new Date()
      offer.createdAt = new Date()
      offer.createdBy = user.id
      offer.effectiveDate = new Date(data.effectiveDate)
      offer.endDate = new Date(data.endDate)
      offer.currency = data.currency
      offer.timePeriod = data.timePeriod
      offer.condition = data.condition
      offer.isNotConfigTrade = data.isNotConfigTrade
      offer.address = data.address
      offer.shipmentId = data.shipmentId
      offer.statusTrade = enumData.BidTradeStatus.DaDuyet.code
      offer.isHaveVat = data.isHaveVat
      offer.fileAttach = data.fileAttach
      offer.noteTrade = data.noteTrade
      offer.status = enumData.OfferStatus.MoiTao.code
      offer.isShowClient = data.isShowClient
      offer.description = data.description
      offer.offerTypeCode = data.offerTypeCode
      offer.isGetFromPr = data.isGetFromPr
      offer.isCompleteAll = data.isCompleteAll
      const newOffer = await repo.save(offer)
      if (data.lstPriceShipment && data.lstPriceShipment.length > 0 && data.offerTypeCode === enumData.BiddingType.SHIPPING.code) {
        for (const shipment of data.lstPriceShipment) {
          const bidShipment = new OfferShipmentPriceEntity()
          bidShipment.offerId = offer.id
          bidShipment.value = 0
          await offerShipmentPriceRepo.insert(bidShipment)
        }
      }

      //tạo mới lĩnh vực mua hàng ( 1 - n)\
      const lstService = []

      /* region tạo shipment */
      if (data.offerTypeCode === enumData.BiddingType.SHIPPING.code) {
        if (data.lstPriceShipment && data.lstPriceShipment.length > 0) {
          //check xem có lĩnh vực mua hàng không tồn tạị không
          const service = await serviceRepo.findOne({
            where: { isDeleted: false },
          })
          /* Tạo item với exGr*/
          const offerPrice = new OfferServiceEntity()
          offerPrice.id = uuidv4()
          offerPrice.offerId = newOffer.id
          offerPrice.serviceId = service.id
          // offerPrice.externalMaterialGroupId = data.externalMaterialGroupId
          offerPrice.isExGr = true

          offerPrice.serviceName = service.name
          await offerServicerepo.save(offerPrice)
          lstService.push(offerPrice)

          for (const item of data.lstPriceShipment) {
            const offerPrice = new OfferServiceEntity()
            offerPrice.id = uuidv4()
            offerPrice.offerId = newOffer.id
            offerPrice.offerId = newOffer.id
            offerPrice.prItemId = item.prItemId || item.id
            offerPrice.serviceId = service.id
            offerPrice.itemNo = item.itemNo
            offerPrice.category = item.category
            offerPrice.categoryName = item.categoryName
            offerPrice.materialGroupName = item.materialGroupName
            offerPrice.category = item.category
            offerPrice.shortText = item.shortText
            offerPrice.quantity = item.quantity
            offerPrice.deliveryDate = new Date(item.deliveryDate)
            offerPrice.value = item.quantity
            offerPrice.conditionType = item.conditionType
            offerPrice.description = item.description
            offerPrice.amount = item.amount
            offerPrice.crcy = item.crcy
            offerPrice.per = item.per
            offerPrice.conditionValue = item.conditionValue
            offerPrice.curr = item.curr
            offerPrice.cConDe = item.cConDe
            offerPrice.numCCo = item.numCCo
            // offerPrice.p = item.serviceId
            offerPrice.quantity = item.amount
            offerPrice.percentTech = item.percentTech || 0
            offerPrice.percentTrade = item.percentTrade || 0
            offerPrice.percentPrice = item.percentPrice || 0
            if (data.offerTypeCode === enumData.BiddingType.SHIPPING.code) offerPrice.shipmentPriceId = data.shipmentId
            offerPrice.serviceName = service.name
            await offerServicerepo.save(offerPrice)
            lstService.push(offerPrice)
          }
        } else {
          const offerPrice = new OfferServiceEntity()
          offerPrice.id = uuidv4()
          offerPrice.offerId = newOffer.id
          offerPrice.isExGr = true
          offerPrice.serviceName = 'Shipment'
          await offerServicerepo.save(offerPrice)
          lstService.push(offerPrice)
        }
      }
      if (data.offerTypeCode === enumData.BiddingType.PRODUCT.code)
        if (data.lstItem && data.lstItem.length > 0) {
          //check xem có lĩnh vực mua hàng không tồn tạị không
          const service = await serviceRepo.findOne({
            where: { isDeleted: false },
          })
          /* Tạo item với exGr*/
          const offerPrice = new OfferServiceEntity()
          offerPrice.id = uuidv4()
          offerPrice.offerId = newOffer.id
          offerPrice.serviceId = service.id
          offerPrice.isExGr = true
          offerPrice.serviceName = service.name
          await offerServicerepo.save(offerPrice)
          lstService.push(offerPrice)

          for (const item of data.lstItem) {
            const offerPrice = new OfferServiceEntity()
            offerPrice.id = uuidv4()
            offerPrice.offerId = newOffer.id
            offerPrice.offerId = newOffer.id
            offerPrice.prItemId = item.prItemId || item.id
            offerPrice.serviceId = service.id
            offerPrice.itemNo = item.itemNo
            offerPrice.category = item.category
            offerPrice.categoryName = item.categoryName
            offerPrice.materialGroupName = item.materialGroupName
            offerPrice.category = item.category
            offerPrice.shortText = item.shortText
            offerPrice.quantity = item.quantity
            offerPrice.deliveryDate = new Date(item.deliveryDate)
            offerPrice.value = item.quantity
            offerPrice.serviceName = service.name
            await offerServicerepo.save(offerPrice)
            lstService.push(offerPrice)
          }
        } else {
          const offerPrice = new OfferServiceEntity()
          offerPrice.id = uuidv4()
          offerPrice.offerId = newOffer.id
          offerPrice.isExGr = true
          offerPrice.serviceName = 'Shipment'
          await offerServicerepo.save(offerPrice)
          lstService.push(offerPrice)
        }

      // }
      return {
        message: UPDATE_SUCCESS,
      }
    })
  }

  async updateData(user: UserDto, data: any) {
    return await this.repo.manager.transaction(async (trans) => {})
  }

  async sendApprove(user: UserDto, data: { targetId: string }) {
    const offer = await this.repo.findOne({ where: { id: data.targetId, isDeleted: false } })
    if (!offer) throw new Error('Đơn hàng không tồn tại hoặc đã bị xóa!')
    offer.status = enumData.OfferStatus.ChoDuyet.code
    await this.repo.save(offer)

    let flowType: string

    return {
      message: UPDATE_SUCCESS,
    }
  }

  async approve(user: UserDto, data: { targetId: string }) {
    const offer = await this.repo.findOne({ where: { id: data.targetId, isDeleted: false } })
    if (!offer) throw new Error('Đơn hàng không tồn tại hoặc đã bị xóa!')
    offer.status = enumData.OfferStatus.MoiTao.code
    await this.repo.save(offer)
    /* Thêm bước clone cấu hình giá thương mại vào trong danh sách client */

    return {
      message: UPDATE_SUCCESS,
    }
  }

  async public(user: UserDto, data: { targetId: string }) {
    const offer = await this.repo.findOne({ where: { id: data.targetId, isDeleted: false } })
    if (!offer) throw new Error('Đơn hàng không tồn tại hoặc đã bị xóa!')
    offer.status = enumData.OfferStatus.DaCongKhai.code
    await this.repo.save(offer)
    /* Thêm bước clone cấu hình giá thương mại vào trong danh sách client */

    return {
      message: UPDATE_SUCCESS,
    }
  }

  async findWithExmat(user: UserDto, data: { exMatGrId?: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    /**Tìm ra danh sách id của gói thầu  */
    const res: any = await this.repo.find({
      where: { isDeleted: false },
    })
    return res
  }

  public async findItem(user: UserDto, data: { lstId: string[] }) {
    // tìm ra danh sách PR
    const lstPr: any = await this.repo.find({ where: { id: In(data.lstId) }, relations: { offerService: { service: true } } })
    if (lstPr.length !== data.lstId.length) throw new Error('Có PR không tồn tại hoặc đã bị ngưng hoạt động!')
    // lấy ra danh sách item và map lại theo itemID
    const lstData: any = {}

    for (const pr of lstPr) {
      for (const item of pr.__offerService__) {
        if (lstData[item.materialId]) {
          lstData[item.materialId].quantity += item.quantity
        }
        // nếu như không có thì thêm vào danh sách PrItem
        else {
          item.percentTech = item?.__service__?.percentTech || 0
          item.percentTrade = item?.__service__?.percentTrade || 0
          item.percentPrice = item?.__service__?.percentPrice || 0
          item.materialDetail = await item.material
          item.name = item.shortText
          item.materialGrDetail = await item.materialGroup
          lstData[item.materialId] = item
          item.materialCode = item.materialDetail?.code
        }
      }
    }
    return Object.values(lstData)
  }

  async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}

    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)

    const res: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { offerSupplier: true },
      order: { createdAt: 'DESC' },
    })
    for (const item of res[0]) {
      /* nếu như nó không cấu hình điều kiện thương mại thì ẩn điều kiện thương mại*/
      item.hiddenTrade = false
      if (item?.isNotConfigTrade) {
        item.hiddenTrade = true
      }
      item.countSp = 0

      item.canApproveOffer = true
      item.canApproveTrade = true
      item.canApproveTradeRate = true
      item.canApproveEnd = true

      item.countSp += +item?.__offerSupplier__?.length || 0
      item.statusName = enumData.OfferStatus[item.status]?.name
      item.statusColor = enumData.OfferStatus[item.status]?.color
      item.statusBgColor = enumData.OfferStatus[item.status]?.bgColor
      item.statusBorderColor = enumData.OfferStatus[item.status]?.borderColor
      if (new Date() < new Date(item.endDate)) item.canAddDate = true

      item.isShowEnd =
        item.status === enumData.BidStatus.HoanTatDanhGia.code ||
        item.status === enumData.BidStatus.DongDamPhanGia.code ||
        item.status === enumData.BidStatus.DongDauGia.code

      item.isShowAcceptEnd = item.status === enumData.BidStatus.DongThau.code || true
    }
    return res
  }

  async paginationClient(user: UserDto, data: any) {
    const whereCon: any = {}
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    const res: any = await this.repo.find({
      relations: { offerService: true },
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    })
    for (const item of res) {
      item.statusName = enumData.OfferStatus[item.status]?.name
      const lstService = item.__offerService__.map((e) => e.serviceName)
      item.service = lstService.join(',')
      item.month = new Date(item.effectiveDate).getMonth()
      item.day = new Date(item.effectiveDate).getDate()
    }
    return res
  }

  async paginationClientNoToken(user: UserDto, data: any) {
    const whereCon: any = {}
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    const res: any = await this.repo.find({
      relations: { offerService: true },
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    })
    for (const item of res) {
      item.statusName = enumData.OfferStatus[item.status]?.name
      const lstService = item.__offerService__.map((e) => e.serviceName)
      item.service = lstService.join(',')
      item.month = new Date(item.effectiveDate).getMonth()
      item.day = new Date(item.effectiveDate).getDate()
    }
    return res
  }

  async detailClient(user: UserDto, data: any) {
    /* Tìm ra offer hiện tại*/
    const offer: any = await this.repo.findOne({ where: { id: data.id } })
    if (!offer) throw new Error('Chào giá không tồn tại!')
    const sup = await this.supplierRepository.findOne({ where: { id: user.supplierId } })
    offer.sup = sup
    const thisOne = await this.offerSupplierRepo.findOne({ where: { offerId: data.id, supplierId: user.supplierId, parentId: IsNull() } })
    if (!thisOne) throw new Error('Bạn không có quyền truy cập vào màn hình này')
    offer.canSend = thisOne?.isJoin

    offer.lstSend = await this.offerSupplierRepo.find({ where: { parentId: thisOne.id } })
    for (const item of offer?.lstSend) {
      item.statusName = enumData.OfferSupplierStatus[item.status]?.name
      item.statusColor = enumData.OfferSupplierStatus[item.status]?.color
      item.statusBgColor = enumData.OfferSupplierStatus[item.status]?.bgColor
      item.statusBorderColor = enumData.OfferSupplierStatus[item.status]?.borderColor
    }
    offer.statusName = enumData.OfferStatus[offer.status]?.name
    offer.statusColor = enumData.OfferStatus[offer.status]?.color
    offer.statusBgColor = enumData.OfferStatus[offer.status]?.bgColor
    offer.statusBorderColor = enumData.OfferStatus[offer.status]?.borderColor
    if (offer.offerTypeCode === enumData.BiddingType.SHIPPING.code) {
      offer.isShipment = true
    } else {
      offer.isShipment = false
    }
    const trade = await this.offerTradeService.getTrade(user, offer.id)
    offer.trade = trade
    const price = await this.offerPriceService.getPrice(user, offer.id)
    offer.tradeShipment = await this.loadDataTradeShipment(user, { bidId: offer.id, offerSupplierId: data.offerSupplierId })

    const lstData = []
    for (const item of price.listItem) {
      if (item.isExGr) lstData.push(item)
    }
    price.listItem = lstData
    offer.price = price
    return offer
  }

  async loadDataTradeShipment(user: UserDto, data: { bidId: string; supplierId?: string; offerSupplierId?: string }) {
    const offerPriceShipmentValueRepo = this.repo.manager.getRepository(OfferSupplierShipmentValueEntity)
    const offerPriceShipmentRepo = this.repo.manager.getRepository(OfferShipmentPriceEntity)
    const offerSupplierRepo = this.repo.manager.getRepository(OfferSupplierEntity)
    const listPrice: any[] = await offerPriceShipmentRepo.find({
      where: { offerId: data.bidId, isDeleted: false },
      // relations: { shipmentPrice: true },
    })

    const supplierId = user.supplierId || data.supplierId
    if (supplierId && listPrice.length > 0) {
      let where: any = { offerId: data.bidId, supplierId, isDeleted: false }
      if (data.offerSupplierId) where = { id: data.offerSupplierId, isDeleted: false }
      const bidSupplier = await offerSupplierRepo.findOne({
        where: where,
        select: { id: true, status: true },
      })
      for (const data1 of listPrice) {
        data1.colz = data1.__shipmentPrice__.jsonPrice
        const jsonParse = JSON.parse(data1.colz)
        data1.conditionType = jsonParse.conditionType
        data1.description = jsonParse.description
        data1.amount = jsonParse.amount
        data1.crcy = jsonParse.crcy
        data1.per = jsonParse.per
        data1.conditionValue = jsonParse.conditionValue
        data1.curr = jsonParse.curr
        data1.numCCo = jsonParse.numCCo
        data1.cConDe = jsonParse.cConDe
      }
      if (!bidSupplier) return listPrice

      // nếu đã nộp thầu => show các data đã nộp
      if (bidSupplier.status == enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code) {
        const dicValue: any = {}
        {
          const lstTechValue = await offerPriceShipmentValueRepo.find({
            where: { offerSupplierId: data.offerSupplierId, isDeleted: false },
          })
          // lstTechValue.forEach((c) => (dicValue[c.shipmentPriceId] = c.value))
        }

        for (const data1 of listPrice) {
          data1.value = dicValue[data1.shipmentPriceId] || ''
          // for (const data2 of data1.__childs__) {
          //   data2.value = dicValue[data2.id] || ''
          // }
        }
      }
    }

    return listPrice
  }

  public async detailOfferClient(user: UserDto, data: { id: string }) {
    const res: any = await this.offerSupplierRepo.findOne({
      where: { id: data.id },
    })
    return res
  }
  public async sendOffer(user: UserDto, data: any) {
    const dup = await this.offerSupplierRepo.findOne({ where: { id: data.id } })
    if (!dup) throw new Error('Không tìm thấy dữ liệu!')
    return this.offerSupplierRepo.update(dup.id, { status: enumData.OfferSupplierStatus.SEND.code })
  }
  public async createDataClient(user: UserDto, data: any) {
    const offer: any = await this.repo.findOne({ where: { id: data.id } })
    if (!offer) throw new Error('Chào giá không tồn tại!')
    const thisOne = await this.offerSupplierRepo.findOne({ where: { offerId: data.id, supplierId: user.supplierId } })
    return await this.offerSupplierRepo.update(thisOne.id, { isJoin: true })
  }

  public async getOfferDealId(user: UserDto, data: any) {
    /* tìm ra offer deal dựa vào id truyền vào và parentid = null */
    const res: any = await this.bidDealRepo.findOne({
      where: { offerId: data.id, parentId: IsNull(), status: enumData.BidDealStatus.DangDamPhan.code },
    })
    return res
  }
  async updateTime(data: { targetId: string; timePeriod: Date }, user: UserDto) {
    const offerData = await this.repo.findOne({ where: { id: data.targetId } })
    if (!offerData) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    return await this.repo.update(offerData.id, { endDate: data.timePeriod })
  }
  async configPagination(user: UserDto, data: { id: string }) {
    const lstData = await this.serviceRepo.find({ where: { offerId: data.id, isDeleted: false } })
    return lstData
  }

  public async bidTechListDetail_list(user: UserDto, id: string) {
    // return await this.priceRepo.find({
    //   where: { offerServiceId: id },
    //   order: { price: 'DESC' },
    // })
  }

  public async listDetailCreateData(user: UserDto, data: { offerServiceId: string; nameCol: string; price: number }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    // const offerServiece = await this.serviceRepo.findOne({ where: { id: data.offerServiceId, companyId: user.companyId } })
    const offerServiece = await this.serviceRepo.findOne({ where: { id: data.offerServiceId } })

    if (!offerServiece) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const entity = new OfferPriceEntity()
    entity.companyId = user.companyId
    entity.createdBy = user.id
    // entity.price = +data.price
    // entity.nameCol = data.nameCol
    entity.offerServiceId = offerServiece.id
    entity.offerId = offerServiece.offerId
    // priceRepo
    await this.priceRepo.save(entity)
    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async listDetailUpdateData(user: UserDto, data: { id: string; offerServiceId: string; nameCol: string; price: number }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    // const offerServiece = await this.serviceRepo.findOne({ where: { id: data.offerServiceId, companyId: user.companyId } })
    const offerServiece = await this.serviceRepo.findOne({ where: { id: data.offerServiceId } })

    if (!offerServiece) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const offerPriceServiece = await this.priceRepo.findOne({ where: { id: data.id } })

    if (!offerPriceServiece) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    offerPriceServiece.companyId = user.companyId
    offerPriceServiece.createdBy = user.id
    // offerPriceServiece.price = +data.price
    // offerPriceServiece.nameCol = data.nameCol
    offerPriceServiece.offerServiceId = offerServiece.id
    offerPriceServiece.offerId = offerServiece.offerId
    // priceRepo
    await this.priceRepo.save(offerPriceServiece)
    return { id: offerPriceServiece.id, message: CREATE_SUCCESS }
  }
  public async listDetailDeleteData(user: UserDto, id: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    await this.priceRepo.delete(id)

    return { message: DELETE_SUCCESS }
  }

  async findOfferPr(user: UserDto, data: { ltsOfferId: [] }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const ltsOffer: any = await this.repo.find({
      where: { id: In(data.ltsOfferId), isDeleted: false },
    })

    const res = []
    for (const item of ltsOffer) {
      if (item.__pr__) {
        res.push(item.__pr__)
      }
    }
    return res
  }

  /** Lấy danh sách đàm phán giá */
  async getListBidResultDeal(user: UserDto, data: PaginationDto) {
    const res: any[] = await this.bidDealRepo.findAndCount({
      where: { offerId: data.where.bidId, companyId: user.companyId },
      relations: { childs: { offer: true } },
      skip: data.skip,
      take: data.take,
      select: {
        id: true,
        createdAt: true,
        endDate: true,
        status: true,
        isSendDealPrice: true,
        childs: { id: true, offer: { id: true } },
      },
    })

    if (res[0].length == 0) return res

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidDealStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }

    for (const item of res[0]) {
      item.statusName = dicStatus[item.status]
      item.itemName = item.__childs__.map((c) => c.__offer__.__exMatGroup__.code + ' - ' + c.__offer__.__exMatGroup__.name)
      delete item.__childs__
    }

    return res
  }

  /** Lấy chi tiết đàm phán giá */
  async getBidResultDeal(user: UserDto, bidDealId: string) {
    const res: any = await this.bidDealRepo.findOne({
      where: { id: bidDealId, companyId: user.companyId },
      relations: {
        childs: {
          offer: true,
          offerDealSupplier: { supplier: true, offerDealSupplierPriceValue: true },
          offerDealPrices: { offerPrice: true },
        },
      },
    })
    if (!res) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidDealSupplierStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }

    res.listChild = res.__childs__
    delete res.__childs__
    for (const child of res.listChild) {
      child.itemName = child.__offer__.__exMatGroup__.code + ' - ' + child.__offer__.__exMatGroup__.name
      delete child.__offer__

      for (const sup of child.__offerDealSupplier__) {
        sup.supplierName = sup.__supplier__.name
        delete sup.__supplier__

        sup.statusName = dicStatus[sup.status]
      }

      let lstTemp1 = child.__offerDealPrices__.filter((c: any) => c.__offerPrice__.level == 1)
      const lstTemp2 = child.__offerDealPrices__.filter((c: any) => c.__offerPrice__.level == 2)
      const lstTemp3 = child.__offerDealPrices__.filter((c: any) => c.__offerPrice__.level == 3)
      for (let i = 0; i < lstTemp1.length; i++) {
        const temp = lstTemp1[i]
        let childInLv1 = lstTemp2.filter((c: any) => c.__offerPrice__.parentId == temp.__offerPrice__.id)
        for (let j = 0; j < childInLv1.length; j++) {
          const temp2 = childInLv1[j]
          const childInLv2 = lstTemp3.filter((c: any) => c.__offerPrice__.parentId == temp2.__offerPrice__.id)
          childInLv1.splice(j + 1, 0, ...childInLv2)
        }
        lstTemp1.splice(i + 1, 0, ...childInLv1)
      }
      child.__offerDealPrices__ = lstTemp1
    }

    return res
  }

  /** Lấy chi tiết đàm phán giá theo Doanh nghiệp */
  async getBidResultDealSupplierDetail(user: UserDto, bidDealSupplierId: string) {
    const bidDealSupplier = await this.repo.manager
      .getRepository(OfferDealSupplierEntity)
      .findOne({ where: { id: bidDealSupplierId, companyId: user.companyId } })
    if (!bidDealSupplier) throw new Error(ERROR_NOT_FOUND_DATA)
    const bidDealItem = await bidDealSupplier.offerDeal
    const bidDealPrices = await bidDealItem.offerDealPrices
    const bidDealSupplierPrice = await this.repo.manager
      .getRepository(OfferDealSupplierPriceValueEntity)
      .find({ where: { offerDealSupplierId: bidDealSupplierId, companyId: user.companyId } })
    const lstBidPrice = await this.bidPriceRepo.getPrice(user, bidDealItem.offerId)
    lstBidPrice.sort((a, b) => a.sort - b.sort)

    for (const item of lstBidPrice) {
      const bidDealPrice = bidDealPrices.find((c) => c.offerPriceId == item.id)
      if (bidDealPrice && bidDealPrice.number > 0) {
        item.number = bidDealPrice.number
      }
    }
    return { bidPrices: lstBidPrice, bidDealSupplierPrice }
  }

  async findWithShipment(user: UserDto, data: { shipmentId?: string; biddingTypeCode?: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    /**Tìm ra danh sách id của báo giá  */
    const whereCon: any = { shipmentId: data.shipmentId, offerTypeCode: data.biddingTypeCode, isDeleted: false }
    const res: any = await this.repo.find({
      where: whereCon,
      select: { id: true, title: true },
    })
    return res
  }

  public async loadOfferSupplierFromOffer(user: UserDto, data: { offerId: string }) {
    const res: any = await this.offerSupplierRepo.findOne({
      where: {
        offerId: data.offerId,
        parentId: Not(IsNull()),
      },
      order: { createdAt: 'DESC' },
      relations: { supplier: true },
    })
    return res
  }

  public async loadSupplierShipmentValue(user: UserDto, data: { offerSupplierId: string }) {
    const res: any = await this.offerSupplierShipmentValueRepo.find({
      where: {
        offerSupplierId: data.offerSupplierId,
      },
    })
    for (let item of res) {
      const json = JSON.parse(item.__shipmentPrice__.__shipmentCostStageCost__.jsonCost)
      item.isCheck = false
      item.conditionType = json.conditionType
      item.description = json.description
      item.amount = json.amount
      item.crcy = json.crcy
      item.per = json.per
      item.conditionValue = json.conditionValue
      item.curr = json.curr
      item.numCCo = json.numCCo
      item.cConDe = json.cConDe
      item.id = item.__shipmentPrice__.__shipmentCostStageCost__.id
      item.jsonCost = item.__shipmentPrice__.__shipmentCostStageCost__.jsonCost

      item.costExpect = +item.value
      delete item.__shipmentPrice__.__shipmentCostStageCost__
    }
    return res
  }
}
