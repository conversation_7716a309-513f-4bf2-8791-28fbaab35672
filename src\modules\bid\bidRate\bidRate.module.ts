import { Module } from '@nestjs/common'
import { BidRateController } from './bidRate.controller'
import { BidRateService } from './bidRate.service'
import { EmailModule } from '../../email/email.module'
import {
  BidRepository,
  BidTechRepository,
  BidPriceRepository,
  BidTradeRepository,
  BidEmployeeAccessRepository,
  BidSupplierRepository,
  BidDealRepository,
  BidPriceColRepository,
} from '../../../repositories'
import { TypeOrmExModule } from '../../../typeorm'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      BidEmployeeAccessRepository,
      BidRepository,
      BidDealRepository,
      BidTechRepository,
      BidTradeRepository,
      BidPriceRepository,
      BidPriceColRepository,
      BidSupplierRepository,
    ]),
    EmailModule,
  ],
  controllers: [BidRateController],
  providers: [BidRateService],
  exports: [BidRateService],
})
export class BidRateModule {}
