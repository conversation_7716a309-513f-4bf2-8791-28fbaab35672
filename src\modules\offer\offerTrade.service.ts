import { Injectable, NotFoundException, BadRequestException, MethodNotAllowedException } from '@nestjs/common'
import { ERROR_NOT_FOUND_DATA, ERROR_SUPPLIER_USED_TEMPLATE, UPDATE_SUCCESS, CREATE_SUCCESS, DELETE_SUCCESS, IMPORT_SUCCESS } from '../../constants'
import { ServiceTradeRepository } from '../../repositories'
import { enumData } from '../../constants/enumData'
import { UserDto } from '../../dto'
import { IsNull } from 'typeorm'
import { BidTradeCreateDto, BidTradeUpdateDto } from './dto2'
import { OfferRepository, OfferServiceRepository, OfferSupplierRepository, OfferTradeRepository } from '../../repositories/offer.repository'
import { OfferTradeEntity } from '../../entities/offerTrade.entity'
import { OfferTradeListDetailEntity } from '../../entities/offerTradeListDetail.entity'
import { OfferEntity, OfferServiceEntity } from '../../entities'

@Injectable()
export class OfferTradeService {
  constructor(
    private readonly repo: OfferRepository,
    private readonly serviceTradeRepo: ServiceTradeRepository,
    private readonly bidTradeRepo: OfferTradeRepository,
    private readonly bidPrItemRepository: OfferServiceRepository,
  ) {}

  //#region  bidTrade

  /** Check quyền tạo thiết lập điều kiện thương mại cho gói thầu */
  async checkPermissionTradeCreate(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const lstStatusCanEdit = [
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    const bid = await this.repo.findOne({
      where: [{ id: bidId, isDeleted: false }],
    })
    if (bid) {
      if (lstStatusCanEdit.includes(bid.status)) {
        result = true
        // result = await this.bidEmployeeAccessRepo.isMPO(user, bid.id)
        if (!result) message = 'Bạn không có quyền thiết lập điều kiện thương mại cho gói thầu.'
      } else {
        result = false
        message = 'Chỉ được phép thiết lập điều kiện thương mại khi chưa mở thầu.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Update statusTrade => DangTao */
  async creatingTrade(user: UserDto, bidId: string) {
    await this.repo.update(bidId, {
      statusTrade: enumData.BidTradeStatus.DangTao.code,
      status: enumData.BidStatus.DangCauHinhGoiThau.code,
      updatedBy: user.id,
    })
  }

  /** Lấy thông tin thiết lập điều kiện thương mại của lĩnh vực mời thầu */
  async loadTrade(user: UserDto, offerServiceId: string) {
    // const bid = await this.repo.findOne({ where: { id: offerServiceId, isDeleted: false } })
    const bid = await this.bidPrItemRepository.findOne({ where: { id: offerServiceId, isDeleted: false } })

    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const flag = await this.checkPermissionMpoEditTemplate(user, offerServiceId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    let lstServiceTrade = await this.serviceTradeRepo.find({
      where: { serviceId: bid.serviceId, parentId: IsNull(), isDeleted: false },
      relations: { childs: { serviceTradeListDetails: true }, serviceTradeListDetails: true },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })

    // Tạo danh sách điều kiện thương mại cho gói thầu
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(OfferTradeEntity)
      const bidTradeListDetailRepo = manager.getRepository(OfferTradeListDetailEntity)
      for (const a of lstServiceTrade) {
        const item = new OfferTradeEntity()
        item.companyId = user?.companyId
        item.createdBy = user.id
        item.offerId = bid.offerId
        item.offerServiceId = bid.id
        item.sort = a.sort
        item.name = a.name
        item.isRequired = a.isRequired
        item.type = a.type
        item.percent = a.percent
        item.percentRule = a.percentRule
        item.isCalUp = a.isCalUp
        item.percentDownRule = a.percentDownRule
        item.level = a.level
        item.description = a.description
        item.parentId = a.parentId
        item.scoreDLC = a.scoreDLC
        item.requiredMin = a.requiredMin
        const bidTradeEntity = await bidTradeRepo.save(item)

        const lstChild = (await a.childs).filter((c) => !c.isDeleted)
        if (lstChild && lstChild.length > 0) {
          for (const b of lstChild) {
            const itemChild = new OfferTradeEntity()
            itemChild.companyId = user?.companyId
            itemChild.createdBy = user.id
            item.offerId = bid.offerId
            itemChild.offerServiceId = offerServiceId
            itemChild.sort = b.sort
            itemChild.name = b.name
            itemChild.isRequired = b.isRequired
            itemChild.type = b.type
            itemChild.percent = b.percent
            itemChild.percentRule = b.percentRule
            itemChild.isCalUp = b.isCalUp
            itemChild.percentDownRule = b.percentDownRule
            itemChild.level = b.level
            itemChild.description = b.description
            itemChild.parentId = bidTradeEntity.id
            itemChild.scoreDLC = b.scoreDLC
            itemChild.requiredMin = b.requiredMin
            const bidTradeChildEntity = await bidTradeRepo.save(itemChild)

            const lstDataTypeList = (await b.serviceTradeListDetails).filter((c) => !c.isDeleted)
            if (lstDataTypeList && lstDataTypeList.length > 0) {
              for (const c of lstDataTypeList) {
                const itemListDetail = new OfferTradeListDetailEntity()
                itemListDetail.companyId = user?.companyId
                itemListDetail.createdBy = user.id
                itemListDetail.offerTradeId = bidTradeChildEntity.id
                itemListDetail.name = c.name
                itemListDetail.value = c.value
                await bidTradeListDetailRepo.save(itemListDetail)
              }
            }
          }
        }

        const lstDataTypeList = (await a.serviceTradeListDetails).filter((c) => !c.isDeleted)
        for (const c of lstDataTypeList) {
          const itemListDetail = new OfferTradeListDetailEntity()
          itemListDetail.companyId = user?.companyId
          itemListDetail.createdBy = user.id
          itemListDetail.offerTradeId = bidTradeEntity.id
          itemListDetail.name = c.name
          itemListDetail.value = c.value
          await bidTradeListDetailRepo.save(itemListDetail)
        }
      }
    })

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.offerId || bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái điều kiện thương mại cho gói thầu thất bại.')
    })
  }

  /** Tạo thiết lập điều kiện thương mại cho gói thầu */
  /**
   * gởi yêu cầu duyệt điều kiện thương mại
   */

  async createTrade(user: UserDto, bidId: string, data: { noteTrade: string }) {
    const objPermission = await this.checkPermissionTradeCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: bidId, isDeleted: false } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    bid.statusTrade = enumData.BidTradeStatus.DaDuyet.code
    bid.noteTrade = data.noteTrade
    // if (
    //   bid.statusTech === enumData.BidTechStatus.DaDuyet.code &&
    //   (bid.statusPrice === enumData.BidPriceStatus.DaTao.code || bid.statusPrice === enumData.BidPriceStatus.DaDuyet.code)
    // ) {
    //   bid.status = enumData.BidStatus.DangChonNCC.code
    //   // chưa chọn => đang chọn, để chọn Doanh nghiệp
    //   if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.ChuaChon.code) {
    //     bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DangChon.code
    //   }
    //   // đã duyệt => đã chọn, để duyệt lại
    //   if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaDuyet.code) {
    //     bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DaChon.code
    //   }
    // }
    bid.updatedBy = user.id
    await this.repo.save(bid)

    if (bid.statusPrice === enumData.BidPriceStatus.DaDuyet.code && bid.statusChooseSupplier === enumData.BidTechStatus.DaDuyet.code) {
      await this.repo.update({ id: bid.id }, { status: enumData.OfferStatus.DaCongKhai.code })
      // bid.status = enumData.OfferStatus.DaCongKhai.code
    }

    // return { message: 'Gởi yêu cầu phê duyệt thiết lập điều kiện thương mại cho gói thầu thành công.' }

    return { message: 'Thiết lập điều kiện thương mại cho gói thầu thành công.' }
  }

  /** Duyệt thiết lập điều kiện thương mại của gói thầu */
  async tradeAccept(user: UserDto, bidId: string, data: { noteTrade?: string }) {
    // const approveStatus = await this.flowService.approveRule(user, {
    //   targetId: bidId,
    //   entityName: OfferEntity.name,
    //   type: enumData.FlowCode.OFFERTRADE.code,
    // })

    // if (approveStatus.status === enumData.APPROVE_TYPE.NOT_DONE.code) {
    //   return { message: `Đã duyệt thành công, Vui lòng chờ cấp sau duyệt` }
    // } else {
    // const objPermission = await this.checkPermissionTechAccept(user, bidId)
    // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (!bid) throw new Error('Không tìm thấy dữ liệu')
    if (bid) {
      bid.statusTrade = enumData.BidTradeStatus.DaDuyet.code
      bid.noteTrade = data.noteTrade
      if (bid.statusPrice === enumData.BidPriceStatus.DaTao.code || bid.statusPrice === enumData.BidPriceStatus.DaDuyet.code) {
        bid.status = enumData.OfferStatus.HoanTatCauHinh.code
        // chưa chọn => đang chọn, để chọn Doanh nghiệp
        if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.ChuaChon.code) {
          bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DangChon.code
        }
        // đã duyệt => đã chọn, để duyệt lại
        if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaDuyet.code) {
          bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DaChon.code
        }
      }

      bid.updatedBy = user.id
      await this.repo.save(bid)
      // }

      // gửi email
      // await this.emailService.ThongBaoDaDuyetKyThuat(bidId)

      return { message: 'Duyệt thiết lập điều kiện thương mại của gói thầu thành công.' }
    }
  }

  /** Lấy thiết lập điều kiện thương mại của gói thầu */
  async getTrade(user: UserDto, bidId: string) {
    //
    const res = await this.repo.getBid1(user, bidId)

    // lọc lại data
    const lstDataItem = []
    for (const item of res.listItem) {
      const service = await item.service
      item.itemName = service?.code + ' - ' + service?.name
      lstDataItem.push(item)
    }
    res.canApprove = true
    res.listItem = lstDataItem
    const lstData = []
    for (const item of res.listItem) {
      item.listTrade = await this.bidTradeRepo.getTrade(user, item.id)

      item.sumPercent = 0
      for (const data1 of item.listTrade) {
        if (data1.percent > 0) item.sumPercent += data1.percent

        const lstChild = data1.__childs__.filter((c) => c.type === enumData.DataType.List.code || c.type === enumData.DataType.Number.code)
        if (lstChild.length > 0) {
          data1.sumPercent = 0
          for (const child of lstChild) {
            if (child.percent > 0) data1.sumPercent += child.percent
          }
        }
      }
      if (item.isExGr) lstData.push(item)
    }
    res.listItem = lstData
    return res
  }

  /** Lấy data cbb tiêu chí cấp 1 */
  async tradeGetData(user: UserDto, bidId: string) {
    return await this.bidTradeRepo.find({ where: { offerServiceId: bidId, level: 1, isDeleted: false } })
  }

  /** Tạo thêm ngoài lấy từ template */
  async tradeCreateData(user: UserDto, data: BidTradeCreateDto) {
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    // const bid = await this.bidPrItemRepository.findOne({ where: { id: data.bidId, isDeleted: false } })
    const bid = await this.bidPrItemRepository.findOne({ where: { id: data.bidId, isDeleted: false } })

    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Tạo thêm ngoài lấy từ template
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(OfferTradeEntity)

      const item = new OfferTradeEntity()
      item.companyId = user?.companyId
      item.createdBy = user.id
      item.offerId = bid.offerId
      item.offerServiceId = bid.id
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.percentRule = data.percentRule
      item.isCalUp = data.isCalUp
      item.percentDownRule = data.percentDownRule
      item.level = data.level
      item.description = data.description
      item.parentId = data.parentId
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      await bidTradeRepo.save(item)
    })

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.offerId || bid.id)
  }

  /** Cập nhật */
  async tradeUpdateData(user: UserDto, data: BidTradeUpdateDto) {
    if (!data.offerId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    // const bid = await this.bidPrItemRepository.findOne({ where: { id: data.bidId, isDeleted: false } })
    const bid = await this.bidPrItemRepository.findOne({
      where: [
        { offerId: data.offerId, isDeleted: false },
        { id: data.offerId, isDeleted: false },
      ],
    })

    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionTradeCreate(user, data.offerId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.offerId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Cập nhật
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(OfferTradeEntity)

      const item = await bidTradeRepo.findOne({ where: { id: data.id } })
      if (!item) throw new Error(ERROR_NOT_FOUND_DATA)
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.percentRule = data.percentRule
      item.isCalUp = data.isCalUp
      item.percentDownRule = data.percentDownRule
      item.level = data.level
      item.description = data.description
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      item.updatedBy = user.id
      await bidTradeRepo.save(item)
    })

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.offerId || bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái điều kiện thương mại cho gói thầu thất bại.')
    })
  }

  /** Xóa */
  async tradeDeleteData(user: UserDto, bidTradeId: string) {
    const bidTrade = await this.bidTradeRepo.findOne({ where: { id: bidTradeId } })
    if (!bidTrade) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidTrade.offerService
    const objPermission = await this.checkPermissionTradeCreate(user, bid.offerId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, bid.offerId)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Xóa
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(OfferTradeEntity)
      const bidTradeListDetailRepo = manager.getRepository(OfferTradeListDetailEntity)

      const bidTrade = await bidTradeRepo.findOne({ where: { id: bidTradeId } })
      if (!bidTrade) throw new Error(ERROR_NOT_FOUND_DATA)

      const lstChild = await bidTrade.childs
      for (const bidTradeChild of lstChild) {
        await bidTradeListDetailRepo.delete({ offerTradeId: bidTradeChild.id })
      }
      await bidTradeListDetailRepo.delete({ offerTradeId: bidTradeId })
      await bidTradeRepo.delete({ parentId: bidTradeId })
      await bidTradeRepo.delete(bidTradeId)

      return { message: DELETE_SUCCESS }
    })

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.offerId || bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái điều kiện thương mại cho gói thầu thất bại.')
    })
  }

  /** Xoá tất cả */
  async tradeDeleteAllData(user: UserDto, bidId: string) {
    // const bid = await this.bidPrItemRepository.findOne({ where: { id: bidId, isDeleted: false } })
    const bid = await this.bidPrItemRepository.findOne({ where: { id: bidId, isDeleted: false } })

    const objPermission = await this.checkPermissionTradeCreate(user, bid.offerId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const flag = await this.checkPermissionMpoEditTemplate(user, bid.offerId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Xoá tất cả
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(OfferTradeEntity)
      const bidTradeListDetailRepo = manager.getRepository(OfferTradeListDetailEntity)

      const lstBidTrade = await bidTradeRepo.find({ where: { offerServiceId: bidId } })
      for (const bidTrade of lstBidTrade) {
        const lstBidTradeChild = await bidTrade.childs
        for (const bidTradeChild of lstBidTradeChild) {
          // xoá lst detail
          await bidTradeListDetailRepo.delete({ offerTradeId: bidTradeChild.id })
        }
        // xoá lst
        await bidTradeListDetailRepo.delete({ offerTradeId: bidTrade.id })
        // xoá con
        await bidTradeRepo.delete({ parentId: bidTrade.id })
        // xoá
        await bidTradeRepo.delete(bidTrade.id)
      }

      return { message: DELETE_SUCCESS }
    })

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.offerId || bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái điều kiện thương mại cho gói thầu thất bại.')
    })
  }

  /** Import excel chào giá */
  public async trade_import(user: UserDto, bidId: string, data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    await this.tradeDeleteAllData(user, bidId)
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(OfferTradeEntity)
      const bidTradeListDetailRepo = manager.getRepository(OfferTradeListDetailEntity)
      const offerRepo = manager.getRepository(OfferServiceEntity)
      const bid = await offerRepo.findOne({ where: { id: bidId } })
      // add lv1
      var lstDataLv1 = data.lstDataTable1.filter((c: any) => c.level == 1)
      for (const item of lstDataLv1) {
        const objBidTradeNew = new OfferTradeEntity()
        objBidTradeNew.companyId = user?.companyId
        objBidTradeNew.createdBy = user.id
        // objBidTradeNew.bidId = bidId\
        item.offerId = bid.offerId
        objBidTradeNew.offerServiceId = bidId
        objBidTradeNew.level = 1
        objBidTradeNew.sort = item.sort || 0
        objBidTradeNew.name = item.name
        objBidTradeNew.percent = item.percent
        objBidTradeNew.percentRule = item.percentRule
        objBidTradeNew.type = item.type
        objBidTradeNew.isRequired = item.isRequired
        objBidTradeNew.isCalUp = item.isCalUp
        objBidTradeNew.percentDownRule = item.percentDownRule

        const objBidTrade = await bidTradeRepo.save(objBidTradeNew)
        item.id = objBidTrade.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new OfferTradeListDetailEntity()
            detailNew.companyId = user?.companyId
            detailNew.createdBy = user.id
            detailNew.offerTradeId = item.id
            detailNew.name = detail.nameList
            detailNew.value = detail.valueList
            await bidTradeListDetailRepo.save(detailNew)
          }
        }
      }

      // add lv2
      var lstDataLv2 = data.lstDataTable1.filter((c: any) => c.level == 2)
      for (const item of lstDataLv2) {
        const objBidTradeNew = new OfferTradeEntity()
        objBidTradeNew.companyId = user?.companyId
        objBidTradeNew.createdBy = user.id
        // objBidTradeNew.bidId = bidId
        item.offerId = bid.offerId
        objBidTradeNew.offerServiceId = bidId
        objBidTradeNew.level = 2
        objBidTradeNew.sort = item.sort || 0
        objBidTradeNew.name = item.name
        objBidTradeNew.percent = item.percent
        objBidTradeNew.percentRule = item.percentRule
        objBidTradeNew.type = item.type
        objBidTradeNew.isRequired = item.isRequired
        objBidTradeNew.isCalUp = item.isCalUp
        objBidTradeNew.percentDownRule = item.percentDownRule

        const parent = lstDataLv1.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objBidTradeNew.parentId = parent.id

        const objBidTrade = await bidTradeRepo.save(objBidTradeNew)
        item.id = objBidTrade.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new OfferTradeListDetailEntity()
            detailNew.companyId = user?.companyId
            detailNew.createdBy = user.id
            detailNew.offerTradeId = item.id
            detailNew.name = detail.nameList
            detailNew.value = detail.valueList
            await bidTradeListDetailRepo.save(detailNew)
          }
        }
      }
    })

    return { message: IMPORT_SUCCESS }
  }

  public async bidTradeListDetail_list(user: UserDto, bidTradeId: string) {
    return await this.repo.manager.getRepository(OfferTradeListDetailEntity).find({
      where: { offerTradeId: bidTradeId },
      order: { value: 'DESC' },
    })
  }

  public async bidTradeListDetail_create_data(user: UserDto, data: { bidTradeId: string; name: string; value: number }) {
    const bidTrade = await this.bidTradeRepo.findOne({ where: { id: data.bidTradeId } })
    if (!bidTrade) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidTrade.offerService
    const objPermission = await this.checkPermissionTradeCreate(user, bid.offerId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, bid.offerId)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.offerId || bid.id)

    const entity = new OfferTradeListDetailEntity()
    entity.companyId = user?.companyId
    entity.createdBy = user.id
    entity.name = data.name
    entity.value = data.value
    entity.offerTradeId = data.bidTradeId
    await entity.save()

    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async bidTradeListDetail_update_data(user: UserDto, data: { id: string; bidTradeId: string; name: string; value: number }) {
    const bidTrade = await this.bidTradeRepo.findOne({ where: { id: data.bidTradeId } })
    if (!bidTrade) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidTrade.offerService
    const objPermission = await this.checkPermissionTradeCreate(user, bid.offerId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, bid.offerId)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.offerId || bid.id)

    const entity = await this.repo.manager.getRepository(OfferTradeListDetailEntity).findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    entity.name = data.name
    entity.value = data.value
    entity.updatedBy = user.id
    await entity.save()

    return { id: entity.id, message: UPDATE_SUCCESS }
  }

  public async bidTradeListDetail_delete_data(user: UserDto, id: string) {
    const bidTradeListDetail = await this.repo.manager.getRepository(OfferTradeListDetailEntity).findOne({ where: { id } })
    if (!bidTradeListDetail) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bidTrade = await bidTradeListDetail.offerTrade
    const bid = await bidTrade.offerService
    const objPermission = await this.checkPermissionTradeCreate(user, bid.offerId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bid.offerId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.offerId || bid.id)

    await this.repo.manager.getRepository(OfferTradeListDetailEntity).delete(id)

    return { message: DELETE_SUCCESS }
  }

  //#endregion

  async checkPermissionMpoEditTemplate(user: UserDto, bidId: string) {
    let result = true

    return result
  }
}
