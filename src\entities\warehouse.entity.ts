import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'
import { AsnEntity } from './asn.entity'

@Entity('warehouse')
export class WarehouseEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @OneToMany(() => AsnEntity, (p) => p.warehouse)
  asn: Promise<AsnEntity[]>
}
