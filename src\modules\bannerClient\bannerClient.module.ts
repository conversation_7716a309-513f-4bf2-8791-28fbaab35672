import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { BannerClientService } from './bannerClient.service'
import { BannerClientController } from './bannerClient.controller'
import { BannerClientRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BannerClientRepository])],
  controllers: [BannerClientController],
  providers: [BannerClientService],
})
export class BannerClientModule {}
