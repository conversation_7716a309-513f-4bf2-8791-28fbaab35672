import { Body, Controller, Get, Param, Post, Query, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { enumProject } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { BidDetailService } from './bidDetail.service'

@ApiBearerAuth()
@ApiTags('Bid')
@Controller('bid_details')
export class BidDetailController {
  constructor(private readonly service: BidDetailService) {}

  @ApiOperation({ summary: 'Lấy danh sách Doanh nghiệp bổ sung hồ sơ kỹ thuật theo bidTechId' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_supplier_tech_by_bid_tech_id/:bidtechid')
  public async getSupplierTechByBidId(@CurrentUser() user: UserDto, @Param('bidtechid') bidTechId: string, @Query() data: PaginationDto) {
    return await this.service.getSupplierTechByBidId(user, bidTechId, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách Doanh nghiệp bổ sung hồ sơ thương mại theo bidTradeId' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_supplier_trade_by_bid_trade_id/:bidtradeid')
  public async getSupplierTradeByBidId(@CurrentUser() user: UserDto, @Param('bidtradeid') bidTradeId: string, @Query() data: PaginationDto) {
    return await this.service.getSupplierTradeByBidId(user, bidTradeId, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách Doanh nghiệp bổ sung hồ sơ giá theo bidPriceId' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_supplier_price_by_bid_price_id/:bidpriceid')
  public async getSupplierPriceByBidId(@CurrentUser() user: UserDto, @Param('bidpriceid') bidPriceId: string, @Query() data: PaginationDto) {
    return await this.service.getSupplierPriceByBidId(user, bidPriceId, data)
  }

  @ApiOperation({ summary: 'Chi tiết gói thầu' })
  @Roles(
    enumProject.Features.BID_001.code,
    enumProject.Features.BID_002.code,
    enumProject.Features.BID_005.code,
    enumProject.Features.BID_011.code,
    enumProject.Features.BID_012.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetail(user, data)
  }

  @ApiOperation({ summary: 'Kiểm tra ẩn hiện các tab trong chi tiết gói thầu' })
  @Roles(
    enumProject.Features.BID_001.code,
    enumProject.Features.BID_002.code,
    enumProject.Features.BID_005.code,
    enumProject.Features.BID_011.code,
    enumProject.Features.BID_012.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_check_deal_and_auction/:bidid')
  public async checkDealAndAuction(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.service.checkDealAndAuction(user, bidId)
  }

  @ApiOperation({ summary: 'Lịch sử gói thầu phân trang' })
  @Roles(
    enumProject.Features.BID_001.code,
    enumProject.Features.BID_002.code,
    enumProject.Features.BID_005.code,
    enumProject.Features.BID_011.code,
    enumProject.Features.BID_012.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination_history')
  public async paginationHistory(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.paginationHistory(user, data)
  }

  @ApiOperation({ summary: 'Lấy kết quả đánh giá gói thầu' })
  @Roles(
    enumProject.Features.BID_001.code,
    enumProject.Features.BID_002.code,
    enumProject.Features.BID_005.code,
    enumProject.Features.BID_011.code,
    enumProject.Features.BID_012.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_bid_result')
  public async loadBidResult(@CurrentUser() user: UserDto, @Body() data: { bidId: string; name?: string; statusFile?: string[] }) {
    return await this.service.loadBidResult(user, data)
  }

  @ApiOperation({ summary: 'Lấy kết quả đánh giá năng lực của Doanh nghiệp tham gia gói thầu' })
  @Roles(
    enumProject.Features.BID_001.code,
    enumProject.Features.BID_002.code,
    enumProject.Features.BID_005.code,
    enumProject.Features.BID_011.code,
    enumProject.Features.BID_012.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_bid_result_capacity/:bidid')
  public async getBidResultCapacity(@CurrentUser() user: UserDto, @Param('bidid') bidId: string, @Query() data: { supplierId: string }) {
    return await this.service.getBidResultCapacity(user, bidId, data.supplierId)
  }

  @ApiOperation({ summary: 'Lấy kết quả đánh giá điều kiện kỹ thuật của Doanh nghiệp tham gia gói thầu' })
  @Roles(
    enumProject.Features.BID_001.code,
    enumProject.Features.BID_002.code,
    enumProject.Features.BID_005.code,
    enumProject.Features.BID_011.code,
    enumProject.Features.BID_012.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_bid_result_tech/:bidid')
  public async getBidResultTech(@CurrentUser() user: UserDto, @Param('bidid') bidId: string, @Query() data: { supplierId: string }) {
    return await this.service.getBidResultTech(user, bidId, data.supplierId)
  }

  @ApiOperation({ summary: 'Lấy kết quả đánh giá điều kiện thương mại của Doanh nghiệp tham gia gói thầu' })
  @Roles(
    enumProject.Features.BID_001.code,
    enumProject.Features.BID_002.code,
    enumProject.Features.BID_005.code,
    enumProject.Features.BID_011.code,
    enumProject.Features.BID_012.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_bid_result_trade/:bidid')
  public async getBidResultTrade(@CurrentUser() user: UserDto, @Param('bidid') bidId: string, @Query() data: { supplierId: string }) {
    return await this.service.getBidResultTrade(user, bidId, data.supplierId)
  }

  @ApiOperation({ summary: 'Lấy kết quả đánh giá bảng chào giá của Doanh nghiệp tham gia gói thầu' })
  @Roles(
    enumProject.Features.BID_001.code,
    enumProject.Features.BID_002.code,
    enumProject.Features.BID_005.code,
    enumProject.Features.BID_011.code,
    enumProject.Features.BID_012.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_bid_result_price/:bidid')
  public async getBidResultPrice(@CurrentUser() user: UserDto, @Param('bidid') bidId: string, @Query() data: { supplierId: string }) {
    return await this.service.getBidResultPrice(user, bidId, data.supplierId)
  }

  @ApiOperation({ summary: 'Lấy lịch sử chào giá gói thầu của NCC' })
  @Roles(
    enumProject.Features.BID_001.code,
    enumProject.Features.BID_002.code,
    enumProject.Features.BID_005.code,
    enumProject.Features.BID_011.code,
    enumProject.Features.BID_012.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_bid_history_price/:bidid')
  public async getBidHistoryPrice(@CurrentUser() user: UserDto, @Param('bidid') bidId: string, @Query() data: { supplierId: string }) {
    return await this.service.getBidHistoryPrice(user, bidId, data.supplierId)
  }

  @ApiOperation({ summary: 'Lấy kết quả đánh giá cơ cấu giá' })
  @Roles(
    enumProject.Features.BID_001.code,
    enumProject.Features.BID_002.code,
    enumProject.Features.BID_005.code,
    enumProject.Features.BID_011.code,
    enumProject.Features.BID_012.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_bid_result_custom_price/:bidid')
  public async getBidResultCustomPrice(@CurrentUser() user: UserDto, @Param('bidid') bidId: string, @Query() data: { supplierId: string }) {
    return await this.service.getBidResultCustomPrice(user, bidId, data.supplierId)
  }

  @ApiOperation({ summary: 'Lấy danh sách đàm phán giá' })
  @Roles(
    enumProject.Features.BID_001.code,
    enumProject.Features.BID_002.code,
    enumProject.Features.BID_005.code,
    enumProject.Features.BID_009.code,
    enumProject.Features.BID_011.code,
    enumProject.Features.BID_012.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('get_list_bid_result_deal')
  public async getListBidResultDeal(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.getListBidResultDeal(user, data)
  }

  @ApiOperation({ summary: 'Lấy chi tiết đàm phán giá' })
  @Roles(
    enumProject.Features.BID_001.code,
    enumProject.Features.BID_002.code,
    enumProject.Features.BID_005.code,
    enumProject.Features.BID_009.code,
    enumProject.Features.BID_011.code,
    enumProject.Features.BID_012.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_bid_result_deal/:biddealid')
  public async getBidResultDeal(@CurrentUser() user: UserDto, @Param('biddealid') bidDealId: string) {
    return await this.service.getBidResultDeal(user, bidDealId)
  }

  @ApiOperation({ summary: 'Lấy chi tiết đàm phán giá theo Doanh nghiệp' })
  @Roles(
    enumProject.Features.BID_001.code,
    enumProject.Features.BID_002.code,
    enumProject.Features.BID_005.code,
    enumProject.Features.BID_009.code,
    enumProject.Features.BID_011.code,
    enumProject.Features.BID_012.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_bid_result_deal_supplier_detail/:biddealsupplierid')
  public async getBidResultDealSupplierDetail(@CurrentUser() user: UserDto, @Param('biddealsupplierid') bidDealSupplierId: string) {
    return await this.service.getBidResultDealSupplierDetail(user, bidDealSupplierId)
  }

  @ApiOperation({ summary: 'Lấy ds đấu giá của gói thầu' })
  @Roles(
    enumProject.Features.BID_001.code,
    enumProject.Features.BID_002.code,
    enumProject.Features.BID_005.code,
    enumProject.Features.BID_010.code,
    enumProject.Features.BID_011.code,
    enumProject.Features.BID_012.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('get_list_bid_result_auction')
  public async getListBidResultAuction(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.getListBidResultAuction(user, data)
  }

  @ApiOperation({ summary: 'Lấy chi tiết đấu giá' })
  @Roles(
    enumProject.Features.BID_001.code,
    enumProject.Features.BID_002.code,
    enumProject.Features.BID_005.code,
    enumProject.Features.BID_010.code,
    enumProject.Features.BID_011.code,
    enumProject.Features.BID_012.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_bid_result_auction/:bidauctionid')
  public async getBidResultAuction(@CurrentUser() user: UserDto, @Param('bidauctionid') bidAuctionId: string) {
    return await this.service.getBidResultAuction(user, bidAuctionId)
  }

  @ApiOperation({ summary: 'Lấy chi tiết đấu giá theo Doanh nghiệp' })
  @Roles(
    enumProject.Features.BID_001.code,
    enumProject.Features.BID_002.code,
    enumProject.Features.BID_005.code,
    enumProject.Features.BID_010.code,
    enumProject.Features.BID_011.code,
    enumProject.Features.BID_012.code,
  )
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_bid_result_auction_supplier_detail/:bidauctionsupplierid')
  public async getBidResultAuctionSupplierDetail(@CurrentUser() user: UserDto, @Param('bidauctionsupplierid') bidAuctionSupplierId: string) {
    return await this.service.getBidResultAuctionSupplierDetail(user, bidAuctionSupplierId)
  }
}
