import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'
import { PrEntity } from './pr.entity'

/** ds cấp duyệt service */
@Entity('pr_approver')
export class PrApproverEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  prId: string
  @ManyToOne(() => PrEntity, (p) => p.prApprovers)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>

  /** cấp duyệt */
  @Column({
    nullable: false,
    default: 1,
  })
  level: number

  /** nv duyệt */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.prApprovers)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>
}
