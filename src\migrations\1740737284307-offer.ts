import { MigrationInterface, QueryRunner } from "typeorm";

export class offer1740737284307 implements MigrationInterface {
    name = 'offer1740737284307'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`offer_price_col\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(50) NOT NULL, \`fomular\` text NULL, \`isRequired\` tinyint NOT NULL DEFAULT 0, \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NOT NULL, \`description\` text NULL, \`type\` varchar(50) NOT NULL, \`colType\` varchar(50) NOT NULL, \`offerId\` varchar(255) NULL, \`offerServiceId\` varchar(255) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`offer_supplier_price_col_value\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`value\` varchar(250) NULL, \`offerSupplierId\` varchar(255) NULL, \`offerPriceId\` varchar(255) NULL, \`offerPriceColId\` varchar(255) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`offer_supplier_custom_price_value\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`unit\` varchar(255) NULL, \`currency\` varchar(255) NULL, \`number\` int NULL DEFAULT '0', \`sort\` int NULL DEFAULT '0', \`name\` varchar(250) NULL, \`value\` varchar(250) NULL, \`offerSupplierId\` varchar(255) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`offer_supplier_shipment_value\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`score\` float NULL, \`value\` varchar(250) NULL, \`offerSupplierId\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`offer_price_list_detail\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`name\` varchar(250) NOT NULL, \`description\` text NULL, \`type\` varchar(50) NOT NULL, \`value\` varchar(250) NULL, \`offerPriceId\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`offer_deal_supplier_price_value\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`score\` float NULL, \`value\` varchar(250) NULL, \`offerDealSupplierId\` varchar(255) NOT NULL, \`offerPriceId\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`offer_deal_supplier\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`offerDealId\` varchar(255) NOT NULL, \`score\` float NOT NULL DEFAULT '0', \`supplierId\` varchar(255) NOT NULL, \`status\` varchar(50) NOT NULL, \`filePriceDetail\` varchar(500) NULL, \`fileTechDetail\` varchar(500) NULL, \`linkDriver\` varchar(500) NULL, \`submitDate\` datetime NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`offer_deal\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`status\` varchar(50) NOT NULL, \`endDate\` datetime NOT NULL, \`isSendDealPrice\` tinyint NOT NULL DEFAULT 0, \`offerId\` varchar(255) NOT NULL, \`parentId\` varchar(255) NULL, \`isRequireFilePriceDetail\` tinyint NULL DEFAULT 0, \`isRequireFileTechDetail\` tinyint NULL DEFAULT 0, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`offer_deal_price\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`sort\` int NOT NULL DEFAULT '0', \`number\` int NOT NULL DEFAULT '0', \`bestPrice\` float NULL, \`bestPriceHistory\` float NULL, \`bestPriceCurrent\` float NULL, \`suggestPrice\` float NULL, \`maxPrice\` float NULL, \`offerDealId\` varchar(255) NOT NULL, \`offerPriceId\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`offer_custom_price\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`number\` int NOT NULL DEFAULT '0', \`sort\` int NOT NULL DEFAULT '0', \`name\` varchar(250) NOT NULL, \`isRequired\` tinyint NOT NULL DEFAULT 0, \`type\` varchar(255) NOT NULL DEFAULT 'Number', \`unit\` varchar(255) NULL, \`currency\` varchar(255) NULL, \`offerId\` varchar(255) NULL, \`offerServiceId\` varchar(255) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`offer_shipment_price\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`offerId\` varchar(255) NULL, \`value\` int NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`nameCol\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`nameCol\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`price\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`isSuccessBid\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`scoreTech\` float NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`scorePrice\` float NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`scoreTrade\` float NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`scoreManualTech\` float NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`scoreManualPrice\` float NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`scoreManualTrade\` float NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`parentId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`statusFile\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`statusTech\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`statusTrade\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`isPriceValid\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`isJoin\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`statusResetPrice\` varchar(50) NOT NULL DEFAULT 'KhongYeuCau'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`noteMPOLeader\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`noteTrade\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`isTradeValid\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`status\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`notePrice\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`statusPrice\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`fileAttach\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`fileTech\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`filePrice\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`linkDrive\` varchar(400) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`note\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`offerPriceName\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`offerPriceLevel\` int NULL DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`offerId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`serviceId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`supplierId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`submitDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`submitType\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`number\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`unitPrice\` bigint NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`code\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`fomular\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`isRequired\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`sort\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`name\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`description\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`unit\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`currency\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`percent\` float NULL DEFAULT '100'`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`level\` int NULL DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`parentId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`type\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`colType\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`offerItemId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`scoreDLC\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`requiredMin\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`number\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`isSetup\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`isTemplate\` tinyint NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`title\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`effectiveDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`endDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`currency\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`timePeriod\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`condition\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`scoreDLC\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`address\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`isHaveVat\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`isCompleteAll\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`isNotConfigTrade\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`isGetFromPr\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`description\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`refType\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`isLoadFromItem\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`publicDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`statusRateTech\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`statusRateTrade\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`statusRatePrice\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`statusResetPrice\` varchar(50) NULL DEFAULT 'ChuaTao'`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`isRequestDelete\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`noteRequestDelete\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`fileScan\` varchar(500) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`noteFinishBidMPO\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`statusTrade\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`resetPriceEndDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`statusPrice\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`isRequireFileTechDetail\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`isRequireFilePriceDetail\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`wayCalScorePrice\` varchar(50) NULL DEFAULT 'SumScore'`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`fomular\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`statusChooseSupplier\` varchar(50) NULL DEFAULT 'ChuaChon'`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`noteTrade\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`fileAttach\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`notePrice\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`approveChooseSupplierWinDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`bidCloseDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`noteTechLeader\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`noteMPOLeader\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`shipmentId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`offerTypeCode\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`itemNo\` bigint NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`materialGroupName\` varchar(100) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`categoryName\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`category\` varchar(1) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`shortText\` varchar(1000) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`quantity\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`status\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`unitCode\` varchar(10) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`prItemId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`percentTech\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`percentTrade\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`percentPrice\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`scoreDLC\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`fomular\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`isExGr\` tinyint NOT NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`shipmentPriceId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`conditionType\` varchar(150) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`description\` varchar(4000) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`amount\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`crcy\` varchar(150) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`per\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`conditionValue\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`curr\` varchar(150) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`cConDe\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`numCCo\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`deliveryDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` CHANGE \`dateStart\` \`dateStart\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` CHANGE \`dateEnd\` \`dateEnd\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` CHANGE \`serviceName\` \`serviceName\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_service\` ADD CONSTRAINT \`FK_0f870976dd62fbe5ff8084fc567\` FOREIGN KEY (\`offerSupplierId\`) REFERENCES \`offer_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_service\` ADD CONSTRAINT \`FK_a08481d93d35c70c12603e60014\` FOREIGN KEY (\`offerServiceId\`) REFERENCES \`offer_service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_tech_list_detail\` ADD CONSTRAINT \`FK_f70094a3975f2e3552b884d83d1\` FOREIGN KEY (\`bidTechId\`) REFERENCES \`offer_tech\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_tech\` ADD CONSTRAINT \`FK_3c593faf2ddfd1a0c7161c83161\` FOREIGN KEY (\`parentId\`) REFERENCES \`offer_tech\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_tech\` ADD CONSTRAINT \`FK_9182f39b9c62fd4bc4e6e013a31\` FOREIGN KEY (\`offerId\`) REFERENCES \`offer\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_tech\` ADD CONSTRAINT \`FK_da26ea1a2620a955851f69210ac\` FOREIGN KEY (\`offerServiceId\`) REFERENCES \`offer_service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_tech_value\` ADD CONSTRAINT \`FK_81bd2a5c71e28586e2ba4e65176\` FOREIGN KEY (\`offerSupplierId\`) REFERENCES \`offer_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_tech_value\` ADD CONSTRAINT \`FK_c67bce06945fe1793a9e89e4aab\` FOREIGN KEY (\`offerTechId\`) REFERENCES \`offer_tech\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_trade_list_detail\` ADD CONSTRAINT \`FK_70d1ba4d578cf661165e1412d5b\` FOREIGN KEY (\`offerTradeId\`) REFERENCES \`offer_trade\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_trade\` ADD CONSTRAINT \`FK_81aa09f465c800e6f82a739aaf6\` FOREIGN KEY (\`parentId\`) REFERENCES \`offer_trade\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_trade\` ADD CONSTRAINT \`FK_7f6124a13c3f3d94b43ddf85026\` FOREIGN KEY (\`offerId\`) REFERENCES \`offer\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_trade\` ADD CONSTRAINT \`FK_769211822e9a9130696244fd060\` FOREIGN KEY (\`offerServiceId\`) REFERENCES \`offer_service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_trade_value\` ADD CONSTRAINT \`FK_0c88283a1c1d6b97ea830c17b90\` FOREIGN KEY (\`offerSupplierId\`) REFERENCES \`offer_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_trade_value\` ADD CONSTRAINT \`FK_d84e9c117fe73bf398cc38b8616\` FOREIGN KEY (\`offerTradeId\`) REFERENCES \`offer_trade\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price_value\` ADD CONSTRAINT \`FK_6ddc272faa5c3750ae754e264d7\` FOREIGN KEY (\`offerSupplierId\`) REFERENCES \`offer_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price_value\` ADD CONSTRAINT \`FK_9c1553b44e928ae378ed2c107bc\` FOREIGN KEY (\`offerPriceId\`) REFERENCES \`offer_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_price_col_value\` ADD CONSTRAINT \`FK_a6fd9457077b6e9f2a35cbb25ba\` FOREIGN KEY (\`offerPriceId\`) REFERENCES \`offer_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_price_col_value\` ADD CONSTRAINT \`FK_1b467c5d4dc200dd94cfb9a7318\` FOREIGN KEY (\`offerPriceColId\`) REFERENCES \`offer_price_col\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_price_col\` ADD CONSTRAINT \`FK_a4b6bcc6142788605753f739bb0\` FOREIGN KEY (\`offerId\`) REFERENCES \`offer\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_price_col\` ADD CONSTRAINT \`FK_930bd8534a1d0efdacefc41505a\` FOREIGN KEY (\`offerServiceId\`) REFERENCES \`offer_service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price_col_value\` ADD CONSTRAINT \`FK_17d00a51f00b2c3f48fd25e0830\` FOREIGN KEY (\`offerSupplierId\`) REFERENCES \`offer_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price_col_value\` ADD CONSTRAINT \`FK_b3fa0454d450bd3566b67b11b4d\` FOREIGN KEY (\`offerPriceId\`) REFERENCES \`offer_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price_col_value\` ADD CONSTRAINT \`FK_c41fdd84d9524aeefa881e51fdd\` FOREIGN KEY (\`offerPriceColId\`) REFERENCES \`offer_price_col\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_custom_price_value\` ADD CONSTRAINT \`FK_161b879aab20df18ceb15b86f93\` FOREIGN KEY (\`offerSupplierId\`) REFERENCES \`offer_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_shipment_value\` ADD CONSTRAINT \`FK_2126f0cb48c3e70f5cc8ece47ce\` FOREIGN KEY (\`offerSupplierId\`) REFERENCES \`offer_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD CONSTRAINT \`FK_d19b3ad6ea4d242e224e79d1a81\` FOREIGN KEY (\`parentId\`) REFERENCES \`offer_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_price_list_detail\` ADD CONSTRAINT \`FK_ecd5c780d592dc89d69a4fa460c\` FOREIGN KEY (\`offerPriceId\`) REFERENCES \`offer_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_deal_supplier_price_value\` ADD CONSTRAINT \`FK_559e2900d2c0772ce9c75d6f496\` FOREIGN KEY (\`offerDealSupplierId\`) REFERENCES \`offer_deal_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_deal_supplier_price_value\` ADD CONSTRAINT \`FK_c1fbf776df2ef4524df71effd59\` FOREIGN KEY (\`offerPriceId\`) REFERENCES \`offer_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_deal_supplier\` ADD CONSTRAINT \`FK_a77f267009cea06dfcef3d048e2\` FOREIGN KEY (\`offerDealId\`) REFERENCES \`offer_deal\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_deal_supplier\` ADD CONSTRAINT \`FK_9cd5118d0138adb52a93fb0c465\` FOREIGN KEY (\`supplierId\`) REFERENCES \`supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_deal\` ADD CONSTRAINT \`FK_e6626d6cb9ceef159383789e712\` FOREIGN KEY (\`offerId\`) REFERENCES \`offer\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_deal\` ADD CONSTRAINT \`FK_920b0e8da9bac1e3954ad903486\` FOREIGN KEY (\`parentId\`) REFERENCES \`offer_deal\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_deal_price\` ADD CONSTRAINT \`FK_3d4dc213d47aae6bd81b371d794\` FOREIGN KEY (\`offerDealId\`) REFERENCES \`offer_deal\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_deal_price\` ADD CONSTRAINT \`FK_555f706d11d79d5aaefb1566602\` FOREIGN KEY (\`offerPriceId\`) REFERENCES \`offer_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD CONSTRAINT \`FK_bb4bacf4af6f704d19e6f4c4edb\` FOREIGN KEY (\`parentId\`) REFERENCES \`offer_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD CONSTRAINT \`FK_f9fe2bd7a8fb496d41d4bb20282\` FOREIGN KEY (\`offerItemId\`) REFERENCES \`offer_service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_custom_price\` ADD CONSTRAINT \`FK_fe1611c35570631d3312c462ade\` FOREIGN KEY (\`offerId\`) REFERENCES \`offer\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_custom_price\` ADD CONSTRAINT \`FK_2e325531d7c12fd685a133439bc\` FOREIGN KEY (\`offerServiceId\`) REFERENCES \`offer_service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_shipment_price\` ADD CONSTRAINT \`FK_098c4adda530658b3e8615e80d5\` FOREIGN KEY (\`offerId\`) REFERENCES \`offer\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD CONSTRAINT \`FK_929cb4b9994dfaba179b6f8ae88\` FOREIGN KEY (\`prItemId\`) REFERENCES \`pr_item\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP FOREIGN KEY \`FK_929cb4b9994dfaba179b6f8ae88\``);
        await queryRunner.query(`ALTER TABLE \`offer_shipment_price\` DROP FOREIGN KEY \`FK_098c4adda530658b3e8615e80d5\``);
        await queryRunner.query(`ALTER TABLE \`offer_custom_price\` DROP FOREIGN KEY \`FK_2e325531d7c12fd685a133439bc\``);
        await queryRunner.query(`ALTER TABLE \`offer_custom_price\` DROP FOREIGN KEY \`FK_fe1611c35570631d3312c462ade\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP FOREIGN KEY \`FK_f9fe2bd7a8fb496d41d4bb20282\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP FOREIGN KEY \`FK_bb4bacf4af6f704d19e6f4c4edb\``);
        await queryRunner.query(`ALTER TABLE \`offer_deal_price\` DROP FOREIGN KEY \`FK_555f706d11d79d5aaefb1566602\``);
        await queryRunner.query(`ALTER TABLE \`offer_deal_price\` DROP FOREIGN KEY \`FK_3d4dc213d47aae6bd81b371d794\``);
        await queryRunner.query(`ALTER TABLE \`offer_deal\` DROP FOREIGN KEY \`FK_920b0e8da9bac1e3954ad903486\``);
        await queryRunner.query(`ALTER TABLE \`offer_deal\` DROP FOREIGN KEY \`FK_e6626d6cb9ceef159383789e712\``);
        await queryRunner.query(`ALTER TABLE \`offer_deal_supplier\` DROP FOREIGN KEY \`FK_9cd5118d0138adb52a93fb0c465\``);
        await queryRunner.query(`ALTER TABLE \`offer_deal_supplier\` DROP FOREIGN KEY \`FK_a77f267009cea06dfcef3d048e2\``);
        await queryRunner.query(`ALTER TABLE \`offer_deal_supplier_price_value\` DROP FOREIGN KEY \`FK_c1fbf776df2ef4524df71effd59\``);
        await queryRunner.query(`ALTER TABLE \`offer_deal_supplier_price_value\` DROP FOREIGN KEY \`FK_559e2900d2c0772ce9c75d6f496\``);
        await queryRunner.query(`ALTER TABLE \`offer_price_list_detail\` DROP FOREIGN KEY \`FK_ecd5c780d592dc89d69a4fa460c\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP FOREIGN KEY \`FK_d19b3ad6ea4d242e224e79d1a81\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_shipment_value\` DROP FOREIGN KEY \`FK_2126f0cb48c3e70f5cc8ece47ce\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_custom_price_value\` DROP FOREIGN KEY \`FK_161b879aab20df18ceb15b86f93\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price_col_value\` DROP FOREIGN KEY \`FK_c41fdd84d9524aeefa881e51fdd\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price_col_value\` DROP FOREIGN KEY \`FK_b3fa0454d450bd3566b67b11b4d\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price_col_value\` DROP FOREIGN KEY \`FK_17d00a51f00b2c3f48fd25e0830\``);
        await queryRunner.query(`ALTER TABLE \`offer_price_col\` DROP FOREIGN KEY \`FK_930bd8534a1d0efdacefc41505a\``);
        await queryRunner.query(`ALTER TABLE \`offer_price_col\` DROP FOREIGN KEY \`FK_a4b6bcc6142788605753f739bb0\``);
        await queryRunner.query(`ALTER TABLE \`offer_price_col_value\` DROP FOREIGN KEY \`FK_1b467c5d4dc200dd94cfb9a7318\``);
        await queryRunner.query(`ALTER TABLE \`offer_price_col_value\` DROP FOREIGN KEY \`FK_a6fd9457077b6e9f2a35cbb25ba\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price_value\` DROP FOREIGN KEY \`FK_9c1553b44e928ae378ed2c107bc\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price_value\` DROP FOREIGN KEY \`FK_6ddc272faa5c3750ae754e264d7\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_trade_value\` DROP FOREIGN KEY \`FK_d84e9c117fe73bf398cc38b8616\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_trade_value\` DROP FOREIGN KEY \`FK_0c88283a1c1d6b97ea830c17b90\``);
        await queryRunner.query(`ALTER TABLE \`offer_trade\` DROP FOREIGN KEY \`FK_769211822e9a9130696244fd060\``);
        await queryRunner.query(`ALTER TABLE \`offer_trade\` DROP FOREIGN KEY \`FK_7f6124a13c3f3d94b43ddf85026\``);
        await queryRunner.query(`ALTER TABLE \`offer_trade\` DROP FOREIGN KEY \`FK_81aa09f465c800e6f82a739aaf6\``);
        await queryRunner.query(`ALTER TABLE \`offer_trade_list_detail\` DROP FOREIGN KEY \`FK_70d1ba4d578cf661165e1412d5b\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_tech_value\` DROP FOREIGN KEY \`FK_c67bce06945fe1793a9e89e4aab\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_tech_value\` DROP FOREIGN KEY \`FK_81bd2a5c71e28586e2ba4e65176\``);
        await queryRunner.query(`ALTER TABLE \`offer_tech\` DROP FOREIGN KEY \`FK_da26ea1a2620a955851f69210ac\``);
        await queryRunner.query(`ALTER TABLE \`offer_tech\` DROP FOREIGN KEY \`FK_9182f39b9c62fd4bc4e6e013a31\``);
        await queryRunner.query(`ALTER TABLE \`offer_tech\` DROP FOREIGN KEY \`FK_3c593faf2ddfd1a0c7161c83161\``);
        await queryRunner.query(`ALTER TABLE \`offer_tech_list_detail\` DROP FOREIGN KEY \`FK_f70094a3975f2e3552b884d83d1\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_service\` DROP FOREIGN KEY \`FK_a08481d93d35c70c12603e60014\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_service\` DROP FOREIGN KEY \`FK_0f870976dd62fbe5ff8084fc567\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` CHANGE \`serviceName\` \`serviceName\` varchar(250) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` CHANGE \`dateEnd\` \`dateEnd\` datetime NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` CHANGE \`dateStart\` \`dateStart\` datetime NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`deliveryDate\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`numCCo\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`cConDe\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`curr\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`conditionValue\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`per\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`crcy\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`amount\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`description\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`conditionType\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`shipmentPriceId\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`isExGr\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`fomular\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`scoreDLC\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`percentPrice\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`percentTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`percentTech\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`prItemId\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`unitCode\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`status\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`quantity\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`shortText\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`category\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`categoryName\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`materialGroupName\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`itemNo\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`offerTypeCode\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`shipmentId\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`noteMPOLeader\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`noteTechLeader\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`bidCloseDate\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`approveChooseSupplierWinDate\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`notePrice\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`fileAttach\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`noteTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`statusChooseSupplier\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`fomular\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`wayCalScorePrice\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`isRequireFilePriceDetail\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`isRequireFileTechDetail\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`statusPrice\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`resetPriceEndDate\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`statusTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`noteFinishBidMPO\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`fileScan\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`noteRequestDelete\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`isRequestDelete\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`statusResetPrice\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`statusRatePrice\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`statusRateTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`statusRateTech\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`publicDate\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`isLoadFromItem\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`refType\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`description\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`isGetFromPr\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`isNotConfigTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`isCompleteAll\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`isHaveVat\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`address\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`scoreDLC\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`condition\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`timePeriod\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`currency\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`endDate\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`effectiveDate\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`title\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`isTemplate\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`isSetup\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`number\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`requiredMin\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`scoreDLC\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`offerItemId\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`colType\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`type\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`parentId\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`level\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`percent\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`currency\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`unit\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`description\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`name\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`sort\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`isRequired\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`fomular\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`code\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`unitPrice\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`number\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`submitType\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`submitDate\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`supplierId\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`serviceId\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`offerId\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`offerPriceLevel\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`offerPriceName\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`note\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`linkDrive\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`filePrice\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`fileTech\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`fileAttach\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`statusPrice\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`notePrice\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`status\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`isTradeValid\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`noteTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`noteMPOLeader\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`statusResetPrice\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`isJoin\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`isPriceValid\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`statusTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`statusTech\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`statusFile\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`parentId\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`scoreManualTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`scoreManualPrice\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`scoreManualTech\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`scoreTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`scorePrice\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`scoreTech\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`isSuccessBid\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`price\` bigint NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`nameCol\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`nameCol\` varchar(250) NULL`);
        await queryRunner.query(`DROP TABLE \`offer_shipment_price\``);
        await queryRunner.query(`DROP TABLE \`offer_custom_price\``);
        await queryRunner.query(`DROP TABLE \`offer_deal_price\``);
        await queryRunner.query(`DROP TABLE \`offer_deal\``);
        await queryRunner.query(`DROP TABLE \`offer_deal_supplier\``);
        await queryRunner.query(`DROP TABLE \`offer_deal_supplier_price_value\``);
        await queryRunner.query(`DROP TABLE \`offer_price_list_detail\``);
        await queryRunner.query(`DROP TABLE \`offer_supplier_shipment_value\``);
        await queryRunner.query(`DROP TABLE \`offer_supplier_custom_price_value\``);
        await queryRunner.query(`DROP TABLE \`offer_supplier_price_col_value\``);
        await queryRunner.query(`DROP TABLE \`offer_price_col\``);
    }

}
