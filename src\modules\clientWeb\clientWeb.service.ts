import { Injectable, Req } from '@nestjs/common'
import { PaginationDto, UserDto } from '../../dto'
import { SupplierCreateCustomPriceItemDto, SupplierCreatePriceItemDto, SupplierCreateTechItemDto, SupplierCreateTradeItemDto } from '../bid/dto'
import { BidService } from '../bid/bid.service'
import { BidDetailService } from '../bidDetail/bidDetail.service'
import { SupplierService } from '../supplier/supplier.service'
import { BidDealService } from '../bidDeal/bidDeal.service'
import { BidAuctionService } from '../bidAuction/bidAuction.service'
import { BidAuctionSupplierSaveDto } from '../bidAuction/dto'
import { FaqService } from '../faq/faq.service'
import { FaqCategoryService } from '../faqCategory/faqCategory.service'
import { BidDealSupplierSaveDto } from '../bidDeal/dto'
import { POService } from '../po/po.service'
import { POUpdateDeliveryDateDto, POUpdateStatusDto } from '../po/dto'
import { InvoiceSuggestCreate } from '../invoiceSuggest/dto'
import { InvoiceSuggestService } from '../invoiceSuggest/invoiceSuggest.service'
import { ContractService } from '../contract/contract.service'

@Injectable()
export class ClientWebService {
  constructor(
    private supplierService: SupplierService,

    private invoiceSuggestService: InvoiceSuggestService,

    private bidService: BidService,
    private bidDetailService: BidDetailService,
    private bidDealService: BidDealService,
    private bidAuctionService: BidAuctionService,
    private faqService: FaqService,
    private faqCategoryService: FaqCategoryService,
    private pOService: POService,
    private contractService: ContractService,
  ) {}

  //#region Supplier

  /** Kiểm tra quyền xem kết quả thẩm định */
  async checkIsYourExpertise(user: UserDto, data: { supplierExpertiseId: string }) {
    return await this.supplierService.checkIsYourExpertise(user, data)
  }

  /** Lấy dữ liệu mà MPO đề xuất cho nhà cung cấp */
  async getDataSuggest(user: UserDto, data: { supplierExpertiseId: string }) {
    return await this.supplierService.getDataSuggest(user, data)
  }

  async supplierAcceptChangeData(user: UserDto, data: { supplierExpertiseId: string }) {
    return await this.supplierService.supplierAcceptChangeData(user, data)
  }
  //#endregion

  //#region Bid
  async paginationHomePage(@Req() req: Request, data: PaginationDto) {
    return await this.bidService.paginationHomePage(req, data)
  }

  async paginationHomePageHadToken(user: UserDto, data: PaginationDto) {
    return await this.bidService.paginationHomePageHadToken(user, data)
  }

  async loadDataBidding(data: { bidId: string }, user: UserDto) {
    return await this.bidService.loadDataBidding(data, user)
  }

  async bidDetailHadToken(data: { bidId: string }, user: UserDto) {
    return await this.bidService.bidDetailHadToken(data, user)
  }

  async isDisplayBtnAcceptBid(user: UserDto, data: { bidId: string }) {
    return await this.bidService.isDisplayBtnAcceptBid(data.bidId, user)
  }

  async isDisplayBtnBid(user: UserDto, data: { bidId: string }) {
    return await this.bidService.isDisplayBtnBid(data.bidId, user)
  }

  async acceptBid(user: UserDto, data: { bidId: string }) {
    return await this.bidService.acceptBid(data.bidId, user)
  }

  async rejectBid(user: UserDto, data: { bidId: string }) {
    return await this.bidService.rejectBid(data.bidId, user)
  }

  async createBidSupplier(
    user: UserDto,
    data: {
      bidId: string
      techInfo: SupplierCreateTechItemDto[]
      tradeInfo: SupplierCreateTradeItemDto[]
      priceInfo: SupplierCreatePriceItemDto[]
      customPriceInfo: SupplierCreateCustomPriceItemDto[]
    },
  ) {
    return await this.bidService.createBidSupplier(user, data)
  }

  /** Check quyền truy cập gói thầu của Doanh nghiệp */
  async checkPermissionLoadDataBid(user: UserDto, data: { bidId: string }) {
    return await this.bidService.checkPermissionLoadDataBid(data?.bidId, user)
  }

  async loadDataBidTech(user: UserDto, data: { bidId: string }) {
    return await this.bidService.loadDataBidTech(user, data)
  }

  async loadDataBidTrade(user: UserDto, data: { bidId: string }) {
    return await this.bidService.loadDataBidTrade(user, data)
  }

  async loadDataBidPrice(user: UserDto, data: { bidId: string }) {
    return await this.bidService.loadDataBidPrice(user, data)
  }

  async loadDataBidCustomPrice(user: UserDto, data: { bidId: string }) {
    return await this.bidService.loadDataBidCustomPrice(user, data)
  }

  /** Lịch sử đấu thầu Doanh nghiệp */
  async paginationBidHistory(user: UserDto, data: PaginationDto) {
    return await this.bidService.paginationBidHistory(user, data)
  }

  /** Check quyền nộp giá bổ sung cho gói thầu của Doanh nghiệp */
  async checkPermissionJoinResetPrice(user: UserDto, data: { bidId: string }) {
    return await this.bidService.checkPermissionJoinResetPrice(data?.bidId, user)
  }

  async supplierSaveResetPrice(
    user: UserDto,
    data: {
      bidId: string
      dataInfo: { filePriceDetail?: string; fileTechDetail?: string }
      priceInfo: SupplierCreatePriceItemDto[]
    },
  ) {
    return await this.bidService.supplierSaveResetPrice(user, data)
  }
  //#endregion

  //#region BidDetail
  /** Danh sách kế hoạch mua hàng có phân trang */
  async getBidHistoryPrice(user: UserDto, bidId: string) {
    return await this.bidDetailService.getBidHistoryPrice(user, bidId)
  }
  //#endregion

  //#region BidDeal

  /** Lấy thông tin đàm phán giá NCC */
  async getBidDealSupplier(user: UserDto, bidDealId: string) {
    return await this.bidDealService.getBidDealSupplier(user, bidDealId)
  }

  /** Đề nghị đàm phán giá/ Chấp nhận giá đề nghị/ Lưu */
  async acceptBidDealSupplier(user: UserDto, data: BidDealSupplierSaveDto) {
    return await this.bidDealService.acceptBidDealSupplier(user, data)
  }

  /** Từ chối giá đề nghị */
  async rejectBidDealSupplier(user: UserDto, bidDealId: string) {
    return await this.bidDealService.rejectBidDealSupplier(user, bidDealId)
  }

  /** Lấy kết quả đàm phán */
  async checkResultMessage(user: UserDto, bidDealId: string) {
    return await this.bidDealService.checkResultMessage(user, bidDealId)
  }
  //#endregion

  //#region BidAuction

  /** Lấy thông tin đấu giá NCC */
  async getBidAuctionSupplier(user: UserDto, bidAuctionId: string) {
    return await this.bidAuctionService.getBidAuctionSupplier(user, bidAuctionId)
  }

  /** NCC nộp đấu giá của mình */
  async supplierSaveAuction(user: UserDto, data: BidAuctionSupplierSaveDto) {
    await this.bidAuctionService.supplierSaveAuction(user, data)
  }

  // async getCurrentRank(user: UserDto, bidId: string) {
  //   return await this.bidAuctionService.getCurrentRank(user, bidId)
  // }

  /** Danh sách PO */
  async paginationSupplier(user: UserDto, data: PaginationDto) {
    return await this.pOService.paginationSupplier(user, data)
  }

  /** Nhà cung cấp cập nhật xác nhận giao hàng */
  async updateStatusDelivery(user: UserDto, data: { id: string }) {
    return await this.pOService.updateStatusDelivery(user, data)
  }

  /** Nhà cung cấp cập nhật ngày giao hàng */
  async updateDeliveryDate(user: UserDto, data: POUpdateDeliveryDateDto) {
    return await this.pOService.updateDeliveryDate(user, data)
  }
  /** Nhà cung cấp cập nhật ngày giao hàng */
  async updateStatus(user: UserDto, data: POUpdateStatusDto) {
    return await this.pOService.updateStatus(user, data)
  }

  /** Tạo ĐNTT */
  public async createData(data: InvoiceSuggestCreate, user: UserDto) {
    return await this.invoiceSuggestService.createData(data, user)
  }

  public async paginationInivoce(user: UserDto, data: { id: string }) {
    return await this.invoiceSuggestService.findClient(data, user)
  }

  /** Nhà cung cấp cập nhật xác nhận PO */
  async updateStatusConfirm(user: UserDto, data: { id: string }) {
    return await this.pOService.updateStatusConfirm(user, data)
  }

  /** Nhà cung cấp cập nhật từ chối giao hàng xác nhận PO */
  async updateStatusSupplierRefuse(user: UserDto, data: { id: string; reason: string }) {
    return await this.pOService.updateStatusSupplierRefuse(user, data)
  }

  /** Chi tiết của PO theo nhà cung cấp */
  async findDetailPO(user: UserDto, data: { id: string }) {
    return await this.pOService.findDetailPo(user, data)
  }

  async findPO(user: UserDto, data: {}) {
    return await this.pOService.find(user, data)
  }
  //#endregion

  //#region FAQ
  public async getFAQHomePage(req: Request, id: string) {
    return await this.faqService.getFAQHomePage(req, id)
  }

  public async getFAQCategoryHomePage(@Req() req: Request) {
    return await this.faqCategoryService.getFAQCategoryHomePage(req)
  }
  //#endregion

  //#region Contract
  async findContract(user: UserDto, data: {}) {
    return await this.contractService.findContractClient(user, data)
  }

  async findContractPO(user: UserDto, data: { poId: string }) {
    return await this.pOService.findContractPO(user, data)
  }

  async loadPoProduct(user: UserDto, data: { poId: string }) {
    return await this.pOService.loadPoProduct(user, data)
  }

  async loadListPoContract(user: UserDto, data: { contractId: string }) {
    return await this.pOService.loadListPoContract(user, data)
  }

  //#endregion
}
