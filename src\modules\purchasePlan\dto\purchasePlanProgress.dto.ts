import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional } from 'class-validator'

export class PurchasePlanProgressDto {
  @ApiPropertyOptional()
  @IsOptional()
  id: string

  @ApiPropertyOptional()
  @IsOptional()
  purchasePlanId: string

  @ApiProperty()
  @IsNotEmpty()
  sort: number

  @ApiPropertyOptional()
  @IsOptional()
  progressDate: Date

  @ApiProperty()
  @IsNotEmpty()
  quantity: number

  @ApiPropertyOptional()
  @IsOptional()
  description: string
}
