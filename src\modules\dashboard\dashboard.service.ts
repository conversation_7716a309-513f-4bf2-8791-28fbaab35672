import { Injectable } from '@nestjs/common'
import * as moment from 'moment'
import { In, Raw } from 'typeorm'
import { enumData } from '../../constants'
import { UserDto } from '../../dto'
import { BidEntity, BidSupplierPriceEntity, PrEntity, SupplierEntity } from '../../entities'
import { SupplierServiceRepository } from '../../repositories'

@Injectable()
export class DashboardService {
  constructor(private readonly repo: SupplierServiceRepository) {}

  public async loadDashBoard(user: UserDto) {
    let whereCon: any = { companyId: user.companyId, isDeleted: false }
    var date = new Date()
    var firstMonth = new Date(date.getFullYear(), date.getMonth(), 1)
    var lastMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0)
    whereCon.createdAt = Raw(
      (alias) => `DATE(${alias}) BETWEEN DATE("${moment(firstMonth).format('YYYY-MM-DD')}") AND DATE("${moment(lastMonth).format('YYYY-MM-DD')}")`,
    )
    const listPr = await this.repo.manager.getRepository(PrEntity).find({
      where: whereCon,
      order: { createdAt: 'DESC' },
    })
    let PrAriseMoiTao = []
    let PrPlantMoiTao = []
    let PrAriseApproved = []
    let PrPlantApproved = []
    let PrAriseCancel = []
    let PrPlantCancel = []
    let PrAriseProcess = []
    let PrPlantProcess = []
    let PrAriseClose = []
    let PrPlantClose = []
    for (let item of listPr) {
      if (item.prType === enumData.PRType.Arise.code) {
        if (item.status === enumData.PRStatus.New.code) {
          PrAriseMoiTao.push(item)
        } else if (item.status === enumData.PRStatus.Approved.code) {
          PrAriseApproved.push(item)
        } else if (item.status === enumData.PRStatus.Cancel.code) {
          PrAriseCancel.push(item)
        } else if (item.status === enumData.PRStatus.Processing.code) {
          PrAriseProcess.push(item)
        } else if (item.status === enumData.PRStatus.Close.code) {
          PrAriseClose.push(item)
        }
      }
      if (item.prType === enumData.PRType.Plan.code) {
        if (item.status === enumData.PRStatus.New.code) {
          PrPlantMoiTao.push(item)
        } else if (item.status === enumData.PRStatus.Approved.code) {
          PrPlantApproved.push(item)
        } else if (item.status === enumData.PRStatus.Cancel.code) {
          PrPlantCancel.push(item)
        } else if (item.status === enumData.PRStatus.Processing.code) {
          PrPlantProcess.push(item)
        } else if (item.status === enumData.PRStatus.Close.code) {
          PrPlantClose.push(item)
        }
      }
    }
    let lstStatusPrArise = []
    lstStatusPrArise.push({
      code: enumData.PRStatus.New.code,
      name: enumData.PRStatus.New.name,
      data: PrAriseMoiTao,
      count: PrAriseMoiTao.length,
    })
    lstStatusPrArise.push({
      code: enumData.PRStatus.Approved.code,
      name: enumData.PRStatus.Approved.name,
      data: PrAriseApproved,
      count: PrAriseApproved.length,
    })
    lstStatusPrArise.push({
      code: enumData.PRStatus.Cancel.code,
      name: enumData.PRStatus.Cancel.name,
      data: PrAriseCancel,
      count: PrAriseCancel.length,
    })

    lstStatusPrArise.push({
      code: enumData.PRStatus.Processing.code,
      name: enumData.PRStatus.Processing.name,
      data: PrAriseProcess,
      count: PrAriseProcess.length,
    })

    lstStatusPrArise.push({
      code: enumData.PRStatus.Close.code,
      name: enumData.PRStatus.Close.name,
      data: PrAriseClose,
      count: PrAriseClose.length,
    })

    let lstStatusPrPlant = []
    lstStatusPrPlant.push({
      code: enumData.PRStatus.New.code,
      name: enumData.PRStatus.New.name,
      data: PrPlantMoiTao,
      count: PrPlantMoiTao.length,
    })
    lstStatusPrPlant.push({
      code: enumData.PRStatus.Approved.code,
      name: enumData.PRStatus.Approved.name,
      data: PrPlantApproved,
      count: PrPlantApproved.length,
    })
    lstStatusPrPlant.push({
      code: enumData.PRStatus.Cancel.code,
      name: enumData.PRStatus.Cancel.name,
      data: PrPlantCancel,
      count: PrPlantCancel.length,
    })

    lstStatusPrPlant.push({
      code: enumData.PRStatus.Processing.code,
      name: enumData.PRStatus.Processing.name,
      data: PrPlantProcess,
      count: PrPlantProcess.length,
    })

    lstStatusPrPlant.push({
      code: enumData.PRStatus.Close.code,
      name: enumData.PRStatus.Close.name,
      data: PrPlantClose,
      count: PrPlantClose.length,
    })

    let bidGoiThauTam = []
    let bidChoDuyetGoiThauTam = []
    let bidDangCauHinhGoiThau = []
    let bidDangChonNCC = []
    let bidTuChoiGoiThau = []
    let bidDangDuyetGoiThau = []
    let bidDangNhanBaoGia = []
    let bidDangDanhGia = []
    let bidDangDuyetDanhGia = []
    let bidHoanTatDanhGia = []
    let bidDangDamPhanGia = []
    let bidDongDamPhanGia = []
    let bidDangDauGia = []
    let bidDongDauGia = []
    let bidDongThau = []
    let bidDuyetNCCThangThau = []
    let bidDangDuyetKetThucThau = []
    let bidHoanTat = []
    let bidHuy = []

    const listBid = await this.repo.manager.getRepository(BidEntity).find({
      where: whereCon,
      order: { createdAt: 'DESC' },
    })
    if (listBid && listBid.length > 0) {
      for (let bid of listBid) {
        if (bid.status === enumData.BidStatus.GoiThauTam.code) {
          bidGoiThauTam.push(bid)
        }
        if (bid.status === enumData.BidStatus.ChoDuyetGoiThauTam.code) {
          bidChoDuyetGoiThauTam.push(bid)
        }
        if (bid.status === enumData.BidStatus.DangCauHinhGoiThau.code) {
          bidDangCauHinhGoiThau.push(bid)
        }
        if (bid.status === enumData.BidStatus.DangChonNCC.code) {
          bidDangChonNCC.push(bid)
        }
        if (bid.status === enumData.BidStatus.TuChoiGoiThau.code) {
          bidTuChoiGoiThau.push(bid)
        }
        if (bid.status === enumData.BidStatus.DangDuyetGoiThau.code) {
          bidDangDuyetGoiThau.push(bid)
        }
        if (bid.status === enumData.BidStatus.DangNhanBaoGia.code) {
          bidDangNhanBaoGia.push(bid)
        }
        if (bid.status === enumData.BidStatus.DangDanhGia.code) {
          bidDangDanhGia.push(bid)
        }
        if (bid.status === enumData.BidStatus.DangDuyetDanhGia.code) {
          bidDangDuyetDanhGia.push(bid)
        }
        if (bid.status === enumData.BidStatus.HoanTatDanhGia.code) {
          bidHoanTatDanhGia.push(bid)
        }
        if (bid.status === enumData.BidStatus.DangDamPhanGia.code) {
          bidDangDamPhanGia.push(bid)
        }
        if (bid.status === enumData.BidStatus.DongDamPhanGia.code) {
          bidDongDamPhanGia.push(bid)
        }
        if (bid.status === enumData.BidStatus.DangDauGia.code) {
          bidDangDauGia.push(bid)
        }
        if (bid.status === enumData.BidStatus.DongDauGia.code) {
          bidDongDauGia.push(bid)
        }
        if (bid.status === enumData.BidStatus.DongThau.code) {
          bidDongThau.push(bid)
        }
        if (bid.status === enumData.BidStatus.DuyetNCCThangThau.code) {
          bidDuyetNCCThangThau.push(bid)
        }
        if (bid.status === enumData.BidStatus.DangDuyetKetThucThau.code) {
          bidDangDuyetKetThucThau.push(bid)
        }
        if (bid.status === enumData.BidStatus.HoanTat.code) {
          bidHoanTat.push(bid)
        }
        if (bid.status === enumData.BidStatus.Huy.code) {
          bidHuy.push(bid)
        }
      }
    }

    let lstStatusBid = []

    lstStatusBid.push({
      code: enumData.BidStatus.GoiThauTam.code,
      name: enumData.BidStatus.GoiThauTam.name,
      data: bidGoiThauTam,
      count: bidGoiThauTam.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.ChoDuyetGoiThauTam.code,
      name: enumData.BidStatus.ChoDuyetGoiThauTam.name,
      data: bidChoDuyetGoiThauTam,
      count: bidChoDuyetGoiThauTam.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.DangCauHinhGoiThau.code,
      name: enumData.BidStatus.DangCauHinhGoiThau.name,
      data: bidDangCauHinhGoiThau,
      count: bidDangCauHinhGoiThau.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.DangChonNCC.code,
      name: enumData.BidStatus.DangChonNCC.name,
      data: bidDangChonNCC,
      count: bidDangChonNCC.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.TuChoiGoiThau.code,
      name: enumData.BidStatus.TuChoiGoiThau.name,
      data: bidTuChoiGoiThau,
      count: bidTuChoiGoiThau.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.DangNhanBaoGia.code,
      name: enumData.BidStatus.DangNhanBaoGia.name,
      data: bidDangNhanBaoGia,
      count: bidDangNhanBaoGia.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.DangDanhGia.code,
      name: enumData.BidStatus.DangDanhGia.name,
      data: bidDangDanhGia,
      count: bidDangDanhGia.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.DangDuyetDanhGia.code,
      name: enumData.BidStatus.DangDuyetDanhGia.name,
      data: bidDangDuyetDanhGia,
      count: bidDangDuyetDanhGia.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.HoanTatDanhGia.code,
      name: enumData.BidStatus.HoanTatDanhGia.name,
      data: bidHoanTatDanhGia,
      count: bidHoanTatDanhGia.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.DangDamPhanGia.code,
      name: enumData.BidStatus.DangDamPhanGia.name,
      data: bidDangDamPhanGia,
      count: bidDangDamPhanGia.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.DongDamPhanGia.code,
      name: enumData.BidStatus.DongDamPhanGia.name,
      data: bidDongDamPhanGia,
      count: bidDongDamPhanGia.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.DangDauGia.code,
      name: enumData.BidStatus.DangDauGia.name,
      data: bidDangDauGia,
      count: bidDangDauGia.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.DongDauGia.code,
      name: enumData.BidStatus.DongDauGia.name,
      data: bidDongDauGia,
      count: bidDongDauGia.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.DongThau.code,
      name: enumData.BidStatus.DongThau.name,
      data: bidDongThau,
      count: bidDongThau.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.DuyetNCCThangThau.code,
      name: enumData.BidStatus.DuyetNCCThangThau.name,
      data: bidDuyetNCCThangThau,
      count: bidDuyetNCCThangThau.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.DangDuyetKetThucThau.code,
      name: enumData.BidStatus.DangDuyetKetThucThau.name,
      data: bidDangDuyetKetThucThau,
      count: bidDangDuyetKetThucThau.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.HoanTat.code,
      name: enumData.BidStatus.HoanTat.name,
      data: bidHoanTat,
      count: bidHoanTat.length,
    })

    lstStatusBid.push({
      code: enumData.BidStatus.Huy.code,
      name: enumData.BidStatus.Huy.name,
      data: bidHuy,
      count: bidHuy.length,
    })

    let supMoiDangKy = []
    let supDaDuyet = []
    let supHuy = []
    const listSupplier = await this.repo.manager.getRepository(SupplierEntity).find({
      where: whereCon,
      order: { createdAt: 'DESC' },
    })
    if (listSupplier.length > 0) {
      for (let sup of listSupplier) {
        if (sup.status === enumData.SupplierStatus.MoiDangKy.code) {
          supMoiDangKy.push(sup)
        }
        if (sup.status === enumData.SupplierStatus.DaDuyet.code) {
          supDaDuyet.push(sup)
        }
        if (sup.status === enumData.SupplierStatus.Huy.code) {
          supHuy.push(sup)
        }
      }
    }

    let lstStatusSupplier = []

    lstStatusSupplier.push({
      code: enumData.SupplierStatus.MoiDangKy.code,
      name: enumData.SupplierStatus.MoiDangKy.name,
      data: supMoiDangKy,
      count: supMoiDangKy.length,
    })

    lstStatusSupplier.push({
      code: enumData.SupplierStatus.DaDuyet.code,
      name: enumData.SupplierStatus.DaDuyet.name,
      data: supDaDuyet,
      count: supDaDuyet.length,
    })

    lstStatusSupplier.push({
      code: enumData.SupplierStatus.Huy.code,
      name: enumData.SupplierStatus.Huy.name,
      data: supHuy,
      count: supHuy.length,
    })

    return { lstStatusPrArise, lstStatusPrPlant, lstStatusBid, lstStatusSupplier }
  }

  public async loadDashBoardSupplier(
    user: UserDto,
    data: {
      serviceId?: string[]
      dateFrom?: Date
      dateTo?: Date
      isDeleted?: boolean
      listChart?: number[]
      category?: string
    },
  ) {
    data.listChart = data.listChart || []
    let whereCon: any = { companyId: user.companyId, serviceId: In(data.serviceId), isDeleted: false }

    // Chart 1: Doanh số cao nhất
    const lstTop10SupplierTotalPriceMax = []
    if (data.listChart.includes(1)) {
      const where1: any = { ...whereCon }
      where1.status = enumData.SupplierServiceStatus.DaDuyet.code
      const lstSupplierService1 = await this.repo.find({
        where: where1,
        order: { totalPrice: 'DESC' },
        skip: 0,
        take: 10,
      })
      for (const supplierService of lstSupplierService1) {
        const supplier = await supplierService.supplier
        const service = await supplierService.service
        supplierService.totalPrice = supplierService.totalPrice || 0
        const totalPriceBillion = supplierService.totalPrice / 1000000000
        const data = {
          ...supplierService,
          totalPriceBillion,
          supplierCode: supplier.code,
          supplierName: supplier.name,
          itemName: service.code + ' - ' + service.name,
        }

        lstTop10SupplierTotalPriceMax.push(data)
      }
    }

    // Chart 2: Doanh số thấp nhất
    const lstTop10SupplierTotalPriceMin = []
    if (data.listChart.includes(2)) {
      const where2: any = { ...whereCon }
      where2.status = enumData.SupplierServiceStatus.DaDuyet.code
      const lstSupplierService2 = await this.repo.find({
        where: where2,
        order: { totalPrice: 'ASC' },
        skip: 0,
        take: 10,
      })
      for (const supplierService of lstSupplierService2) {
        const supplier = await supplierService.supplier
        const service = await supplierService.service
        supplierService.totalPrice = supplierService.totalPrice || 0
        const totalPriceBillion = supplierService.totalPrice / 1000000000
        const data = {
          ...supplierService,
          totalPriceBillion,
          supplierCode: supplier.code,
          supplierName: supplier.name,
          itemName: service.code + ' - ' + service.name,
        }

        lstTop10SupplierTotalPriceMin.push(data)
      }
    }

    // Chart 3: Đơn giá cao nhất
    const lstTop10SupplierUnitPriceMax = []
    if (data.listChart.includes(3) && data.category != null && data.category.trim() != '') {
      const where3: any = { ...whereCon }
      const categoryName = data.category.trim()
      where3.bidPriceName = categoryName
      const lstBidSupplierPrice = await this.repo.manager.getRepository(BidSupplierPriceEntity).find({
        where: where3,
        order: { unitPrice: 'DESC' },
        skip: 0,
        take: 10,
      })
      for (const bidSupplierPrice of lstBidSupplierPrice) {
        const bidSupplier = await bidSupplierPrice.bidSupplier
        const supplier = await bidSupplier.supplier
        const bid = await bidSupplier.bid
        const service = await bid.service

        bidSupplierPrice.unitPrice = bidSupplierPrice.unitPrice || 0
        const data = {
          ...bidSupplierPrice,
          bidCode: bid.code,
          bidName: bid.name,
          supplierCode: supplier.code,
          supplierName: supplier.name,
          itemName: service.code + ' - ' + service.name,
          bidSupplierName: `${supplier.code}-${bid.code}`,
        }

        lstTop10SupplierUnitPriceMax.push(data)
      }
    }

    // Chart 4: Đơn giá thấp nhất
    const lstTop10SupplierUnitPriceMin = []
    if (data.listChart.includes(4) && data.category != null && data.category.trim() != '') {
      const where4: any = { ...whereCon }
      const categoryName = data.category.trim()
      where4.bidPriceName = categoryName
      const lstBidSupplierPrice = await this.repo.manager.getRepository(BidSupplierPriceEntity).find({
        where: where4,
        order: { unitPrice: 'ASC' },
        skip: 0,
        take: 10,
      })
      for (const bidSupplierPrice of lstBidSupplierPrice) {
        const bidSupplier = await bidSupplierPrice.bidSupplier
        const supplier = await bidSupplier.supplier
        const bid = await bidSupplier.bid
        const service = await bid.service

        bidSupplierPrice.unitPrice = bidSupplierPrice.unitPrice || 0
        const data = {
          ...bidSupplierPrice,
          bidCode: bid.code,
          bidName: bid.name,
          supplierCode: supplier.code,
          supplierName: supplier.name,
          itemName: service.code + ' - ' + service.name,
          bidSupplierName: `${supplier.code}-${bid.code}`,
        }

        lstTop10SupplierUnitPriceMin.push(data)
      }
    }

    // Chart 5: Điểm năng lực cao nhất
    const lstTop10SupplierScoreMax = []
    if (data.listChart.includes(5)) {
      const where5: any = { ...whereCon }
      where5.status = enumData.SupplierServiceStatus.DaDuyet.code
      const lstSupplierService4 = await this.repo.find({
        where: where5,
        order: { score: 'DESC' },
        skip: 0,
        take: 10,
      })
      for (const supplierService of lstSupplierService4) {
        const supplier = await supplierService.supplier
        const service = await supplierService.service
        const data = {
          ...supplierService,
          supplierCode: supplier.code,
          supplierName: supplier.name,
          itemName: service.code + ' - ' + service.name,
        }

        lstTop10SupplierScoreMax.push(data)
      }
    }

    // Chart 6: Điểm năng lực thấp nhất
    const lstTop10SupplierScoreMin = []
    if (data.listChart.includes(6)) {
      const where6: any = { ...whereCon }
      where6.status = enumData.SupplierServiceStatus.DaDuyet.code
      const lstSupplierService4 = await this.repo.find({
        where: where6,
        order: { score: 'ASC' },
        skip: 0,
        take: 10,
      })
      for (const supplierService of lstSupplierService4) {
        const supplier = await supplierService.supplier
        const service = await supplierService.service
        const data = {
          ...supplierService,
          supplierCode: supplier.code,
          supplierName: supplier.name,
          itemName: service.code + ' - ' + service.name,
        }

        lstTop10SupplierScoreMin.push(data)
      }
    }

    return {
      lstTop10SupplierTotalPriceMax,
      lstTop10SupplierTotalPriceMin,
      lstTop10SupplierUnitPriceMax,
      lstTop10SupplierUnitPriceMin,
      lstTop10SupplierScoreMax,
      lstTop10SupplierScoreMin,
    }
  }
  public async loadDashBoardTop10Supplier(user: UserDto) {
    const res: any[] = await this.repo.find({
      order: { score: 'DESC' },
      skip: 0,
      take: 10,
      relations: { supplier: true, service: true },
    })
    for (const item of res) {
      item.supplierName = item.__supplier__.name
      item.supplierCode = item.__supplier__.code
      item.itemName = item.__service__.code + ' - ' + item.__service__.name
      delete item.__supplier__
      delete item.__service__
    }

    return res
  }
}
