import { MigrationInterface, QueryRunner } from 'typeorm'

export class addChildPo1718778030775 implements MigrationInterface {
  name = 'addChildPo1718778030775'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`po\` ADD \`isChild\` tinyint NULL DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE \`po\` ADD \`parentId\` varchar(36) NULL`)
    await queryRunner.query(
      `ALTER TABLE \`po\` ADD CONSTRAINT \`FK_fe4f20bbf025284450f3eb76dfb\` FOREIGN KEY (\`parentId\`) REFERENCES \`po\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`po\` DROP FOREIGN KEY \`FK_fe4f20bbf025284450f3eb76dfb\``)
    await queryRunner.query(`ALTER TABLE \`po\` DROP COLUMN \`parentId\``)
    await queryRunner.query(`ALTER TABLE \`po\` DROP COLUMN \`isChild\``)
  }
}
