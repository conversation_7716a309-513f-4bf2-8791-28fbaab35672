import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { BidDealRepository, BidRepository, BidSupplierPriceRepository, BidSupplierRepository } from '../../repositories'
import { BidDealController } from './bidDeal.controller'
import { BidDealService } from './bidDeal.service'
import { EmailModule } from '../email/email.module'
import { AiModule } from '../ai/ai.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([BidDealRepository, BidRepository, BidSupplierRepository, BidSupplierPriceRepository]),
    EmailModule,
    AiModule,
  ],
  controllers: [BidDealController],
  providers: [BidDealService],
  exports: [BidDealService],
})
export class BidDealModule {}
