import { Injectable } from '@nestjs/common'
import { PaginationDto, UserDto } from '../../dto'
import { PaymentProgressRepository } from '../../repositories'
import { PaymentProgressUpdateOrder } from './dto/paymentProgressUpdateOrder.dto'
import { PaymentProgressEntity } from '../../entities'
import { UPDATE_SUCCESS } from '../../constants'

@Injectable()
export class PaymentProgressService {
  constructor(private readonly repo: PaymentProgressRepository) {}

  public async find(user: UserDto, data: { contractId?: string; poId?: string }) {
    const whereCon: any = { companyId: user.companyId, isDeleted: false }
    if (data.contractId) whereCon.contractId = data.contractId
    if (data.poId) whereCon.poId = data.poId
    const rs = await this.repo.find({ where: whereCon, relations: { contract: { bid: true } } })

    return rs
  }
}
