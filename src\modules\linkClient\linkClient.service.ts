import { Injectable } from '@nestjs/common'
import { LinkClientCreateDto } from './dto/linkClientCreate.dto'
import { LinkClientRepository } from '../../repositories'
import { CREATE_SUCCESS, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { Like } from 'typeorm'
import { PaginationDto, UserDto } from '../../dto'
import { LinkClientUpdateDto } from './dto'

@Injectable()
export class LinkClientService {
  constructor(private readonly repo: LinkClientRepository) {}

  public async createData(user: UserDto, data: LinkClientCreateDto) {
    const newEntity = this.repo.create(data)
    newEntity.companyId = user.companyId
    newEntity.createdBy = user.id
    await this.repo.save(newEntity)

    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserD<PERSON>, data: Link<PERSON>lientUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.url = data.url
    entity.description = data.description
    entity.updatedBy = user.id
    await entity.save()

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { companyId: user.companyId }

    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.repo.findAndCount({
      where: whereCon,
      order: { name: 'ASC' },
      skip: data.skip,
      take: data.take,
    })
  }

  public async updateIsDelete(user: UserDto, id: string) {
    const entity = await this.repo.findOne({ where: { id: id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }
}
