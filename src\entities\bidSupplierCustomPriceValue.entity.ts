import { BaseEntity } from './base.entity'
import { Entity, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>um<PERSON>, OneToMany } from 'typeorm'
import { BidSupplierEntity } from './bidSupplier.entity'

@Entity('bid_supplier_custom_price_value')
export class BidSupplierCustomPriceValueEntity extends BaseEntity {
  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  unit: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  currency: string

  /** Số lượng */
  @Column({
    nullable: false,
    default: 0,
  })
  number: number

  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  /** Tên  */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  value: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidSupplierId: string
  @ManyToOne(() => BidSupplierEntity, (p) => p.bidSupplierCustomPriceValue)
  @JoinColumn({ name: 'bidSupplierId', referencedColumnName: 'id' })
  bidSupplier: Promise<BidSupplierEntity>
}
