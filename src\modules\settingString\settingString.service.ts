import { Injectable, ConflictException } from '@nestjs/common'
import { CREATE_SUCCESS, ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { SettingStringCreateDto } from './dto/settingStringCreate.dto'
import { SettingStringRepository } from '../../repositories'
import { Like } from 'typeorm'
import { PaginationDto, UserDto } from '../../dto'
import { SettingStringUpdateDto } from './dto'

@Injectable()
export class SettingStringService {
  constructor(private readonly repo: SettingStringRepository) {}

  public async find(user: UserDto, data: { type?: string }) {
    const whereCon: any = { isDeleted: false, companyId: user.companyId }
    if (data.type) whereCon.type = data.type
    return await this.repo.find({ where: whereCon, order: { name: '<PERSON><PERSON>' } })
  }

  public async createData(user: UserDto, data: SettingStringCreateDto) {
    const objCheckCode = await this.repo.findOne({ where: { code: data.code, type: data.type, companyId: user.companyId }, select: { id: true } })
    if (objCheckCode) throw new ConflictException(ERROR_CODE_TAKEN)

    const newEntity = this.repo.create(data)
    newEntity.companyId = user.companyId
    newEntity.createdBy = user.id
    await this.repo.save(newEntity)

    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: SettingStringUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.description = data.description
    entity.updatedBy = user.id
    await this.repo.save(entity)

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { companyId: user.companyId }
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.type) whereCon.type = data.where.type
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { name: 'ASC' },
    })

    return res
  }

  public async updateIsDelete(user: UserDto, data: { id: string }) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }
}
