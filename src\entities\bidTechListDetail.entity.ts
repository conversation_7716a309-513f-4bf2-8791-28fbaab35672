import { BaseEntity } from './base.entity'
import { En<PERSON><PERSON>, Column, ManyTo<PERSON>ne, JoinC<PERSON>umn } from 'typeorm'
import { BidTechEntity } from './bidTech.entity'

@Entity('bid_tech_list_detail')
export class BidTechListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
  })
  value: number

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidTechId: string
  @ManyToOne(() => BidTechEntity, (p) => p.bidTechListDetails)
  @JoinColumn({ name: 'bidTechId', referencedColumnName: 'id' })
  bidTech: Promise<BidTechEntity>
}
