import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { BranchService } from './branch.service'
import { BranchCreateDto } from './dto/branchCreate.dto'
import { BranchUpdateDto } from './dto/branchUpdate.dto'
import { CurrentUser, Roles } from '../common/decorators'
import { PaginationDto, UserDto } from '../../dto'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { enumProject } from '../../constants'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Branch')
@Controller('branch')
export class BranchController {
  constructor(private readonly service: BranchService) {}

  @ApiOperation({ summary: 'Lấy danh sách chi nhánh' })
  @UseGuards(ApeAuthGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: { level?: number; parentId?: string }) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: '<PERSON><PERSON>y danh sách chi nhánh' })
  @UseGuards(ApeAuthGuard)
  @Post('find_all')
  public async findSelection(@CurrentUser() user: UserDto, @Body() data: { level?: number; parentId?: string }) {
    return await this.service.findSelection(user, data)
  }

  @ApiOperation({ summary: 'Danh sách chi nhánh phân trang' })
  @Roles(enumProject.Features.SETTING_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo chi nhánh' })
  @Roles(enumProject.Features.SETTING_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: BranchCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật chi nhánh' })
  @Roles(enumProject.Features.SETTING_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: BranchUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động chi nhánh' })
  @Roles(enumProject.Features.SETTING_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@Body() data: { id: string }, @CurrentUser() user: UserDto) {
    const warehouse = await this.service.updateIsDelete(data, user)
    return warehouse
  }
}
