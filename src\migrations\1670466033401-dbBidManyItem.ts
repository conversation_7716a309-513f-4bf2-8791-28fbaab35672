import { MigrationInterface, QueryRunner } from "typeorm";

export class dbBidManyItem1670466033401 implements MigrationInterface {
    name = 'dbBidManyItem1670466033401'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid\` ADD \`quantityItem\` int NULL DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid\` DROP COLUMN \`quantityItem\``);
    }

}
