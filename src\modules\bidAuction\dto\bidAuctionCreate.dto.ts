import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsNumber, IsArray, IsNotEmpty } from 'class-validator'

export class BidAuctionCreateDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  bidId: string

  @ApiProperty()
  @IsNotEmpty()
  endDate: Date

  @ApiProperty()
  @IsArray()
  lstSupplierChoose: any[]

  @ApiProperty()
  @IsArray()
  lstPrice: BidAuctionPriceCreateDto[]
}

export class BidAuctionPriceCreateDto {
  @ApiProperty()
  @IsString()
  bidPriceId: string

  @ApiProperty()
  @IsNumber()
  maxPrice: number
}

export class BidAuctionSupplierSaveDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  id: string

  @ApiProperty()
  @IsArray()
  listChild: BidAuctionItemDto[]
}

class BidAuctionItemDto {
  @ApiProperty()
  id: string
  @ApiProperty()
  bidId: string
  @ApiProperty()
  lstAuctionPrice: any[]
}
