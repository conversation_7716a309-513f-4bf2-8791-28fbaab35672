import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { LanguageConfigRepository } from '../../repositories'
import { LanguageConfigService } from './languageConfig.service'
import { LanguageConfigController } from './languageConfig.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([LanguageConfigRepository])],
  controllers: [LanguageConfigController],
  providers: [LanguageConfigService],
})
export class LanguageConfigModule {}
