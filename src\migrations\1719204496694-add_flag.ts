import { MigrationInterface, QueryRunner } from 'typeorm'

export class addFlag1719204496694 implements MigrationInterface {
  name = 'addFlag1719204496694'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`pr_item_child\` ADD \`isGroup\` tinyint NULL DEFAULT 0`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`pr_item_child\` DROP COLUMN \`isGroup\``)
  }
}
