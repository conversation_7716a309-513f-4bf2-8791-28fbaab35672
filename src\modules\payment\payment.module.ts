import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { PaymentRepository } from '../../repositories'
import { PaymentService } from './payment.service'
import { PaymentController } from './payment.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([PaymentRepository])],
  controllers: [PaymentController],
  providers: [PaymentService],
})
export class PaymentModule {}
