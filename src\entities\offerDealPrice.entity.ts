import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { OfferDealEntity } from './offerDeal.entity'
import { OfferPriceEntity } from './offerPrice.entity'

@Entity('offer_deal_price')
export class OfferDealPriceEntity extends BaseEntity {
  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  /** Số lượng đàm phán */
  @Column({
    nullable: false,
    default: 0,
  })
  number: number

  /** Gi<PERSON> tốt nhất đang chào */
  @Column({
    type: 'float',
    nullable: true,
  })
  bestPrice: number

  /** Giá tốt nhất đã trúng thầu */
  @Column({
    type: 'float',
    nullable: true,
  })
  bestPriceHistory: number

  /** Gi<PERSON> tốt nhất đang mua */
  @Column({
    type: 'float',
    nullable: true,
  })
  bestPriceCurrent: number

  /** <PERSON><PERSON><PERSON> mong muốn đàm phán */
  @Column({
    type: 'float',
    nullable: true,
  })
  suggestPrice: number

  @Column({
    type: 'float',
    nullable: true,
  })
  maxPrice: number

  @Column({
    type: 'varchar',
    nullable: false,
  })
  offerDealId: string
  @ManyToOne(() => OfferDealEntity, (p) => p.offerDealPrices)
  @JoinColumn({ name: 'offerDealId', referencedColumnName: 'id' })
  offerDeal: Promise<OfferDealEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  offerPriceId: string
  @ManyToOne(() => OfferPriceEntity, (p) => p.offerDealPrices)
  @JoinColumn({ name: 'offerPriceId', referencedColumnName: 'id' })
  offerPrice: Promise<OfferPriceEntity>

  // @OneToMany(
  //   () => OfferDealSupplierPriceValueEntity,
  //   p => p.offerDealPrice,
  // )
  // offerDealSupplierPriceValue: Promise<OfferDealSupplierPriceValueEntity[]>
}
