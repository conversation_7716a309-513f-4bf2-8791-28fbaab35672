#ConfigMap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ape-pms-lite-api-config
  namespace: ape-pms-lite
data:
  NODE_ENV: 'production'
  TYPEORM_HOST: 'ape-mysql.c7uzjfmteanl.ap-southeast-1.rds.amazonaws.com'
  TYPEORM_USERNAME: 'ape-auth'
  TYPEORM_PASSWORD: 'MPO123456!@#'
  TYPEORM_DATABASE: 'ape-pms-lite'
  TYPEORM_PORT: '3306'
  TYPEORM_LOGGING: 'false'
  PORT: '80'
  JWT_SECRET: '<your_secret>'
  JWT_EXPIRY: '10h'

  ADMIN_URL: 'https://ape-pms-lite-admin.apetechs.co'
  CLIENT_URL: 'https://ape-pms-lite-client.apetechs.co'

  apiVersion: '2023-03-09'
  AWS_S3_BUCKET_NAME: 'ape-devs-co'
  AWS_S3_ACCESS_KEY_ID: '********************'
  AWS_S3_SECRET_ACCESS_KEY: '/DIKQa//iyYZUvucHau/cRItB+LCkQ76XWspfrcO'

  AWS_SMTP_END_POINT: 'smtp.gmail.com'
  AWS_SMTP_PORT: '587'
  AWS_SMTP_SENDER_ADDRESS: '<EMAIL>'
  AWS_SMTP_USERNAME: '<EMAIL>'
  AWS_SMTP_PASSWORD: 'uzvtxiackykazjib'

  AWS_SQS_URL: 'https://sqs.ap-southeast-1.amazonaws.com/077293829360/ape-pms-lite'
  AWS_SQS_REGION: 'ap-southeast-1'
  AWS_SQS_ACCESS_KEY_ID: '********************'
  AWS_SQS_SECRET_ACCESS_KEY: 'AvdcLjY5ucwKV6zarBHBqFyNefoEqbalsRyYEKX4'

  LINK_UPLOAD_S3: 'ape-pms-lite'
  IS_PRODUCT: 'false'
  PROJECT_NAME: 'PMS'
  SOURCE_CODE: 'PMS_API'
  ENVIRONMENT: 'LITE'
  LOG_URL: 'https://ape-bot-api.apetechs.co/bug_log/create_data'

  SERVICE: 'gmail'
  TYPE: 'OAuth2'
  USER_MAIL: '<EMAIL>'
  CLIENT_ID: '666851175573-hpdf9kk8snkspigsou2d3pqjfk3l480v.apps.googleusercontent.com'
  CLIENT_SECRET: '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'
  REFRESH_TOKEN: '1//043H0BxdSRMAtCgYIARAAGAQSNwF-L9Irf9mUOdFY0ci8Lxg9goakSV4E-WKkY6ALGnVsjkgJWEPzhBmyg5iLgTE3aK-zruLh7WA'
  OAUTH_PLAYGROUND: 'https://developers.google.com/oauthplayground'
  X_API_KEY: 'bizzi@demo'
  URL_API_ADD_BILL: 'https://api-uat.bizzi.services/v1/invoices/xml'
  URL_API_GET_COMPANY: 'https://api-uat.bizzi.services/v1/companies'

  BOT_API: 'https://ape-bot-api-test.apetechs.co'

  SCRIPT_TEMPLATE: 'Tôi mua món {{S_01}} với giá bao nhiêu là hợp lý, biết rằng giá tốt nhất của tôi là {{S_02}}VND, hãy cho tôi 1 giá tốt hơn đi và hợp với giá của thị trường,nhớ trả giá xuống dưới mức giá tốt nhất của tôi. Trả lời cho tôi một vài giá gợi ý đi'
  SCRIPT_TEMPLATE_02: 'Tôi mua món {{S_01}} với giá bao nhiêu là hợp lý, hãy cho tôi 1 giá tốt hơn đi và hợp với giá của thị trường. Trả lời cho tôi một vài giá gợi ý đi'

---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ape-pms-lite-api
  namespace: ape-pms-lite
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ape-pms-lite-api
  template:
    metadata:
      labels:
        app: ape-pms-lite-api
    spec:
      containers:
        - name: ape-pms-lite-api
          image: 077293829360.dkr.ecr.ap-southeast-1.amazonaws.com/ape-pms-lite-api:latest
          ports:
            - containerPort: 80
          envFrom:
            - configMapRef:
                name: ape-pms-lite-api-config
          volumeMounts:
            - mountPath: /etc/localtime
              name: tz-config
      volumes:
        - name: tz-config
          hostPath:
            path: /usr/share/zoneinfo/Asia/Ho_Chi_Minh

---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: ape-pms-lite-api
  namespace: ape-pms-lite
  labels:
    run: ape-pms-lite-api
spec:
  type: ClusterIP
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: ape-pms-lite-api
