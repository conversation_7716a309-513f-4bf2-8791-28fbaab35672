import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { BranchMemberService } from './branchMember.service'
import { CurrentUser, Roles } from '../../common/decorators'
import { PaginationDto, UserDto } from '../../../dto'
import { ApeAuthGuard, RoleGuard } from '../../common/guards'
import { enumProject } from '../../../constants'
import { BranchMemberCreateDto, BranchMemberUpdateDto } from './dto'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Branch')
@Controller('branch_member')
export class BranchMemberController {
  constructor(private readonly service: BranchMemberService) {}

  @ApiOperation({ summary: 'Danh sách quản lý chi nhánh phân trang' })
  @Roles(enumProject.Features.SETTING_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo quản lý chi nhánh' })
  @Roles(enumProject.Features.SETTING_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: BranchMemberCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật quản lý chi nhánh' })
  @Roles(enumProject.Features.SETTING_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: BranchMemberUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động của quản lý chi nhánh' })
  @Roles(enumProject.Features.SETTING_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@Body() data: { id: string }, @CurrentUser() user: UserDto) {
    const warehouse = await this.service.updateIsDelete(data, user)
    return warehouse
  }
}
