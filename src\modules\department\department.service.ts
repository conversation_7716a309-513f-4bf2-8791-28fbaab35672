import { Injectable, ConflictException } from '@nestjs/common'
import { DepartmentRepository } from '../../repositories'
import { CREATE_SUCCESS, ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { IsNull, Like } from 'typeorm'
import { PaginationDto, UserDto } from '../../dto'
import { DepartmentCreateDto, DepartmentUpdateDto } from './dto'

@Injectable()
export class DepartmentService {
  constructor(private readonly repo: DepartmentRepository) {}

  public async find(user: UserDto, data: { branchId?: string }) {
    const whereCon: any = { companyId: user.companyId, isDeleted: false }
    if (data.branchId) whereCon.branchId = data.branchId
    return await this.repo.find({ where: whereCon, order: { name: 'ASC' } })
  }

  public async createData(user: UserD<PERSON>, data: DepartmentCreateDto) {
    const objCheckCode = await this.repo.findOne({
      where: { companyId: user.companyId, code: data.code, branchId: data.branchId || IsNull() },
      select: { id: true },
    })
    if (objCheckCode) throw new ConflictException(ERROR_CODE_TAKEN)

    const newEntity = this.repo.create(data)
    newEntity.companyId = user.companyId
    newEntity.createdBy = user.id
    await this.repo.save(newEntity)

    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: DepartmentUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    if (data.branchId != entity.branchId) {
      const objCheckCode: any = await this.repo.findOne({
        where: { companyId: user.companyId, code: entity.code, branchId: data.branchId || IsNull() },
        relations: { branch: true },
        select: { id: true, branch: { id: true, name: true } },
      })
      if (objCheckCode) {
        if (data.branchId) throw new ConflictException(`Mã chi nhánh ${entity.code} đã tồn tại ở chi nhánh ${objCheckCode.__branch__.name}`)
        else throw new ConflictException(`Mã chi nhánh ${entity.code} đã tồn tại`)
      }

      entity.branchId = data.branchId
    }

    entity.name = data.name
    entity.description = data.description
    entity.updatedBy = user.id
    await this.repo.save(entity)

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { companyId: user.companyId }
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { branch: true },
      order: { name: 'ASC' },
    })

    for (const item of res[0]) {
      if (item.branchId) item.branchName = item.__branch__.name
      delete item.__branch__
    }

    return res
  }

  public async updateIsDelete(user: UserDto, data: { id: string }) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }
}
