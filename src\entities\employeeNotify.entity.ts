import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm'
import { EmployeeEntity } from './employee.entity'
@Entity('employee_notify')
export class EmployeeNotifyEntity extends BaseEntity {
  /** Tiêu đề */
  @Column({
    type: 'text',
    nullable: true,
  })
  message: string

  /** Nội dung */
  @Column({
    type: 'text',
    nullable: true,
  })
  messageFull: string

  /** Là thông báo mới */
  @Column({
    nullable: false,
    default: true,
  })
  isNew: boolean

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.employeeNotify)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>
}
