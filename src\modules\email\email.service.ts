'use strict'
const nodemailer = require('nodemailer')
import { Injectable } from '@nestjs/common'
import {
  BidRepository,
  BidAuctionRepository,
  BidEmployeeAccessRepository,
  BidDealRepository,
  BidSupplierRepository,
  EmailHistoryRepository,
  EmailTemplateRepository,
  EmployeeRepository,
  EmployeeNotifyRepository,
  EmployeeWarningRepository,
  SupplierRepository,
  SupplierExpertiseRepository,
  SupplierServiceRepository,
  SupplierNotifyRepository,
  AuctionRepository,
  PORepository,
} from '../../repositories'
import { SQSService } from '../common/sqs/sqs.service'
import { EmailHistoryEntity, BidDealSupplierEntity } from '../../entities'
import { In } from 'typeorm'
import { apeAuthApiHelper, coreHelper } from '../../helpers'
import { UserDto } from '../../dto'
import { enumData, enumEmailType, enumEmailPO } from '../../constants'
import * as moment from 'moment'
@Injectable()
export class EmailService {
  //#region config

  constructor(
    private readonly sqsService: SQSService,

    private readonly PORepo: PORepository,
    private readonly supplierServiceRepo: SupplierServiceRepository,
    private readonly supplierRepo: SupplierRepository,
    private readonly supplierExpertiseRepo: SupplierExpertiseRepository,
    private readonly bidRepo: BidRepository,
    private readonly bidDealRepo: BidDealRepository,
    private readonly bidAuctionRepo: BidAuctionRepository,
    private readonly bidEmployeeAccessRepo: BidEmployeeAccessRepository,
    private readonly bidSupplierRepo: BidSupplierRepository,
    private readonly emailHistoryRepo: EmailHistoryRepository,
    private readonly emailTemplateRepo: EmailTemplateRepository,
    private readonly employeeRepo: EmployeeRepository,
    private readonly supplierNotifyRepo: SupplierNotifyRepository,
    private readonly employeeNotifyRepo: EmployeeNotifyRepository,
    private readonly employeeWarningRepo: EmployeeWarningRepository,

    private readonly auctionRepo: AuctionRepository,
  ) {}

  public async testEmail() {
    // Replace <EMAIL> with a "To" address. If your account
    // is still in the sandbox, this address must be verified. To specify
    // multiple addresses, separate each address with a comma
    const toAddresses = '<EMAIL>'

    // CC and BCC addresses. If your account is in the sandbox, these
    // addresses have to be verified. To specify multiple addresses, separate
    // each address with a comma.
    const ccAddresses = '' //'<EMAIL>,<EMAIL>'
    const bccAddresses = '' //'<EMAIL>'

    // The subject line of the email
    const subject = 'IT Test'

    // The email body for recipients with non-HTML email clients.
    const body_text = `IT`

    // The body of the email for recipients whose email clients support HTML content.
    const body_html = `<html>
<head></head>
<body>
  <h1>ABC</h1>
</body>
</html>`

    // The message tags that you want to apply to the email.
    const type = enumEmailType.TEMP01
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
      },
    })
  }

  public async sendEmail(
    toAddresses: string,
    subject: string,
    ccAddresses: string,
    bccAddresses: string,
    body_text: string,
    body_html: string,
    type: string,
    isResend: boolean = false,
    historyId: string = '',
    companyId: string = '',
    userId: string = '',
  ) {
    const configSMTP = await apeAuthApiHelper.getSMTP(companyId)
    // Create the SMTP transport.
    const transporter = await nodemailer.createTransport({
      host: configSMTP?.smtpEndPoint,
      port: configSMTP?.smtpPort,
      secure: false, // true for 465, false for other ports
      auth: {
        user: configSMTP?.smtpUsername,
        pass: configSMTP?.smtpPassword,
      },
    })

    // Specify the fields in the email.
    const mailOptions = {
      from: configSMTP?.smtpSenderAddress,
      to: toAddresses,
      subject: subject,
      cc: ccAddresses,
      bcc: bccAddresses,
      text: body_text,
      html: body_html,
    }

    let info
    let status = enumData.EmailStatus.Success.code
    // Send the email.
    try {
      info = await transporter.sendMail(mailOptions)
      //  info = await this.emailNodeService.sendMail(mailOptions)
    } catch (error) {
      info = error
      status = enumData.EmailStatus.Fail.code
    }
    if (!isResend) {
      const emailHistory = new EmailHistoryEntity()
      emailHistory.toAddresses = toAddresses
      emailHistory.subject = subject
      emailHistory.ccAddresses = ccAddresses
      emailHistory.bccAddresses = bccAddresses
      emailHistory.body_text = body_text
      emailHistory.body_html = body_html
      emailHistory.status = status
      emailHistory.result = info
      emailHistory.type = type
      emailHistory.companyId = companyId
      emailHistory.createdBy = userId
      await emailHistory.save()
    } else {
      const emailHistory = await this.emailHistoryRepo.findOne({ where: { id: historyId } })
      if (!emailHistory) return

      emailHistory.toAddresses = toAddresses
      emailHistory.subject = subject
      emailHistory.ccAddresses = ccAddresses
      emailHistory.bccAddresses = bccAddresses
      emailHistory.body_text = body_text
      emailHistory.body_html = body_html
      emailHistory.status = status
      emailHistory.result = info
      emailHistory.count = emailHistory.count + 1
      emailHistory.updatedBy = userId

      await this.emailHistoryRepo.update(historyId, emailHistory)
    }
  }

  //#endregion

  public async GuiThayDoiNgayGiaoHang(po: any) {
    const PO: any = await this.PORepo.findOne({
      where: { id: po },
      relations: { supplier: true, members: { employee: true } },
      select: {
        id: true,
        code: true,
        companyId: true,
        deliveryDate: true,
        supplier: { id: true, name: true },
        members: {
          employee: {
            name: true,
            email: true,
          },
        },
      },
    })
    if (!PO) return

    const type = enumEmailPO.TEMP00
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: PO.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const supplier = PO.__supplier__
    // const service = PO.__service__
    const date = moment(PO.deliveryDate).format('YYYY-MM-DD')
    const member = await PO.members
    const filteredEmployee = member.filter((obj) => obj.poRoleCode === 'COMFIRM')

    if (filteredEmployee.length == 0) return

    for (const member of filteredEmployee) {
      const employee = await member.employee
      const domainObj = await apeAuthApiHelper.getDomain(PO.companyId)
      const link = `<a href="${domainObj?.adminUrl}/contract/delivery-manager">link</a>`
      if (employee.email) {
        const toAddresses = employee.email
        const ccAddresses = ''
        const bccAddresses = ''

        const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [
          employee.name,
          PO.code,
          supplier.name,
          link,
          date,
        ])
        const body_text = convertHtml
        const body_html = convertHtml
        this.sqsService.sendMessage({
          type: enumData.SQSMessageType.Email,
          data: {
            toAddresses,
            subject,
            ccAddresses,
            bccAddresses,
            body_text,
            body_html,
            type: type.code,
            companyId: PO.companyId,
          },
        })
      }
    }
  }

  //#region QT1

  /** 1/ Mẫu email gửi nhân viên MPO phụ trách việc xét duyệt đăng ký' */
  public async GuiMpoDuyetNccDangKy(supplierServiceId: string) {
    const supplierService: any = await this.supplierServiceRepo.findOne({
      where: { id: supplierServiceId },
      relations: { supplier: true, service: { approveBy: true, serviceAccess: { employee: true } } },
      select: {
        id: true,
        companyId: true,
        supplier: { id: true, name: true },
        service: {
          id: true,
          name: true,
          approveBy: { id: true, email: true },
          serviceAccess: { id: true, employee: { id: true, email: true, name: true } },
        },
      },
    })
    if (!supplierService) return

    const type = enumEmailType.TEMP01
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: supplierService.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const supplier = supplierService.__supplier__
    const service = supplierService.__service__
    const approve = service.__approveBy__
    const lstEmployee = service.__serviceAccess__.map((c) => c.__employee__)
    if (lstEmployee.length == 0) return
    const employee = lstEmployee[0]

    const domainObj = await apeAuthApiHelper.getDomain(supplierService.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/supplier-capacity">link</a>`
    if (employee.email) {
      const toAddresses = employee.email
      const ccAddresses = approve?.email
      const bccAddresses = ''

      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [
        employee.name,
        supplier.name,
        service.name,
        link,
      ])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: supplierService.companyId,
        },
      })

      // gửi notify trang admin
      let lstEmployeeId = [approve.id, employee.id]
      lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
      for (const employeeId of lstEmployeeId) {
        this.employeeNotifyRepo.createEmployeeNotify(supplierService.companyId, employeeId, subject, convertHtml || '')
      }
    }
  }

  /** 2/ Mẫu email gửi người duyệt khi nhân viên phụ trách đã duyệt */
  public async GuiNguoiDuyetDuyetNccDangKy(supplierServiceId: string) {
    const supplierService: any = await this.supplierServiceRepo.findOne({
      where: { id: supplierServiceId },
      relations: { supplier: true, service: { approveBy: true, serviceAccess: { employee: true } } },
      select: {
        id: true,
        companyId: true,
        supplier: { id: true, name: true },
        service: {
          id: true,
          approveBy: { id: true, email: true, name: true },
          serviceAccess: { id: true, employee: { id: true, email: true, name: true } },
        },
      },
    })
    if (!supplierService) return

    const type = enumEmailType.TEMP02
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: supplierService.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const supplier = supplierService.__supplier__
    const service = supplierService.__service__
    const approve = service.__approveBy__
    const lstEmployee = service.__serviceAccess__.map((c) => c.__employee__)
    const employee = lstEmployee[0]

    const domainObj = await apeAuthApiHelper.getDomain(supplierService.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/supplier-capacity">link</a>`
    if (approve?.email) {
      const toAddresses = approve.email
      const ccAddresses = employee?.email
      const bccAddresses = ''

      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [
        approve.name,
        supplier.name,
        employee?.name,
        link,
      ])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: supplierService.companyId,
        },
      })

      // gửi notify trang admin
      let lstEmployeeId = [approve.id]
      if (employee) lstEmployeeId.push(employee.id)
      // distinct
      lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
      for (const employeeId of lstEmployeeId) {
        await this.employeeNotifyRepo.createEmployeeNotify(supplierService.companyId, employeeId, subject, convertHtml || '')
      }
    }
  }

  // public async GuiNccNguoiDuyetDuyetDangKy(supplierServiceId: string, username: string, password: string) {
  //   const supplierService: any = await this.supplierServiceRepo.findOne({
  //     where: { id: supplierServiceId },
  //     relations: { supplier: true, service: { approveBy: true } },
  //     select: {
  //       id: true,
  //       companyId: true,
  //       supplier: { id: true, email: true, name: true },
  //       service: {
  //         id: true,
  //         approveBy: { id: true, email: true },
  //       },
  //     },
  //   })
  //   if (!supplierService) return

  //   const type = enumEmailType.TEMP03
  //   let html = type.default
  //   let subject = type.name
  //   const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: supplierService.companyId, isDeleted: false } })
  //   if (template) {
  //     html = template.description
  //     subject = template.name
  //   }

  //   const supplier = supplierService.__supplier__
  //   const service = supplierService.__service__
  //   const approve = service.__approveBy__

  //   const toAddresses = supplier.email
  //   const ccAddresses = ''
  //   const bccAddresses = approve?.email

  //   if (!supplier.email) return

  //   const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, username, password])
  //   const body_text = convertHtml
  //   const body_html = convertHtml
  //   this.sqsService.sendMessage({
  //     type: enumData.SQSMessageType.Email,
  //     data: {
  //       toAddresses,
  //       subject,
  //       ccAddresses,
  //       bccAddresses,
  //       body_text,
  //       body_html,
  //       type: type.code,
  //       companyId: supplierService.companyId,
  //     },
  //   })

  //   // gửi notify trang client
  //   await this.supplierNotifyRepo.createSuplierNotify(supplierService.companyId, supplier.id, subject, convertHtml || '', '')
  // }

  public async GuiNccNguoiDuyetDuyetDangKy2(supplierId: string, username: string, password: string) {
    const supplier: any = await this.supplierRepo.findOne({
      where: { id: supplierId },
    })
    if (!supplier) return

    const type = enumEmailType.TEMP03
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: supplier.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    // const service = supplier.__service__
    // const approve = service.__approveBy__

    const toAddresses = supplier.email
    const ccAddresses = ''
    const bccAddresses = ''

    if (!supplier.email) return

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, username, password])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: supplier.companyId,
      },
    })

    // gửi notify trang client
    await this.supplierNotifyRepo.createSuplierNotify(supplier.companyId, supplier.id, subject, convertHtml || '', '')
  }

  /** 3/ Mẫu email gửi Doanh nghiệp khi người duyệt đã duyệt và thông tin tài khoản */
  public async GuiNccNguoiDuyetDuyetDangKy(supplierServiceId: string, username: string, password: string) {
    const supplierService: any = await this.supplierServiceRepo.findOne({
      where: { id: supplierServiceId },
      relations: { supplier: true, service: { approveBy: true } },
      select: {
        id: true,
        companyId: true,
        supplier: { id: true, email: true, name: true },
        service: {
          id: true,
          approveBy: { id: true, email: true },
        },
      },
    })
    if (!supplierService) return

    const type = enumEmailType.TEMP03
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: supplierService.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const supplier = supplierService.__supplier__
    const service = supplierService.__service__
    const approve = service.__approveBy__

    const toAddresses = supplier.email
    const ccAddresses = ''
    const bccAddresses = approve?.email

    if (!supplier.email) return

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, username, password])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: supplierService.companyId,
      },
    })

    // gửi notify trang client
    await this.supplierNotifyRepo.createSuplierNotify(supplierService.companyId, supplier.id, subject, convertHtml || '', '')
  }

  /** 4/ Mẫu email gửi nhân viên MPO khi người duyệt đã duyệt */
  public async GuiMpoNguoiDuyetDuyetDangKy(supplierServiceId: string) {
    const supplierService: any = await this.supplierServiceRepo.findOne({
      where: { id: supplierServiceId },
      relations: { supplier: true, service: { approveBy: true, serviceAccess: { employee: true } } },
      select: {
        id: true,
        companyId: true,
        supplier: { id: true, name: true },
        service: {
          id: true,
          approveBy: { id: true, name: true, email: true },
          serviceAccess: { id: true, employee: { id: true, name: true, email: true } },
        },
      },
    })
    if (!supplierService) return

    const type = enumEmailType.TEMP04
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: supplierService.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const supplier = supplierService.__supplier__
    const service = supplierService.__service__
    const approve = service.__approveBy__
    const lstEmployee = service.__serviceAccess__.map((c) => c.__employee__)
    let employee = null
    if (lstEmployee.length > 0) employee = lstEmployee[0]

    if (approve.email) {
      const toAddresses = employee?.email
      const ccAddresses = approve.email
      const bccAddresses = ''

      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [employee?.name, supplier.name, approve.name])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: supplierService.companyId,
        },
      })

      // gửi notify trang admin
      let lstEmployeeId = [approve.id]
      if (employee) lstEmployeeId.push(employee.id)
      // distinct
      lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
      for (const employeeId of lstEmployeeId) {
        await this.employeeNotifyRepo.createEmployeeNotify(supplierService.companyId, employeeId, subject, convertHtml || '')
      }
    }
  }

  /** 5/ Mẫu email thông báo người phụ trách reject */
  public async GuiNccMPOTuChoiDangKy(note: string, supplierServiceId: string) {
    const supplierService: any = await this.supplierServiceRepo.findOne({
      where: { id: supplierServiceId },
      relations: { supplier: true, service: { approveBy: true } },
      select: {
        id: true,
        companyId: true,
        supplier: { id: true, email: true, name: true },
        service: {
          id: true,
          approveBy: { id: true, email: true },
        },
      },
    })
    if (!supplierService) return

    const type = enumEmailType.TEMP57
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: supplierService.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const supplier = supplierService.__supplier__
    const service = supplierService.__service__
    const approve = service.__approveBy__
    if (!supplier.email) return

    const toAddresses = supplier.email
    const ccAddresses = approve.email
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, note])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: supplierService.companyId,
      },
    })

    // gửi notify trang client
    await this.supplierNotifyRepo.createSuplierNotify(supplierService.companyId, supplier.id, subject, convertHtml || '', '')

    // gửi notify trang admin
    const lstEmployeeId = [approve.id]
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(supplierService.companyId, employeeId, subject, convertHtml || '')
    }
  }

  /** 6/ Mẫu email thông báo người duyệt reject */
  public async GuiMpoNguoiDuyetTuChoiDangKy(note: string, supplierServiceId: string) {
    const supplierService: any = await this.supplierServiceRepo.findOne({
      where: { id: supplierServiceId },
      relations: { supplier: true, service: { approveBy: true, serviceAccess: { employee: true } } },
      select: {
        id: true,
        companyId: true,
        supplier: { id: true, name: true },
        service: {
          id: true,
          approveBy: { id: true, name: true, email: true },
          serviceAccess: { id: true, employee: { id: true, name: true, email: true } },
        },
      },
    })
    if (!supplierService) return

    const type = enumEmailType.TEMP58
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: supplierService.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const supplier = supplierService.__supplier__
    const service = supplierService.__service__
    const approve = service.__approveBy__
    const lstEmployee = service.__serviceAccess__.map((c) => c.__employee__)
    let employee = null
    if (lstEmployee.length > 0) employee = lstEmployee[0]

    if (employee && employee.email) {
      const toAddresses = employee.email
      const ccAddresses = approve.email
      const bccAddresses = ''

      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, approve.name, note])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: supplierService.companyId,
        },
      })

      // gửi notify trang client
      await this.supplierNotifyRepo.createSuplierNotify(supplierService.companyId, supplier.id, subject, convertHtml || '', '')

      // gửi notify trang admin
      let lstEmployeeId = [approve.id, employee.id]
      // distinct
      lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
      for (const employeeId of lstEmployeeId) {
        await this.employeeNotifyRepo.createEmployeeNotify(supplierService.companyId, employeeId, subject, convertHtml || '')
      }
    }
  }

  //#endregion

  //#region QT2

  /** 7/ Mẫu email gửi nhân viên pháp lý, các thành viên ban thẩm định về yêu cầu thẩm định nhà cung cấp */
  public async GuiThongBaoThamDinhNcc(supplierExpertiseId: string) {
    const supplierExpertise: any = await this.supplierExpertiseRepo.findOne({
      where: { id: supplierExpertiseId },
      relations: {
        supplier: true,
        approvedLaw: true,
        members: { employee: true },
        service: { approveBy: true },
      },
      select: {
        id: true,
        companyId: true,
        note: true,

        supplier: { id: true, name: true },
        approvedLaw: { id: true, email: true },
        members: { id: true, employee: { id: true, email: true } },
        service: { id: true, approveBy: { id: true, email: true } },
      },
    })
    if (!supplierExpertise) return

    const type = enumEmailType.TEMP07
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: supplierExpertise.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const supplier = supplierExpertise.__supplier__
    const approvedLaw = supplierExpertise.__approvedLaw__
    const lstMember = supplierExpertise.__members__.map((c) => c.__employee__)
    const lstEmailMember = lstMember.map((c) => c.email)
    let lstEmployeeId = lstMember.map((c) => c.id)

    const service = supplierExpertise.__service__
    const approve = service.__approveBy__

    const domainObj = await apeAuthApiHelper.getDomain(supplierExpertise.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/supplier-expertise">link</a>`

    let toAddresses = '',
      ccAddresses = ''
    lstEmployeeId.push(approve.id)
    if (approvedLaw) {
      toAddresses = approvedLaw.email
      ccAddresses = lstEmailMember.toString() + ',' + approve.email
      lstEmployeeId.push(approvedLaw.id)
    } else {
      toAddresses = approve.email
      ccAddresses = lstEmailMember.toString()
    }

    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, link, supplierExpertise.note || ''])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: supplierExpertise.companyId,
      },
    })

    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(supplierExpertise.companyId, employeeId, subject, convertHtml || '')
    }
  }

  /** 8/ Mẫu email gửi các thành viên ban thẩm định, người yêu cầu thẩm định về kết quả thẩm định các thông tin pháp lý */
  public async GuiKetQuaThamDinhPhapLyNcc(supplierExpertiseId: string) {
    const supplierExpertise: any = await this.supplierExpertiseRepo.findOne({
      where: { id: supplierExpertiseId },
      relations: {
        supplier: true,
        members: { employee: true },
        service: { approveBy: true },
      },
      select: {
        id: true,
        companyId: true,

        supplier: { name: true },
        members: { id: true, employee: { id: true, email: true } },
        service: { id: true, approveBy: { id: true, email: true } },
      },
    })
    if (!supplierExpertise) return

    const type = enumEmailType.TEMP08
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: supplierExpertise.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const supplier = supplierExpertise.__supplier__
    const lstMember = supplierExpertise.__members__.map((c) => c.__employee__)
    const lstEmailMember = lstMember.map((c) => c.email)
    let lstEmployeeId = lstMember.map((c) => c.id)

    const service = supplierExpertise.__service__
    const approve = service.__approveBy__
    if (!approve?.email) return

    const domainObj = await apeAuthApiHelper.getDomain(supplierExpertise.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/supplier-expertise">link</a>`

    const toAddresses = approve.email
    const ccAddresses = lstEmailMember.toString()
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: supplierExpertise.companyId,
      },
    })

    lstEmployeeId.push(approve.id)
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(supplierExpertise.companyId, employeeId, subject, convertHtml || '')
    }
  }

  /** 9/ Mẫu email gửi nhà cung cấp kết quả thẩm định thông tin pháp lý khi có yêu cầu điều chỉnh, bổ sung */
  public async GuiNccBoSungPhapLy(supplierExpertiseId: string) {
    const supplierExpertise: any = await this.supplierExpertiseRepo.findOne({
      where: { id: supplierExpertiseId },
      relations: { supplier: true, service: { approveBy: true } },
      select: {
        id: true,
        companyId: true,
        supplier: { id: true, email: true, name: true },
        service: { id: true, approveBy: { id: true, email: true } },
      },
    })
    if (!supplierExpertise) return

    const type = enumEmailType.TEMP09
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: supplierExpertise.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const supplier = supplierExpertise.__supplier__
    const service = supplierExpertise.__service__
    const approve = service.__approveBy__

    const domainObj = await apeAuthApiHelper.getDomain(supplierExpertise.companyId)
    const link = `<a href="${domainObj?.clientUrl}/supplier-info">link</a>`
    var lstField = [
      {
        title: 'Mã số doanh nghiệp',
        field: 'code',
        type: 'String',
        required: true,
        value: undefined,
      },
      {
        title: 'Tên doanh nghiệp',
        field: 'name',
        type: 'String',
        required: true,
        value: undefined,
      },
      {
        title: 'Mô tả về doanh nghiệp',
        field: 'description',
        type: 'String',
        required: true,
        value: undefined,
      },
      {
        title: 'Tên giao dịch',
        field: 'dealName',
        type: 'String',
        required: true,
        value: undefined,
      },
      {
        title: 'Địa chỉ trụ sở',
        field: 'address',
        type: 'StringChooseAddess',
        required: true,
        value: undefined,
      },
      {
        title: 'Địa chỉ giao dịch',
        field: 'dealAddress',
        type: 'StringChooseAddess',
        required: true,
        value: undefined,
      },
      {
        title: 'Giấy phép kinh doanh/Mã số thuế',
        field: 'fileMST',
        type: 'File',
        required: true,
        value: undefined,
      },
      {
        title: 'Người đại diện pháp luật',
        field: 'represen',
        type: 'String',
        required: true,
        value: undefined,
      },
      {
        title: 'Tên giám đốc',
        field: 'chief',
        type: 'String',
        required: true,
        value: undefined,
      },
      {
        title: 'Số tài khoản ngân hàng',
        field: 'bankNumber',
        type: 'String',
        required: true,
        value: undefined,
      },
      {
        title: 'Tên ngân hàng',
        field: 'bankname',
        type: 'String',
        required: true,
        value: undefined,
      },
      {
        title: 'Tên chi nhánh ngân hàng',
        field: 'bankBrand',
        type: 'String',
        required: true,
        value: undefined,
      },
      {
        title: 'File thông báo mở tài khoản/mẫu 08',
        field: 'fileAccount',
        type: 'File',
        value: undefined,
      },
      {
        title: 'Người liên hệ',
        field: 'contactName',
        type: 'String',
        required: true,
        value: undefined,
      },
      {
        title: 'Email',
        field: 'email',
        type: 'Email',
        required: true,
        value: undefined,
      },
      {
        title: 'Điện thoại',
        field: 'phone',
        type: 'String',
        required: true,
        value: undefined,
      },
      {
        title: 'Năm thành lập công ty',
        field: 'createYear',
        type: 'StringYear',
        required: true,
        value: undefined,
      },
      {
        title: 'Vốn điều lệ (tỷ đồng)',
        field: 'capital',
        type: 'Number',
        required: true,
        value: undefined,
      },
      {
        title: 'Tài sản cố định (tỷ đồng)',
        field: 'assets',
        type: 'Number',
        required: true,
        value: undefined,
      },
      {
        title: 'File hóa đơn mẫu/phiếu thu/biên lai',
        field: 'fileBill',
        type: 'File',
        required: true,
        value: undefined,
      },
      {
        title: 'File thông tin phát hành hóa đơn',
        field: 'fileInfoBill',
        type: 'File',
        value: undefined,
      },
    ]
    var lawDetails = await supplierExpertise.supplierExpertiseLawDetails
    var lawDetail = null
    if (lawDetails?.length > 0) lawDetail = lawDetails[0] as any
    for (const col of lstField) {
      if (lawDetail) col.value = lawDetail[col.field]
    }
    lstField = lstField.filter((c) => c.value != null && c.value != undefined)
    let detail = ``
    for (const field of lstField) {
      detail += `- ${field.title}: ${field.value}<br>`
    }

    const toAddresses = supplier.email
    const ccAddresses = approve.email
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, detail, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: supplierExpertise.companyId,
      },
    })

    // gửi notify trang client
    await this.supplierNotifyRepo.createSuplierNotify(supplierExpertise.companyId, supplier.id, subject, convertHtml || '', '')
    await this.employeeNotifyRepo.createEmployeeNotify(supplierExpertise.companyId, approve.id, subject, convertHtml || '')
  }

  /** 10/ Mẫu email gửi các thành viên ban thẩm định kết quả thẩm định về thông tin năng lực và yêu cầu điều chỉnh */
  public async GuiKetQuaThamDinhNangLucNcc(supplierExpertiseId: string) {
    const supplierExpertise: any = await this.supplierExpertiseRepo.findOne({
      where: { id: supplierExpertiseId },
      relations: { supplier: true, members: { employee: true }, service: { approveBy: true } },
      select: {
        id: true,
        companyId: true,
        supplier: { name: true },
        service: { id: true, approveBy: { id: true, email: true } },
        members: { id: true, employee: { id: true, email: true } },
      },
    })
    if (!supplierExpertise) return

    const type = enumEmailType.TEMP10
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: supplierExpertise.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const supplier = supplierExpertise.__supplier__
    const lstMember = supplierExpertise.__members__.map((c) => c.__employee__)
    const lstEmailMember = lstMember.map((c) => c.email)
    let lstEmployeeId = lstMember.map((c) => c.id)

    const service = supplierExpertise.__service__
    const approve = service.__approveBy__

    const domainObj = await apeAuthApiHelper.getDomain(supplierExpertise.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/supplier-expertise">link</a>`

    const toAddresses = approve.email
    const ccAddresses = lstEmailMember.toString()
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: supplierExpertise.companyId,
      },
    })

    lstEmployeeId.push(approve.id)
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(supplierExpertise.companyId, employeeId, subject, convertHtml || '')
    }
  }

  /** 11/ Mẫu email gửi nhà cung cấp kết quả thẩm định thông tin năng lực và yêu cầu điều chỉnh, bổ sung nếu có */
  public async GuiNccBoSungNangLuc(supplierExpertiseId: string) {
    const supplierExpertise: any = await this.supplierExpertiseRepo.findOne({
      where: { id: supplierExpertiseId },
      relations: {
        supplier: true,
        members: { employee: true },
        service: { approveBy: true },
        supplierExpertiseDetails: {
          supplierCapacity: { supplierCapacityListDetails: true },
          supplierExpertiseYearDetails: true,
        },
      },
      select: {
        id: true,
        companyId: true,
        supplier: { id: true, email: true, name: true },
        service: { id: true, approveBy: { id: true, email: true } },
        members: { id: true, employee: { id: true, email: true } },
        supplierExpertiseDetails: true,
      },
      order: {
        supplierExpertiseDetails: { createdAt: 'ASC' },
      },
    })
    if (!supplierExpertise) return

    const type = enumEmailType.TEMP11
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: supplierExpertise.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const supplier = supplierExpertise.__supplier__
    const lstMember = supplierExpertise.__members__.map((c) => c.__employee__)
    const lstEmailMember = lstMember.map((c) => c.email)
    let lstEmployeeId = lstMember.map((c) => c.id)

    const service = supplierExpertise.__service__
    const approve = service.__approveBy__

    const domainObj = await apeAuthApiHelper.getDomain(supplierExpertise.companyId)
    const link = `<a href="${domainObj?.clientUrl}/supplier-info">link</a>`
    var lstExpertiseCapacity = supplierExpertise.__supplierExpertiseDetails__
    const lstField = lstExpertiseCapacity.filter((c) => (c.value != null && c.value != undefined) || c.__supplierExpertiseYearDetails__?.length > 0)
    let detail = ``
    for (const field of lstField) {
      const capacity = field.__supplierCapacity__
      if (!capacity.isChangeByYear) {
        if (capacity.type == enumData.DataType.List.code) {
          if (field.value) {
            const objChoose = capacity.__supplierCapacityListDetails__.find((c) => c.id == field.value)
            if (objChoose) detail += `- ${capacity.name}: ${objChoose.name}<br>`
          }
        } else detail += `- ${capacity.name}: ${field.value}<br>`
      } else {
        if (!field.__supplierExpertiseYearDetails__?.length) continue

        detail += `- ${capacity.name}:<br>`
        for (const yearDetail of field.__supplierExpertiseYearDetails__) {
          detail += `++ ${yearDetail.year}: ${yearDetail.value}<br>`
        }
      }
    }

    const toAddresses = supplier.email
    const ccAddresses = lstEmailMember.toString() + ',' + approve.email
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, detail, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: supplierExpertise.companyId,
      },
    })

    // gửi notify trang client
    await this.supplierNotifyRepo.createSuplierNotify(supplierExpertise.companyId, supplier.id, subject || '', convertHtml || '', link)

    lstEmployeeId.push(approve.id)
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(supplierExpertise.companyId, employeeId, subject, convertHtml || '')
    }
  }

  //#endregion

  //#region QT3 tạo thầu

  /** Hàm dùng chung */
  private getEmploy(lstMember: any[], type: string) {
    const member = lstMember.find((c) => c.type === type)
    return member?.__employee__
  }

  /** Hàm dùng chung */
  private getListEmploy(lstMember: any[], type: string = '') {
    if (type != '') lstMember = lstMember.filter((c) => c.type === type)
    return lstMember.map((c) => c.__employee__)
  }

  /** 12/  Mẫu email gửi cho người duyệt mua hàng, nhân viên phụ trách kỹ thuật, người duyệt kỹ thuật  */
  public async ThongBaoDaTaoGoiThau(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP12
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const lstEmployee = this.getListEmploy(lstMember)
    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    let lstEmployeeId = lstEmployee.map((c) => c.id)
    const lstEmail = lstEmployee.map((c) => c.email).filter((value, index, self) => self.indexOf(value) === index)

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-new">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpoLeader.email
    let ccAddresses = lstEmail.filter((c) => c != mpoLeader.email).toString()
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 13/ Mẫu email gửi cho kỹ thuật, người duyệt kỹ thuật, cc nhân viên phụ trách mua hàng, ng duyệt mua hàng khi nhân viên phụ trách kỹ thuật đã thiết lập xong yêu cầu kỹ thuật cho gói thầu  */
  public async GuiTechLeadDuyetKyThuat(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP13
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    const tech = this.getEmploy(lstMember, enumData.BidRuleType.Tech.code)
    if (!tech) return

    const techLeader = this.getEmploy(lstMember, enumData.BidRuleType.TechLeader.code)
    if (!techLeader || !techLeader.email) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-new">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = techLeader.email
    const ccAddresses = mpo.email + ',' + mpoLeader.email + ',' + tech.email
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [techLeader.name, bid.name, tech.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = [mpo.id, mpoLeader.id, tech.id, techLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 14/ Mẫu email gửi cho nhân viên phụ trách mua hàng, người duyệt mua hàng, nhân viên phụ trách kỹ thuật, ng duyệt trách kỹ thuật và các thành viên trong hội đồng khi yêu cầu kỹ thuật cho gói thầu đã được duyệt  */
  public async ThongBaoDaDuyetKyThuat(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP14
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const lstEmployee = this.getListEmploy(lstMember)

    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return
    const techLeader = this.getEmploy(lstMember, enumData.BidRuleType.TechLeader.code)
    if (!techLeader) return
    const lstEmail = lstEmployee.map((c) => c.email).filter((value, index, self) => self.indexOf(value) === index)
    let lstEmployeeId = lstEmployee.map((c) => c.id)

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-new">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpo.email
    const ccAddresses = lstEmail.filter((c) => c !== mpo.email).toString()
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [bid.name, techLeader.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 15/ Mẫu email gửi cho nhân viên phụ trách kỹ thuật, cc người phụ trách mua hàng và mpoleader và techleader khi yêu cầu kỹ thuật bị reject  */
  public async GuiTechTuChoiKyThuat(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        noteTechLeader: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP15
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    const tech = this.getEmploy(lstMember, enumData.BidRuleType.Tech.code)
    if (!tech || !tech.email) return

    const techLeader = this.getEmploy(lstMember, enumData.BidRuleType.TechLeader.code)
    if (!techLeader) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-new">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = tech.email
    const ccAddresses = mpo.email + ',' + mpoLeader.email + ',' + techLeader.email
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [tech.name, bid.name, bid.noteTechLeader, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = [mpo.id, mpoLeader.id, tech.id, techLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 16/ Mẫu email gửi cho người duyệt mua hàng và MPO khi nhân viên phụ trách mua hàng đã thiết lập xong bảng chào giá, cơ cấu giá, điều kiện thương mại, và chọn doanh nghiệp tham gia cho gói thầu  */
  public async GuiMpoLeadDuyetGia(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP16
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-new">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpoLeader.email
    const ccAddresses = mpo.email
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [mpoLeader.name, bid.name, mpo.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = [mpo.id, mpoLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 17/ Mẫu email thông báo gửi cho nhân viên phụ trách mua hàng, các thành viên trong hội đồng và MPOLeader khi người duyệt đã duyệt bảng chào giá, cơ cấu giá, điều kiện thương mại, doanh nghiệp tham gia cho gói thầu  */
  public async ThongBaoDaDuyetGia(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP17
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const lstEmployee = this.getListEmploy(lstMember)
    const lstEmail = lstEmployee.map((c) => c.email).filter((value, index, self) => self.indexOf(value) === index)
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-new">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpo.email
    const ccAddresses = lstEmail.filter((c) => c !== mpo.email).toString()
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [bid.name, mpoLeader.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = lstEmployee.map((c) => c.id)
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 18/ Mẫu email gửi cho nhân viên phụ trách mua hàng, MPOLeader khi người duyệt reject bảng chào giá, cơ cấu giá, điều kiện thương mại cho gói thầu */
  public async GuiMpoTuChoiGia(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        noteMPOLeader: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP18
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-new">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpo.email
    const ccAddresses = mpoLeader.email
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [mpo.name, bid.name, bid.noteMPOLeader, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = [mpo.id, mpoLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 19/ Mẫu email gửi Doanh nghiệp, cc nhân viên phụ trách mua hàng, MPOLeader về thư thông báo mời thầu và yêu cầu xác nhận tham gia thầu */
  public async GuiNccThongBaoMoiThau(user: UserDto, bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true }, bidSuppliers: { supplier: true } },
      select: {
        id: true,
        name: true,
        acceptEndDate: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
        bidSuppliers: { id: true, supplier: { id: true, email: true, name: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP19
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.clientUrl}/detail?bidid=${bidId}">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const acceptEndDate_text = coreHelper.dateToString(bid.acceptEndDate)
    let hasSendEmailInviteBid = false
    for (const bidSupplier of bid.__bidSuppliers__) {
      const supplier = bidSupplier.__supplier__

      const toAddresses = supplier.email
      const ccAddresses = mpo.email + ',' + mpoLeader.email
      const bccAddresses = ''

      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [
        supplier.name,
        bid.name,
        acceptEndDate_text,
        link,
      ])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject: subject_text,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: bid.companyId,
        },
      })

      // gửi notify trang client
      await this.supplierNotifyRepo.createSuplierNotify(bid.companyId, supplier.id, subject_text || '', convertHtml || '', link)

      let lstEmployeeId = [mpo.id, mpoLeader.id]
      // distinct
      lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
      for (const employeeId of lstEmployeeId) {
        await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
      }

      hasSendEmailInviteBid = true
    }

    await this.bidRepo.update(bidId, { hasSendEmailInviteBid, updatedBy: user.id })
  }

  /** 20/ Mẫu email đến các Doanh nghiệp, cc nhân viên phụ trách mua hàng, MPOLeader chưa xác nhận tham gia thầu */
  public async GuiNccChuaXacNhanThamGiaThau(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true }, bidSuppliers: { supplier: true } },
      select: {
        id: true,
        name: true,
        submitEndDate: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
        bidSuppliers: { id: true, status: true, supplier: { id: true, email: true, name: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP20
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const lstBidSupplier = bid.__bidSuppliers__.filter((c) => c.status === enumData.BidSupplierStatus.DaThongBaoMoiThau.code)

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.clientUrl}/detail?bidid=${bidId}">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const submitEndDate_text = coreHelper.dateToString(bid.submitEndDate)
    for (const bidSupplier of lstBidSupplier) {
      const supplier = bidSupplier.__supplier__

      const toAddresses = supplier.email
      const ccAddresses = mpo.email + ',' + mpoLeader.email
      const bccAddresses = ''

      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [
        supplier.name,
        bid.name,
        submitEndDate_text,
        link,
      ])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject: subject_text,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: bid.companyId,
        },
      })

      // gửi notify trang client
      await this.supplierNotifyRepo.createSuplierNotify(bid.companyId, supplier.id, subject_text || '', convertHtml || '', link)

      let lstEmployeeId = [mpo.id, mpoLeader.id]
      // distinct
      lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
      for (const employeeId of lstEmployeeId) {
        await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
      }
    }
  }

  /** 21.1/ Mẫu email gửi cho nhân viên phụ trách mua hàng khi có nhà cung cấp xác nhận tham gia thầu */
  public async GuiMpoNccXacNhanThamGiaThau(bidId: string, supplierId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP21_1
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const supplier = await this.supplierRepo.findOne({ where: { id: supplierId }, select: { name: true } })
    if (!supplier) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])

    const toAddresses = mpo.email
    const ccAddresses = mpoLeader.email
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [mpo.name, supplier.name, bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = [mpo.id, mpoLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 21.2/ Mẫu email gửi cho nhân viên phụ trách mua hàng khi có nhà cung cấp từ chối tham gia thầu */
  public async GuiMpoNccTuChoiThamGiaThau(bidId: string, supplierId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP21_2
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const supplier = await this.supplierRepo.findOne({ where: { id: supplierId }, select: { name: true } })
    if (!supplier) return

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])

    const toAddresses = mpo.email
    const ccAddresses = mpoLeader.email
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [mpo.name, supplier.name, bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = [mpo.id, mpoLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 22/ Mẫu email gửi cho nhân viên phụ trách, MPOLeader mua hàng khi có nhà cung cấp nộp xong hồ sơ thầu */
  public async GuiMpoNccNopHoSoThau(bidId: string, supplierId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP22
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const supplier = await this.supplierRepo.findOne({ where: { id: supplierId }, select: { name: true } })
    if (!supplier) return

    const lstMember = bid.__employeeAccess__
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])

    const toAddresses = mpo.email
    const ccAddresses = mpoLeader.email
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [mpo.name, supplier.name, bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = [mpo.id, mpoLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 23/ Mẫu email gửi các nhân viên phụ trách mua hàng, người duyệt mua hàng, nhân viên phụ trách kỹ thuật, người duyệt kỹ thuật và các thành viên trong hội đồng thầu thông báo sắp tới hạn mở thầu */
  public async ThongBaoSapMoThau(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        startBidDate: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP23
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const lstEmployee = this.getListEmploy(lstMember)
    const lstEmail = lstEmployee.map((c) => c.email).filter((value, index, self) => self.indexOf(value) === index)
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const startBidDate_text = coreHelper.dateToString(bid.startBidDate)
    const toAddresses = mpo.email
    const ccAddresses = lstEmail.filter((c) => c !== mpo.email).toString()
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [bid.name, startBidDate_text, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = lstEmployee.map((c) => c.id)
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeWarningRepo.createEmployeeWarning(employeeId, subject, convertHtml || '')
    }
  }

  /** 24/ Mẫu email gửi các nhân viên phụ trách mua hàng, người duyệt mua hàng, nhân viên phụ trách kỹ thuật, người duyệt kỹ thuật và các thành viên trong hội đồng thầu thông báo đã quá hạn mở thầu 1 ngày */
  public async ThongBaoQuaHanMoThau(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP24
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const lstEmployee = this.getListEmploy(lstMember)
    const lstEmail = lstEmployee.map((c) => c.email).filter((value, index, self) => self.indexOf(value) === index)
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpo.email
    const ccAddresses = lstEmail.filter((c) => c !== mpo.email).toString()
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    // Cảnh báo hệ thống
    let lstEmployeeId = lstEmployee.map((c) => c.id)
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeWarningRepo.createEmployeeWarning(employeeId, subject, convertHtml || '')
    }
  }

  /** 25/ Mẫu email gửi cho người duyệt kỹ thuật, cc nhân viên phụ trách mua hàng, techleader, mpoleader khi nhân viên phụ trách kỹ thuật đã hoàn thành xong việc đánh giá yêu cầu kỹ thuật cho các nhà cung cấp */
  public async GuiTechLeadDuyetDanhGiaKyThuat(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP25
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    const tech = this.getEmploy(lstMember, enumData.BidRuleType.Tech.code)
    if (!tech) return

    const techLeader = this.getEmploy(lstMember, enumData.BidRuleType.TechLeader.code)
    if (!techLeader || !techLeader.email) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = techLeader.email
    const ccAddresses = mpo.email + ',' + mpoLeader.email
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [techLeader.name, tech.name, bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = [mpo.id, mpoLeader.id, tech.id, techLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 26/ Mẫu email gửi cho nhân viên phụ trách mua hàng, người duyệt mua hàng, nhân viên phụ trách kỹ thuật và các thành viên trong hội đồng khi kết quả đánh giá yêu cầu kỹ thuật cho gói thầu đã được duyệt */
  public async ThongBaoDuyetDanhGiaKyThuat(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP26
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const lstEmployee = this.getListEmploy(lstMember)
    const lstEmail = lstEmployee.map((c) => c.email).filter((value, index, self) => self.indexOf(value) === index)
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const techLeader = this.getEmploy(lstMember, enumData.BidRuleType.TechLeader.code)
    if (!techLeader) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpo.email
    const ccAddresses = lstEmail.filter((c) => c !== mpo.email).toString()
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [techLeader.name, bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = lstEmployee.map((c) => c.id)
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 27/ Mẫu email gửi cho nhân viên phụ trách kỹ thuật, nhân viên phụ trách mua hàng khi đánh giá yêu cầu kỹ thuật bị reject */
  public async GuiTechTuChoiDanhGiaKyThuat(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP27
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    const tech = this.getEmploy(lstMember, enumData.BidRuleType.Tech.code)
    if (!tech || !tech.email) return

    const techLeader = this.getEmploy(lstMember, enumData.BidRuleType.TechLeader.code)
    if (!techLeader) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = tech.email
    const ccAddresses = mpo.email + ',' + mpoLeader.email + ',' + techLeader.email
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [tech.name, techLeader.name, bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = [mpo.id, mpoLeader.id, tech.id, techLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 28/ Mẫu email gửi cho người duyệt mua hàng khi nhân viên phụ trách mua hàng đã hoàn thành xong việc đánh giá kết quả chào giá, cơ cấu giá, điều kiện thương mại cho các nhà cung cấp */
  public async GuiMpoLeadDuyetDanhGiaGia(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP28
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpoLeader.email
    const ccAddresses = mpo.email
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [mpoLeader.name, mpo.name, bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = [mpo.id, mpoLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 29/ Mẫu email gửi cho nhân viên phụ trách mua hàng, mpoleader, các thành viên trong hội đồng khi kết quả đánh giá kết quả chào giá, cơ cấu giá, điều kiện thương mại cho gói thầu đã được duyệt */
  public async ThongBaoDuyetDanhGiaGia(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP29
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const lstEmployee = this.getListEmploy(lstMember)
    const lstEmail = lstEmployee.map((c) => c.email).filter((value, index, self) => self.indexOf(value) === index)
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpo.email
    const ccAddresses = lstEmail.filter((c) => c !== mpo.email).toString()
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [mpoLeader.name, bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = lstEmployee.map((c) => c.id)
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 30/ Mẫu email gửi cho nhân viên phụ trách mua hàng, mpoleader khi đánh giá kết quả chào giá, cơ cấu giá, điều kiện thương mại bị reject */
  public async GuiMpoTuChoiDanhGiaGia(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP30
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpo.email
    const ccAddresses = mpoLeader.email
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [mpo.name, mpoLeader.name, bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = [mpo.id, mpoLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  //#endregion

  //#region Đàm phán

  /** 31/ Mẫu email gửi Doanh nghiệp, cc nhân viên phụ trách mua hàng, mpoleader về việc tham gia đàm phán giá*/
  public async ThongBaoNccThamGiaDamPhan(bidDealId: string) {
    const bidDeal: any = await this.bidDealRepo.findOne({
      where: { id: bidDealId },
      relations: { bidDealSupplier: { supplier: true } },
      select: {
        id: true,
        bidId: true,
        endDate: true,
        companyId: true,
        bidDealSupplier: { id: true, supplier: { id: true, email: true, name: true } },
      },
    })
    if (!bidDeal) return

    const type = enumEmailType.TEMP31
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bidDeal.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstSupplier = bidDeal.__bidDealSupplier__.map((c) => c.__supplier__)
    if (lstSupplier.length == 0) return

    const dtNow = new Date()
    const startDate = coreHelper.dateToString(dtNow)
    const endDate = coreHelper.dateToString(bidDeal.endDate)

    const bid: any = await this.bidRepo.findOne({
      where: { id: bidDeal.bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const domainObj = await apeAuthApiHelper.getDomain(bidDeal.companyId)
    for (const supplier of lstSupplier) {
      const toAddresses = supplier.email
      const ccAddresses = mpo.email + ',' + mpoLeader.email
      const bccAddresses = ''

      const link = `<a href="${domainObj?.clientUrl}/bid-deal?biddealid=${bidDealId}">link</a>`
      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [
        supplier.name,
        bid.name,
        startDate,
        endDate,
        link,
      ])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject: subject_text,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: bidDeal.companyId,
        },
      })

      // gửi notify trang client
      await this.supplierNotifyRepo.createSuplierNotify(bidDeal.companyId, supplier.id, subject_text || '', convertHtml || '', link)

      let lstEmployeeId = [mpo.id, mpoLeader.id]
      // distinct
      lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
      for (const employeeId of lstEmployeeId) {
        await this.employeeNotifyRepo.createEmployeeNotify(bidDeal.companyId, employeeId, subject_text || '', convertHtml || '')
      }
    }
  }

  /** 32/ Mẫu email gửi nhân viên phụ trách mua hàng, mpoleader về việc Doanh nghiệp xác nhận giữ nguyên giá hoặc gửi giá đàm phán mới */
  public async GuiMpoNCCXacNhanDamPhan(bidDealId: string, supplierId: string) {
    const bidDealSupplier: any = await this.bidRepo.manager.getRepository(BidDealSupplierEntity).findOne({
      where: {
        bidDealId: bidDealId,
        supplierId: supplierId,
        isDeleted: false,
      },
      relations: { supplier: true, bidDeal: true },
      select: {
        id: true,
        companyId: true,
        status: true,
        supplier: { id: true, name: true },
        bidDeal: { id: true, bidId: true },
      },
    })
    if (!bidDealSupplier) return

    const type = enumEmailType.TEMP32
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bidDealSupplier.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const bidDeal = bidDealSupplier.__bidDeal__
    const supplier = bidDealSupplier.__supplier__

    const bid: any = await this.bidRepo.findOne({
      where: { id: bidDeal.bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const toAddresses = mpo.email
    const ccAddresses = mpoLeader.email
    const bccAddresses = ''
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    let typeDeal = ''
    if (bidDealSupplier.status === enumData.BidDealSupplierStatus.DaTuChoi.code) {
      typeDeal = 'Từ chối giữ nguyên giá'
    }
    if (bidDealSupplier.status === enumData.BidDealSupplierStatus.DaGuiGiaMoi.code) {
      typeDeal = 'Gửi đàm phán giá mới'
    }

    const domainObj = await apeAuthApiHelper.getDomain(bidDealSupplier.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`
    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, bid.name, typeDeal, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bidDealSupplier.companyId,
      },
    })

    let lstEmployeeId = [mpo.id, mpoLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bidDealSupplier.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  //#endregion

  //#region Đấu giá

  /** 33/ Mẫu email gửi Doanh nghiệp, cc nhân viên phụ trách mua hàng, mpoleader về việc tham gia đấu giá */
  public async ThongBaoNccThamGiaDauGia(bidAuctionId: string) {
    const bidAuction: any = await this.bidAuctionRepo.findOne({
      where: { id: bidAuctionId },
      relations: { bidAuctionSupplier: { supplier: true } },
      select: { id: true, bidId: true, companyId: true, bidAuctionSupplier: { id: true, supplier: { id: true, email: true, name: true } } },
    })
    if (!bidAuction) return

    const type = enumEmailType.TEMP33
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bidAuction.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstSupplier = bidAuction.__bidAuctionSupplier__.map((c) => c.__supplier__)
    if (lstSupplier.length == 0) return

    const bid: any = await this.bidRepo.findOne({
      where: { id: bidAuction.bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const domainObj = await apeAuthApiHelper.getDomain(bidAuction.companyId)
    for (const sup of lstSupplier) {
      const toAddresses = sup.email
      const ccAddresses = mpo.email + ',' + mpoLeader.email
      const bccAddresses = ''
      const link = `<a href="${domainObj?.clientUrl}/bid-auction?bidauctionid=${bidAuctionId}">link</a>`
      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [sup.name, bid.name, link])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject: subject_text,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: bidAuction.companyId,
        },
      })

      // gửi notify trang client
      await this.supplierNotifyRepo.createSuplierNotify(bidAuction.companyId, sup.id, subject_text || '', convertHtml || '', link)

      let lstEmployeeId = [mpo.id, mpoLeader.id]
      // distinct
      lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
      for (const employeeId of lstEmployeeId) {
        await this.employeeNotifyRepo.createEmployeeNotify(bidAuction.companyId, employeeId, subject_text || '', convertHtml || '')
      }
    }
  }
  //#endregion

  //#region Chọn Doanh nghiệp trúng thầu

  /** 34/ Mẫu email gửi người duyệt mua hàng, mpo duyệt kết quả đấu thầu */
  public async GuiMpoDuyetKetQuaDauThau(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP34
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const subject_text = coreHelper.stringInject(subject, [bid.name])

    const toAddresses = mpoLeader.email
    const ccAddresses = mpo.email
    const bccAddresses = ''
    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`
    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [mpoLeader.name, bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = [mpo.id, mpoLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 35/ Mẫu email gửi nhân viên phụ trách mua hàng, nhân viên phụ trách kỹ thuật, người duyệt kỹ thuật, mpoleader và các thành viên trong hội đồng khi kết quả đấu thầu đã được duyệt */
  public async ThongBaoKetQuaDauThauDuocDuyet(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP35
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const lstEmployee = this.getListEmploy(lstMember)
    const lstEmail = lstEmployee.map((c) => c.email).filter((value, index, self) => self.indexOf(value) === index)
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpo.email
    const ccAddresses = lstEmail.filter((c) => c !== mpo.email).toString()
    const bccAddresses = ''
    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [bid.name, mpoLeader.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = lstEmployee.map((c) => c.id)
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 36/ Mẫu email gửi nhân viên phụ trách mua hàng, mpoleader khi kết quả đấu thầu bị reject */
  public async ThongBaoKetQuaDauThauBiTuChoi(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        noteCloseBidMPOLeader: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP36
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpo.email
    const ccAddresses = mpoLeader.email
    const bccAddresses = ''
    const reason = bid.noteCloseBidMPOLeader
    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [mpo.name, bid.name, mpoLeader.name, reason, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    let lstEmployeeId = [mpo.id, mpoLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 37/ Mẫu email gửi thông báo trúng thầu đến Doanh nghiệp trúng thầu, cc nhân viên phụ trách mua hàng, mpoleader, tech, techleader */
  public async ThongBaoTrungThau(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP37
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    const tech = this.getEmploy(lstMember, enumData.BidRuleType.Tech.code)
    if (!tech) return

    const techLeader = this.getEmploy(lstMember, enumData.BidRuleType.TechLeader.code)
    if (!techLeader) return

    const lstBidSupplierSuccess: any[] = await this.bidSupplierRepo.find({
      where: {
        bid: { parentId: bidId, isDeleted: false },
        isSuccessBid: true,
        isDeleted: false,
      },
      relations: { bid: { service: true } },
      // select: { id: true, supplierId: true, bid: { id: true, quantityItem: true, service: { code: true, name: true } } },
    })
    if (lstBidSupplierSuccess.length == 0) return

    const lstSupplierId = lstBidSupplierSuccess.map((c) => c.supplierId).filter((value, index, self) => self.indexOf(value) === index)

    const lstSupplier = await this.supplierRepo.find({
      where: { id: In(lstSupplierId) },
      select: { id: true, email: true, name: true },
    })

    for (const supplier of lstSupplier) {
      const lstItem = lstBidSupplierSuccess.filter((c) => c.supplierId == supplier.id)
      // const lstItem = lstBidSupplierSuccess[0]

      let itemStr = ``
      for (const [i2, item] of lstItem.entries()) {
        let bid: any
        if (item.__bid__) {
          if (item.__bid__.parentId) {
            bid = await this.bidRepo.findOne({ where: { id: item.__bid__.parentId }, relations: { bidItems: true } })
          } else {
            bid = await this.bidRepo.findOne({ where: { id: item.__bid__.id }, relations: { bidItems: true } })
          }
          if (bid)
            for (const item of bid.__bidItems__)
              itemStr += `(${i2 + 1}) Tên Item: ${item.productName} &nbsp;&nbsp;&nbsp; Số lượng: ${item.quantityItem}<br>`
        }
      }
      const subject_text = coreHelper.stringInject(subject, [bid.name])
      const toAddresses = supplier.email
      const ccAddresses = mpo.email + ',' + mpoLeader.email + ',' + tech.email + ',' + techLeader.email
      const bccAddresses = ''

      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, bid.name, itemStr])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject: subject_text,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: bid.companyId,
        },
      })

      // gửi notify trang client
      await this.supplierNotifyRepo.createSuplierNotify(bid.companyId, supplier.id, subject_text || '', convertHtml || '', '')

      let lstEmployeeId = [mpo.id, mpoLeader.id, tech.id, techLeader.id]
      // distinct
      lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
      for (const employeeId of lstEmployeeId) {
        await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
      }
    }
  }

  /** 38/ Mẫu email gửi thư cảm ơn đến Doanh nghiệp tham gia thầu nhưng không trúng thầu, cc nhân viên phụ trách mua hàng, mpoleader, tech, techleader */
  public async ThuCamOnThamGiaThau(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP38
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    const tech = this.getEmploy(lstMember, enumData.BidRuleType.Tech.code)
    if (!tech) return

    const techLeader = this.getEmploy(lstMember, enumData.BidRuleType.TechLeader.code)
    if (!techLeader) return

    const lstBidSupplierItem: any[] = await this.bidSupplierRepo.find({
      where: {
        bid: { parentId: bidId, isDeleted: false },
        isDeleted: false,
      },
      select: {
        id: true,
        supplierId: true,
        isSuccessBid: true,
      },
    })
    if (lstBidSupplierItem.length == 0) return
    // Lấy các NCC có fail ít nhất là 1 item
    const lstSupplierId = lstBidSupplierItem
      .filter((c) => !c.isSuccessBid)
      .map((c) => c.supplierId)
      .filter((value, index, self) => self.indexOf(value) === index)

    const lstSupplier = await this.supplierRepo.find({
      where: { id: In(lstSupplierId) },
      select: { id: true, email: true, name: true },
    })

    for (const supplier of lstSupplier) {
      // nếu có trúng thầu 1 item thì bỏ qua, không cần gửi thư cảm ơn
      const isSuccessBid = lstBidSupplierItem.some((c) => c.supplierId == supplier.id && c.isSuccessBid)
      if (isSuccessBid) continue

      const subject_text = coreHelper.stringInject(subject, [bid.name])
      const toAddresses = supplier.email
      const ccAddresses = mpo.email + ',' + mpoLeader.email + ',' + tech.email + ',' + techLeader.email
      const bccAddresses = ''

      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, supplier.name, bid.name])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject: subject_text,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: bid.companyId,
        },
      })

      // gửi notify trang client
      this.supplierNotifyRepo.createSuplierNotify(bid.companyId, supplier.id, subject_text || '', convertHtml || '', '')

      let lstEmployeeId = [mpo.id, mpoLeader.id, tech.id, techLeader.id]
      // distinct
      lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
      for (const employeeId of lstEmployeeId) {
        this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
      }
    }
  }

  //#endregion

  //#region Các email bsung sau

  /** 39/ Mẫu email thông báo mở thầu đến các thành viên liên quan gói thầu */
  public async ThongBaoMoThau(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP39
    let html = type.default
    let subject = type.name

    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const dtNow = coreHelper.newDateTZ()
    let dateString = `${dtNow.getHours()} giờ ${dtNow.getMinutes()}, ngày ${dtNow.getDate()} tháng ${dtNow.getMonth() + 1} năm ${dtNow.getFullYear()}`
    const lstMember = bid.__employeeAccess__ || []
    const lstEmployee = this.getListEmploy(lstMember)
    const lstEmail = lstEmployee.map((c) => c.email).filter((value, index, self) => self.indexOf(value) === index)
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    if (!lstEmail) return

    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpo.email
    const ccAddresses = lstEmail.filter((c) => c !== mpo.email).toString()
    const bccAddresses = ''
    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [bid.name, dateString, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    const lstEmployeeId = lstEmployee.map((c) => c.id).filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 40/ Mẫu email thông báo mở thầu đến các Doanh nghiệp */
  public async ThongBaoMoThauNCC(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { bidSuppliers: { supplier: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        bidSuppliers: { id: true, supplier: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP40
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const dtNow = coreHelper.newDateTZ()
    let dateString = `${dtNow.getHours()} giờ ${dtNow.getMinutes()}, ngày ${dtNow.getDate()} tháng ${dtNow.getMonth() + 1} năm ${dtNow.getFullYear()}`
    const lstBidSupplier = bid.__bidSuppliers__ || []
    for (const bidSupplier of lstBidSupplier) {
      const supplier = bidSupplier.__supplier__
      const subject_text = coreHelper.stringInject(subject, [bid.name])
      const toAddresses = supplier.email
      const ccAddresses = ''
      const bccAddresses = ''
      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, bid.name, dateString])
      const body_text = convertHtml
      const body_html = convertHtml

      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject: subject_text,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: bid.companyId,
        },
      })

      // gửi notify trang client
      await this.supplierNotifyRepo.createSuplierNotify(bid.companyId, supplier.id, subject_text || '', convertHtml || '', '')
    }
  }

  /** 41/ Mẫu email gửi các nhân viên phụ trách mua hàng, người duyệt mua hàng, Người tạo kỹ thuật và duyệt kỹ thuật còn 03 ngày là đến hạn duyệt hồ sơ kỹ thuật */
  public async ThongBaoHanDuyetKyThuat3Ngay(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP41
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    const tech = this.getEmploy(lstMember, enumData.BidRuleType.Tech.code)
    if (!tech) return

    const techLeader = this.getEmploy(lstMember, enumData.BidRuleType.TechLeader.code)
    if (!techLeader) return

    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpo.email
    const ccAddresses = mpoLeader.email + ',' + tech.email + ',' + techLeader.email
    const bccAddresses = ''
    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    // Cảnh báo admin
    let lstEmployeeId = [mpo.id, mpoLeader.id, tech.id, techLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeWarningRepo.createEmployeeWarning(employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 42/ Mẫu email gửi các nhân viên phụ trách mua hàng, người duyệt mua hàng, Người tạo kỹ thuật và duyệt kỹ thuật Đã Hết thời hạn duyệt hồ sơ kỹ thuật 01 ngày */
  public async ThongBaoHetHanDuyetKyThuat1Ngay(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP42
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    const tech = this.getEmploy(lstMember, enumData.BidRuleType.Tech.code)
    if (!tech) return

    const techLeader = this.getEmploy(lstMember, enumData.BidRuleType.TechLeader.code)
    if (!techLeader) return

    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpo.email
    const ccAddresses = mpoLeader.email + ',' + tech.email + ',' + techLeader.email
    const bccAddresses = ''
    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    // Cảnh báo admin
    let lstEmployeeId = [mpo.id, mpoLeader.id, tech.id, techLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeWarningRepo.createEmployeeWarning(employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 43/ Mẫu email gửi các nhân viên phụ trách mua hàng, người duyệt mua hàng còn 03 ngày là đến hạn duyệt hồ sơ chào giá */
  public async ThongBaoHanDuyetChaoGia3Ngay(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP43
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    const tech = this.getEmploy(lstMember, enumData.BidRuleType.Tech.code)
    if (!tech) return

    const techLeader = this.getEmploy(lstMember, enumData.BidRuleType.TechLeader.code)
    if (!techLeader) return

    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpo.email
    const ccAddresses = mpoLeader.email + ',' + tech.email + ',' + techLeader.email
    const bccAddresses = ''
    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    // Cảnh báo admin
    let lstEmployeeId = [mpo.id, mpoLeader.id, tech.id, techLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeWarningRepo.createEmployeeWarning(employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 44/ Mẫu email gửi các nhân viên phụ trách mua hàng, người duyệt mua hàng Đã Hết thời hạn duyệt hồ sơ chào giá 01 ngày */
  public async ThongBaoHetHanDuyetChaoGia1Ngay(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP44
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    const tech = this.getEmploy(lstMember, enumData.BidRuleType.Tech.code)
    if (!tech) return

    const techLeader = this.getEmploy(lstMember, enumData.BidRuleType.TechLeader.code)
    if (!techLeader) return

    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpo.email
    const ccAddresses = mpoLeader.email + ',' + tech.email + ',' + techLeader.email
    const bccAddresses = ''
    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    // Cảnh báo admin
    let lstEmployeeId = [mpo.id, mpoLeader.id, tech.id, techLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeWarningRepo.createEmployeeWarning(employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 45/ Mẫu email gửi cho ng duyệt mua hàng khi nhân viên phụ trách mua hàng yêu cầu duyệt gói thầu tạm  */
  public async GuiMPOLeadDuyetGoiThauTam(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP45
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-new">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpoLeader.email
    const ccAddresses = ''
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [mpoLeader.name, bid.name, mpo.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    // Thông báo admin
    let lstEmployeeId = [mpoLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 46/ Mẫu email gửi cho nhân viên phụ trách mua hàng khi người duyệt mua hàng yêu cầu kiểm tra lại gói thầu tạm  */
  public async GuiMPOKiemTraLaiGoiThauTam(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP46
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-new">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpo.email
    const ccAddresses = ''
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [mpo.name, bid.name, mpoLeader.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    // Thông báo admin
    let lstEmployeeId = [mpo.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 47/ Mẫu email gửi cho Doanh nghiệp yêu cầu nộp chào giá khi có chỉnh sửa bảng giá */
  public async GuiNCCNopLaiChaoGia(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        resetPriceEndDate: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP47
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const lstBidSupplier: any[] = await this.bidSupplierRepo.find({
      where: { bidId, statusResetPrice: enumData.BidSupplierResetPriceStatus.YeuCauBoSung.code },
      relations: { supplier: true },
      select: {
        id: true,
        supplier: { id: true, email: true, name: true },
      },
    })
    if (lstBidSupplier.length == 0) return

    const lstSupplier = lstBidSupplier.map((c) => c.__supplier__)
    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.clientUrl}/bid-price?bidid=${bidId}">link</a>`
    const dtNow = new Date()
    const startDate = coreHelper.dateToString(dtNow)
    const endDate = coreHelper.dateToString(bid.resetPriceEndDate)

    for (const supplier of lstSupplier) {
      const subject_text = coreHelper.stringInject(subject, [bid.name])
      const toAddresses = supplier.email
      const ccAddresses = ''
      const bccAddresses = mpo.email + ',' + mpoLeader.email

      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [
        supplier.name,
        bid.name,
        link,
        startDate,
        endDate,
      ])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject: subject_text,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: bid.companyId,
        },
      })

      // gửi notify trang client
      await this.supplierNotifyRepo.createSuplierNotify(bid.companyId, supplier.id, subject_text || '', convertHtml || '', '')

      // Thông báo admin
      let lstEmployeeId = [mpo.id, mpoLeader.id]
      // distinct
      lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
      for (const employeeId of lstEmployeeId) {
        await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
      }
    }
  }

  /** 48/ Mẫu email gửi cho nhân viên phụ trách, MPOLeader mua hàng khi có nhà cung cấp nộp chào giá bổ sung */
  public async GuiMpoNccNopChaoGiaBosung(bidId: string, supplierId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP48
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const supplier = await this.supplierRepo.findOne({ where: { id: supplierId }, select: { name: true } })
    if (!supplier) return

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
    const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`
    const subject_text = coreHelper.stringInject(subject, [bid.name])

    const toAddresses = mpo.email
    const ccAddresses = mpoLeader.email
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [mpo.name, supplier.name, bid.name, link])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })

    // Thông báo admin
    let lstEmployeeId = [mpo.id, mpoLeader.id]
    // distinct
    lstEmployeeId = lstEmployeeId.filter((value, index, self) => self.indexOf(value) === index)
    for (const employeeId of lstEmployeeId) {
      await this.employeeNotifyRepo.createEmployeeNotify(bid.companyId, employeeId, subject_text || '', convertHtml || '')
    }
  }

  /** 49/ Mẫu email gửi người duyệt mua hàng duyệt Doanh nghiệp thắng thầu */
  public async GuiMpoDuyetNCCThangThau(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const type = enumEmailType.TEMP49
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    if (mpoLeader.email) {
      const subject_text = coreHelper.stringInject(subject, [bid.name])

      const toAddresses = mpoLeader.email
      const ccAddresses = mpo.email
      const bccAddresses = ''
      const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
      const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`
      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [mpoLeader.name, bid.name, link])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject: subject_text,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: bid.companyId,
        },
      })
    }
  }

  /** 50/ Mẫu email gửi nhân viên phụ trách mua hàng, nhân viên phụ trách kỹ thuật, người duyệt kỹ thuật và các thành viên trong hội đồng khi Doanh nghiệp thắng thầu đã được duyệt */
  public async ThongBaoNCCThangThauDuocDuyet(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const tech = this.getEmploy(lstMember, enumData.BidRuleType.Tech.code)
    if (!tech) return

    const techLeader = this.getEmploy(lstMember, enumData.BidRuleType.TechLeader.code)
    if (!techLeader) return

    const type = enumEmailType.TEMP50
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }
    const listemail = await this.bidEmployeeAccessRepo.getListEmailByBidId(bidId)

    if (tech.email && techLeader.email && mpo.email && mpoLeader.email) {
      const subject_text = coreHelper.stringInject(subject, [bid.name])
      const toAddresses = mpo.email
      const ccAddresses = listemail.filter((c) => c !== mpo.email).toString() + ',' + tech.email + ',' + techLeader.email + ',' + mpoLeader.email
      const bccAddresses = ''
      const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
      const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`

      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [bid.name, mpoLeader.name, link])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject: subject_text,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: bid.companyId,
        },
      })
    }
  }

  /** 51/ Mẫu email gửi nhân viên phụ trách mua hàng khi Doanh nghiệp thắng thầu bị reject */
  public async ThongBaoNCCThangThauBiTuChoi(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP51
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    if (mpo.email && mpoLeader.email) {
      const subject_text = coreHelper.stringInject(subject, [bid.name])
      const toAddresses = mpo.email
      const ccAddresses = mpoLeader.email
      const bccAddresses = ''
      const reason = bid.noteCloseBidMPOLeader
      const domainObj = await apeAuthApiHelper.getDomain(bid.companyId)
      const link = `<a href="${domainObj?.adminUrl}/bid/bid-rate">link</a>`

      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [
        mpo.name,
        bid.name,
        mpoLeader.name,
        reason,
        link,
      ])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject: subject_text,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: bid.companyId,
        },
      })
    }
  }

  /** 52/ Mẫu email gửi cho ng duyệt mua hàng khi nhân viên phụ trách mua hàng yêu cầu hủy gói thầu tạm  */
  public async GuiMPOLeadHuyGoiThau(bidId: string) {
    const bid = await this.bidRepo.findOne({
      where: { id: bidId },
      select: {
        id: true,
        name: true,
        status: true,
        isDeleted: true,
        isRequestDelete: true,
        companyId: true,
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP52
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    // Không gửi mail khi không có yêu cầu hủy gói thầu, gói thầu tạm và đã bị xóa
    if (!bid.isRequestDelete && bid.status == enumData.BidStatus.GoiThauTam.code && bid.isDeleted) return

    const mpo = await this.bidEmployeeAccessRepo.getMPO(bidId)
    const mpoLeader = await this.bidEmployeeAccessRepo.getMPOLead(bidId)

    const subject_text = coreHelper.stringInject(subject, [bid.name])
    const toAddresses = mpoLeader.email
    const ccAddresses = ''
    const bccAddresses = ''

    const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [mpoLeader.name, bid.name, mpo.name])
    const body_text = convertHtml
    const body_html = convertHtml
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject: subject_text,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: type.code,
        companyId: bid.companyId,
      },
    })
  }

  /** 53/ Mẫu email thông báo hủy gói thầu đến các Doanh nghiệp */
  public async ThongBaoHuyGoiThauNCC(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId, isDeleted: false },
      relations: { bidSuppliers: { supplier: true } },
      select: {
        id: true,
        name: true,
        companyId: true,
        bidSuppliers: { id: true, supplier: { id: true, email: true, name: true } },
      },
    })
    if (!bid) return

    const type = enumEmailType.TEMP53
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: bid.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    const lstBidSupplier = bid.__bidSuppliers__ || []
    if (lstBidSupplier.length == 0) return

    const dtNow = new Date()
    let dateString = `${dtNow.getHours()} giờ ${dtNow.getMinutes()}, ngày ${dtNow.getDate()} tháng ${dtNow.getMonth()} năm ${dtNow.getFullYear()}`
    for (const bidSupplier of lstBidSupplier) {
      const supplier = bidSupplier.__supplier__
      const subject_text = coreHelper.stringInject(subject, [bid.name])
      const toAddresses = supplier.email
      const ccAddresses = ''
      const bccAddresses = ''
      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, bid.name, dateString])
      const body_text = convertHtml
      const body_html = convertHtml

      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject: subject_text,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: bid.companyId,
        },
      })

      await this.supplierNotifyRepo.createSuplierNotify(bid.companyId, supplier.id, subject_text || '', convertHtml || '', '')
    }
  }
  //#endregion

  //#region Đấu giá

  /** 54/ Mẫu email thông báo NCC tham gia đấu giá */
  public async ThongBaoNccThamGiaDauGiaNhanh(auctionId: string, lstSupplierId: string[] = []) {
    const auction: any = await this.auctionRepo.findOne({
      where: { id: auctionId },
      relations: { auctionSuppliers: { supplier: true } },
      select: {
        id: true,
        title: true,
        dateStart: true,
        dateEnd: true,
        companyId: true,
        createdBy: true,
        auctionSuppliers: { id: true, supplier: { id: true, email: true, name: true } },
      },
    })
    if (!auction) return

    const type = enumEmailType.TEMP54
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: auction.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    let lstSupplier = auction.__auctionSuppliers__.map((c) => c.__supplier__)
    if (lstSupplierId?.length > 0) {
      lstSupplier = lstSupplier.filter((c) => lstSupplierId.includes(c.id))
    }
    if (lstSupplier.length == 0) return

    const startDate = coreHelper.dateToString(auction.dateStart)
    const endDate = coreHelper.dateToString(auction.dateEnd)

    const emp = await this.employeeRepo.findOne({ where: { userId: auction.createdBy } })

    const subject_text = coreHelper.stringInject(subject, [auction.title])
    const domainObj = await apeAuthApiHelper.getDomain(auction.companyId)
    for (const supplier of lstSupplier) {
      const toAddresses = supplier.email
      const ccAddresses = emp?.email
      const bccAddresses = ''

      const link = `<a href="${domainObj?.clientUrl}/auction?auctionid=${auctionId}">link</a>`
      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [
        supplier.name,
        auction.title,
        startDate,
        endDate,
        link,
      ])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject: subject_text,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: auction.companyId,
        },
      })

      // gửi notify trang client
      await this.supplierNotifyRepo.createSuplierNotify(auction.companyId, supplier.id, subject_text || '', convertHtml || '', link)
    }
  }

  /** 55/ Mẫu email thông báo NCC cập nhật đấu giá */
  public async ThongBaoNccCapNhatDauGiaNhanh(auctionId: string) {
    const auction: any = await this.auctionRepo.findOne({
      where: { id: auctionId },
      relations: { auctionSuppliers: { supplier: true } },
      select: {
        id: true,
        title: true,
        companyId: true,
        createdBy: true,
        auctionSuppliers: { id: true, supplier: { id: true, email: true, name: true } },
      },
    })
    if (!auction) return

    const type = enumEmailType.TEMP55
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: auction.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    let lstSupplier = auction.__auctionSuppliers__.map((c) => c.__supplier__)
    if (lstSupplier.length == 0) return

    const emp = await this.employeeRepo.findOne({ where: { userId: auction.createdBy } })

    const subject_text = coreHelper.stringInject(subject, [auction.title])
    const domainObj = await apeAuthApiHelper.getDomain(auction.companyId)
    for (const supplier of lstSupplier) {
      const toAddresses = supplier.email
      const ccAddresses = emp?.email
      const bccAddresses = ''

      const link = `<a href="${domainObj?.clientUrl}/auction?auctionid=${auctionId}">link</a>`
      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, auction.title, link])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject: subject_text,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: auction.companyId,
        },
      })

      // gửi notify trang client
      await this.supplierNotifyRepo.createSuplierNotify(auction.companyId, supplier.id, subject_text || '', convertHtml || '', link)
    }
  }

  /** 56/ Mẫu email thông báo NCC cập nhật đấu giá */
  public async ThongBaoNccHuyDauGiaNhanh(auctionId: string) {
    const auction: any = await this.auctionRepo.findOne({
      where: { id: auctionId },
      relations: { auctionSuppliers: { supplier: true } },
      select: {
        id: true,
        title: true,
        companyId: true,
        createdBy: true,
        auctionSuppliers: { id: true, supplier: { id: true, email: true, name: true } },
      },
    })
    if (!auction) return

    const type = enumEmailType.TEMP56
    let html = type.default
    let subject = type.name
    const template = await this.emailTemplateRepo.findOne({ where: { code: type.code, companyId: auction.companyId, isDeleted: false } })
    if (template) {
      html = template.description
      subject = template.name
    }

    let lstSupplier = auction.__auctionSuppliers__.map((c) => c.__supplier__)
    if (lstSupplier.length == 0) return

    const emp = await this.employeeRepo.findOne({ where: { userId: auction.createdBy } })

    const subject_text = coreHelper.stringInject(subject, [auction.title])
    for (const supplier of lstSupplier) {
      const toAddresses = supplier.email
      const ccAddresses = emp?.email
      const bccAddresses = ''

      const convertHtml = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [supplier.name, auction.title])
      const body_text = convertHtml
      const body_html = convertHtml
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject: subject_text,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: type.code,
          companyId: auction.companyId,
        },
      })

      // gửi notify trang client
      await this.supplierNotifyRepo.createSuplierNotify(auction.companyId, supplier.id, subject_text || '', convertHtml || '', '')
    }
  }
  //#endregion

  //#region Danh sách các hàm email

  /** MPO đề xuất Doanh nghiệp thay đổi data - Kết thúc thẩm định Doanh nghiệp */
  public async finishEvaluation(supplierId: string, supplierExpertiseId: string) {
    const supplier = await this.supplierRepo.findOne({
      where: { id: supplierId },
      select: { id: true, code: true, name: true, companyId: true },
    })
    if (!supplier || !supplier.email) return

    const toAddresses = supplier.email
    const ccAddresses = ''
    const bccAddresses = ''
    const subject = 'APE Bidding Online'
    let body_text = `MPO thẩm định thành công.
    ---------------------------------
    `
    let body_html = `<html>
    <head></head>
    <body>
      <h1>MPO thẩm định thành công</h1>
      <h1>Cty: ${supplier.name} - Mã: ${supplier.code}</h1>
    </body>
    </html>`

    const expertise = await this.supplierExpertiseRepo.findOne({
      where: { id: supplierExpertiseId },
      select: { id: true, status: true },
    })
    if (expertise?.status === enumData.SupplierExpertiseStatus.KhongDuyetQT2.code) {
      const domainObj = await apeAuthApiHelper.getDomain(supplier.companyId)

      body_text = `MPO đề xuất chỉnh sửa thông tin!
      ---------------------------------
      `
      body_html = `<html>
      <head></head>
      <body>
        <h1>MPO đề xuất chỉnh sửa thông tin</h1>
        <h1>Cty: ${supplier.name} - Mã: ${supplier.code}</h1>
        <a href="${domainObj?.clientUrl}/evaluation/${supplierExpertiseId}">Ấn vào link</a>
      </body>
      </html>`
    }

    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: enumData.EmailTemplate.FinishEvaluation.code,
        companyId: supplier.companyId,
      },
    })

    await this.supplierNotifyRepo.createSuplierNotify(supplier.companyId, supplier.id, subject || '', body_html || '', '')
  }

  /** Doanh nghiệp đồng ý thay đổi theo yêu cầu của MPO */
  public async supplierAcceptChangeData(supplierId: string, supplierExpertiseId: string) {}

  /** Doanh nghiệp gửi mã xác nhận */
  public async sendConfirmCode(data: { companyId: string; email: string; confirmCode: string }) {
    const toAddresses = data.email

    // CC and BCC addresses. If your account is in the sandbox, these
    // addresses have to be verified. To specify multiple addresses, separate
    // each address with a comma.
    const ccAddresses = data.email
    const bccAddresses = data.email

    // The subject line of the email
    const subject = 'Mã xác nhận'

    // The email body for recipients with non-HTML email clients.
    const body_text = `Confirm Code
---------------------------------
Mã code cho tài khoản của bạn là: ${data.confirmCode}
`

    // The body of the email for recipients whose email clients support HTML content.
    const body_html = `<html>
<head></head>
<body>
  <h1>Confirm Code</h1>
  <p>Mã code cho tài khoản của bạn là: ${data.confirmCode}
  </p>
</body>
</html>`

    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: enumData.EmailTemplate.SendConfirmCode.code,
        companyId: data.companyId,
      },
    })
  }

  /** Chỉnh sửa thông tin chung của gói thầu thành công - gửi email cho MPO Leader và cc mọi người*/
  public async updateBidSuccess(bidId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        code: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader || !mpoLeader.email) return

    const tech = this.getEmploy(lstMember, enumData.BidRuleType.Tech.code)
    if (!tech) return

    const techLeader = this.getEmploy(lstMember, enumData.BidRuleType.TechLeader.code)
    if (!techLeader) return

    let lstEmail = await this.bidEmployeeAccessRepo.getListEmailByBidId(bidId)
    lstEmail.push(tech.email, techLeader.email, mpo.email)
    lstEmail = lstEmail.filter((value, index, self) => self.indexOf(value) === index)

    const toAddresses = mpoLeader.email
    const ccAddresses = lstEmail.filter((c) => c !== mpoLeader.email).toString()
    const bccAddresses = ''
    const subject = 'APE Bidding Online'
    const body_text = `Gói thầu đã được chỉnh sửa thông tin chung.
    ---------------------------------
    `
    const body_html = `<html>
    <head></head>
    <body>
      <h1>Gói thầu đã được chỉnh sửa thông tin chung</h1>
      <p>Mã gói thầu: ${bid.code}
      <p>Tên gói thầu: ${bid.name}
    </body>
    </html>`
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: enumData.EmailTemplate.UpdateBidSuccess.code,
        companyId: bid.companyId,
      },
    })
  }

  /** Thông báo nội bộ và Doanh nghiệp được chọn */
  public async sendEmailBid(data: { bidId: string; lstEmployeeId: string[]; lstSupplierId: string[]; emailContent: string }) {
    const bid = await this.bidRepo.findOne({
      where: { id: data.bidId },
      select: {
        id: true,
        name: true,
        companyId: true,
      },
    })
    if (!bid) return

    const lstEmployee = await this.employeeRepo.find({ where: { id: In(data.lstEmployeeId) }, select: { id: true, email: true } })
    const lstEmail = lstEmployee.map((c) => c.email).filter((value, index, self) => self.indexOf(value) === index)
    if (lstEmail.length == 0) return
    const emailTo = lstEmail[0]

    const lstEmailSupplier = await this.supplierRepo.find({ where: { id: In(data.lstSupplierId) }, select: { id: true, email: true } })
    const lstEmailBcc = lstEmailSupplier.map((c) => c.email).filter((value, index, self) => self.indexOf(value) === index)

    const toAddresses = emailTo
    const ccAddresses = lstEmail.filter((p) => p !== emailTo).toString()
    const bccAddresses = lstEmailBcc.toString()
    const subject = `Thông báo liên quan gói thầu [${bid.name}]`
    const body_text = ''
    const body_html = data.emailContent
    this.sqsService.sendMessage({
      type: enumData.SQSMessageType.Email,
      data: {
        toAddresses,
        subject,
        ccAddresses,
        bccAddresses,
        body_text,
        body_html,
        type: enumData.EmailTemplate.SendEmailBid.code,
        companyId: bid.companyId,
      },
    })
  }

  /** Doanh nghiệp đã đấu thầu */
  public async supplierBidSuccess(bidId: string, supplierId: string) {
    const bid: any = await this.bidRepo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        code: true,
        name: true,
        companyId: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const supplier = await this.supplierRepo.findOne({ where: { id: supplierId }, select: { id: true, code: true, name: true } })
    if (!supplier) return

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    const tech = this.getEmploy(lstMember, enumData.BidRuleType.Tech.code)
    if (!tech) return

    const techLeader = this.getEmploy(lstMember, enumData.BidRuleType.TechLeader.code)
    if (!techLeader) return

    const lstEmail = await this.bidEmployeeAccessRepo.getListEmailByBidId(bidId)

    if (tech.email && techLeader.email && mpo.email && mpoLeader.email) {
      const toAddresses = mpoLeader.email
      const ccAddresses = lstEmail.filter((c) => c !== mpoLeader.email).toString() + ',' + tech.email + ',' + techLeader.email + ',' + mpo.email
      const bccAddresses = ''
      const subject = 'APE Bidding Online'
      const body_text = `Doanh nghiệp đấu thầu thành công!
    ---------------------------------
    `
      const body_html = `<html>
    <head></head>
    <body>
      <h1>Doanh nghiệp đấu thầu thành công</h1>
      <p>Mã gói thầu: ${bid.code}
      <p>Tên gói thầu: ${bid.name}
      <p>Mã Doanh nghiệp: ${supplier.code}
      <p>Tên Doanh nghiệp: ${supplier.name}
    </body>
    </html>`
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: enumData.EmailTemplate.SupplierBidSuccess.code,
          companyId: bid.companyId,
        },
      })
    }
  }

  /** Tạo đàm phán giá thành công */
  public async createBidDealSuccess(bidDealId: string) {
    const bidDeal: any = await this.bidDealRepo.findOne({
      where: { id: bidDealId },
      relations: { bidDealSupplier: { supplier: true } },
      select: {
        id: true,
        bidId: true,
        companyId: true,
        bidDealSupplier: { id: true, supplier: { id: true, email: true } },
      },
    })
    if (!bidDeal) return

    const listSupplier = bidDeal.__bidDealSupplier__.map((c) => c.__supplier__)
    if (listSupplier.length === 0) return

    const bid: any = await this.bidRepo.findOne({
      where: { id: bidDeal.bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        code: true,
        name: true,
        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    const supplierMails = listSupplier.map((p) => p.email)
    const firstSupplier = supplierMails[0]
    const lstEmail = await this.bidEmployeeAccessRepo.getListEmailByBidId(bidDeal.bidId)

    if (mpo.email && mpoLeader.email && firstSupplier) {
      const domainObj = await apeAuthApiHelper.getDomain(bidDeal.companyId)

      const toAddresses = firstSupplier
      const ccAddresses = lstEmail.toString() + ',' + mpoLeader.email + ',' + mpo.email
      const bccAddresses = supplierMails.filter((p) => p !== firstSupplier).toString()
      const subject = 'APE Bidding Online'
      const body_text = `Tạo yêu cầu đàm phán giá thành công!
    ---------------------------------
    `
      const body_html = `<html>
    <head></head>
    <body>
      <h1>Tạo yêu cầu đàm phán giá thành công!</h1>
      <p>Mã gói thầu: ${bid.code}
      <p>Tên gói thầu: ${bid.name}
      <a href="${domainObj?.clientUrl}/bid-deal?biddealid=${bidDealId}">Ấn vào link</a>
    </body>
    </html>`
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: enumData.EmailTemplate.SupplierBidSuccess.code,
          companyId: bidDeal.companyId,
        },
      })
    }
  }

  /** Tạo đấu giá thành công */
  public async createBidAuctionSuccess(bidAuctionId: string) {
    const bidAuction: any = await this.bidAuctionRepo.findOne({
      where: { id: bidAuctionId },
      relations: { bidAuctionSupplier: { supplier: true } },
      select: {
        id: true,
        bidId: true,
        companyId: true,
        bidAuctionSupplier: { id: true, supplier: { id: true, email: true } },
      },
    })
    if (!bidAuction) return

    const listSupplier = bidAuction.__bidAuctionSupplier__.map((c) => c.__supplier__)
    if (listSupplier.length === 0) return

    const bid: any = await this.bidRepo.findOne({
      where: { id: bidAuction.bidId },
      relations: { employeeAccess: { employee: true } },
      select: {
        id: true,
        code: true,
        name: true,

        employeeAccess: { id: true, type: true, employee: { id: true, name: true, email: true } },
      },
    })
    if (!bid) return

    const lstMember = bid.__employeeAccess__ || []
    const mpo = this.getEmploy(lstMember, enumData.BidRuleType.MPO.code)
    if (!mpo || !mpo.email) return

    const mpoLeader = this.getEmploy(lstMember, enumData.BidRuleType.MPOLeader.code)
    if (!mpoLeader) return

    if (listSupplier?.length === 0) return
    const supplierMails = listSupplier.map((p) => p.email)
    const firstSupplier = supplierMails[0]
    const lstEmail = await this.bidEmployeeAccessRepo.getListEmailByBidId(bidAuction.bidId)

    if (mpo.email && mpoLeader.email && lstEmail && lstEmail.length > 0 && firstSupplier) {
      const toAddresses = firstSupplier
      const ccAddresses = lstEmail.filter((c) => c !== mpo.email).toString() + ',' + mpoLeader.email + ',' + mpo.email
      const bccAddresses = supplierMails.filter((p) => p !== firstSupplier).toString()
      const subject = 'APE Bidding Online'
      const body_text = `Tạo yêu cầu đấu giá thành công!
      ---------------------------------
      `
      const body_html = `<html>
      <head></head>
      <body>
        <h1>Tạo yêu cầu đấu giá thành công</h1>
        <p>Mã gói thầu: ${bid.code}
        <p>Tên gói thầu: ${bid.name}
      </body>
      </html>`
      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses,
          subject,
          ccAddresses,
          bccAddresses,
          body_text,
          body_html,
          type: enumData.EmailTemplate.SupplierBidSuccess.code,
          companyId: bidAuction.companyId,
        },
      })
    }
  }

  //#endregion
}
