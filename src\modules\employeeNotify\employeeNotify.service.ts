import { Injectable } from '@nestjs/common'
import { UserDto } from '../../dto'
import { EmployeeNotifyRepository } from '../../repositories'

@Injectable()
export class EmployeeNotifyService {
  constructor(private readonly repo: EmployeeNotifyRepository) {}

  /** Lấy ds thông báo */
  public async loadEmployeeNotify(user: UserDto, data: { take: number }) {
    if (!user.employeeId) return { lstNotify: [], numNotifyNew: 0 }

    const lstNotify = []
    const res1 = await this.repo.findAndCount({
      where: { employeeId: user.employeeId, companyId: user.companyId, isNew: true },
      order: { createdAt: 'DESC' },
      skip: 0,
      take: data.take,
    })
    lstNotify.push(...res1[0])

    // Lấy thêm thông báo đã đọc cho đủ số lượng
    if (res1[1] < data.take) {
      const res2 = await this.repo.findAndCount({
        where: { employeeId: user.employeeId, companyId: user.companyId, isNew: false },
        order: { createdAt: 'DESC' },
        skip: 0,
        take: data.take - res1[1],
      })
      lstNotify.push(...res2[0])
    }

    return {
      lstNotify, //ds thông báo
      numNotifyNew: res1[1], //số thông báo mới
    }
  }

  /** Đọc thông báo */
  public async readEmployeeNotify(user: UserDto, data: { id: string }) {
    await this.repo.update(data.id, { isNew: false, updatedBy: user.id })
  }
}
