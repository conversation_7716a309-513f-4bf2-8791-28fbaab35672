import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { enumProject } from '../../constants'
import { UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { PaymentProgressService } from './paymentProgress.service'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('PaymentProgress')
@Controller('paymentProgress')
export class PaymentProgressController {
  constructor(private readonly service: PaymentProgressService) {}

  @ApiOperation({ summary: 'Danh sách tiến độ thanh toán' })
  @Roles(enumProject.Features.PO_001.code, enumProject.Features.PAYMENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: { contractId?: string; poId?: string }) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Danh sách tiến độ thanh toán' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_list')
  public async findList(@CurrentUser() user: UserDto, @Body() data: { contractId?: string; poId?: string }) {
    return await this.service.find(user, data)
  }
}
