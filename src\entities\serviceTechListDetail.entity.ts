import { BaseEntity } from './base.entity'
import { En<PERSON>ty, Column, ManyToOne, JoinC<PERSON>umn } from 'typeorm'
import { ServiceTechEntity } from './serviceTech.entity'

@Entity('service_tech_list_detail')
export class ServiceTechListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
  })
  value: number

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  serviceTechId: string
  @ManyToOne(() => ServiceTechEntity, (p) => p.serviceTechListDetails)
  @JoinColumn({ name: 'serviceTechId', referencedColumnName: 'id' })
  serviceTech: Promise<ServiceTechEntity>
}
