import { Body, Controller, Get, Param, Post, Req, UseGuards } from '@nestjs/common'
import { enumProject } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { SupplierCreateCustomPriceItemDto, SupplierCreatePriceItemDto, SupplierCreateTechItemDto, SupplierCreateTradeItemDto } from '../bid/dto'
import { BidAuctionSupplierSaveDto } from '../bidAuction/dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { ClientWebService } from './clientWeb.service'
import { BidDealSupplierSaveDto } from '../bidDeal/dto'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { POUpdateDeliveryDateDto, POUpdateStatusDto } from '../po/dto'
import { PaymentProgressUpdateOrder } from '../paymentProgress/dto/paymentProgressUpdateOrder.dto'
import { InvoiceSuggestCreate } from '../invoiceSuggest/dto'

@ApiBearerAuth()
@ApiTags('Client')
@Controller('client_web')
export class ClientWebController {
  constructor(private service: ClientWebService) { }

  @ApiOperation({ summary: 'Kiểm tra quyền xem kết quả thẩm định' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('check_is_your_expertise')
  public async checkIsYourExpertise(@CurrentUser() user: UserDto, @Body() data: { supplierExpertiseId: string }) {
    return await this.service.checkIsYourExpertise(user, data)
  }

  @ApiOperation({ summary: 'Lấy dữ liệu mà MPO đề xuất cho nhà cung cấp' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('get_data_suggest')
  public async getDataSuggest(@CurrentUser() user: UserDto, @Body() data: { supplierExpertiseId: string }) {
    return await this.service.getDataSuggest(user, data)
  }

  @ApiOperation({ summary: 'Đồng ý cập nhật lại thông tin theo nội dung thẩm định' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('supplier_accept_change_data')
  public async supplierAcceptChangeData(@CurrentUser() user: UserDto, @Body() data: { supplierExpertiseId: string }) {
    return await this.service.supplierAcceptChangeData(user, data)
  }

  @ApiOperation({ summary: 'Danh sách gói thầu chưa đăng nhập' })
  @Post('paginationHomePage')
  public async paginationHomePage(@Req() req: Request, @Body() data: PaginationDto) {
    return await this.service.paginationHomePage(req, data)
  }

  @ApiOperation({ summary: 'Danh sách gói thầu sau khi đăng nhập' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('paginationHomePageHadToken')
  public async paginationHomePageHadToken(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.paginationHomePageHadToken(user, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách Item của gói thầu khi nộp hồ sơ thầu' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('loadDataBidding')
  public async loadDataBidding(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadDataBidding(data, user)
  }

  @ApiOperation({ summary: 'Chi tiết gói thầu' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('bidDetailHadToken')
  public async bidDetailHadToken(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.bidDetailHadToken(data, user)
  }

  @ApiOperation({ summary: 'Kiểm tra hiển thị nút xác nhận tham gia gói thầu' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('isDisplayBtnAcceptBid')
  public async isDisplayBtnAcceptBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.isDisplayBtnAcceptBid(user, data)
  }

  @ApiOperation({ summary: 'Kiểm tra hiển thị nút bổ sung hồ sơ không' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('isDisplayBtnBid')
  public async isDisplayBtnBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.isDisplayBtnBid(user, data)
  }

  @ApiOperation({ summary: 'Xác nhận tham gia gói thầu' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('acceptBid')
  public async acceptBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.acceptBid(user, data)
  }

  @ApiOperation({ summary: 'Từ chối tham gia gói thầu' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('rejectBid')
  public async rejectBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.rejectBid(user, data)
  }

  @ApiOperation({ summary: 'NCC nộp hồ sơ thầu cho Item' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('createBidSupplier')
  public async createBidSupplier(
    @CurrentUser() user: UserDto,
    @Body()
    data: {
      bidId: string
      techInfo: SupplierCreateTechItemDto[]
      tradeInfo: SupplierCreateTradeItemDto[]
      priceInfo: SupplierCreatePriceItemDto[]
      customPriceInfo: SupplierCreateCustomPriceItemDto[]
    },
  ) {
    return await this.service.createBidSupplier(user, data)
  }

  @ApiOperation({ summary: 'Check quyền truy cập gói thầu của Doanh nghiệp' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('checkPermissionLoadDataBid')
  public async checkPermissionLoadDataBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.checkPermissionLoadDataBid(user, data)
  }

  @ApiOperation({ summary: 'Lấy hồ sơ kỹ thuật Item của NCC khi nộp hồ sơ thầu' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('loadDataBidTech')
  public async loadDataBidTech(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadDataBidTech(user, data)
  }

  @ApiOperation({ summary: 'Lấy hồ sơ ĐKTM Item của NCC khi nộp hồ sơ thầu' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('loadDataBidTrade')
  public async loadDataBidTrade(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadDataBidTrade(user, data)
  }

  @ApiOperation({ summary: 'Lấy hồ sơ giá Item của NCC khi nộp hồ sơ thầu' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('loadDataBidPrice')
  public async loadDataBidPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadDataBidPrice(user, data)
  }

  @ApiOperation({ summary: 'Lấy hồ sơ cơ cấu giá Item của NCC khi nộp hồ sơ thầu' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('loadDataBidCustomPrice')
  public async loadDataBidCustomPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadDataBidCustomPrice(user, data)
  }

  @ApiOperation({ summary: 'Lịch sử đấu thầu Doanh nghiệp' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('paginationBidHistory')
  public async paginationBidHistory(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.paginationBidHistory(user, data)
  }

  @ApiOperation({ summary: 'Check quyền nộp giá bổ sung cho gói thầu của Doanh nghiệp' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('checkPermissionJoinResetPrice')
  public async checkPermissionJoinResetPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.checkPermissionJoinResetPrice(user, data)
  }

  @ApiOperation({ summary: 'NCC nộp chào giá hiệu chỉnh' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('supplierSaveResetPrice')
  public async supplierSaveResetPrice(
    @CurrentUser() user: UserDto,
    @Body()
    data: {
      bidId: string
      dataInfo: { filePriceDetail?: string; fileTechDetail?: string }
      priceInfo: SupplierCreatePriceItemDto[]
    },
  ) {
    return await this.service.supplierSaveResetPrice(user, data)
  }

  @ApiOperation({ summary: 'Lịch sử chào giá gói thầu của NCC' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_bid_history_price_client/:bidid')
  public async getBidHistoryPrice(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.service.getBidHistoryPrice(user, bidId)
  }

  //#region BidDeal

  @ApiOperation({ summary: 'Lấy thông tin đàm phán giá của NCC' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_bid_deal_supplier/:biddealid')
  public async getBidDealSupplier(@CurrentUser() user: UserDto, @Param('biddealid') bidDealId: string) {
    return await this.service.getBidDealSupplier(user, bidDealId)
  }

  @ApiOperation({ summary: 'Đề nghị đàm phán giá/ Chấp nhận giá đề nghị/ Lưu' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('accept_bid_deal_supplier')
  public async acceptBidDealSupplier(@CurrentUser() user: UserDto, @Body() data: BidDealSupplierSaveDto) {
    return await this.service.acceptBidDealSupplier(user, data)
  }

  @ApiOperation({ summary: 'Từ chối giá đề nghị' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('reject_bid_deal_supplier')
  public async rejectBidDealSupplier(@CurrentUser() user: UserDto, @Body() data: { bidDealId: string }) {
    return await this.service.rejectBidDealSupplier(user, data?.bidDealId || '')
  }

  @ApiOperation({ summary: 'Lấy kết quả đàm phán' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('check_result_message/:biddealid')
  public async checkResultMessage(@CurrentUser() user: UserDto, @Param('biddealid') bidDealId: string) {
    return await this.service.checkResultMessage(user, bidDealId)
  }
  //#endregion

  //#region BidAuction

  @ApiOperation({ summary: 'Lấy thông tin đấu giá của NCC' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_bid_auction_supplier/:bidauctionid')
  public async getBidAuctionSupplier(@CurrentUser() user: UserDto, @Param('bidauctionid') bidAuctionId: string) {
    return await this.service.getBidAuctionSupplier(user, bidAuctionId)
  }

  @ApiOperation({ summary: 'NCC nộp đấu giá của mình' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('supplier_save_auction')
  public async supplierSaveAuction(@CurrentUser() user: UserDto, @Body() data: BidAuctionSupplierSaveDto) {
    await this.service.supplierSaveAuction(user, data)
  }
  //#endregion

  //#region FAQ
  @ApiOperation({ summary: 'Lấy danh sách FAQ theo danh mục FAQ' })
  @Get('get_faq/:id')
  public async getFAQHomePage(@Req() req: Request, @Param('id') id: string) {
    return await this.service.getFAQHomePage(req, id)
  }

  @ApiOperation({ summary: 'Lấy danh sách danh mục FAQ' })
  @Get('get_faq_category')
  public async getFAQCategoryHomePage(@Req() req: Request) {
    return await this.service.getFAQCategoryHomePage(req)
  }
  //#endregion

  //#region PO
  @ApiOperation({ summary: 'Danh sách PO của nhà cung cấp' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('paginationSupplier')
  public async paginationSupplier(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.paginationSupplier(user, data)
  }

  @ApiOperation({ summary: 'Nhà cung cấp cập nhật xác nhận giao hàng' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_status_po_delivery')
  public async updateStatusDelivery(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateStatusDelivery(user, data)
  }

  @ApiOperation({ summary: 'Nhà cung cấp cập nhật ngày giao hàng' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_delivery_date')
  public async updateDeliveryDate(@CurrentUser() user: UserDto, @Body() data: POUpdateDeliveryDateDto) {
    return await this.service.updateDeliveryDate(user, data)
  }

  @ApiOperation({ summary: 'Nhà cung cấp cập nhật ngày giao hàng' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_status_order')
  public async updateStatus(@CurrentUser() user: UserDto, @Body() data: POUpdateStatusDto) {
    return await this.service.updateStatus(user, data)
  }

  @ApiOperation({ summary: 'Tạo ĐNTT' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@Body() data: InvoiceSuggestCreate, @CurrentUser() user: UserDto) {
    return await this.service.createData(data, user)
  }

  @ApiOperation({ summary: 'Pagination ĐNTT' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination_invoice')
  public async paginationInvoice(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.paginationInivoce(user, data)
  }

  @ApiOperation({ summary: 'Nhà cung cấp cập nhật xác nhận giao hàng' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_status_po_confirm')
  public async updateStatusConfirm(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateStatusConfirm(user, data)
  }

  @ApiOperation({ summary: 'Nhà cung cấp cập nhật xác nhận giao hàng' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_status_po_refuse')
  public async updateStatusSupplierRefuse(@CurrentUser() user: UserDto, @Body() data: { id: string; reason: string }) {
    return await this.service.updateStatusSupplierRefuse(user, data)
  }

  @ApiOperation({ summary: 'Chi tiết PO' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_detail_po')
  public async findDetailPO(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetailPO(user, data)
  }

  @ApiOperation({ summary: 'Danh sách po' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_po')
  public async findPO(@CurrentUser() user: UserDto, @Body() data: {}) {
    return await this.service.findPO(user, data)
  }

  @ApiOperation({ summary: 'Danh sách hợp đồng' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_contract')
  public async findContract(@CurrentUser() user: UserDto, @Body() data: {}) {
    return await this.service.findContract(user, data)
  }

  @ApiOperation({ summary: 'Load HD của Po' })
  @Post('find_contract_po')
  @UseGuards(ApeAuthGuard, RoleGuard)
  public async findContractPO(@CurrentUser() user: UserDto, @Body() data: { poId: string }) {
    return await this.service.findContractPO(user, data)
  }

  @ApiOperation({ summary: 'Lấy List item của po' })
  @Post('load_po_product')
  @UseGuards(ApeAuthGuard, RoleGuard)
  public async loadPoProduct(@CurrentUser() user: UserDto, @Body() data: { poId: string }) {
    return await this.service.loadPoProduct(user, data)
  }

  @ApiOperation({ summary: 'Lấy List po của contract đã chọn ' })
  @Post('load_list_po_contract')
  @UseGuards(ApeAuthGuard, RoleGuard)
  public async loadListPoContract(@CurrentUser() user: UserDto, @Body() data: { contractId: string }) {
    return await this.service.loadListPoContract(user, data)
  }

  //#endregion
}
