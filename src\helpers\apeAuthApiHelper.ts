import { HttpService } from '@nestjs/axios'
import { UnauthorizedException } from '@nestjs/common'
import { lastValueFrom } from 'rxjs'
import { enumDomainReplace } from '../constants'

class ApeAuthApiHelper {
  constructor(private httpService: HttpService) {}

  /** Đăng nhập Ape Auth */
  public login(req: Request, data: { username: string; password: string }) {
    const header: any = req?.headers
    let domain = header?.origin
    if (!domain) throw new UnauthorizedException('Không xác định domain truy cập! (code: REQUEST_DOMAIN_ERROR)')
    domain = domain.replace('http://', '').replace('https://', '')
    if (domain.includes('localhost')) domain = enumDomainReplace[domain]

    return new Promise((resolve, reject) => {
      const request = this.httpService.post(`${process.env.HOST_APE_AUTH}/login_pms`, { secretKey: process.env.JWT_SECRET, domain, ...data })
      lastValueFrom(request)
        .then((res) => {
          resolve(res.data)
        })
        .catch((err: any) => {
          reject(err.response)
        })
    })
  }

  /** Xác thực token */
  public validateToken(req: Request) {
    const header: any = req?.headers
    let domain = header?.origin
    if (!domain) throw new UnauthorizedException('Không xác định domain truy cập! (code: REQUEST_DOMAIN_ERROR)')
    domain = domain.replace('http://', '').replace('https://', '')
    if (domain.includes('localhost')) domain = enumDomainReplace[domain]
    const token = header?.authorization
    if (!token) throw new UnauthorizedException('Không có quyền truy cập! (code: BEARER_TOKEN_ERROR)')
    return new Promise((resolve, reject) => {
      const request = this.httpService.post(`${process.env.HOST_APE_AUTH}/validate_token`, { domain }, { headers: { Authorization: token } })
      lastValueFrom(request)
        .then((res) => {
          // console.log('OK', res.data)
          resolve(res.data)
        })
        .catch((err: any) => {
          // console.log('FAIL', err.response)
          reject(err.response)
        })
    })
  }

  /** Lấy companyId từ request */
  public getCompanyId(req: Request) {
    let companyId = undefined
    const isProduct = process.env.IS_PRODUCT == 'true'
    if (!isProduct) return companyId

    const header: any = req?.headers
    let domain = header?.origin
    if (!domain) throw new UnauthorizedException('Không xác định domain truy cập! (code: REQUEST_DOMAIN_ERROR)')
    domain = domain.replace('http://', '').replace('https://', '')
    if (domain.includes('localhost')) domain = enumDomainReplace[domain]

    return new Promise((resolve, reject) => {
      const request = this.httpService.post(`${process.env.HOST_APE_AUTH}/get_company_id`, { domain })
      lastValueFrom(request)
        .then((res) => {
          // console.log('OK', res.data)
          if (!res.data) throw new UnauthorizedException(`Không tìm thấy companyId của domain [${domain}]`)
          resolve(res.data)
        })
        .catch((err: any) => {
          // console.log('FAIL', err.response)
          reject(err.response)
        })
    })
  }

  private getSMTP_default() {
    return {
      smtpEndPoint: process.env.AWS_SMTP_END_POINT || 'smtp.gmail.com',
      smtpPort: process.env.AWS_SMTP_PORT || '587',
      smtpSenderAddress: process.env.AWS_SMTP_SENDER_ADDRESS || '<EMAIL>',
      smtpUsername: process.env.AWS_SMTP_USERNAME || '<EMAIL>',
      smtpPassword: process.env.AWS_SMTP_PASSWORD || 'geppubclgzeddebc',
    }
  }

  /** Lấy config smtp từ companyId
   * output sample: {smtpEndPoint: 'xxx',smtpPort: 'xxx', smtpSenderAddress: '', smtpUsername:'', smtpPassword: '' }
   */
  public getSMTP(companyId?: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const isProduct = process.env.IS_PRODUCT == 'true'
      if (!isProduct || !companyId) {
        resolve(this.getSMTP_default())
        return
      }

      const request = this.httpService.post(`${process.env.HOST_APE_AUTH}/get_smtp_pms`, { companyId })
      lastValueFrom(request)
        .then((res) => {
          let configSMTP = res.data
          // Nếu chưa có config SMTP thì lấy theo default
          if (!configSMTP) configSMTP = this.getSMTP_default()

          resolve(configSMTP)
        })
        .catch((err: any) => {
          // console.log('FAIL', err.response)
          reject(err.response)
        })
    })
  }

  /** Lấy domain từ companyId
   * output sample: { clientUrl: 'https://ape-pms-client.apetechs.co',adminUrl: 'https://ape-pms-admin.apetechs.co' }
   * output sample: {clientUrl: 'https://ape-pms-client.apetechs.co',adminUrl: 'https://ape-pms-admin.apetechs.co' }
   */
  public getDomain(companyId?: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const isProduct = process.env.IS_PRODUCT == 'true'
      if (!isProduct || !companyId) {
        resolve({
          clientUrl: process.env.CLIENT_URL || 'https://ape-pms-client.apetechs.co',
          adminUrl: process.env.ADMIN_URL || 'https://ape-pms-admin.apetechs.co',
        })
        return
      }

      const request = this.httpService.post(`${process.env.HOST_APE_AUTH}/get_domain_pms`, { companyId })
      lastValueFrom(request)
        .then((res) => {
          // console.log('OK', res.data)
          if (!res.data) throw new UnauthorizedException(`Không tìm thấy companyId trong hệ thống!`)
          resolve(res.data)
        })
        .catch((err: any) => {
          // console.log('FAIL', err.response)
          reject(err.response)
        })
    })
  }

  /** Tạo user PMS */
  public createUserPMS(authorization: string, companyId: string, type: string, username: string, password: string) {
    return new Promise((resolve, reject) => {
      const request = this.httpService.post(
        `${process.env.HOST_APE_AUTH}/create_user_pms`,
        { companyId, type, username, password },
        { headers: { Authorization: authorization } },
      )
      lastValueFrom(request)
        .then((res) => {
          resolve(res.data)
        })
        .catch((err: any) => {
          reject(err.response)
        })
    })
  }

  /** Đăng ký user PMS */
  public registerUserPMS(companyId: string, type: string, username: string, password: string) {
    return new Promise((resolve, reject) => {
      const request = this.httpService.post(`${process.env.HOST_APE_AUTH}/register_user_pms`, {
        secretKey: process.env.JWT_SECRET,
        companyId,
        type,
        username,
        password,
      })
      lastValueFrom(request)
        .then((res) => {
          resolve(res.data)
        })
        .catch((err: any) => {
          reject(err.response)
        })
    })
  }

  /** Đổi mật khẩu */
  public updatePassword(authorization: string, currentPassword: string, newPassword: string) {
    return new Promise((resolve, reject) => {
      const request = this.httpService.post(
        `${process.env.HOST_APE_AUTH}/update_password`,
        { currentPassword, newPassword },
        { headers: { Authorization: authorization } },
      )
      lastValueFrom(request)
        .then((res) => {
          resolve(res.data)
        })
        .catch((err: any) => {
          reject(err.response)
        })
    })
  }

  /** Đổi tên tài khoản */
  public updateUsername(authorization: string, currentPassword: string, newUsername: string) {
    return new Promise((resolve, reject) => {
      const request = this.httpService.post(
        `${process.env.HOST_APE_AUTH}/update_username`,
        { currentPassword, newUsername },
        { headers: { Authorization: authorization } },
      )
      lastValueFrom(request)
        .then((res) => {
          resolve(res.data)
        })
        .catch((err: any) => {
          reject(err.response)
        })
    })
  }

  /** Đổi mật khẩu secret */
  public updatePasswordSecret(companyId: string, username: string, newPassword: string) {
    return new Promise((resolve, reject) => {
      const request = this.httpService.post(`${process.env.HOST_APE_AUTH}/update_password_secret`, {
        secretKey: process.env.JWT_SECRET,
        companyId,
        username,
        newPassword,
      })
      lastValueFrom(request)
        .then((res) => {
          resolve(res.data)
        })
        .catch((err: any) => {
          reject(err.response)
        })
    })
  }

  /** Active/Inactive tài khoản */
  public updateStatusUser(authorization: string, data: { companyId: string; type: string; username: string; isDeleted: boolean }) {
    return new Promise((resolve, reject) => {
      const request = this.httpService.post(`${process.env.HOST_APE_AUTH}/update_status_user_pms`, data, {
        headers: { Authorization: authorization },
      })
      lastValueFrom(request)
        .then((res) => {
          resolve(res.data)
        })
        .catch((err: any) => {
          reject(err.response)
        })
    })
  }

  public async downloadFileAsBase64(fileUrl: string): Promise<string> {
    try {
      // Gửi yêu cầu HTTP để tải file
      const response = await lastValueFrom(this.httpService.get(fileUrl, { responseType: 'arraybuffer' }))

      // Chuyển đổi buffer thành base64
      const base64Data = Buffer.from(response.data).toString('base64')
      return base64Data
    } catch (error) {
      console.error('Error downloading file:', error.message)
      throw new Error('Unable to download file')
    }
  }
}

export const apeAuthApiHelper = new ApeAuthApiHelper(new HttpService())

export const enumApeAuth = {
  UserType: {
    CompanyPackageAdmin: 'CompanyPackageAdmin',
    CompanyPackageEmployee: 'CompanyPackageEmployee',
    CompanyPackageSupplier: 'CompanyPackageSupplier',
  },
}

export class UserAuthDto {
  id: string
  username: string
  type: string
  companyId?: string
  supplierId?: string
  createdAt: Date
  updatedAt: Date
  roles?: any[]
  accessToken: string
  domain: string
}
