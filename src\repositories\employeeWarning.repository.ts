import { Repository } from 'typeorm'
import { EmployeeWarningEntity } from '../entities'
import { CustomRepository } from '../typeorm'

@CustomRepository(EmployeeWarningEntity)
export class EmployeeWarningRepository extends Repository<EmployeeWarningEntity> {
  async createEmployeeWarning(employeeId: string, message: string, messageFull: string) {
    const newWarning = new EmployeeWarningEntity()
    newWarning.message = message
    newWarning.messageFull = messageFull
    newWarning.employeeId = employeeId
    await this.save(newWarning)
  }
}
