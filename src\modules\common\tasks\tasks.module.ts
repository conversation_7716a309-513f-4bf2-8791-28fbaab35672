import { Module } from '@nestjs/common'
import { TasksService } from './tasks.service'
import { EmailModule } from '../../email/email.module'
import { BidRepository, BidHistoryRepository, BidAuctionRepository, BidDealRepository, EmailHistoryRepository } from '../../../repositories'
import { BidAuctionModule } from '../../bidAuction/bidAuction.module'
import { SQSModule } from '../sqs/sqs.module'
import { TypeOrmExModule } from '../../../typeorm'
import { PurchasePlanModule } from '../../purchasePlan/purchasePlan.module'
import { PrModule } from '../../pr/pr.module'
import { BidModule } from '../../bid/bid.module'
import { ContractModule } from '../../contract/contract.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([BidRepository, BidDealRepository, BidAuctionRepository, BidHistoryRepository, EmailHistoryRepository]),
    EmailModule,
    BidAuctionModule,
    SQSModule,
    PrModule,
    BidModule,
    ContractModule,
    PurchasePlanModule,
  ],
  providers: [TasksService],
})
export class TasksModule { }
