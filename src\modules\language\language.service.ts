import { Injectable, ConflictException, BadRequestException } from '@nestjs/common'
import { LanguageRepository } from '../../repositories'
import { CREATE_SUCCESS, ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, ERROR_VALIDATE, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { Like } from 'typeorm'
import { LanguageCreateDto, LanguageUpdateDto } from './dto'
import { apeAuthApiHelper } from '../../helpers'

@Injectable()
export class LanguageService {
  constructor(private readonly repo: LanguageRepository) {}

  public async find(req: Request) {
    const companyId = await apeAuthApiHelper.getCompanyId(req)
    return await this.repo.find({ where: { companyId, isDeleted: false } })
  }

  public async createData(data: LanguageCreateDto, user: UserDto) {
    const objCheckCode = await this.repo.findOne({ where: { code: data.code, companyId: user.companyId }, select: { id: true } })
    if (objCheckCode) throw new ConflictException(ERROR_CODE_TAKEN)

    const newEntity = this.repo.create(data)
    newEntity.companyId = user.companyId
    newEntity.createdBy = user.id
    await this.repo.save(newEntity)

    return { message: CREATE_SUCCESS }
  }

  public async updateData(data: LanguageUpdateDto, user: UserDto) {
    if (!data.id) throw new BadRequestException(ERROR_VALIDATE)

    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.avatarUrl = data.avatarUrl
    entity.description = data.description
    entity.updatedBy = user.id
    await this.repo.update(data.id, entity)

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(data: PaginationDto, user: UserDto) {
    const whereCon: any = { companyId: user.companyId }
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })
  }

  public async updateIsDelete(id: string, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }
}
