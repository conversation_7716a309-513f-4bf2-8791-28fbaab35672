import { MigrationInterface, QueryRunner } from 'typeorm'

export class genChildContract1718872487258 implements MigrationInterface {
  name = 'genChildContract1718872487258'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`contract\` ADD \`isGenChild\` tinyint NULL DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE \`contract\` ADD \`parentId\` varchar(36) NULL`)
    await queryRunner.query(
      `ALTER TABLE \`contract\` ADD CONSTRAINT \`FK_94c9bf9f42633f9470305c3ae55\` FOREIGN KEY (\`parentId\`) REFERENCES \`contract\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`contract\` DROP FOREIGN KEY \`FK_94c9bf9f42633f9470305c3ae55\``)
    await queryRunner.query(`ALTER TABLE \`contract\` DROP COLUMN \`parentId\``)
    await queryRunner.query(`ALTER TABLE \`contract\` DROP COLUMN \`isGenChild\``)
  }
}
