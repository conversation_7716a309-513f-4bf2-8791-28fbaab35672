export const enumData = {
  Page: {
    pageIndex: 1,
    pageSize: 10,
    pageSizeMax: 10000,
    total: 0,
  },

  UserType: {
    Employee: { code: 'Employee', name: '<PERSON>h<PERSON> viên' },
    Admin: { code: 'Admin', name: '<PERSON>min' },
    AdminCompany: { code: 'AdminCompany', name: '<PERSON><PERSON> Công ty' },
    Supplier: { code: 'Supplier', name: '<PERSON>hà cung cấp' },
  },
  SupplierStatus: {
    MoiDangKy: { code: 'MoiDangKy', name: '<PERSON><PERSON><PERSON> đăng ký', color: 'darkblue' },
    DaDuyet: { code: 'DaDuyet', name: 'Đã duyệt', color: 'darkgreen' },
    Huy: { code: 'Huy', name: 'Huỷ', color: 'darkred' },
  },
  SupplierServiceStatus: {
    ChuaDangKy: {
      code: 'ChuaDangKy',
      name: '<PERSON><PERSON><PERSON> nghiệ<PERSON> chưa đăng ký đang chờ bổ sung',
      description: '<PERSON><PERSON><PERSON><PERSON> tạo ra khi mời thầu nhưng chưa kịp đăng ký mới',
      color: 'gray',
    },
    MoiDangKy: { code: 'MoiDangKy', name: 'LVKD mới đang chờ xét duyệt', color: 'lightgreen' },
    CapNhatThongTin: {
      code: 'CapNhatThongTin',
      name: 'NCC điều chỉnh thông tin đang chờ xét duyệt',
      color: 'lightseagreen',
    },
    PhuTrachDuyet: {
      code: 'PhuTrachDuyet',
      name: 'Đã duyệt bước 1',
      color: 'green',
    },
    PhuTrachKhongDuyet: {
      code: 'PhuTrachKhongDuyet',
      name: 'Không được duyệt bước 1',
      color: 'lightcoral',
    },
    DaDuyet: { code: 'DaDuyet', name: 'Đã là thành viên', color: 'darkblue' },
    KhongDuyet: { code: 'KhongDuyet', name: 'Không được duyệt', color: 'red' },
    NgungHoatDong: { code: 'NgungHoatDong', name: 'Ngừng hoạt động', color: 'darkred' },
  },
  SupplierServiceExpertiseStatus: {
    DaThamDinh: { code: 'DaThamDinh', name: 'Đã thẩm định' },
    ChuaThamDinh: {
      code: 'ChuaThamDinh',
      name: 'Chưa thẩm định',
    },
    ChuaDangKy: {
      code: 'ChuaDangKy',
      name: 'Chưa đăng ký lĩnh vực kinh doanh',
    },
  },
  SupplierExpertiseStatus: {
    DangThamDinh: {
      code: 'DangThamDinh',
      name: 'Đang thẩm định',
    },
    DaThamDinh: { code: 'DaThamDinh', name: 'Đã thẩm định' },
    KhongDuyetQT2: { code: 'KhongDuyetQT2', name: 'Không duyệt Doanh nghiệp' },
  },
  SupplierExpertiseLawStatus: {
    ChuaThamDinh: {
      code: 'ChuaThamDinh',
      name: 'Chưa hoàn thành',
    },
    KhongThamDinh: {
      code: 'KhongThamDinh',
      name: 'Không yêu cầu thẩm định',
    },
    DaThamDinh: { code: 'DaThamDinh', name: 'Đã hoàn thành' },
  },
  SupplierExpertiseCapacityStatus: {
    ChuaThamDinh: {
      code: 'ChuaThamDinh',
      name: 'Chưa hoàn thành',
    },
    KhongThamDinh: {
      code: 'KhongThamDinh',
      name: 'Không yêu cầu thẩm định',
    },
    DaThamDinh: { code: 'DaThamDinh', name: 'Đã hoàn thành' },
  },
  SupplierExpertiseDetailType: {
    Law: { code: 'Law', name: 'Thông tin pháp lý' },
    Capacity: { code: 'Capacity', name: 'Thông tin năng lực' },
  },
  SettingStringType: {
    address: 'address',
    company: 'company',
    masterBidGuarantee: 'masterBidGuarantee',
    unit: 'unit',
    currency: 'currency',
  },
  SettingStringClientType: {
    BannerName: { code: 'BannerName', name: '' },
    Footer1: { code: 'Footer1', name: '' },
    Footer2: { code: 'Footer2', name: '' },
    Footer3: { code: 'Footer3', name: '' },
  },
  BannerClientType: {
    Video: { code: 'Video', name: '' },
    Image: { code: 'Image', name: '' },
  },
  BannerClientPosition: {
    Left: { code: 'Left', name: 'Bên trái' },
    Right: { code: 'Right', name: 'Bên phải' },
    Top: { code: 'Top', name: 'Bên trên' },
  },
  /** Kiểu dữ liệu */
  DataType: {
    String: { code: 'String', name: 'Free Text' },
    Number: { code: 'Number', name: 'Số' },
    File: { code: 'File', name: 'File' },
    List: { code: 'List', name: 'Danh sách' },
    Date: { code: 'Date', name: 'Ngày giờ' },
    Address: { code: 'Address', name: 'Địa chỉ' },
    Km: { code: 'Km', name: 'Khoảng cách (km)' },
    Time: { code: 'Time', name: 'Thời gian di chuyển (giờ)' },
  },

  BidStatus: {
    GoiThauTam: {
      code: 'GoiThauTam',
      name: 'Gói thầu tạm',
    },
    ChoDuyetGoiThauTam: {
      code: 'ChoDuyetGoiThauTam',
      name: 'Chờ duyệt gói thầu tạm',
    },
    DangCauHinhGoiThau: {
      code: 'DangCauHinhGoiThau',
      name: 'Mới tạo',
    },
    DangChonNCC: {
      code: 'DangChonNCC',
      name: 'Đang chọn nhà cung cấp',
    },
    TuChoiGoiThau: {
      code: 'TuChoiGoiThau',
      name: 'Từ chối gói thầu',
    },
    DangDuyetGoiThau: {
      code: 'DangDuyetGoiThau',
      name: 'Đang duyệt gói thầu',
    },
    DangNhanBaoGia: {
      code: 'DangNhanBaoGia',
      name: 'Đang mời thầu',
    },
    DangDanhGia: {
      code: 'DangDanhGia',
      name: 'Đã mở thầu',
    },
    DangDuyetDanhGia: {
      code: 'DangDuyetDanhGia',
      name: 'Đang duyệt đánh giá thầu',
    },
    HoanTatDanhGia: {
      code: 'HoanTatDanhGia',
      name: 'Hoàn tất đánh giá thầu',
    },

    DangDamPhanGia: {
      code: 'DangDamPhanGia',
      name: 'Đang đàm phán giá',
      color: '#ffc107',
    },
    DongDamPhanGia: {
      code: 'DongDamPhanGia',
      name: 'Hoàn tất đàm phán giá',
      color: '#1890ff',
    },
    DangDauGia: { code: 'DangDauGia', name: 'Đang đấu giá', color: '#ffc107' },
    DongDauGia: {
      code: 'DongDauGia',
      name: 'Hoàn tất đấu giá',
      color: '#1890ff',
    },
    DongThau: { code: 'DongThau', name: 'Đang duyệt Doanh nghiệp thắng thầu' },
    DuyetNCCThangThau: { code: 'DuyetNCCThangThau', name: 'Đã duyệt Doanh nghiệp thắng thầu' },
    DangDuyetKetThucThau: { code: 'DangDuyetKetThucThau', name: 'Đang duyệt kết thúc thầu' },
    HoanTat: { code: 'HoanTat', name: 'Hoàn tất' },
    Huy: { code: 'Huy', name: 'Huỷ' },
  },
  BidTechStatus: {
    DangTao: {
      code: 'DangTao',
      name: 'Đang tạo',
    },
    DaTao: {
      code: 'DaTao',
      name: 'Đã tạo',
    },
    TuChoi: {
      code: 'TuChoi',
      name: 'Từ chối',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidTradeStatus: {
    DangTao: {
      code: 'DangTao',
      name: 'Đang tạo',
    },
    DaTao: {
      code: 'DaTao',
      name: 'Đã tạo',
    },
    TuChoi: {
      code: 'TuChoi',
      name: 'Từ chối',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidPriceStatus: {
    DangTao: {
      code: 'DangTao',
      name: 'Đang tạo',
    },
    DaTao: {
      code: 'DaTao',
      name: 'Đã tạo',
    },
    TuChoi: {
      code: 'TuChoi',
      name: 'Từ chối',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidChooseSupplierStatus: {
    ChuaChon: {
      code: 'ChuaChon',
      name: 'Chưa chọn',
    },
    DangChon: {
      code: 'DangChon',
      name: 'Đang chọn',
    },
    DaChon: {
      code: 'DaChon',
      name: 'Đã chọn',
    },
    GuiDuyet: {
      code: 'GuiDuyet',
      name: 'Đã chọn',
    },
    TuChoi: {
      code: 'DaChon',
      name: 'Từ chối',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidTechRateStatus: {
    ChuaTao: {
      code: 'ChuaTao',
      name: 'Chưa tạo',
    },
    DangTao: {
      code: 'DangTao',
      name: 'Đang tạo',
    },
    DaTao: {
      code: 'DaTao',
      name: 'Đã tạo',
    },
    TuChoi: {
      code: 'TuChoi',
      name: 'Từ chối',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidTradeRateStatus: {
    ChuaTao: {
      code: 'ChuaTao',
      name: 'Chưa tạo',
    },
    DangTao: {
      code: 'DangTao',
      name: 'Đang tạo',
    },
    DaTao: {
      code: 'DaTao',
      name: 'Đã tạo',
    },
    TuChoi: {
      code: 'TuChoi',
      name: 'Từ chối',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidPriceRateStatus: {
    ChuaTao: {
      code: 'ChuaTao',
      name: 'Chưa tạo',
    },
    DangTao: {
      code: 'DangTao',
      name: 'Đang tạo',
    },
    DaTao: {
      code: 'DaTao',
      name: 'Đã tạo',
    },
    TuChoi: {
      code: 'TuChoi',
      name: 'Từ chối',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidResetPriceStatus: {
    ChuaTao: {
      code: 'ChuaTao',
      name: 'Chưa tạo',
      description: 'Mặc định khi tạo ra gói thầu',
    },
    DangTao: {
      code: 'DangTao',
      name: 'Đang tạo',
      description: 'Khi mới xác nhận hiệu chỉnh bảng giá (Chưa lưu)',
    },
    DaTao: {
      code: 'DaTao',
      name: 'Đã tạo',
      description: 'Khi đã xác nhận tạo xong bảng giá hiệu chỉnh (Lưu xong)',
    },
    KetThuc: {
      code: 'KetThuc',
      name: 'Kết thúc',
      description: 'Kết thúc nộp chào giá hiệu chỉnh',
    },
  },
  BidSupplierResetPriceStatus: {
    KhongYeuCau: {
      code: 'KhongYeuCau',
      name: 'Không yêu cầu',
      description: 'Mặc định khi tạo mời thầu',
    },
    YeuCauBoSung: {
      code: 'YeuCauBoSung',
      name: 'Yêu cầu bổ sung',
      description: 'Khi chọn Doanh nghiệp nộp bảng giá hiệu chỉnh',
    },
    DaBoSung: {
      code: 'DaBoSung',
      name: 'Đã bổ sung',
      description: 'Khi Doanh nghiệp đã nộp bổ sung bảng giá hiệu chỉnh',
    },
  },
  BidHistoryStatus: {
    SaoChepGoiThau: {
      code: 'SaoChepGoiThau',
      name: 'Sao chép gói thầu',
    },
    TaoGoiThauExcel: {
      code: 'TaoGoiThauExcel',
      name: 'Tạo thông tin chung cho gói thầu bằng excel',
    },
    TaoGoiThau: {
      code: 'TaoGoiThau',
      name: 'Tạo thông tin chung cho gói thầu',
    },
    SuaTaoGoiThau: {
      code: 'SuaTaoGoiThau',
      name: 'Sửa thông tin chung của gói thầu',
    },
    SuaTaoGoiThauSauDuyet: {
      code: 'SuaTaoGoiThauSauDuyet',
      name: 'Sửa thông tin chung của gói thầu sau duyệt',
    },
    YeuCauDuyetGoiThauTam: {
      code: 'YeuCauDuyetGoiThauTam',
      name: 'Yêu cầu duyệt gói thầu tạm',
    },
    TuChoiGoiThauTam: {
      code: 'TuChoiGoiThauTam',
      name: 'Yêu cầu kiểm tra lại thông tin chung của gói thầu tạm',
    },
    DuyetGoiThauTam: {
      code: 'DuyetGoiThauTam',
      name: 'Duyệt thông tin chung của gói thầu tạm',
    },
    DuyetThauNhanh: {
      code: 'DuyetThauNhanh',
      name: 'Duyệt thầu nhanh',
    },
    TaoKyThuat: {
      code: 'TaoKyThuat',
      name: 'Tạo thông tin kỹ thuật cho gói thầu',
    },
    TuChoiTaoKyThuat: {
      code: 'TuChoiTaoKyThuat',
      name: 'Từ chối thông tin kỹ thuật của gói thầu',
    },
    DuyetTaoKyThuat: {
      code: 'DuyetTaoKyThuat',
      name: 'Duyệt thông tin kỹ thuật của gói thầu',
    },
    TaoThuongMai: {
      code: 'TaoThuongMai',
      name: 'Tạo thông tin thương mại cho gói thầu',
    },
    TaoGia: {
      code: 'TaoGia',
      name: 'Tạo thông tin giá cho gói thầu',
    },
    ChonNCC: {
      code: 'ChonNCC',
      name: 'Chọn nhà cung cấp mời tham gia thầu',
    },
    ChonLaiNCC: {
      code: 'ChonNCC',
      name: 'Chọn lại nhà cung cấp mời tham gia thầu',
    },
    GuiMPOLeader: {
      code: 'GuiMPOLeader',
      name: 'Gửi yêu cầu phê duyệt bảng chào giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu',
    },
    TuChoiGoiThau: {
      code: 'TuChoiGoiThau',
      name: 'Từ chối bảng chào giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu',
    },
    DuyetGoiThau: {
      code: 'DuyetGoiThau',
      name: 'Duyệt gói thầu',
    },
    NhanBaoGia: {
      code: 'NhanBaoGia',
      name: 'Nhận báo giá',
    },
    EmailNhacMoThauLan1: {
      code: 'EmailNhacMoThauLan1',
      name: 'Email nhắc mở thầu lần 1',
    },
    EmailNhacMoThauLan2: {
      code: 'EmailNhacMoThauLan2',
      name: 'Email nhắc mở thầu lần 2',
    },
    MoThau: {
      code: 'MoThau',
      name: 'Mở đánh giá',
    },
    DanhGiaKyThuat: {
      code: 'DanhGiaKyThuat',
      name: 'Đánh giá kỹ thuật',
    },
    TuChoiDanhGiaKyThuat: {
      code: 'TuChoiDanhGiaKyThuat',
      name: 'Từ chối đánh giá kỹ thuật',
    },
    DuyetDanhGiaKyThuat: {
      code: 'DuyetDanhGiaKyThuat',
      name: 'Duyệt đánh giá kỹ thuật',
    },
    DanhGiaThuongMai: {
      code: 'DanhGiaThuongMai',
      name: 'Đánh giá thương mại',
    },
    DanhGiaGia: {
      code: 'DanhGiaGia',
      name: 'Đánh giá giá',
    },
    TuChoiDanhGiaThuongMai: {
      code: 'TuChoiDanhGiaThuongMai',
      name: 'Từ chối đánh giá chào giá và thương mại',
    },
    DuyetDanhGiaThuongMai: {
      code: 'DuyetDanhGiaThuongMai',
      name: 'Duyệt đánh giá chào giá và thương mại',
    },
    HieuChinhBangGia: {
      code: 'HieuChinhBangGia',
      name: 'Hiệu chỉnh bảng giá',
    },
    HoanTatHieuChinhBangGia: {
      code: 'HoanTatHieuChinhBangGia',
      name: 'Hoàn tất hiệu chỉnh bảng giá',
    },
    KetThucNopChaoGiaHieuChinh: {
      code: 'KetThucNopChaoGiaHieuChinh',
      name: 'Kết thúc nộp chào giá hiệu chỉnh',
    },
    TaoDamPhanGia: {
      code: 'TaoDamPhanGia',
      name: 'Tạo đàm phán giá',
    },
    DongDamPhanGia: {
      code: 'DongDamPhanGia',
      name: 'Hoàn tất đàm phán giá',
    },
    TaoDauGia: { code: 'TaoDauGia', name: 'Tạo đấu giá' },
    DongDauGia: {
      code: 'DongDauGia',
      name: 'Hoàn tất đấu giá',
    },
    ThamDinh: {
      code: 'ThamDinh',
      name: 'Thẩm định',
    },
    DuyetThamDinh: {
      code: 'DuyetThamDinh',
      name: 'Duyệt thẩm định',
    },
    XacNhanNCCTrungThau: {
      code: 'PheDuyet',
      name: 'Chọn Doanh nghiệp thắng thầu',
    },
    PheDuyetNCCThangThau: { code: 'PheDuyetNCCThangThau', name: 'Phê duyệt Doanh nghiệp thắng thầu' },
    TuChoiNCCThangThau: { code: 'TuChoiNCCThangThau', name: 'Phê duyệt Doanh nghiệp thắng thầu' },
    YeuCauKiemTraLai: {
      code: 'YeuCauKiemTraLai',
      name: 'Yêu cầu đánh giá và chọn lại Doanh nghiệp thắng thầu',
    },
    GuiYeuCauPheDuyetKetThucThau: {
      code: 'GuiYeuCauPheDuyetKetThucThau',
      name: 'Gửi yêu cầu phê duyệt kết thúc thầu',
    },
    PheDuyetKetThucThau: { code: 'PheDuyetKetThucThau', name: 'Phê duyệt kết thúc thầu' },
    PheDuyetKetQua: { code: 'HoanTat', name: 'Phê duyệt kết quả' },
    Huy: { code: 'Huy', name: 'Huỷ' },
    YeuCauHuyGoiThau: { code: 'YeuCauHuyGoiThau', name: 'Yêu cầu hủy gói thầu' },
    XacNhanHuyGoiThau: { code: 'XacNhanHuyGoiThau', name: 'Xác nhận hủy gói thầu' },
  },
  BidSupplierStatus: {
    DaDuocChon: {
      code: 'DaDuocChon',
      name: 'Đã được chọn tham gia gói thầu',
    },
    DaThongBaoMoiThau: {
      code: 'DaThongBaoMoiThau',
      name: 'Đã gửi thông báo mời thầu',
    },
    DaXacNhanKhongThamGiaThau: {
      code: 'DaXacNhanKhongThamGiaThau',
      name: 'Đã xác nhận không tham gia thầu',
    },
    DaXacNhanThamGiaThau: {
      code: 'DaXacNhanThamGiaThau',
      name: 'Đã xác nhận tham gia thầu',
    },
    DaHoanThanhBoSungHoSo: {
      code: 'DaHoanThanhBoSungHoSo',
      name: 'Đã hoàn thành bổ sung hồ sơ',
    },
    DangDanhGia: {
      code: 'DangDanhGia',
      name: 'Đang đánh giá',
    },
    DaDanhGia: {
      code: 'DaDanhGia',
      name: 'Đã đánh giá',
    },
  },
  BidSupplierFileStatus: {
    ChuaKiemTra: {
      code: 'ChuaKiemTra',
      name: 'Chưa kiểm tra',
    },
    HopLe: {
      code: 'HopLe',
      name: 'Hợp lệ',
    },
    KhongHopLe: {
      code: 'KhongHopLe',
      name: 'Không hợp lệ',
    },
  },
  BidSupplierTechStatus: {
    KhongXacNhan: {
      code: 'KhongXacNhan',
      name: 'Không xác nhận',
    },
    ChuaXacNhan: {
      code: 'ChuaXacNhan',
      name: 'Chưa xác nhận',
    },
    DangBoSung: {
      code: 'DangBoSung',
      name: 'Đang bổ sung',
    },
    DangDanhGia: {
      code: 'DangDanhGia',
      name: 'Đang đánh giá',
    },
    DaXacNhan: {
      code: 'DaXacNhan',
      name: 'Đã xác nhận',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidSupplierPriceStatus: {
    KhongXacNhan: {
      code: 'KhongXacNhan',
      name: 'Không xác nhận',
    },
    ChuaXacNhan: {
      code: 'ChuaXacNhan',
      name: 'Chưa xác nhận',
    },
    DangBoSung: {
      code: 'DangBoSung',
      name: 'Đang bổ sung',
    },
    DangDanhGia: {
      code: 'DangDanhGia',
      name: 'Đang đánh giá',
    },
    DaXacNhan: {
      code: 'DaXacNhan',
      name: 'Đã xác nhận',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidSupplierTradeStatus: {
    KhongXacNhan: {
      code: 'KhongXacNhan',
      name: 'Không xác nhận',
    },
    ChuaXacNhan: {
      code: 'ChuaXacNhan',
      name: 'Chưa xác nhận',
    },
    DangBoSung: {
      code: 'DangBoSung',
      name: 'Đang bổ sung',
    },
    DangDanhGia: {
      code: 'DangDanhGia',
      name: 'Đang đánh giá',
    },
    DaXacNhan: {
      code: 'DaXacNhan',
      name: 'Đã xác nhận',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidRuleType: {
    MPO: {
      code: 'MPO',
      name: 'Thành viên phụ trách mua hàng',
      description: '1 nhân viên',
    },
    MPOLeader: {
      code: 'MPOLeader',
      name: 'Người duyệt nội dung mua hàng',
      description: '1 nhân viên',
    },
    Tech: {
      code: 'Tech',
      name: 'Thành viên phụ trách yêu cầu kỹ thuật',
      description: '1 nhân viên',
    },
    TechLeader: {
      code: 'TechLeader',
      name: 'Người duyệt yêu cầu kỹ thuật',
      description: '1 nhân viên',
    },
    Memmber: {
      code: 'Memmber',
      name: 'Các thành viên khác thuộc hội đồng xét thầu',
      description: 'nhiều nhân viên',
    },
    Other: {
      code: 'Other',
      name: 'Các thành viên khác',
      description: 'nhiều nhân viên',
    },
  },
  BidDealStatus: {
    DangDamPhan: { code: 'DangDamPhan', name: 'Đang đàm phán' },
    DongDamPhanGia: { code: 'DongDamPhanGia', name: 'Hoàn tất' },
  },
  BidDealSupplierStatus: {
    DangDamPhan: { code: 'DangDamPhan', name: 'Chưa gửi giá' },
    DaGuiGiaMoi: { code: 'DaGuiGiaMoi', name: 'Đã gửi giá mới' },
    DaTuChoi: { code: 'DaTuChoi', name: 'Đã từ chối' },
  },

  BidAuctionStatus: {
    DangDauGia: { code: 'DangDauGia', name: 'Đang đấu giá' },
    DongDauGia: { code: 'DongDauGia', name: 'Hoàn tất' },
  },
  BidAuctionSupplierStatus: {
    DangDauGia: { code: 'DangDauGia', name: 'Chưa đấu giá' },
    DaDauGia: { code: 'DaDauGia', name: 'Đã đấu giá' },
  },
  EmailTemplate: {
    SendConfirmCode: {
      code: 'SendConfirmCode',
      name: 'Doanh nghiệp gửi mã xác nhận',
    },
    FinishEvaluation: {
      code: 'FinishEvaluation',
      name: 'Kết thúc thẩm định Doanh nghiệp',
    },
    UpdateBidSuccess: {
      code: 'UpdateBidSuccess',
      name: 'Chỉnh sửa thông tin chung của gói thầu thành công',
    },
    SupplierBidSuccess: {
      code: 'SupplierBidSuccess',
      name: 'Doanh nghiệp đã đấu thầu',
    },
    SendEmailBid: {
      code: 'SendEmailBid',
      name: 'Gửi thông báo nội bộ, thông báo Doanh nghiệp',
    },
  },
  DataHistoryTable: {
    Supplier: 'supplier_entity',
    SupplierCapacity: 'supplier_capacity_entity',
  },
  SQSMessageType: {
    Test: 'test',
    Email: 'email',
  },
  EmailStatus: {
    Success: {
      code: 'Success',
      name: 'Gửi email thành công',
    },
    Fail: {
      code: 'Fail',
      name: 'Gửi email thất bại',
    },
  },
  NotifyStatus: {
    ChuaDoc: { code: 'ChuaDoc', name: 'Chưa đọc' },
    DaDoc: { code: 'DaDoc', name: 'Đã đọc' },
  },
  ColType: {
    MPO: { code: 'MPO', name: 'Nhân viên' },
    Supplier: { code: 'Supplier', name: 'Nhà cung cấp' },
  },
  StatusServiceCapacity: {
    ChuaDuyet: { code: 'ChuaDuyet', name: 'Chưa duyệt' },
    GuiDuyet: { code: 'GuiDuyet', name: 'Đã gửi duyệt' },
    DaDuyet: { code: 'DaDuyet', name: 'Đã duyệt' },
  },

  ContractStatus: {
    Open: { code: 'OPEN', name: 'Mới tạo', description: 'Hợp đồng Mới tạo', color: 'darkblue' },
    Processing: {
      code: 'PROCESSING',
      name: 'Đang thực hiện',
      description: 'Hợp đồng Đang thực hiện',
      color: 'deeppink',
    },
    Expired: { code: 'EXPIRED', name: 'Hết hạn', description: 'Hợp đồng đã Hết hạn', color: 'darkcyan' },
    Extend: { code: 'EXTEND', name: 'Gia hạn', description: 'Hợp đồng đã Được gia hạn', color: 'darkorange' },
    Complete: { code: 'COMPLETE', name: 'Hoàn thành', description: 'Hợp đồng đã hoàn tất', color: 'darkgreen' },
    Cancel: { code: 'CANCEL', name: 'Hủy', description: 'Hợp đồng bị hủy', color: 'darkred' },
  },

  PORoleCode: {
    View: { code: 'VIEW', name: 'Xem', description: 'Quyền xem thông tin PO' },
    Edit: {
      code: 'EDIT',
      name: 'Chỉnh sửa',
      description: 'Quyền chỉnh sửa thông tin PO',
    },
    Confirm: {
      code: 'COMFIRM',
      name: 'Duyệt PO',
      description: 'Quyền duyệt  PO',
    },
    PurchaseOrderPayMent: {
      code: 'PURCHASEORDERPAYMENT',
      name: 'Thanh toán PO',
      description: 'Quyền thanh toán  PO',
    },
    Cancel: {
      code: 'CANCEL',
      name: 'Hủy',
      description: 'Quyền chỉnh sửa Hủy PO',
    },
  },
  /** Trạng thái ĐNTT của tiến độ */
  PaymentProgressStatus: {
    Unpaid: { code: 'UNPAIND', name: 'Chưa đề nghị thanh toán' },
    Partial: { code: 'PARTIAL', name: 'Đã đề nghị thanh toán một phần' },
    Paid: { code: 'PAID', name: 'Đã đề nghị thanh toán' },
  },
  ContractRoleCode: {
    View: { code: 'VIEW', name: 'Xem', description: 'Quyền xem thông tin hợp đồng' },
    Edit: { code: 'EDIT', name: 'Chỉnh sửa', description: 'Quyền chỉnh sửa thông tin hợp đồng' },
    Confirm: { code: 'COMFIRM', name: 'Duyệt hợp đồng', description: 'Quyền duyệt hợp đồng' },
    Extended: { code: 'EXTEND', name: 'Gia hạn', description: 'Quyền gia hạn hợp đồng' },
    Management: { code: 'MANAGEMENT', name: 'Quản lý', description: 'Quyền quản lý hợp đồng' },
  },
  ContractTypeAppendix: {
    Money: { code: 'MONEY', name: 'Tiền' },
    Procedure: { code: 'PROCEDURE', name: 'Quy trình' },
    Rules: { code: 'RULES', name: 'Điều khoản' },
  },
  PurchaseOrderPayMent: {
    Unpaid: {
      code: 'UNPAIND',
      name: 'Chưa thanh toán',
      description: 'PO chưa được thanh toán',
    },
    SuggestPaid: { code: 'SUGGEST_PAID', name: 'Đã đề nghị thanh toán', description: 'PO đã được đề nghị thanh toán' },
    Partial: {
      code: 'PARTIAL',
      name: 'Thanh toán một phần',
      description: 'PO được thanh toán 1 phần',
    },
    Paid: {
      code: 'PAID',
      name: 'Đã thanh toán',
      description: 'PO đã được thanh toán',
    },
  },
  PurchaseOrderStatus: {
    Open: { code: 'OPEN', name: 'Mới tạo', description: 'PO Mới tạo', color: 'darkblue' },
    Approved: { code: 'APPROVED', name: 'Duyệt PO', description: 'PO đã được duyệt', color: 'darkorange' },
    Confirm: { code: 'COMFIRM', name: 'NCC xác nhận', description: 'Nhà cung cấp xác nhận PO', color: 'darkcyan' },
    DeliveryRefuse: {
      code: 'DELIVERYREFUSE',
      name: 'NCC từ chối giao hàng',
      description: 'Nhà cung cấp từ chối giao hàng',
      color: 'orangered',
    },
    Delivery: {
      code: 'DELIVERY',
      name: 'NCC giao hàng',
      description: 'Nhà cung cấp xác nhận giao hàng',
      color: '#ff409c',
    },

    Complete: { code: 'COMPLETE', name: 'Hoàn thành', description: 'PO đã hoàn tất', color: '#008000' },
    Cancel: { code: 'CANCEL', name: 'Hủy PO', description: 'Hủy PO', color: '#cf1322' },
  },
  PRStatus: {
    New: { code: 'NEW', name: 'Mới tạo', color: '#096dd9' },
    Approved: { code: 'APPROVED', name: 'Đã duyệt', color: '#d46b08' },
    Processing: { code: 'PROCESSING', name: 'Đang thực hiện', color: 'deeppink' },
    Close: { code: 'CLOSE', name: 'Đóng PR', color: '#008000' },
    Cancel: { code: 'CANCEL', name: 'Hủy PR', color: '#cf1322' },
  },
  PRType: {
    Arise: { code: 'ARISE', name: 'YCMH phát sinh' },
    Plan: { code: 'PLAN', name: 'YCMH theo kế hoạch' },
    GROUP: { code: 'GROUP', name: 'YCMH theo kế hoạch' },
  },
  QCStatus: {
    YET: { code: 'YET', name: 'Chưa QC', color: 'darkblue' },
    DONE: { code: 'DONE', name: 'Đã QC', color: 'darkgreen' },
  },
  ContractTypePo: {
    NonContract: {
      code: 'NONCONTRACT',
      name: 'Chọn không theo hợp đồng',
    },
    Contract: { code: 'CONTRACT', name: 'Chọn theo hợp đồng' },
  },

  BranchType: {
    Company: { code: 'COMPANY', name: 'Công ty' },
    Branch: { code: 'BRANCH', name: 'Chi nhánh' },
  },

  BranchMember: {
    Bob: { code: 'BOD', name: 'Giám Đốc' },
    Secretary: { code: 'SECRETARY', name: 'Thư ký' },
    Lead: { code: 'LEAD', name: 'Trưởng phòng' },
    Deputy: { code: 'DEPUTY', name: 'Phó phòng' },
    Employee: { code: 'EMPLOYEE', name: 'Loại nhân viên' },
  },

  /** Trạng thái ĐNTT */
  InvoiceSuggestStatus: {
    Unpaid: { code: 'UNPAID', name: 'Chưa thanh toán', description: 'ĐNTT chưa được thanh toán' },
    Partial: {
      code: 'PARTIAL',
      name: 'Thanh toán 1 phần',
      description: 'ĐNTT được thanh toán 1 phần',
    },
    Paid: { code: 'PAID', name: 'Đã hoàn thành', description: 'ĐNTT đã được thanh toán' },
  },

  SourceType: {
    Admin: { name: 'Quản Lý Admin', code: 'ADMIN' },
    Client: { name: 'Cổng Đấu Thầu', code: 'CLIENT' },
  },

  Component_Admin: {
    Asn: {
      name: 'Nhập kho',
      data: {
        Asn: { name: 'Nhập kho', code: 'ASN' },
        AddEdit: { name: 'Thêm mới & Chỉnh sửa', code: 'ASN_ADD_EDIT' },
        Detail: { name: 'Chi tiết', code: 'ASN_DETAIL' },
        History: { name: 'Lịch sử', code: 'ASN_HISTORY' },
      },
    },
    Contract: {
      name: 'Hợp đồng',
      data: {
        Contract: { name: 'Hợp đồng', code: 'CONTRACT' },
        AddEdit: { name: 'Thêm mới & Chỉnh sửa', code: 'CONTRACT_ADD_EDIT' },
        Detail: { name: 'Chi tiết', code: 'CONTRACT_DETAIL' },
        History: { name: 'Lịch sử', code: 'CONTRACT_HISTORY' },
      },
    },
    Language: {
      name: 'DS Ngôn ngữ',
      data: {
        Language: { name: 'DS Ngôn ngữ', code: 'LANGUAGE' },
        AddEdit: { name: 'Thêm mới & Chỉnh sửa', code: 'LANGUAGE_ADD_EDIT' },
      },
    },
    LanguageConfig: {
      name: 'Cấu hình ngôn ngữ',
      data: {
        Language: { name: 'Cấu hình ngôn ngữ', code: 'LANGUAGE_CONFIG' },
        AddEdit: { name: 'Thêm mới & Chỉnh sửa', code: 'LANGUAGE_CONFIG_ADD_EDIT' },
      },
    },
  },

  Component_Client: {
    Main: {
      name: 'Trang chính',
      data: {
        Main: { name: 'Trang chính', code: 'MAIN' },
      },
    },
    BiddingHistory: {
      name: 'Lịch sử đấu thầu',
      data: {
        History: { name: 'Lịch sử', code: 'BID_HISTORY' },
        Price: { name: 'Lịch sử nộp giá', code: 'BID_HISTORY_PRICE' },
      },
    },
  },

  KPIRating: {
    Pass: { name: 'Đạt', code: 'PASS' },
    Fail: { name: 'Không đạt', code: 'FAIL' },
  },

  Evaluate: {
    A: { name: 'Điểm A', code: 'A' },
    B: { name: 'Điểm B', code: 'B' },
    C: { name: 'Điểm C', code: 'C' },
    D: { name: 'Điểm D', code: 'D' },
    E: { name: 'Điểm E', code: 'E' },
  },

  DatetimeQuarterly: {
    Q1: { code: 'Q1', name: 'Quý 1' },
    Q2: { code: 'Q2', name: 'Quý 2' },
    Q3: { code: 'Q3', name: 'Quý 3' },
    Q4: { code: 'Q4', name: 'Quý 4' },
  },

  DatetimeFilter: {
    Month: { code: 'MONTH', name: 'Chọn thời gian theo tháng' },
    Quarterly: { code: 'QUARTERLY', name: 'Chọn thời gian theo quý' },
    Year: { code: 'YEAR', name: 'Chọn thời gian theo năm' },
  },

  WarningType: {
    Purchare_Plan_Expiry: {
      code: 'PURCHARE_PLAN_EXPIRY',
      name: 'Cảnh Báo Khi Hết Hạn Mua Hàng Theo Kế Hoạch {0}',
      default: `
      <p>Kính gửi Anh/Chị:  <b>{0}</b>,</p>
      <br>
      <p>Yêu cầu mua hàng <b>[ {1} ]</b> với mã <b>[ {2} ]</b> đã quá hạn giao hàng theo kế hoạch nhưng vẫn chưa hoàn tất.</p>
      <p>Anh/Chị vui lòng kiểm tra lại thông tin.</p>
      <br>
      <p><i>Lưu ý: Cảnh báo này được gửi tự động từ Hệ Thống Đấu Thầu APE !</i></p>`,
    },
    Purchare_Plan_Over_Budget: {
      code: 'PURCHARE_PLAN_OVER_BUDGET',
      name: 'Cảnh Báo Vượt Ngân Sách Cho Kế Hoạch {0}',
      default: `
      <p>Kính gửi Anh/Chị:  <b>{0}</b>,</p>
      <br>
      <p>Yêu cầu mua hàng <b>[ {1} ]</b> với mã <b>[ {2} ]</b> đã vượt quá ngân sách kế hoạch.</p>
      <p>Ngân sách dự kiến <b>{3}</b> nhưng ngân sách mua hàng thực tế đã là <b>{4}</b> .</p>
      <p>Anh/Chị vui lòng kiểm tra lại thông tin.</p>
      <br>
      <p><i>Lưu ý: Cảnh báo này được gửi tự động từ Hệ Thống Đấu Thầu APE !</i></p>`,
    },
    Contract_Coming_Expiry: {
      code: 'CONTRACT_COMING_EXPIRY',
      name: 'Cảnh Báo Sắp Hết Hạn Hợp Đồng {0}',
      default: `
      <p>Kính gửi Anh/Chị:  <b>{0}</b>,</p>
      <br>
      <p>Hợp đồng <b>[ {1} ]</b> với mã <b>[ {2} ]</b> đã sắp tới ngày hết hạn nhưng vẫn chưa được gia hạn.</p>
      <p>Anh/Chị vui lòng kiểm tra lại thông tin.</p>
      <br>
      <p><i>Lưu ý: Cảnh báo này được gửi tự động từ Hệ Thống Đấu Thầu APE !</i></p>`,
    },
    Contract_Expiry_Payment: {
      code: 'CONTRACT_EXPIRY_PAYMENT',
      name: 'Cảnh Báo Hết Hạn Thanh Toán Hợp Đồng {0}',
      default: `
      <p>Kính gửi Anh/Chị:  <b>{0}</b>,</p>
      <br>
      <p>Hợp đồng <b>[ {1} ]</b> với mã <b>[ {2} ]</b> đã hết hạn thanh toán nhưng vẫn chưa được xử lý.</p>
      <p>Anh/Chị vui lòng kiểm tra lại thông tin.</p>
      <br>
      <p><i>Lưu ý: Cảnh báo này được gửi tự động từ Hệ Thống Đấu Thầu APE !</i></p>`,
    },
    Bid_Expiry_Setting_Evalution: {
      code: 'BID_EXPIRY_SETTING_EVALUTION',
      name: 'Cảnh Báo Sắp Hết Hạn {0} Gói Thầu {1}',
      default: `
      <p>Kính gửi Anh/Chị:  <b>{0}</b>,</p>
      <br>
      <p>Gói thầu <b>[ {1} ]</b> với mã <b>[ {2} ]</b> đã hết hạn thiết lập và đánh giá nhưng vẫn chưa được xử lý.</p>
      <p>Anh/Chị vui lòng kiểm tra lại thông tin.</p>
      <br>
      <p><i>Lưu ý: Cảnh báo này được gửi tự động từ Hệ Thống Đấu Thầu APE !</i></p>`,
    },
  },

  /** Loại data cần cảnh báo */
  DataWarningType: {
    PR_Plan: { code: 'PR_PLAN', name: 'Yêu Cầu Mua Hàng Theo Kế Hoạch' },
    PR_Aries: { code: 'PR_ARIES', name: 'Yêu Cầu Mua Hàng Phát Sinh' },
    Purchase_Plan: { code: 'PURCHASE_PLAN', name: 'Kế Hoạch Mua Hàng' },
    Contract: { code: 'CONTRACT', name: 'Hợp Đồng' },
    Bid: { code: 'BID', name: 'Gói Thầu' },
  },
  DeliveryDateStatus: {
    EQUAL: { code: 'EQUAL', name: 'Đúng ngày giao hàng', color: 'lightgreen' },
    MISS: { code: 'MISS', name: 'Trễ ngày giao hàng', color: 'lightcoral' },
    UP_COMMING: { code: 'UP_COMMING', name: 'Chuẩn bị đến ngày giao hàng', color: 'lightblue' },
  },

  /** Trạng thái đấu giá  */
  AuctionStatus: {
    NEW: { code: 'NEW', name: 'Chưa đến hạn', color: '#2453F8' },
    DOING: { code: 'DOING', name: 'Đang đấu giá', color: 'darkgreen' },
    DONE: { code: 'DONE', name: 'Kết thúc', color: '#00AA25' },
    CANCEL: { code: 'CANCEL', name: 'Hủy', color: '#E11F1F' },
  },

  /** Các cách tính điểm */
  PriceScoreCalculateWay: {
    SumScore: { code: 'SumScore', name: 'Độ lệch chuẩn theo tổng điểm' },
    SumPrice: { code: 'SumPrice', name: 'Độ lệch chuẩn theo tổng thành tiền' },
    SumUnitPrice: { code: 'SumUnitPrice', name: 'Độ lệch chuẩn theo tổng đơn giá' },
  },
  /** Trạng thái đơn Po  */
  PoOrder: {
    REVICED: { code: 'REVICED', name: 'Đã nhận đơn', color: '#2453F8' },
    DOING: { code: 'DOING', name: 'Đang sản xuất', color: 'darkgreen' },
    EXPECT_COMPLETE: { code: 'EXPECT_COMPLETE', name: 'Dự kiến hoàn thành', color: '#00AA25' },
    INVENTORY: { code: 'INVENTORY', name: 'Đang tồn kho', color: '#E11F1F' },
    WAIT_REPLY: { code: 'WAIT_REPLY', name: 'Chờ NCC phản hồi', color: '#ffd800' },
  },

  BillStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    CANCEL: { code: 'CANCEL', name: 'Hủy', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    PROCESS: { code: 'PROCESS', name: 'Đang xử lý', color: 'green', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    WAIT_CONFIRM: { code: 'WAIT_CONFIRM', name: 'Chờ xác nhận', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    CONFIRMED: { code: 'CONFIRMED', name: 'Đã xác nhận', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
  },

  BillPaymentStatus: {
    NEW: { code: 'NEW', name: 'Chưa thanh toán', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    PAID: { code: 'PAID', name: 'Đã thanh toán', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
  },

  /** Nguồn tham chiếu hóa đơn */
  referencesInvoice: {
    C: { code: 'C', name: 'Theo hợp đồng' },
    P: { code: 'P', name: 'Theo PO' },
  },
  OfferStatus: {
    MoiTao: { code: 'MoiTao', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    ChoDuyet: { code: 'ChoDuyet', name: 'Chờ duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    NopBaoGia: { code: 'NopBaoGia', name: 'NCC nộp báo giá', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    DaDuyet: { code: 'DaDuyet', name: 'Đã duyệt', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    HoanTatCauHinh: { code: 'HoanTatCauHinh', name: 'Hoàn tất cấu hình', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    DangCauHinhGoiThau: {
      code: 'DangCauHinhGoiThau',
      name: 'Đang cấu hình',
      color: '#ED9A1F',
      bgColor: '#FCF0DD',
      borderColor: '#F5CA89',
    },
    DangDamPhanGia: {
      code: 'DangDamPhanGia',
      name: 'Đang đàm phán giá',
      color: '#ffc107',
      statusColor: '#99e2f9',
      statusBorderColor: '#99e2f9',
      statusBgColor: '#e6fcf9',
    },
    HoanTatDanhGia: {
      code: 'HoanTatDanhGia',
      name: 'Hoàn tất đánh giá thầu',
      statusColor: '#0a915b',
      statusBorderColor: '#0a915b',
      statusBgColor: '#def2e0',
    },
    DaCongKhai: { code: 'DaCongKhai', name: 'Đã công khai', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    DanhGiaNCC: { code: 'DanhGiaNCC', name: 'Đang đánh giá NCC', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    ChoDuyetKetQua: { code: 'ChoDuyetKetQua', name: 'Chờ duyệt kết quả', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    Huy: { code: 'Huy', name: 'Huỷ', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
  },

  BiddingType: {
    PRODUCT: { code: 'PRODUCT', name: 'Đấu thầu mua hàng hóa' },
    SHIPPING: { code: 'SHIPPING', name: 'Đấu thầu tìm đơn vị vận chuyển' },
  },

  PRCategoryItem: {
    ZERO: { code: '0', name: 'Tồn kho' },
    ONE: { code: '1', name: 'Dịch vụ' },
    TWO: { code: '2', name: 'Tài sản' },
    THREE: { code: '3', name: 'Gia công' },
  },

  OfferSupplierStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    TEMP_SAVE: { code: 'TEMP_SAVE', name: 'Lưu nháp', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    SEND: { code: 'SEND', name: 'Đã gửi', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
  },

  ValuationType: {
    Null: { code: 'Null', name: 'Null' },
    V15X: { code: 'V15X', name: 'V15X' },
    V150: { code: 'V150', name: 'V150' },
    V155: { code: 'V155', name: 'V155' },
  },

  ReferenceType: {
    PR: { code: 'PR', name: 'Tham chiếu từ PR' },
    BusinessPlan: { code: 'BusinessPlan', name: 'Tham chiếu từ kế hoạch kinh doanh ' },
  },
  QuickPriceType: {
    PR: { code: 'PR', name: 'PR' },
    ExMatGr: { code: 'ExMatGr', name: 'External material group' },
  },

  supplierType: {
    NEW: { code: 'NEW', name: 'Mới tạo' },
    POTENTIAL: { code: 'POTENTIAL', name: 'Nhà cung cấp tiềm năng' },
    OFFICIAL: { code: 'OFFICIAL', name: 'Nhà cung cấp chính thức' },
  },

  AuctionType: {
    BID: { code: 'BID', name: 'Gói thầu' },
    PR: { code: 'PR', name: 'PR' },
    MatGroup: { code: 'MatGroup', name: 'MatGroup' },
    File: { code: 'File', name: 'File đính kèm' },
  },

  OrderType: {
    PRODUCT: { code: 'PRODUCT', name: 'Chào giá mua hàng hóa' },
    SHIPPING: { code: 'SHIPPING', name: 'Chào giá tìm đơn vị vận chuyển' },
  },

  PaymentStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0064D9', bgColor: '#99C1F0', borderColor: '#0064D9' },
    CHECKING: { code: 'CHECKING', name: 'Đang kiểm tra hồ sơ', color: '#BA066C', bgColor: '#EEC1DA', borderColor: '#BA066C' },
    WAIT_APPROVE: { code: 'WAIT_APPROVE', name: 'Chờ duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    CONFIRMED: { code: 'CONFIRMED', name: 'Đã xác nhận', color: '#BA066C', bgColor: '#EEC1DA', borderColor: '#BA066C' },
    REQUEST_CONFIRM: { code: 'REQUEST_CONFIRM', name: 'Yêu cầu xác nhận lại', color: '#BA066C', bgColor: '#EEC1DA', borderColor: '#BA066C' },
    PAYING: { code: 'PAYING', name: 'Đang thanh toán', color: '#D8A800', bgColor: '#ddcc8c', borderColor: '#D8A800' },
    PAID: { code: 'PAID', name: 'Đã thanh toán', color: '#006D6A', bgColor: '#F9F2D9', borderColor: '#006D6A' },
    REQUEST_RECHECK: { code: 'REQUEST_RECHECK', name: 'Yêu cầu kiểm tra lại', color: '#AA0808', bgColor: '#E9BFBF', borderColor: '#AA0808' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', description: 'Đã duyệt', color: '#008000', bgColor: '#DEF2E0', borderColor: '#00AA25' },
  },

  paymentType: {
    N: { code: 'N', name: 'Thông thường' },
    A: { code: 'A', name: 'Tạm ứng' },
  },

  /** Trạng thái phiếu nhập kho  */
  InboundStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    WAITING: { code: 'WAITING', name: 'Chờ nhận hàng', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    IMPORTED: { code: 'IMPORTED', name: 'Đã nhập kho', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    CANCEL: { code: 'CANCEL', name: 'Đã huỷ', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
  },
}
