import { Module } from '@nestjs/common'
import { Ai<PERSON>ontroller } from './ai.controller'
import { AiService } from './ai.service'
import { TypeOrmExModule } from '../../typeorm'
import { BidAuctionSupplierPriceValueRepository, BidPriceRepository } from '../../repositories'
import { BidAuctionSupplierPriceValueEntity } from '../../entities'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BidPriceRepository, BidAuctionSupplierPriceValueRepository])],
  controllers: [AiController],
  providers: [AiService],
  exports: [AiService],
})
export class AiModule {}
