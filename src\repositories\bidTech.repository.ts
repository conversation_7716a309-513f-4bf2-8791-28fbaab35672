import { IsNull, Repository } from 'typeorm'
import { UserDto } from '../dto'
import { BidTechEntity } from '../entities'
import { CustomRepository } from '../typeorm'

@CustomRepository(BidTechEntity)
export class BidTechRepository extends Repository<BidTechEntity> {
  // Lấy danh sách yêu cầu kỹ thuật của gói thầu
  async getTech(user: UserDto, bidId: string) {
    return await this.find({
      where: { bidId, companyId: user.companyId, isDeleted: false, parentId: IsNull() },
      relations: { bidTechListDetails: true, childs: { bidTechListDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })
  }
}
