import { MigrationInterface, QueryRunner } from 'typeorm'

export class autoBid1680575015948 implements MigrationInterface {
  name = 'autoBid1680575015948'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`bid\` ADD \`isAutoBid\` tinyint NOT NULL DEFAULT 0`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`bid\` DROP COLUMN \`isAutoBid\``)
  }
}
