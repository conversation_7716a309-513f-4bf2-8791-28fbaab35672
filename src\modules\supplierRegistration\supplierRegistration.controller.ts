import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { SupplierRegistrationService } from './supplierRegistration.service'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { CurrentUser, Roles } from '../common/decorators'
import { UserDto } from '../../dto'
import { SupplierUpdateLawDto, SupplierUpdateCapacityDto, SupplierCreateDto, SupplierAddCapacitiesDto, SupplierRegisterDto } from '../supplier/dto'
import { enumProject } from '../../constants'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Supplier')
@Controller('supplierRegistrations')
export class SupplierRegistrationController {
  constructor(private readonly service: SupplierRegistrationService) {}

  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách Item khi NCC đăng ký */' })
  @Post('get_services')
  public async getServices(@Req() req: Request) {
    return await this.service.getServices(req)
  }

  @ApiOperation({ summary: 'Lấy Item mà NCC có thể thêm' })
  @Post('get_services_can_add')
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  public async getServicesCanAdd(@CurrentUser() user: UserDto) {
    return await this.service.getServicesCanAdd(user)
  }

  @ApiOperation({ summary: 'Lấy template năng lực của Item' })
  @Post('get_service_capacities')
  public async getServiceCapacity(@Req() req: Request, @Body() data: { serviceId: string }) {
    return await this.service.getServiceCapacity(req, data)
  }

  @ApiOperation({ summary: 'Hàm đăng ký Doanh nghiệp mới' })
  @Post('supplier_registration')
  public async supplierRegistration(@Req() req: Request, @Body() data: SupplierRegisterDto) {
    return await this.service.supplierRegistration(req, data)
  }

  @ApiOperation({ summary: 'NCC bổ sung kinh doanh Item mới' })
  @Post('supplier_add_capacity')
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  public async supplierAddCapacities(@CurrentUser() user: UserDto, @Body() data: SupplierAddCapacitiesDto) {
    return await this.service.supplierAddCapacities(user, data)
  }

  @ApiOperation({ summary: 'NCC cập nhật thông tin năng lực' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('supplier_update_law')
  public async updateLaw(@CurrentUser() user: UserDto, @Body() data: SupplierUpdateLawDto) {
    return await this.service.updateLaw(user, data)
  }

  @ApiOperation({ summary: 'NCC cập nhật thông tin pháp lý' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('supplier_update_capacity')
  public async updateCapacity(@CurrentUser() user: UserDto, @Body() data: SupplierUpdateCapacityDto) {
    return await this.service.updateCapacity(user, data)
  }

  @ApiOperation({ summary: 'Lấy thông tin pháp lý, năng lực của NCC' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('get_supplier_info')
  public async getSupplierInfo(@CurrentUser() user: UserDto) {
    return await this.service.getSupplierInfo(user)
  }
}
