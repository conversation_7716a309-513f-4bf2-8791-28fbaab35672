import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { enumProject } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { FaqCategoryCreateDto, FaqCategoryUpdateDto } from './dto'
import { FaqCategoryService } from './faqCategory.service'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Faq')
@Controller('faq_category')
export class FaqCategoryController {
  constructor(private readonly service: FaqCategoryService) {}

  @ApiOperation({ summary: 'Danh sách danh mục faq' })
  @Roles(enumProject.Features.SETTING_019.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto) {
    return await this.service.find(user)
  }

  @ApiOperation({ summary: 'Danh sách danh mục faq phân trang' })
  @Roles(enumProject.Features.SETTING_019.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo danh mục faq' })
  @Roles(enumProject.Features.SETTING_019.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: FaqCategoryCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật danh mục faq' })
  @Roles(enumProject.Features.SETTING_019.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: FaqCategoryUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động cho danh mục faq' })
  @Roles(enumProject.Features.SETTING_019.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateIsDelete(user, data)
  }
}
