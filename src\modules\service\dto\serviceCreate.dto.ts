import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class ServiceCreateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty()
  stockQuantity: number

  @ApiPropertyOptional()
  parent1: string

  @ApiPropertyOptional()
  parent2: string

  @ApiPropertyOptional()
  serviceAccess: string[]

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  approveById: string

  @ApiPropertyOptional()
  description: string

  @ApiPropertyOptional()
  percentTech: number

  @ApiPropertyOptional()
  percentPrice: number

  @ApiPropertyOptional()
  percentTrade: number
}
