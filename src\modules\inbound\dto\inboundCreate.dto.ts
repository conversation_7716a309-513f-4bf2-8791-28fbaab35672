import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class InboundCreateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  poId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty()
  @IsOptional()
  @IsString()
  employeeInchargeId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  deliveryDate: Date

  @ApiProperty()
  @IsOptional()
  @IsString()
  dateArrivalWarehouse: Date

  @ApiProperty()
  @IsOptional()
  @IsString()
  description: string

  @ApiProperty()
  @IsOptional()
  isSupplierCreate: boolean

  @ApiProperty()
  @IsOptional()
  @IsString()
  dateArrivalPort: Date

  listContainer: any[]
}
