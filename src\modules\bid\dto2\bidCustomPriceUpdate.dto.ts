import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsBoolean, IsNumber } from 'class-validator'

export class BidCustomPriceUpdateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  bidId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  itemId: string

  @ApiPropertyOptional()
  sort: number

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isRequired: boolean

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  type: string

  @ApiPropertyOptional()
  unit: string
  @ApiPropertyOptional()
  currency: string
  @ApiPropertyOptional()
  number: number
}
