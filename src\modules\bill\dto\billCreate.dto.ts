import { ApiProperty } from '@nestjs/swagger'
import { IsString } from 'class-validator'

export class BillCreateDto {
  @ApiProperty({ description: 'PO' })
  poId: string

  @ApiProperty({ description: '<PERSON>ợ<PERSON> đồng' })
  contractId: string

  @ApiProperty({ description: 'Mô tả hóa đơn' })
  description: string

  @ApiProperty({ description: 'URL hóa đơn xml ' })
  @IsString()
  fileXml: string

  @ApiProperty({ description: 'Nguồn tham chiếu hóa đơn(<PERSON> hợp đồng hoặc theo PO)' })
  referencesInvoice: string

  @ApiProperty({ description: 'id công ty từ hệ thống bizzi' })
  bizziCompanyId: string

  @ApiProperty({ description: 'File đính kèm ' })
  @IsString()
  fileAttach: string

  @ApiProperty({ description: 'Mã tra cứu hóa đơn' })
  @IsString()
  billLookupCode: string

  @ApiProperty({ description: 'Trang web tra cứu hóa đơn điện tử' })
  @IsString()
  billLookupId: string
}
