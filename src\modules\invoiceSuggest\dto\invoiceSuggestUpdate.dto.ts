import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class InvoiceSuggestUpdate {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  type: string

  @ApiPropertyOptional()
  contractId: string
  @ApiPropertyOptional()
  poId: string
  @ApiPropertyOptional()
  paymentPlanId: string

  @ApiProperty()
  @IsNotEmpty()
  money: number

  @ApiProperty()
  @IsNotEmpty()
  suggestPaid: number

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  invoiceNo: string

  @ApiPropertyOptional()
  discount: number

  @ApiProperty()
  @IsNotEmpty()
  invoiceDate: Date

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  supplierId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  beneficiaryUnit: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  bankName: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  bankAccountNo: string

  @ApiPropertyOptional()
  description: string

  @ApiProperty()
  @IsNotEmpty()
  fileList: any[]
}
