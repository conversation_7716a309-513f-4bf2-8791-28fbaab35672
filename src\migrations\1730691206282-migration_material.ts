import { MigrationInterface, QueryRunner } from "typeorm";

export class migrationMaterial1730691206282 implements MigrationInterface {
    name = 'migrationMaterial1730691206282'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`pr_item\` ADD \`itemId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`pr_item\` ADD CONSTRAINT \`FK_55a685c4bc922de3605dc1b1c16\` FOREIGN KEY (\`itemId\`) REFERENCES \`material\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`pr_item\` DROP FOREIGN KEY \`FK_55a685c4bc922de3605dc1b1c16\``);
        await queryRunner.query(`ALTER TABLE \`pr_item\` DROP COLUMN \`itemId\``);
    }

}
