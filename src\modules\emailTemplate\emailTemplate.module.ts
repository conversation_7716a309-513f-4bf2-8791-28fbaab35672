import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { EmailTemplateService } from './emailTemplate.service'
import { EmailTemplateController } from './emailTemplate.controller'
import { EmailTemplateRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([EmailTemplateRepository])],
  controllers: [EmailTemplateController],
  providers: [EmailTemplateService],
})
export class EmailTemplateModule {}
