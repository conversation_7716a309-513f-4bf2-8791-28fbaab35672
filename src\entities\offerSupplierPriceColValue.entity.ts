import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { OfferSupplierEntity } from './offerSupplier.entity'
import { OfferPriceEntity } from './offerPrice.entity'
import { OfferPriceColEntity } from './offerPriceCol.entity'

@Entity('offer_supplier_price_col_value')
export class OfferSupplierPriceColValueEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerSupplierId: string
  @ManyToOne(() => OfferSupplierEntity, (p) => p.offerSupplierPriceColValue)
  @JoinColumn({ name: 'offerSupplierId', referencedColumnName: 'id' })
  offerSupplier: Promise<OfferSupplierEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerPriceId: string
  @ManyToOne(() => OfferPriceEntity, (p) => p.offerSupplierPriceColValue)
  @JoinColumn({ name: 'offerPriceId', referencedColumnName: 'id' })
  offerPrice: Promise<OfferPriceEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerPriceColId: string
  @ManyToOne(() => OfferPriceColEntity, (p) => p.offerSupplierPriceColValue)
  @JoinColumn({ name: 'offerPriceColId', referencedColumnName: 'id' })
  offerPriceCol: Promise<OfferPriceColEntity>
}
