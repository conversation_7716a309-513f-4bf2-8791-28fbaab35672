import { MigrationInterface, QueryRunner } from "typeorm";

export class updateTableSchemeAddForeignKey1673767177970 implements MigrationInterface {
    name = 'updateTableSchemeAddForeignKey1673767177970'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`scheme\` ADD \`prId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`scheme\` ADD CONSTRAINT \`FK_7c9eb25163968be8b50d92d0fe2\` FOREIGN KEY (\`prId\`) REFERENCES \`pr\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`scheme\` DROP FOREIGN KEY \`FK_7c9eb25163968be8b50d92d0fe2\``);
        await queryRunner.query(`ALTER TABLE \`scheme\` DROP COLUMN \`prId\``);
    }

}
