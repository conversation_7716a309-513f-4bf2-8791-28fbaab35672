import { BaseEntity } from './base.entity'
import { Entity, Column, JoinColumn, ManyToOne, OneToMany } from 'typeorm'
import { BranchEntity } from './branch.entity'
import { AsnItemEntity } from './asnItem.entity'
import { WarehouseEntity } from './warehouse.entity'
import { POEntity } from './po.entity'
import { ObjectEntity } from './object.entity'
import { PurchasePlanEntity } from './purchasePlan.entity'

/** Nhập kho */
@Entity('asn')
export class AsnEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Chi nhánh */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  branchId: string
  @ManyToOne(() => BranchEntity, (p) => p.asn)
  @JoinColumn({ name: 'branchId', referencedColumnName: 'id' })
  branch: Promise<BranchEntity>

  /** Kho */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  warehouseId: string
  @ManyToOne(() => WarehouseEntity, (p) => p.asn)
  @JoinColumn({ name: 'warehouseId', referencedColumnName: 'id' })
  warehouse: Promise<WarehouseEntity>

  /** Ngày nhập kho */
  @Column({
    nullable: false,
  })
  asnDate: Date

  @Column({
    nullable: false,
    default: 0,
  })
  quantity: number

  /** KHMH */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  purchasePlanId: string
  @ManyToOne(() => PurchasePlanEntity, (p) => p.asns)
  @JoinColumn({ name: 'purchasePlanId', referencedColumnName: 'id' })
  purchasePlan: Promise<PurchasePlanEntity>

  /** PO */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.asns)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Đối tượng */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  objectId: string
  @ManyToOne(() => ObjectEntity, (p) => p.asn)
  @JoinColumn({ name: 'objectId', referencedColumnName: 'id' })
  object: Promise<ObjectEntity>

  /** Giá vốn */
  @Column({ nullable: true, default: 0, type: 'decimal', scale: 4, precision: 20 })
  costPrice: number

  /** Giá bán */
  @Column({ nullable: true, default: 0, type: 'decimal', scale: 4, precision: 20 })
  sellPrice: number

  // hạn sử dụng
  @Column({ nullable: true })
  expiryDate: Date

  @OneToMany(() => AsnItemEntity, (p) => p.asn)
  asnItems: Promise<AsnItemEntity[]>
}
