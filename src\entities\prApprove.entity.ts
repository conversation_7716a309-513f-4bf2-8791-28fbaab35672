import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'
import { PrEntity } from './pr.entity'

/** lịch sử duyệt pr */
@Entity('pr_approve')
export class PrApproveEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  prId: string
  @ManyToOne(() => PrEntity, (p) => p.prApproves)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>

  /** cấp duyệt */
  @Column({
    nullable: false,
    default: 1,
  })
  level: number

  /** nv duyệt */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.prApproves)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  status: string

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string
}
