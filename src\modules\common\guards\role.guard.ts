import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { Request } from 'express'
import { UserDto } from '../../../dto'

@Injectable()
export class RoleGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(ctx: ExecutionContext): boolean {
    const roles = this.reflector.get<string[]>('roles', ctx.getHandler())

    if (!roles) return true

    const request = ctx.switchToHttp().getRequest<Request>()
    const user = request.user as UserDto
    if (user.hasAllRoles) return true

    let lstRoleUser = []
    if (user.roles) lstRoleUser = user.roles.map((c) => c.code)
    const checkRole = roles.some((c) => lstRoleUser.includes(c))
    if (!checkRole) throw new Error(`<PERSON>ạn không có quyền thực hiện chức năng này! (code: ROLE_ERROR)`)

    return checkRole
  }
}
