import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { ServiceSettingService } from './serviceSetting.service'
import { ServiceSettingController } from './serviceSetting.controller'
import {
  ServiceCapacityRepository,
  ServiceCustomPriceRepository,
  ServicePriceColRepository,
  ServicePriceRepository,
  ServiceRepository,
  ServiceTechRepository,
  ServiceTradeRepository,
} from '../../repositories'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      ServiceCapacityRepository,
      ServiceTechRepository,
      ServiceTradeRepository,
      ServicePriceRepository,
      ServicePriceColRepository,
      ServiceCustomPriceRepository,
      ServiceRepository,
    ]),
  ],
  controllers: [ServiceSettingController],
  providers: [ServiceSettingService],
})
export class ServiceSettingModule {}
