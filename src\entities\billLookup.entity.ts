import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'
import { BillEntity } from './bill.entity'

/**Tra cứu hóa đơn*/
@Entity('bill_lookup')
export class BillLookupEntity extends BaseEntity {
  /** Mã */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** tên */
  @Column({
    type: 'varchar',
    length: 200,
    nullable: true,
  })
  name: string

  /** Tên viết tắt */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  abbreviation: string

  /**Link trang web tra cứu hóa đơn */
  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  link: string

  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  description: string

  @OneToMany(() => BillEntity, (p) => p.billLookup)
  bills: Promise<BillEntity[]>
}
