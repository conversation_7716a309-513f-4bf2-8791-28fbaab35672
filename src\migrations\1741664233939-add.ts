import { MigrationInterface, QueryRunner } from "typeorm";

export class add1741664233939 implements MigrationInterface {
    name = 'add1741664233939'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid\` ADD \`isSkipEnd\` tinyint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid\` DROP COLUMN \`isSkipEnd\``);
    }

}
