import { MigrationInterface, QueryRunner } from "typeorm";

export class addKpiColumEmployee1673423570611 implements MigrationInterface {
    name = 'addKpiColumEmployee1673423570611'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`service\` DROP COLUMN \`kpi\``);
        await queryRunner.query(`ALTER TABLE \`employee\` ADD \`kpi\` float NOT NULL DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`employee\` DROP COLUMN \`kpi\``);
        await queryRunner.query(`ALTER TABLE \`service\` ADD \`kpi\` float NOT NULL DEFAULT '0'`);
    }

}
