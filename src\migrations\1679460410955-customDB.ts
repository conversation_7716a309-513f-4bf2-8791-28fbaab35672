import { MigrationInterface, QueryRunner } from 'typeorm'

export class customDB1679460410955 implements MigrationInterface {
  name = 'customDB1679460410955'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`supplier_expertise\` DROP FOREIGN KEY \`FK_337bed433a9811ccf8dcd6b704f\``)
    await queryRunner.query(`ALTER TABLE \`supplier_expertise_law_detail\` DROP COLUMN \`comment\``)
    await queryRunner.query(`ALTER TABLE \`supplier_expertise\` DROP COLUMN \`approvedCapacityId\``)
    await queryRunner.query(`ALTER TABLE \`supplier_expertise_law_detail\` CHANGE \`code\` \`code\` varchar(50) NULL`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`supplier_expertise_law_detail\` CHANGE \`code\` \`code\` varchar(50) NOT NULL`)
    await queryRunner.query(`ALTER TABLE \`supplier_expertise\` ADD \`approvedCapacityId\` varchar(36) NULL`)
    await queryRunner.query(`ALTER TABLE \`supplier_expertise_law_detail\` ADD \`comment\` text NULL`)
    await queryRunner.query(
      `ALTER TABLE \`supplier_expertise\` ADD CONSTRAINT \`FK_337bed433a9811ccf8dcd6b704f\` FOREIGN KEY (\`approvedCapacityId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }
}
