import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, Join<PERSON><PERSON>umn, OneToMany } from 'typeorm'
import { ServiceEntity } from './service.entity'
import { SupplierEntity } from './supplier.entity'
import { SupplierServiceEntity } from './supplierService.entity'
import { ServiceCapacityEntity } from './serviceCapacity.entity'
import { SupplierCapacityListDetailEntity } from './supplierCapacityListDetail.entity'
import { SupplierCapacityYearValueEntity } from './supplierCapacityYearValue.entity'
import { SupplierExpertiseDetailEntity } from './supplierExpertiseDetail.entity'

/** Thông tin năng lực NCC */
@Entity('supplier_capacity')
export class SupplierCapacityEntity extends BaseEntity {
  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
    default: 'string',
  })
  type: string

  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  /** <PERSON><PERSON>ch tính điểm theo loại càng cao càng tốt */
  @Column({
    nullable: false,
    default: true,
  })
  isCalUp: boolean

  /** Thay đổi giá trị theo năm. Chỉ áp dụng cho string - number -file */
  @Column({
    nullable: false,
    default: false,
  })
  isChangeByYear: boolean

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  value: string

  /** % tỉ trọng */
  @Column({
    type: 'float',
    nullable: true,
  })
  percent: number

  /** % điều kiện đạt tỉ trọng */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  percentRule: number

  /** % điều kiện liệt tỉ trọng khi tính theo chiều giảm dần */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  percentDownRule: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Id của công thức cha */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  parentId: string
  /** Cha */
  @ManyToOne(() => SupplierCapacityEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: SupplierCapacityEntity

  /** Con - 1 công thức sẽ có thể có nhiều con */
  @OneToMany(() => SupplierCapacityEntity, (p) => p.parent)
  childs: Promise<SupplierCapacityEntity[]>

  /** Con - 1 công thức sẽ có thể có nhiều detail list */
  @OneToMany(() => SupplierCapacityListDetailEntity, (p) => p.supplierCapacity)
  supplierCapacityListDetails: Promise<SupplierCapacityListDetailEntity[]>

  /** 1 công thức sẽ có thể có nhiều giá trị theo năm */
  @OneToMany(() => SupplierCapacityYearValueEntity, (p) => p.supplierCapacity)
  supplierCapacityYearValues: Promise<SupplierCapacityYearValueEntity[]>

  @OneToMany(() => SupplierExpertiseDetailEntity, (p) => p.supplierCapacity)
  supplierCapacityExpertises: Promise<SupplierExpertiseDetailEntity[]>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  serviceId: string
  @ManyToOne(() => ServiceEntity, (p) => p.supplierCapacities)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: ServiceEntity

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  serviceCapacityId: string
  @ManyToOne(() => ServiceCapacityEntity, (p) => p.supplierCapacities)
  @JoinColumn({ name: 'serviceCapacityId', referencedColumnName: 'id' })
  serviceCapacity: Promise<ServiceCapacityEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.supplierCapacities)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  supplierServiceId: string
  @ManyToOne(() => SupplierServiceEntity, (p) => p.capacities)
  @JoinColumn({ name: 'supplierServiceId', referencedColumnName: 'id' })
  supplierService: Promise<SupplierServiceEntity>
}
