import { Injectable, NotFoundException } from '@nestjs/common'
import {
  BidRepository,
  BidSupplierRepository,
  BidTechRepository,
  BidTradeRepository,
  BidPriceRepository,
  BidDealRepository,
  BidAuctionRepository,
  BidPriceColRepository,
  SupplierServiceRepository,
} from '../../repositories'
import {
  BidSupplierPriceValueEntity,
  BidHistoryEntity,
  BidAuctionSupplierPriceValueEntity,
  BidSupplierTechValueEntity,
  BidSupplierTradeValueEntity,
  BidDealSupplierEntity,
  BidDealSupplierPriceValueEntity,
  BidAuctionSupplierEntity,
} from '../../entities'
import { In, Like } from 'typeorm'
import { enumData, ERROR_NOT_FOUND_DATA } from '../../constants'
import { BidRateService } from '../bid/bidRate/bidRate.service'
import { coreHelper } from '../../helpers'
import { PaginationDto, UserDto } from '../../dto'

@Injectable()
export class BidDetailService {
  constructor(
    private readonly repo: BidRepository,
    private readonly supplierServiceRepo: SupplierServiceRepository,
    private readonly bidDealRepo: BidDealRepository,
    private readonly bidAuctionRepo: BidAuctionRepository,
    private readonly bidTechRepo: BidTechRepository,
    private readonly bidTradeRepo: BidTradeRepository,
    private readonly bidPriceRepo: BidPriceRepository,
    private readonly bidPriceColRepo: BidPriceColRepository,
    private readonly bidSupplierRepo: BidSupplierRepository,
    private readonly bidRateService: BidRateService,
  ) {}

  /** Lấy danh sách Doanh nghiệp bổ sung hồ sơ kỹ thuật theo bidTechId */
  async getSupplierTechByBidId(user: UserDto, bidTechId: string, data: PaginationDto) {
    const res: any[] = await this.repo.manager.getRepository(BidSupplierTechValueEntity).findAndCount({
      where: { bidTechId: bidTechId, companyId: user.companyId, isDeleted: false },
      relations: { bidTech: { bidTechListDetails: true }, bidSupplier: { supplier: true } },
      skip: data.skip,
      take: data.take,
    })

    for (let item of res[0]) {
      item.score = this.bidRateService.calScoreTechItem(item.__bidTech__, item.value)
      item.supplierId = item.__bidSupplier__.supplierId
      item.supplierName = item.__bidSupplier__.__supplier__.name
      delete item.__bidSupplier__
    }

    res[0].sort((a, b) => b.score - a.score)
    return res
  }

  /** Lấy danh sách Doanh nghiệp bổ sung hồ sơ thương mại theo bidTradeId */
  async getSupplierTradeByBidId(user: UserDto, bidTradeId: string, data: PaginationDto) {
    const res: any[] = await this.repo.manager.getRepository(BidSupplierTradeValueEntity).findAndCount({
      where: { bidTradeId: bidTradeId, companyId: user.companyId, isDeleted: false },
      skip: data.skip,
      take: data.take,
      relations: { bidTrade: { bidTradeListDetails: true }, bidSupplier: { supplier: true } },
    })
    for (let item of res[0]) {
      item.score = this.bidRateService.calScoreTradeItem(item.__bidTrade__, item.value)
      item.supplierId = item.__bidSupplier__.supplierId
      item.supplierName = item.__bidSupplier__.__supplier__.name
      delete item.__bidSupplier__
    }

    res[0].sort((a, b) => b.score - a.score)
    return res //.sort((a, b) => b.score - a.scoreTech)
  }

  /** Lấy danh sách Doanh nghiệp bổ sung hồ sơ giá theo bidPriceId */
  async getSupplierPriceByBidId(user: UserDto, bidPriceId: string, data: PaginationDto) {
    const res: any[] = await this.repo.manager.getRepository(BidSupplierPriceValueEntity).findAndCount({
      where: { bidPriceId: bidPriceId, companyId: user.companyId, isDeleted: false },
      skip: data.skip,
      take: data.take,
      relations: { bidPrice: true, bidSupplier: { supplier: true } },
      order: { value: 'ASC' },
    })

    for (const item of res[0]) {
      item.supplierId = item.__bidSupplier__.supplierId
      item.supplierName = item.__bidSupplier__.__supplier__.name
      delete item.__bidSupplier__
    }

    return res
  }

  /** Chi tiết gói thầu */
  async findDetail(user: UserDto, data: { id: string }) {
    const res: any = await this.repo.findOne({
      where: { id: data.id, companyId: user.companyId },
      relations: {
        pr: true,
        bidType: true,
        masterBidGuarantee: true,
        employeeAccess: { employee: true },
        childs: { service: true },
        bidItems: { item: true },
        service: true,
      },
    })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)
    if (res.isDeleted) throw new Error('Gói thầu đã ngưng hoạt động')

    res.prCode = res.__pr__?.code || ''
    res.prName = res.__pr__?.name || ''
    delete res.__pr__

    res.bidTypeName = res.__bidType__?.name || ''
    delete res.__bidType__
    res.masterBidGuaranteeName = res.__masterBidGuarantee__?.name || ''
    delete res.__masterBidGuarantee__

    const lstAccess = res.__employeeAccess__ || []
    delete res.__employeeAccess__
    res.listMember = lstAccess
      .filter((c) => c.type === enumData.BidRuleType.Memmber.code)
      .map((c) => c.__employee__.name)
      .join(' - ')

    res.listOther = lstAccess
      .filter((c) => c.type === enumData.BidRuleType.Other.code)
      .map((c) => c.__employee__.name)
      .join(' - ')

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }
    res.statusName = dicStatus[res.status]

    const mpoObj = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
    if (mpoObj) res.mpo = mpoObj.__employee__.name

    const mpoLeaderObj = lstAccess.find((c) => c.type === enumData.BidRuleType.MPOLeader.code)
    if (mpoLeaderObj) res.mpoLeader = mpoLeaderObj.__employee__.name

    const techObj = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
    if (techObj) res.tech = techObj.__employee__.name

    const techLeaderObj = lstAccess.find((c) => c.type === enumData.BidRuleType.TechLeader.code)
    if (techLeaderObj) res.techLeader = techLeaderObj.__employee__.name

    res.__bidItems__ = res.__bidItems__ || []

    res.listItem = []
    for (const item of res.__bidItems__) {
      res.listItem.push({
        itemName: item.__item__?.code + ' - ' + item.__item__?.name,
        quantityItem: item.quantityItem,
      })
    }
    delete res.__childs__

    return res
  }

  async checkDealAndAuction(user: UserDto, bidId: string) {
    const result: any = await this.repo.findOne({
      where: { id: bidId, companyId: user.companyId, isDeleted: false },
      relations: { bidDeals: true, bidAuctions: true },
      select: { id: true, status: true, bidDeals: { id: true }, bidAuctions: { id: true } },
    })
    if (!result) throw new NotFoundException('Không tìm thấy thông tin gói thầu.')

    const setStatus = new Set()
    setStatus.add(enumData.BidStatus.DangDanhGia.code)
    setStatus.add(enumData.BidStatus.DangDuyetDanhGia.code)
    setStatus.add(enumData.BidStatus.HoanTatDanhGia.code)
    setStatus.add(enumData.BidStatus.DongDamPhanGia.code)
    setStatus.add(enumData.BidStatus.DongDauGia.code)
    setStatus.add(enumData.BidStatus.DangDamPhanGia.code)
    setStatus.add(enumData.BidStatus.DangDauGia.code)
    setStatus.add(enumData.BidStatus.DongThau.code)
    setStatus.add(enumData.BidStatus.DuyetNCCThangThau.code)
    setStatus.add(enumData.BidStatus.DangDuyetKetThucThau.code)
    setStatus.add(enumData.BidStatus.HoanTat.code)

    /** Kết quả đánh giá */
    const isShowResult = setStatus.has(result.status)
    /** Kết quả đàm phán */
    const isShowDeal = isShowResult && result.__bidDeals__?.length > 0
    /** Kết quả đấu giá */
    const isShowAuction = isShowResult && result.__bidAuctions__?.length > 0

    return { isShowResult, isShowDeal, isShowAuction }
  }

  /** Lịch sử gói thầu */
  async paginationHistory(user: UserDto, data: PaginationDto) {
    if (!data.where.bidId) throw new Error(ERROR_NOT_FOUND_DATA)

    const res: any[] = await this.repo.manager.getRepository(BidHistoryEntity).findAndCount({
      where: { bidId: data.where.bidId, companyId: user.companyId },
      relations: { employee: true },
      skip: data.skip,
      take: data.take,
      select: { id: true, createdAt: true, status: true, employee: { id: true, name: true } },
      order: { createdAt: 'ASC' },
    })
    if (res[0].length == 0) return res

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidHistoryStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }
    for (const item of res[0]) {
      item.statusName = dicStatus[item.status] || ''
      item.employeeName = item.__employee__?.name || 'SYSTEM'
      delete item.__employee__
    }

    return res
  }

  /** Lấy kết quả đánh giá gói thầu */
  async loadBidResult(user: UserDto, data: { bidId: string; name?: string; statusFile?: string[] }) {
    const res = await this.repo.getBid2(user, data.bidId)
    for (const item of res.listItem) {
      let whereCon: any = { bidId: item.id, companyId: user.companyId }
      if (data.name) whereCon.supplier = { name: Like(`%${data.name}%`) }
      if (data.statusFile?.length > 0) whereCon.statusFile = In(data.statusFile)

      item.lstBidSupplier = await this.bidSupplierRepo.find({
        where: whereCon,
        relations: { supplier: true },
        select: {
          id: true,
          bidId: true,
          supplierId: true,
          statusFile: true,
          statusTech: true,
          scoreTech: true,
          scoreManualTech: true,
          statusTrade: true,
          scoreTrade: true,
          scoreManualTrade: true,
          statusPrice: true,
          scorePrice: true,
          scoreManualPrice: true,
          supplier: { id: true, name: true },
        },
      })
      const lstAll = await this.bidSupplierRepo.find({
        where: { bidId: item.id, companyId: user.companyId },
        select: { id: true, scorePrice: true, scoreManualPrice: true },
      })
      const lstValue = lstAll.map((c) => c.scorePrice)
      const maxValue = Math.max(...lstValue)
      const dlc = coreHelper.calDLC(lstValue)

      const lstValueManual = lstAll.map((c) => c.scoreManualPrice)
      const maxValueManual = Math.max(...lstValueManual)
      const dlcManual = coreHelper.calDLC(lstValueManual)

      const dicStatusFile: any = {}
      {
        const lstStatusFile = coreHelper.convertObjToArray(enumData.BidSupplierFileStatus)
        lstStatusFile.forEach((c) => (dicStatusFile[c.code] = c.name))
      }

      for (const itemBidSupplier of item.lstBidSupplier) {
        itemBidSupplier.supplierName = itemBidSupplier.__supplier__.name
        delete itemBidSupplier.__supplier__
        itemBidSupplier.statusFileName = dicStatusFile[itemBidSupplier.statusFile]
        let isHasTotal = false
        itemBidSupplier.scoreTotal = 0
        itemBidSupplier.scoreManualTotal = 0
        if (
          itemBidSupplier.statusTech === enumData.BidSupplierTechStatus.DaXacNhan.code ||
          itemBidSupplier.statusTech === enumData.BidSupplierTechStatus.DaDuyet.code
        ) {
          isHasTotal = true
          itemBidSupplier.scoreTotal += (itemBidSupplier.scoreTech * item.percentTech) / 100
          itemBidSupplier.scoreManualTotal += (itemBidSupplier.scoreManualTech * item.percentTech) / 100
        } else {
          itemBidSupplier.scoreTech = -1
          itemBidSupplier.scoreManualTech = -1
        }

        if (
          itemBidSupplier.statusTrade === enumData.BidSupplierTradeStatus.DaXacNhan.code ||
          itemBidSupplier.statusTrade === enumData.BidSupplierTradeStatus.DaDuyet.code
        ) {
          isHasTotal = true
          itemBidSupplier.scoreTotal += (itemBidSupplier.scoreTrade * item.percentTrade) / 100
          itemBidSupplier.scoreManualTotal += (itemBidSupplier.scoreManualTrade * item.percentTrade) / 100
        } else {
          itemBidSupplier.scoreTrade = -1
          itemBidSupplier.scoreManualTrade = -1
        }

        if (
          itemBidSupplier.statusPrice === enumData.BidSupplierPriceStatus.DaXacNhan.code ||
          itemBidSupplier.statusPrice === enumData.BidSupplierPriceStatus.DaDuyet.code
        ) {
          isHasTotal = true
          let priceScore = 0
          if (dlc > 0) {
            priceScore = item.percentPrice - (maxValue - itemBidSupplier.scorePrice) / dlc
          } else {
            priceScore = item.percentPrice
          }
          itemBidSupplier.scoreTotal += priceScore

          let priceManualScore = 0
          if (dlcManual > 0) {
            priceManualScore = item.percentPrice - (maxValueManual - itemBidSupplier.scoreManualPrice) / dlcManual
          } else {
            priceManualScore = item.percentPrice
          }
          itemBidSupplier.scoreManualTotal += priceManualScore
        } else {
          itemBidSupplier.scorePrice = -1
          itemBidSupplier.scoreManualPrice = -1
        }

        if (!isHasTotal) {
          itemBidSupplier.scoreTotal = -1
          itemBidSupplier.scoreManualTotal = -1
        }
      }

      item.lstBidSupplier.sort((a, b) => b.scoreTotal - a.scoreTotal)
    }

    return res
  }

  /** Lấy kết quả đánh giá năng lực của Doanh nghiệp tham gia gói thầu */
  async getBidResultCapacity(user: UserDto, bidId: string, supplierId: string) {
    // Lấy hồ sơ năng lực của Doanh nghiệp tham gia
    const res: any = await this.bidSupplierRepo.findOne({
      where: { bidId, supplierId, companyId: user.companyId },
      relations: { bid: true, supplier: true },
    })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    res.supplierName = res.__supplier__.name
    delete res.__supplier__

    const serviceId = res.__bid__.serviceId
    res.bidName = res.__bid__.name
    res.approveChooseSupplierWinDate = res.__bid__.approveChooseSupplierWinDate
    delete res.__bid__

    // Lấy template hồ sơ năng lực của lvmh gói thầu
    const supplierService = await this.supplierServiceRepo.findOne({ where: { supplierId, serviceId, companyId: user.companyId } })
    if (!supplierService) throw new Error(ERROR_NOT_FOUND_DATA)

    res.bidCapacitys = await this.supplierServiceRepo.findCapacity(user, supplierService.id)

    return res
  }

  /** Lấy kết quả đánh giá điều kiện kỹ thuật */
  async getBidResultTech(user: UserDto, bidId: string, supplierId: string) {
    // Lấy hồ sơ kỹ thuật của các Doanh nghiệp tham gia
    const res: any = await this.bidSupplierRepo.findOne({
      where: { bidId, supplierId, companyId: user.companyId },
      relations: { bid: true, supplier: true, bidSupplierTechValue: true },
    })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    res.supplierName = res.__supplier__.name
    delete res.__supplier__

    res.bidName = res.__bid__.name
    res.approveChooseSupplierWinDate = res.__bid__.approveChooseSupplierWinDate
    delete res.__bid__

    res.bidTechs = await this.bidTechRepo.getTech(user, bidId)

    return res
  }

  /** Lấy kết quả đánh giá điều kiện thương mại */
  async getBidResultTrade(user: UserDto, bidId: string, supplierId: string) {
    const res: any = await this.bidSupplierRepo.findOne({
      where: { bidId, supplierId, companyId: user.companyId },
      relations: { bid: true, supplier: true, bidSupplierTradeValue: true },
    })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    res.supplierName = res.__supplier__.name
    delete res.__supplier__

    res.bidName = res.__bid__.name
    res.approveChooseSupplierWinDate = res.__bid__.approveChooseSupplierWinDate
    delete res.__bid__

    res.bidTrades = await this.bidTradeRepo.getTrade(user, bidId)

    return res
  }

  /** Lấy kết quả đánh giá điều kiện giá */
  async getBidResultPrice(user: UserDto, bidId: string, supplierId: string) {
    const res: any = await this.bidSupplierRepo.findOne({
      where: { bidId, supplierId, companyId: user.companyId },
      relations: {
        bid: { bidAuctions: true, bidDeals: { bidDealPrices: true } },
        supplier: true,
        bidSupplierPriceValue: true,
        bidSupplierPriceColValue: true,
      },
    })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    res.supplierName = res.__supplier__.name
    delete res.__supplier__

    const bid = res.__bid__
    res.bidName = bid.name
    res.approveChooseSupplierWinDate = bid.approveChooseSupplierWinDate
    delete res.__bid__

    res.bidPrices = await this.bidPriceRepo.getPrice(user, bidId)

    //#region Map data cột động
    const lstBidPriceCol = await this.bidPriceColRepo.getBidPriceColAll(user, bidId)
    if (lstBidPriceCol.length > 0) {
      res.bidPriceCols = lstBidPriceCol
      const lstPriceColValue = res.__bidSupplierPriceColValue__ || []
      delete res.__bidSupplierPriceColValue__
      // function get data dynamic col by row
      const getDataRow = (row: any) => {
        for (const col of lstBidPriceCol) {
          row[col.id] = ''
          if (col.colType === enumData.ColType.MPO.code) {
            if (row.__bidPriceColValue__?.length > 0) {
              const cell = row.__bidPriceColValue__.find((c: any) => c.bidPriceColId === col.id)
              if (cell) row[col.id] = cell.value
            }
          }
          if (col.colType === enumData.ColType.Supplier.code) {
            const cell = lstPriceColValue.find((c) => c.bidPriceColId === col.id && c.bidPriceId === row.id)
            if (cell) row[col.id] = cell.value
          }
        }
      }

      for (const rowLv1 of res.bidPrices) {
        getDataRow(rowLv1)
        for (const rowLv2 of rowLv1.__childs__) {
          getDataRow(rowLv2)
          for (const rowLv3 of rowLv2.__childs__) {
            getDataRow(rowLv3)
          }
        }
      }
    }
    //#endregion

    //#region Đấu giá
    let flagStop = false
    const lstBidAuction = bid.__bidAuctions__ || []
    if (lstBidAuction.length > 0) {
      const lstBidAuctionId = lstBidAuction.map((c) => c.id)
      const bidAuctionSupplier: any = await this.repo.manager.getRepository(BidAuctionSupplierEntity).findOne({
        where: {
          bidAuctionId: In(lstBidAuctionId),
          supplierId,
          companyId: user.companyId,
          status: enumData.BidAuctionSupplierStatus.DaDauGia.code,
        },
        relations: { bidAuctionSupplierPriceValue: true },
        order: { createdAt: 'DESC' },
      })
      if (bidAuctionSupplier) {
        flagStop = true // nếu có đấu giá thì lấy giá theo kết quả đấu giá mới nhất
        const bidAuction = lstBidAuction.find((c) => c.id == bidAuctionSupplier.bidAuctionId)
        if (!bidAuction) return {}

        const lstPriceValue = bidAuctionSupplier.__bidAuctionSupplierPriceValue__
        for (const rowLv1 of res.bidPrices) {
          const priceValue = lstPriceValue.find((c) => c.bidPriceId == rowLv1.id)
          if (priceValue) {
            rowLv1.value = priceValue.value
            rowLv1.score = priceValue.score
          }
          for (const rowLv2 of rowLv1.__childs__) {
            const priceValue2 = lstPriceValue.find((c) => c.bidPriceId == rowLv2.id)
            if (priceValue2) {
              rowLv2.value = priceValue2.value
              rowLv2.score = priceValue2.score
            }
            for (const rowLv3 of rowLv2.__childs__) {
              const priceValue3 = lstPriceValue.find((c) => c.bidPriceId == rowLv3.id)
              if (priceValue3) {
                rowLv3.value = priceValue3.value
                rowLv3.score = priceValue3.score
              }
            }
          }
        }
      }
    }

    if (flagStop) return res
    //#endregion

    //#region Đàm phán giá
    const lstBidDeal = bid.__bidDeals__ || []
    if (lstBidDeal.length > 0) {
      const lstBidDealId = lstBidDeal.map((c) => c.id)
      // Lấy lần đàm phán cuối cùng mà Doanh nghiệp tham gia
      const bidDealSupplier: any = await this.repo.manager.getRepository(BidDealSupplierEntity).findOne({
        where: {
          bidDealId: In(lstBidDealId),
          supplierId,
          companyId: user.companyId,
          status: enumData.BidDealSupplierStatus.DaGuiGiaMoi.code,
        },
        relations: { bidDealSupplierPriceValue: true },
        order: { createdAt: 'DESC' },
      })
      if (bidDealSupplier) {
        flagStop = true
        const bidDeal = lstBidDeal.find((c) => c.id == bidDealSupplier.bidDealId)
        if (!bidDeal) return {}
        const bidDealPrices = bidDeal.__bidDealPrices__ || []

        for (const price of res.bidPrices) {
          const bidDealPrice = bidDealPrices.find((c) => c.bidPriceId == price.id)
          if (bidDealPrice && bidDealPrice.number > 0) {
            price.number = bidDealPrice.number
          }
        }

        const lstPriceValue = bidDealSupplier.__bidDealSupplierPriceValue__ || []
        for (const rowLv1 of res.bidPrices) {
          const priceValue = lstPriceValue.find((c) => c.bidPriceId == rowLv1.id)
          if (priceValue) {
            rowLv1.value = priceValue.value
            rowLv1.score = priceValue.score
          }
          for (const rowLv2 of rowLv1.__childs__) {
            const priceValue2 = lstPriceValue.find((c) => c.bidPriceId == rowLv2.id)
            if (priceValue2) {
              rowLv2.value = priceValue2.value
              rowLv2.score = priceValue2.score
            }
            for (const rowLv3 of rowLv2.__childs__) {
              const priceValue3 = lstPriceValue.find((c) => c.bidPriceId == rowLv3.id)
              if (priceValue3) {
                rowLv3.value = priceValue3.value
                rowLv3.score = priceValue3.score
              }
            }
          }
        }
      }
    }

    if (flagStop) return res
    //#endregion

    //#region Chào giá
    if (
      res.status == enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code ||
      res.status == enumData.BidSupplierStatus.DangDanhGia.code ||
      res.status == enumData.BidSupplierStatus.DaDanhGia.code
    ) {
      const lstPriceValue = res.__bidSupplierPriceValue__ || []
      delete res.__bidSupplierPriceValue__
      for (const rowLv1 of res.bidPrices) {
        const priceValue = lstPriceValue.find((c) => c.bidPriceId == rowLv1.id)
        if (priceValue) {
          rowLv1.value = priceValue.value
          rowLv1.score = priceValue.score
        }
        for (const rowLv2 of rowLv1.__childs__) {
          const priceValue2 = lstPriceValue.find((c) => c.bidPriceId == rowLv2.id)
          if (priceValue2) {
            rowLv2.value = priceValue2.value
            rowLv2.score = priceValue2.score
          }
          for (const rowLv3 of rowLv2.__childs__) {
            const priceValue3 = lstPriceValue.find((c) => c.bidPriceId == rowLv3.id)
            if (priceValue3) {
              rowLv3.value = priceValue3.value
              rowLv3.score = priceValue3.score
            }
          }
        }
      }
    }
    //#endregion

    return res
  }

  /** Lấy lịch sử chào giá của Doanh nghiệp */
  async getBidHistoryPrice(user: UserDto, bidId: string, supplierId: string = '') {
    const supplierID = user.supplierId || supplierId
    let lstResult: any[] = []
    const lstBidPrice: any[] = await this.bidPriceRepo.getPrice(user, bidId)

    //#region Bảng chào giá
    const bidSupplier = await this.bidSupplierRepo.findOne({ where: { bidId, supplierId: supplierID, companyId: user.companyId } })
    if (!bidSupplier) return {}
    const supplier = await bidSupplier.supplier
    const bid = await bidSupplier.bid

    const lstBidPriceCol = await this.bidPriceColRepo.getBidPriceColAll(user, bidId)
    if (lstBidPriceCol.length > 0) {
      const lstPriceColValue = await bidSupplier.bidSupplierPriceColValue
      // function get data dynamic col by row
      const getDataRow = (row: any) => {
        for (const col of lstBidPriceCol) {
          row[col.id] = ''
          if (col.colType === enumData.ColType.MPO.code) {
            if (row.__bidPriceColValue__?.length > 0) {
              const cell = row.__bidPriceColValue__.find((c: any) => c.bidPriceColId === col.id)
              if (cell) row[col.id] = cell.value
            }
          }
          if (col.colType === enumData.ColType.Supplier.code) {
            const cell = lstPriceColValue.find((c) => c.bidPriceColId === col.id && c.bidPriceId === row.id)
            if (cell) row[col.id] = cell.value
          }
        }
      }
      for (const data1 of lstBidPrice) {
        getDataRow(data1)
        for (const data2 of data1.__childs__) {
          getDataRow(data2)
          for (const data3 of data2.__childs__) {
            getDataRow(data3)
          }
        }
      }
    }

    if (
      bidSupplier.status == enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code ||
      bidSupplier.status == enumData.BidSupplierStatus.DangDanhGia.code ||
      bidSupplier.status == enumData.BidSupplierStatus.DaDanhGia.code
    ) {
      const res: any = {}
      res.name = `Chào giá ${coreHelper.dateToString(bidSupplier.createdAt)}`
      res.date = bidSupplier.createdAt
      res.type = 0
      res.lstPrice = lstBidPrice.map((object) => ({ ...object }))
      res.filePriceDetail = bidSupplier.filePriceDetail
      res.fileTechDetail = bidSupplier.fileTechDetail
      res.lstPriceCol = [...lstBidPriceCol]
      const lstPriceValue = await bidSupplier.bidSupplierPriceValue
      for (const data1 of res.lstPrice) {
        const priceValue = lstPriceValue.find((c) => c.bidPriceId == data1.id)
        if (priceValue) {
          data1.value = priceValue.value
        }
        for (const data2 of data1.__childs__) {
          const priceValue2 = lstPriceValue.find((c) => c.bidPriceId == data2.id)
          if (priceValue2) {
            data2.value = priceValue2.value
          }
          for (const data3 of data2.__childs__) {
            const priceValue3 = lstPriceValue.find((c) => c.bidPriceId == data3.id)
            if (priceValue3) {
              data3.value = priceValue3.value
            }
          }
        }
      }

      lstResult.push(res)
    }
    //#endregion

    //#region Đàm phán giá
    const lstBidDeal = await bid.bidDeals
    if (lstBidDeal.length > 0) {
      const lstBidDealId = lstBidDeal.map((c) => c.id)
      const lstBidDealSupplier = await this.repo.manager.getRepository(BidDealSupplierEntity).find({
        where: {
          bidDealId: In(lstBidDealId),
          supplierId: supplierID,
          companyId: user.companyId,
          status: enumData.BidDealSupplierStatus.DaGuiGiaMoi.code,
        },
      })
      if (lstBidDealSupplier.length > 0) {
        for (const bidDealSupplier of lstBidDealSupplier) {
          const bidDeal = lstBidDeal.find((c) => c.id == bidDealSupplier.bidDealId)
          if (!bidDeal) return {}
          const bidDealPrices = await bidDeal.bidDealPrices

          const res: any = {}
          res.name = `Đàm phán giá ${coreHelper.dateToString(bidDeal.createdAt)}`
          res.date = bidDeal.createdAt
          res.type = 1
          res.lstPrice = lstBidPrice.map((object) => ({ ...object }))
          res.filePriceDetail = bidDealSupplier.filePriceDetail
          res.fileTechDetail = bidDealSupplier.fileTechDetail
          res.linkDriver = bidDealSupplier.linkDriver
          for (const price of res.lstPrice) {
            const bidDealPrice = bidDealPrices.find((c) => c.bidPriceId == price.id)
            if (bidDealPrice && bidDealPrice.number > 0) {
              price.number = bidDealPrice.number
            }
          }
          res.lstPriceCol = [...lstBidPriceCol]
          const lstPriceValue = await bidDealSupplier.bidDealSupplierPriceValue
          for (const data1 of res.lstPrice) {
            const priceValue = lstPriceValue.find((c) => c.bidPriceId == data1.id)
            if (priceValue) {
              data1.value = priceValue.value
            }
            for (const data2 of data1.__childs__) {
              const priceValue2 = lstPriceValue.find((c) => c.bidPriceId == data2.id)
              if (priceValue2) {
                data2.value = priceValue2.value
              }
              for (const data3 of data2.__childs__) {
                const priceValue3 = lstPriceValue.find((c) => c.bidPriceId == data3.id)
                if (priceValue3) {
                  data3.value = priceValue3.value
                }
              }
            }
          }

          lstResult.push(res)
        }
      }
    }
    //#endregion

    //#region Đấu giá
    const lstBidAuction = await bid.bidAuctions
    if (lstBidAuction.length > 0) {
      const lstBidAuctionId = lstBidAuction.map((c) => c.id)
      const lstBidAuctionSupplier = await this.repo.manager.getRepository(BidAuctionSupplierEntity).find({
        where: {
          bidAuctionId: In(lstBidAuctionId),
          supplierId: supplierID,
          companyId: user.companyId,
          status: enumData.BidAuctionSupplierStatus.DaDauGia.code,
        },
      })
      if (lstBidAuctionSupplier.length > 0) {
        for (const bidAuctionSupplier of lstBidAuctionSupplier) {
          const bidAuction = lstBidAuction.find((c) => c.id == bidAuctionSupplier.bidAuctionId)
          if (!bidAuction) return {}

          const res: any = {}
          res.name = `Đấu giá ${coreHelper.dateToString(bidAuction.createdAt)}`
          res.date = bidAuction.createdAt
          res.type = 2
          res.lstPrice = lstBidPrice.map((object) => ({ ...object }))
          res.lstPriceCol = [...lstBidPriceCol]
          const lstPriceValue = await bidAuctionSupplier.bidAuctionSupplierPriceValue
          for (const data1 of res.lstPrice) {
            const priceValue = lstPriceValue.find((c) => c.bidPriceId == data1.id)
            if (priceValue) {
              data1.value = priceValue.value
            }
            for (const data2 of data1.__childs__) {
              const priceValue2 = lstPriceValue.find((c) => c.bidPriceId == data2.id)
              if (priceValue2) {
                data2.value = priceValue2.value
              }
              for (const data3 of data2.__childs__) {
                const priceValue3 = lstPriceValue.find((c) => c.bidPriceId == data3.id)
                if (priceValue3) {
                  data3.value = priceValue3.value
                }
              }
            }
          }

          lstResult.push(res)
        }
      }
    }
    //#endregion

    lstResult = lstResult.sort((a, b) => {
      if (a.date < b.date) return 1
      else return -1
    })

    return { bid, bidSupplier: { ...bidSupplier, supplierName: supplier.name, bidName: bid.name }, lstResult }
  }

  /** Lấy kết quả đánh giá cơ cấu giá */
  async getBidResultCustomPrice(user: UserDto, bidId: string, supplierId: string) {
    const res: any = await this.bidSupplierRepo.findOne({
      where: { bidId, supplierId, companyId: user.companyId },
      relations: { supplier: true, bid: true, bidSupplierCustomPriceValue: true },
      order: { bidSupplierCustomPriceValue: { sort: 'ASC' } },
    })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    res.supplierName = res.__supplier__.name
    delete res.__supplier__

    res.bidName = res.__bid__.name
    res.approveChooseSupplierWinDate = res.__bid__.approveChooseSupplierWinDate
    delete res.__bid__

    return res
  }

  /** Lấy danh sách đàm phán giá */
  async getListBidResultDeal(user: UserDto, data: PaginationDto) {
    const res: any[] = await this.bidDealRepo.findAndCount({
      where: { bidId: data.where.bidId, companyId: user.companyId },
      relations: { childs: { bid: { service: true } } },
      skip: data.skip,
      take: data.take,
      select: {
        id: true,
        createdAt: true,
        endDate: true,
        status: true,
        isSendDealPrice: true,
        childs: { id: true, bid: { id: true, service: { code: true, name: true } } },
      },
      order: { createdAt: 'DESC' },
    })
    if (res[0].length == 0) return res

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidDealStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }

    for (const item of res[0]) {
      item.statusName = dicStatus[item.status]
      item.itemName = item.__childs__.map((c) => c.__bid__.__service__.code + ' - ' + c.__bid__.__service__.name)
      delete item.__childs__
    }

    return res
  }

  /** Lấy chi tiết đàm phán giá */
  async getBidResultDeal(user: UserDto, bidDealId: string) {
    const res: any = await this.bidDealRepo.findOne({
      where: { id: bidDealId, companyId: user.companyId },
      relations: {
        childs: {
          bid: { service: true },
          bidDealSupplier: { supplier: true, bidDealSupplierPriceValue: true },
          bidDealPrices: { bidPrice: true },
        },
      },
    })
    if (!res) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidDealSupplierStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }

    res.listChild = res.__childs__
    delete res.__childs__
    for (const child of res.listChild) {
      child.itemName = child.__bid__.__service__.code + ' - ' + child.__bid__.__service__.name
      delete child.__bid__

      for (const sup of child.__bidDealSupplier__) {
        sup.supplierName = sup.__supplier__.name
        delete sup.__supplier__

        sup.statusName = dicStatus[sup.status]
      }

      let lstTemp1 = child.__bidDealPrices__.filter((c: any) => c.__bidPrice__.level == 1)
      const lstTemp2 = child.__bidDealPrices__.filter((c: any) => c.__bidPrice__.level == 2)
      const lstTemp3 = child.__bidDealPrices__.filter((c: any) => c.__bidPrice__.level == 3)
      for (let i = 0; i < lstTemp1.length; i++) {
        const temp = lstTemp1[i]
        let childInLv1 = lstTemp2.filter((c: any) => c.__bidPrice__.parentId == temp.__bidPrice__.id)
        for (let j = 0; j < childInLv1.length; j++) {
          const temp2 = childInLv1[j]
          const childInLv2 = lstTemp3.filter((c: any) => c.__bidPrice__.parentId == temp2.__bidPrice__.id)
          childInLv1.splice(j + 1, 0, ...childInLv2)
        }
        lstTemp1.splice(i + 1, 0, ...childInLv1)
      }
      child.__bidDealPrices__ = lstTemp1
    }

    return res
  }

  /** Lấy chi tiết đàm phán giá theo Doanh nghiệp */
  async getBidResultDealSupplierDetail(user: UserDto, bidDealSupplierId: string) {
    const bidDealSupplier = await this.repo.manager
      .getRepository(BidDealSupplierEntity)
      .findOne({ where: { id: bidDealSupplierId, companyId: user.companyId } })
    if (!bidDealSupplier) throw new Error(ERROR_NOT_FOUND_DATA)
    const bidDealItem = await bidDealSupplier.bidDeal
    const bidDealPrices = await bidDealItem.bidDealPrices
    const bidDealSupplierPrice = await this.repo.manager
      .getRepository(BidDealSupplierPriceValueEntity)
      .find({ where: { bidDealSupplierId, companyId: user.companyId } })
    const lstBidPrice = await this.bidPriceRepo.getPrice(user, bidDealItem.bidId)
    lstBidPrice.sort((a, b) => a.sort - b.sort)

    for (const item of lstBidPrice) {
      const bidDealPrice = bidDealPrices.find((c) => c.bidPriceId == item.id)
      if (bidDealPrice && bidDealPrice.number > 0) {
        item.number = bidDealPrice.number
      }
    }
    return { bidPrices: lstBidPrice, bidDealSupplierPrice }
  }

  /** Lấy danh sách đấu giá */
  async getListBidResultAuction(user: UserDto, data: PaginationDto) {
    const res: any[] = await this.bidAuctionRepo.findAndCount({
      where: { bidId: data.where.bidId, companyId: user.companyId },
      relations: { childs: { bid: { service: true } } },
      skip: data.skip,
      take: data.take,
      select: {
        id: true,
        createdAt: true,
        endDate: true,
        status: true,
        childs: { id: true, bid: { id: true, service: { code: true, name: true } } },
      },
      order: { createdAt: 'DESC' },
    })
    if (res[0].length == 0) return res

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidAuctionStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }

    for (const item of res[0]) {
      item.statusName = dicStatus[item.status]
      item.itemName = item.__childs__.map((c) => c.__bid__.__service__.code + ' - ' + c.__bid__.__service__.name)
      delete item.__childs__
    }

    return res
  }

  /** Lấy chi tiết đấu giá */
  async getBidResultAuction(user: UserDto, bidAuctionId: string) {
    const res: any = await this.bidAuctionRepo.findOne({
      where: { id: bidAuctionId, companyId: user.companyId },
      relations: {
        childs: {
          bid: { service: true },
          bidAuctionSupplier: { supplier: true, bidAuctionSupplierPriceValue: true },
          bidAuctionPrice: { bidPrice: true },
        },
      },
      order: { childs: { bidAuctionSupplier: { score: 'DESC' } } },
    })
    if (!res) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidAuctionSupplierStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }

    res.listChild = res.__childs__
    delete res.__childs__
    for (const child of res.listChild) {
      child.itemName = child.__bid__.__service__.code + ' - ' + child.__bid__.__service__.name
      delete child.__bid__

      for (const sup of child.__bidAuctionSupplier__) {
        sup.supplierName = sup.__supplier__.name
        delete sup.__supplier__

        sup.statusName = dicStatus[sup.status]
      }

      let lstTemp1 = child.__bidAuctionPrice__.filter((c: any) => c.__bidPrice__.level == 1)
      const lstTemp2 = child.__bidAuctionPrice__.filter((c: any) => c.__bidPrice__.level == 2)
      const lstTemp3 = child.__bidAuctionPrice__.filter((c: any) => c.__bidPrice__.level == 3)
      for (let i = 0; i < lstTemp1.length; i++) {
        const temp = lstTemp1[i]
        let childInLv1 = lstTemp2.filter((c: any) => c.__bidPrice__.parentId == temp.__bidPrice__.id)
        for (let j = 0; j < childInLv1.length; j++) {
          const temp2 = childInLv1[j]
          const childInLv2 = lstTemp3.filter((c: any) => c.__bidPrice__.parentId == temp2.__bidPrice__.id)
          childInLv1.splice(j + 1, 0, ...childInLv2)
        }
        lstTemp1.splice(i + 1, 0, ...childInLv1)
      }
      child.__bidAuctionPrice__ = lstTemp1
    }

    return res
  }

  /** Lấy chi tiết đấu giá theo Doanh nghiệp  */
  async getBidResultAuctionSupplierDetail(user: UserDto, bidAuctionSupplierId: string) {
    const bidAuctionSupplier = await this.repo.manager
      .getRepository(BidAuctionSupplierEntity)
      .findOne({ where: { id: bidAuctionSupplierId, companyId: user.companyId } })
    if (!bidAuctionSupplier) throw new Error(ERROR_NOT_FOUND_DATA)
    const bidAuctionItem = await bidAuctionSupplier.bidAuction

    const bidAuctionSupplierPrice = await this.bidAuctionRepo.manager.getRepository(BidAuctionSupplierPriceValueEntity).find({
      where: { bidAuctionSupplierId, companyId: user.companyId },
    })
    const bidPrices = await this.bidPriceRepo.getPrice(user, bidAuctionItem.bidId)
    return { bidPrices, bidAuctionSupplierPrice }
  }
}
