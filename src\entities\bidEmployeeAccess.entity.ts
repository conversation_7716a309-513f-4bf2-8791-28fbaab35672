import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, JoinColumn, ManyToOne } from 'typeorm'
import { EmployeeEntity } from './employee.entity'
import { BidEntity } from './bid.entity'

@Entity('bid_employee_access')
export class BidEmployeeAccessEntity extends BaseEntity {
  /** Loại quyền nhân viên được cấp */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.bidAccess)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.employeeAccess)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>
}
