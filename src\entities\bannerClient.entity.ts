import { BaseEntity } from './base.entity'
import { Entity, Column } from 'typeorm'

@Entity('banner_client')
export class BannerClientEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  atr: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  position: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  url: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string
}
