import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { BidSupplierEntity } from './bidSupplier.entity'
import { BidPriceEntity } from './bidPrice.entity'

@Entity('bid_supplier_price_value')
export class BidSupplierPriceValueEntity extends BaseEntity {
  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  unit: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  currency: string

  /** Số lượng */
  @Column({
    nullable: false,
    default: 0,
  })
  number: number

  /** Tên  */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'float',
    nullable: true,
  })
  score: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  value: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidSupplierId: string
  @ManyToOne(() => BidSupplierEntity, (p) => p.bidSupplierPriceValue)
  @JoinColumn({ name: 'bidSupplierId', referencedColumnName: 'id' })
  bidSupplier: Promise<BidSupplierEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  bidPriceId: string
  @ManyToOne(() => BidPriceEntity, (p) => p.bidSupplierPriceValue)
  @JoinColumn({ name: 'bidPriceId', referencedColumnName: 'id' })
  bidPrice: Promise<BidPriceEntity>

  /** Id của hạng mục cha có cơ cấu giá */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  bidPriceParentId: string
}
