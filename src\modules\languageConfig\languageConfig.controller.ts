import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { LanguageConfigService } from './languageConfig.service'
import { CurrentUser } from '../common/decorators'
import { PaginationDto, UserDto } from '../../dto'
import { ApeAuthGuard } from '../common/guards'
import { LanguageConfigCreateDto, LanguageConfigUpdateDto } from './dto'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

/** Cấu hình ngôn ngữ */
@ApiBearerAuth()
@ApiTags('Language')
@Controller('languageConfig')
export class LanguageConfigController {
  constructor(private readonly service: LanguageConfigService) {}

  @ApiOperation({ summary: 'Danh sách cấu hình ngôn ngữ phân trang' })
  @UseGuards(ApeAuthGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo cấu hình ngôn ngữ' })
  @UseGuards(ApeAuthGuard)
  @Post('create_data')
  public async createData(@Body() data: LanguageConfigCreateDto, @CurrentUser() user: UserDto) {
    return await this.service.createData(data, user)
  }

  @ApiOperation({ summary: 'Cập nhật cấu hình ngôn ngữ' })
  @UseGuards(ApeAuthGuard)
  @Post('update_data')
  public async updateData(@Body() data: LanguageConfigUpdateDto, @CurrentUser() user: UserDto) {
    return await this.service.updateData(data, user)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động cấu hình ngôn ngữ' })
  @UseGuards(ApeAuthGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateIsDelete(user, data.id)
  }

  @ApiOperation({ summary: 'Chi tiết cấu hình ngôn ngữ' })
  @Post('find_language_config')
  public async findLanguageConfig(@Body() data: { id: string }) {
    return await this.service.findLanguageConfig(data.id)
  }

  @ApiOperation({ summary: 'Tạo excel cấu hình ngôn ngữ' })
  @UseGuards(ApeAuthGuard)
  @Post('create_data_excel')
  public async createDataExcel(@Body() data: LanguageConfigCreateDto[], @CurrentUser() user: UserDto) {
    return await this.service.createDataExcel(data, user)
  }
}
