import { Controller, UseGuards, Post, Body, Get } from '@nestjs/common'
import { EmployeeWarningService } from './employeeWarning.service'
import { ApeAuthGuard } from '../common/guards'
import { CurrentUser } from '../common/decorators'
import { UserDto } from '../../dto'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('EmployeeWarning')
@Controller('employeeWarning')
export class EmployeeWarningController {
  constructor(private readonly service: EmployeeWarningService) {}

  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> các cảnh báo' })
  @UseGuards(ApeAuthGuard)
  @Post('load')
  public async loadEmployeeWarning(@CurrentUser() user: UserDto, @Body() data: { take: number }) {
    return await this.service.loadEmployeeWarning(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái đã đọc cho cảnh báo' })
  @UseGuards(ApeAuthGuard)
  @Post('read')
  public async readEmployeeWarning(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.readEmployeeWarning(user, data)
  }
}
