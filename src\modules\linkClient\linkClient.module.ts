import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { LinkClientService } from './linkClient.service'
import { LinkClientController } from './linkClient.controller'
import { LinkClientRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([LinkClientRepository])],
  controllers: [LinkClientController],
  providers: [LinkClientService],
})
export class LinkClientModule {}
