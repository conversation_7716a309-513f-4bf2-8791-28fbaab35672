import { IsNull, Repository } from 'typeorm'
import { UserDto } from '../dto'
import { BidTradeEntity } from '../entities'
import { CustomRepository } from '../typeorm'

@CustomRepository(BidTradeEntity)
export class BidTradeRepository extends Repository<BidTradeEntity> {
  // Lấy danh sách điều kiện thương mại của gói thầu
  async getTrade(user: UserDto, bidId: string) {
    return await this.find({
      where: { bidId, companyId: user.companyId, isDeleted: false, parentId: IsNull() },
      relations: { bidTradeListDetails: true, childs: { bidTradeListDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })
  }
}
