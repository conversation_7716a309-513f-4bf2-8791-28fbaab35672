import { Module } from '@nestjs/common'
import { ClientWebService } from './clientWeb.service'
import { ClientWebController } from './clientWeb.controller'
import { BidDetailModule } from '../bidDetail/bidDetail.module'
import { BidModule } from '../bid/bid.module'
import { SupplierModule } from '../supplier/supplier.module'
import { BidDealModule } from '../bidDeal/bidDeal.module'
import { BidAuctionModule } from '../bidAuction/bidAuction.module'
import { FaqCategoryModule } from '../faqCategory/faqCategory.module'
import { FaqModule } from '../faq/faq.module'
import { POModule } from '../po/po.module'
import { PaymentProgressModule } from '../paymentProgress/paymentProgress.module'
import { InvoiceSuggestModule } from '../invoiceSuggest/invoiceSuggest.module'
import { ContractModule } from '../contract/contract.module'

@Module({
  imports: [
    BidModule,
    BidDetailModule,
    PaymentProgressModule,
    BidDealModule,
    BidAuctionModule,
    SupplierModule,
    InvoiceSuggestModule,
    FaqModule,
    FaqCategoryModule,
    POModule,
    ContractModule,
  ],
  controllers: [ClientWebController],
  providers: [ClientWebService],
})
export class ClientWebModule {}
