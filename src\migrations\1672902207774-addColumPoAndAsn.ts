import { MigrationInterface, QueryRunner } from "typeorm";

export class addColumPoAndAsn1672902207774 implements MigrationInterface {
    name = 'addColumPoAndAsn1672902207774'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`po\` ADD \`deliveryDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`service\` ADD \`kpi\` float NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`asn\` ADD \`costPrice\` decimal(20,4) NULL DEFAULT '0.0000'`);
        await queryRunner.query(`ALTER TABLE \`asn\` ADD \`sellPrice\` decimal(20,4) NULL DEFAULT '0.0000'`);
        await queryRunner.query(`ALTER TABLE \`asn\` ADD \`expiryDate\` datetime NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`asn\` DROP COLUMN \`expiryDate\``);
        await queryRunner.query(`ALTER TABLE \`asn\` DROP COLUMN \`sellPrice\``);
        await queryRunner.query(`ALTER TABLE \`asn\` DROP COLUMN \`costPrice\``);
        await queryRunner.query(`ALTER TABLE \`service\` DROP COLUMN \`kpi\``);
        await queryRunner.query(`ALTER TABLE \`po\` DROP COLUMN \`deliveryDate\``);
    }

}
