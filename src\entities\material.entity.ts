import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany, JoinColumn, ManyToOne } from 'typeorm'
import { BidItemEntity } from './bidItem.entity'
import { BidTechEntity } from './bidTech.entity'
import { BidPriceEntity } from './bidPrice.entity'
import { PrItemEntity } from './prItem.entity'
import { POProductEntity } from './poProduct.entity'

@Entity('material')
export class MaterialEntity extends BaseEntity {
  /** Tên vật tư = shortext */
  @Column({
    type: 'varchar',
    length: 1000,
    nullable: false,
  })
  name: string

  /** Mã vật tư */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 550,
    nullable: true,
  })
  description: string

  @OneToMany(() => BidItemEntity, (p) => p.item)
  bidItems: Promise<BidItemEntity[]>

  @OneToMany(() => PrItemEntity, (p) => p.service)
  prItems: Promise<PrItemEntity[]>

  @OneToMany(() => BidTechEntity, (p) => p.item)
  techs: Promise<BidTechEntity[]>

  /** 1 dịch vụ sẽ có nhiều cấu hình giá */
  @OneToMany(() => BidPriceEntity, (p) => p.item)
  prices: Promise<BidPriceEntity[]>

  @OneToMany(() => POProductEntity, (p) => p.service)
  poProducts: Promise<POProductEntity[]>
}
