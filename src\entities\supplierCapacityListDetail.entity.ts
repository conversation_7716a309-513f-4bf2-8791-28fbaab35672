import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { SupplierCapacityEntity } from './supplierCapacity.entity'

/** <PERSON><PERSON><PERSON> trị kiểu List */
@Entity('supplier_capacity_list_detail')
export class SupplierCapacityListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
  })
  value: number

  @Column({
    nullable: false,
    default: false,
  })
  isChosen: boolean

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  supplierCapacityId: string
  @ManyToOne(() => SupplierCapacityEntity, (p) => p.supplierCapacityListDetails)
  @JoinColumn({ name: 'supplierCapacityId', referencedColumnName: 'id' })
  supplierCapacity: Promise<SupplierCapacityEntity>
}
