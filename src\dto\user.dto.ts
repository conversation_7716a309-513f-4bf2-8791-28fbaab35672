import { IsUUI<PERSON>, IsString, Is<PERSON>rray, IsOptional } from 'class-validator'

export class UserDto {
  @IsUUID()
  id: string

  @IsString()
  username: string

  @IsString()
  type: string

  @IsOptional()
  @IsString()
  supplierId?: string | null

  @IsOptional()
  @IsString()
  employeeId: string | null

  @IsOptional()
  @IsString()
  companyId?: string | null

  @IsOptional()
  @IsArray()
  roles?: any[]

  /** Nếu On-Premise và là Doanh nghiệp thì all tính năng */
  @IsOptional()
  hasAllRoles?: boolean
}
