import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { OfferEntity } from './offer.entity'
import { OfferServiceEntity } from './offerService.entity'
import { OfferSupplierPriceEntity } from './offerSupplierPrice.entity'
import { OfferPriceColValueEntity } from './offerPriceColValue.entity'
import { OfferSupplierPriceColValueEntity } from './offerSupplierPriceColValue.entity'
import { OfferPriceListDetailEntity } from './offerPriceListDetail.entity'
import { OfferDealPriceEntity } from './offerDealPrice.entity'
import { OfferDealSupplierPriceValueEntity } from './offerDealSupplierPriceValue.entity'

/** Bảng chào giá nhanh */
@Entity('offer_price')
export class OfferPriceEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  @Column({
    type: 'text',
    nullable: true,
  })
  fomular: string

  /** <PERSON><PERSON> bắt buộc nhập hay không */
  @Column({
    nullable: true,
    default: false,
  })
  isRequired: boolean

  @Column({
    nullable: true,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unit: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',

    nullable: true,
  })
  currency: string

  /** % tỉ trọng */
  @Column({
    type: 'float',
    nullable: true,
    default: 100,
  })
  percent: number

  /** Cấp độ */
  @Column({
    nullable: true,
    default: 1,
  })
  level: number

  /** Id của công thức cha */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId: string
  /** Cha */
  @ManyToOne(() => OfferPriceEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: OfferPriceEntity

  /** Con - 1 công thức sẽ có thể có nhiều con */
  @OneToMany(() => OfferPriceEntity, (p) => p.parent)
  childs: Promise<OfferPriceEntity[]>

  @Column({
    length: 50,
    nullable: true,
  })
  type: string

  @Column({
    length: 50,
    nullable: true,
  })
  colType: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerId: string

  @ManyToOne(() => OfferEntity, (p) => p.prices)
  @JoinColumn({ name: 'offerId', referencedColumnName: 'id' })
  offer: Promise<OfferEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerServiceId: string
  @ManyToOne(() => OfferServiceEntity, (p) => p.trades)
  @JoinColumn({ name: 'offerServiceId', referencedColumnName: 'id' })
  offerService: Promise<OfferServiceEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerItemId: string
  @ManyToOne(() => OfferServiceEntity, (p) => p.trades)
  @JoinColumn({ name: 'offerItemId', referencedColumnName: 'id' })
  offerItem: Promise<OfferServiceEntity>

  /** Giá Doanh nghiệp chào */
  @OneToMany(() => OfferSupplierPriceEntity, (p) => p.offerPrice)
  offerSupplierPrices: Promise<OfferSupplierPriceEntity[]>

  @OneToMany(() => OfferPriceColValueEntity, (p) => p.offerPrice)
  offerPriceColValue: Promise<OfferPriceColValueEntity[]>

  @OneToMany(() => OfferSupplierPriceColValueEntity, (p) => p.offerPrice)
  offerSupplierPriceColValue: Promise<OfferSupplierPriceColValueEntity[]>

  @OneToMany(() => OfferPriceListDetailEntity, (p) => p.offerPrice)
  offerPriceListDetails: Promise<OfferPriceListDetailEntity[]>

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @Column({
    nullable: true,
  })
  scoreDLC: number

  /** Giá trị nhỏ nhất */
  @Column({
    nullable: true,
  })
  requiredMin: number

  /** Số lượng */
  @Column({
    nullable: true,
    default: 0,
  })
  number: number

  /** Có cấu hình giá hay không */
  @Column({
    nullable: true,
    default: false,
  })
  isSetup: boolean

  /** Có Theo template cơ cấu giá hay không */
  @Column({
    nullable: true,
    default: true,
  })
  isTemplate: boolean

  @OneToMany(() => OfferDealPriceEntity, (p) => p.offerDeal)
  offerDealPrices: Promise<OfferDealPriceEntity[]>

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => OfferDealSupplierPriceValueEntity, (p) => p.offerPrice)
  offerDealSupplierPriceValue: Promise<OfferDealSupplierPriceValueEntity[]>
}
