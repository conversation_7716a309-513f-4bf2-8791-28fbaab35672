import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { PaginationDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { InboundCreateDto, InboundUpdateDto, InboundUpdateStatusDto } from '../dto'
import { InboundClientService } from '../services/inboundClient.service'
import { ApeAuthGuard, RoleGuard } from '../../common/guards'

/** inbound client */
@ApiBearerAuth()
@ApiTags('InboundClient')
@UseGuards(ApeAuthGuard, RoleGuard)
@Controller('inbound_client')
export class InboundClientController {
  constructor(private readonly service: InboundClientService) {}

  @ApiOperation({ summary: 'Danh sách phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật' })
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: InboundUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật trạng thái' })
  @Post('update_status')
  public async updateStatus(@CurrentUser() user: UserDto, @Body() data: InboundUpdateStatusDto) {
    return await this.service.updateStatus(user, data)
  }

  @ApiOperation({ summary: 'Hàm tạo mới mã inbound' })
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: InboundCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Lấy dữ liệu chi tiết' })
  @Post('load_detail')
  public async dataDetail(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.loadDetail(user, data)
  }
}
