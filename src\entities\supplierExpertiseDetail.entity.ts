import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { SupplierExpertiseEntity } from './supplierExpertise.entity'
import { SupplierCapacityEntity } from './supplierCapacity.entity'
import { SupplierExpertiseYearDetailEntity } from './supplierExpertiseYearDetail.entity'

/** Chi tiết yêu cầu điều chỉnh năng lực trong lần thẩm định */
@Entity('supplier_expertise_detail')
export class SupplierExpertiseDetailEntity extends BaseEntity {
  @Column({
    type: 'text',
    nullable: true,
  })
  comment: string

  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  /** Kiểu dữ liệu: string - number ...*/
  @Column({
    nullable: false,
    default: 'string',
  })
  type: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  value: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  supplierCapacityId: string
  @ManyToOne(() => SupplierCapacityEntity, (p) => p.supplierCapacityExpertises)
  @JoinColumn({ name: 'supplierCapacityId', referencedColumnName: 'id' })
  supplierCapacity: Promise<SupplierCapacityEntity>

  /** 1 công thức sẽ có thể có nhiều giá trị theo năm */
  @OneToMany(() => SupplierExpertiseYearDetailEntity, (p) => p.supplierExpertiseDetail)
  supplierExpertiseYearDetails: Promise<SupplierExpertiseYearDetailEntity[]>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  supplierExpertiseId: string
  @ManyToOne(() => SupplierExpertiseEntity, (p) => p.supplierExpertiseDetails)
  @JoinColumn({ name: 'supplierExpertiseId', referencedColumnName: 'id' })
  supplierExpertise: Promise<SupplierExpertiseEntity>
}
