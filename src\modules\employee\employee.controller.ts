import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { EmployeeService } from './employee.service'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { PaginationDto, UserDto } from '../../dto'
import { enumProject } from '../../constants'
import { CurrentUser, Roles } from '../common/decorators'
import { EmployeeCreateDto, EmployeeCreateExcelDto, EmployeeUpdateDto, EmployeeUpdatePasswordDto } from './dto'
import { Request as IRequest } from 'express'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Employee')
@Controller('employee')
export class EmployeeController {
  constructor(private readonly service: EmployeeService) {}

  @ApiOperation({ summary: 'Lấy ds nhân viên, isGet: l<PERSON>y thêm thông tin' })
  @UseGuards(ApeAuthGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: { departmentId?: string; branchId?: string; bidId?: string; isGet?: boolean }) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds nhân viên phân trang' })
  @Roles(enumProject.Features.SETTING_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo nhân viên' })
  @Roles(enumProject.Features.SETTING_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: EmployeeCreateDto, @Req() req: IRequest) {
    return await this.service.createData(user, data, req)
  }

  @ApiOperation({ summary: 'Tạo nhân viên bằng excel' })
  @Roles(enumProject.Features.SETTING_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data_by_excel')
  public async createDataExcel(@CurrentUser() user: UserDto, @Body() data: EmployeeCreateExcelDto[], @Req() req: IRequest) {
    return await this.service.createDataExcel(user, data, req)
  }

  @ApiOperation({ summary: 'Cập nhật nhân viên' })
  @Roles(enumProject.Features.SETTING_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: EmployeeUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động nhân viên' })
  @Roles(enumProject.Features.SETTING_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }, @Req() req: IRequest) {
    return await this.service.updateIsDelete(user, data, req)
  }

  @ApiOperation({ summary: 'Cập nhật mật khẩu nhân viên' })
  @Roles(enumProject.Features.SETTING_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_password')
  public async updatePassword(@CurrentUser() user: UserDto, @Body() data: EmployeeUpdatePasswordDto) {
    return await this.service.updatePassword(user, data)
  }
}
