import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class ServiceUpdateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty()
  stockQuantity: number

  @ApiPropertyOptional()
  parent1: string

  @ApiPropertyOptional()
  parent2: string

  @ApiPropertyOptional()
  serviceAccess: string[]

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  approveById: string

  @ApiPropertyOptional()
  description: string

  @ApiPropertyOptional()
  percentTech: number

  @ApiPropertyOptional()
  percentPrice: number

  @ApiPropertyOptional()
  percentTrade: number
}
