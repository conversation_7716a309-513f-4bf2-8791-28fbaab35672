import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import {
  ContractRepository,
  DepartmentRepository,
  EmployeeRepository,
  MaterialRepository,
  POProductRepository,
  PORepository,
  PrRepository,
  WarehouseRepository,
} from '../../repositories'
import { InboundService } from './services/inbound.service'
import { InboundController } from './controllers/inbound.controller'
import { InboundClientController } from './controllers/inboundClient.controller'
import { InboundClientService } from './services/inboundClient.service'
import { InboundContainerRepository, InboundRepository } from '../../repositories/inbound.repository'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      InboundRepository,
      DepartmentRepository,
      PrRepository,
      InboundContainerRepository,
      EmployeeRepository,
      PORepository,
      POProductRepository,
      MaterialRepository,
      WarehouseRepository,
      ContractRepository,
    ]),
  ],
  controllers: [InboundController, InboundClientController],
  providers: [InboundService, InboundClientService],
  exports: [InboundService, InboundClientService],
})
export class InboundModule {}
