import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { OfferSupplierEntity } from './offerSupplier.entity'
import { OfferTechEntity } from './offerTech.entity'

@Entity('offer_supplier_tech_value')
export class OfferSupplierTechValueEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: true,
  })
  score: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerSupplierId: string
  @ManyToOne(() => OfferSupplierEntity, (p) => p.offerSupplierTechValue)
  @JoinColumn({ name: 'offerSupplierId', referencedColumnName: 'id' })
  offerSupplier: Promise<OfferSupplierEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerTechId: string
  @ManyToOne(() => OfferTechEntity, (p) => p.offerSupplierTechValue)
  @JoinColumn({ name: 'offerTechId', referencedColumnName: 'id' })
  offerTech: Promise<OfferTechEntity>
}
