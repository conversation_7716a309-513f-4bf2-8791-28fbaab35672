import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { enumProject } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { InvoiceSuggestCreate, InvoiceSuggestUpdate } from './dto'
import { InvoiceSuggestService } from './invoiceSuggest.service'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

/** Qu<PERSON>n lý Đề nghị thanh toán */
@ApiBearerAuth()
@ApiTags('Invoice')
@Controller('invoiceSuggest')
export class InvoiceSuggestController {
  constructor(private readonly service: InvoiceSuggestService) {}
  @ApiOperation({ summary: 'Chi tiết ĐNTT' })
  @Roles(enumProject.Features.PAYMENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_detail')
  public async findDetail(@Body() data: { id: string }, @CurrentUser() user: UserDto) {
    return await this.service.findDetail(data, user)
  }

  @ApiOperation({ summary: 'Danh sách ĐNTT có phân trang' })
  @Roles(enumProject.Features.PAYMENT_001.code, enumProject.Features.PAYMENT_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@Body() data: PaginationDto, @CurrentUser() user: UserDto) {
    return await this.service.pagination(data, user)
  }

  @ApiOperation({ summary: 'Danh sách file ĐNTT ' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination_file')
  public async findClient(@Body() data: { id: string }, user: UserDto) {
    return await this.service.findClient(data, user)
  }

  @ApiOperation({ summary: 'Tạo ĐNTT' })
  @Roles(enumProject.Features.PAYMENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@Body() data: InvoiceSuggestCreate, @CurrentUser() user: UserDto) {
    return await this.service.createData(data, user)
  }

  @ApiOperation({ summary: 'Sửa thông tin ĐNTT' })
  @Roles(enumProject.Features.PAYMENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@Body() data: InvoiceSuggestUpdate, @CurrentUser() user: UserDto) {
    return await this.service.updateData(data, user)
  }

  @ApiOperation({ summary: 'Xóa ĐNTT' })
  @Roles(enumProject.Features.PAYMENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('delete_data')
  public async deleteData(@Body() data: { id: string; reason?: string }, @CurrentUser() user: UserDto) {
    return await this.service.deleteData(data, user)
  }
}
