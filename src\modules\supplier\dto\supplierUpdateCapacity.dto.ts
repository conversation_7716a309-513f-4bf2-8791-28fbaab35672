import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'
export class ListDetailYearUpdateDto {
  @ApiPropertyOptional()
  value: string

  @ApiPropertyOptional()
  year: string
}

export class SupplierCapacityValueUpdateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  type: string

  @ApiPropertyOptional()
  value: string

  @ApiPropertyOptional()
  listDetailYear: ListDetailYearUpdateDto[]

  @ApiPropertyOptional()
  childs: SupplierCapacityValueUpdateDto[]
}

export class SupplierUpdateCapacityDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  supplierServiceId: string

  @ApiPropertyOptional()
  capacities: SupplierCapacityValueUpdateDto[]
}
