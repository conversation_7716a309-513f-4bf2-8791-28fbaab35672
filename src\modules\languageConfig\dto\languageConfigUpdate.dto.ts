import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class LanguageConfigUpdateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  languageId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  sourceType: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  component: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  key: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  value: string

  @ApiPropertyOptional()
  @IsOptional()
  description: string
}
