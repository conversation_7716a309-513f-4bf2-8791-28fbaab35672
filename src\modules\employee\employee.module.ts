import { Module } from '@nestjs/common'
import { BranchRepository, DepartmentRepository, EmployeeRepository, UserRepository } from '../../repositories'
import { TypeOrmExModule } from '../../typeorm'
import { EmployeeController } from './employee.controller'
import { EmployeeService } from './employee.service'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([EmployeeRepository, UserRepository, DepartmentRepository, BranchRepository])],
  controllers: [EmployeeController],
  providers: [EmployeeService],
})
export class EmployeeModule {}
