import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { enumProject } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { FaqService } from './faq.service'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { FaqCreateDto, FaqUpdateDto } from './dto'

@ApiBearerAuth()
@ApiTags('Faq')
@Controller('faq')
export class FaqController {
  constructor(private readonly service: FaqService) {}

  @ApiOperation({ summary: 'Danh sách faq phân trang' })
  @Roles(enumProject.Features.SETTING_020.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo faq' })
  @Roles(enumProject.Features.SETTING_020.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: FaqCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật cho faq' })
  @Roles(enumProject.Features.SETTING_020.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: FaqUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động cho faq' })
  @Roles(enumProject.Features.SETTING_020.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateIsDelete(user, data)
  }
}
