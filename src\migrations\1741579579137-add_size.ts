import { MigrationInterface, QueryRunner } from 'typeorm'

export class addSize1741579579137 implements MigrationInterface {
  name = 'addSize1741579579137'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`service_price_list_detail\` CHANGE \`name\` \`name\` varchar(500) NOT NULL`)
    await queryRunner.query(`ALTER TABLE \`service_price_list_detail\` CHANGE \`value\` \`value\` varchar(500) NULL`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`service_price_list_detail\` CHANGE \`value\` \`value\` varchar(250) NULL`)
    await queryRunner.query(`ALTER TABLE \`service_price_list_detail\` CHANGE \`name\` \`name\` varchar(250) NOT NULL`)
  }
}
