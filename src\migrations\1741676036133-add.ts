import { MigrationInterface, QueryRunner } from "typeorm";

export class add1741676036133 implements MigrationInterface {
    name = 'add1741676036133'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid\` ADD \`isNotImportFromAdmin\` tinyint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid\` DROP COLUMN \`isNotImportFromAdmin\``);
    }

}
