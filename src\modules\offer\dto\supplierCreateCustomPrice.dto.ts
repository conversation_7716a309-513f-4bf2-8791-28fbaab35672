import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsString } from 'class-validator'

export class SupplierCreateCustomPriceItemDto {
  @ApiProperty()
  @IsString()
  value: string

  @ApiPropertyOptional()
  unit: string
  @ApiPropertyOptional()
  currency: string

  @ApiPropertyOptional()
  name: string

  @ApiPropertyOptional()
  number: number

  @ApiPropertyOptional()
  sort: number
}
