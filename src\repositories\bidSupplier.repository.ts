import { Repository } from 'typeorm'
import { UserDto } from '../dto'
import { BidSupplierEntity, BidSupplierPriceEntity } from '../entities'
import { CustomRepository } from '../typeorm'

@CustomRepository(BidSupplierEntity)
export class BidSupplierRepository extends Repository<BidSupplierEntity> {}

@CustomRepository(BidSupplierPriceEntity)
export class BidSupplierPriceRepository extends Repository<BidSupplierPriceEntity> {
  /** Lưu lại lần nộp giá cuối theo từng hạng mục */
  async saveBidSupplierPrice(user: UserDto, bidSupplier: BidSupplierEntity, item: any) {
    const bidSupplierPrice = new BidSupplierPriceEntity()
    bidSupplierPrice.companyId = user.companyId
    bidSupplierPrice.createdBy = user.id
    bidSupplierPrice.bidSupplierId = bidSupplier.id
    bidSupplierPrice.bidPriceId = item.bidPriceId
    bidSupplierPrice.bidPriceName = item.name
    bidSupplierPrice.bidPriceLevel = item.level

    bidSupplierPrice.unitPrice = 0
    const unitPrice = +item.value
    if (!isNaN(unitPrice) && isFinite(unitPrice)) {
      bidSupplierPrice.unitPrice = unitPrice
    }
    bidSupplierPrice.number = item.number
    bidSupplierPrice.price = bidSupplierPrice.unitPrice * bidSupplierPrice.number
    bidSupplierPrice.bidId = bidSupplier.bidId
    bidSupplierPrice.serviceId = bidSupplier.serviceId || (await bidSupplier.bid).serviceId
    bidSupplierPrice.supplierId = bidSupplier.supplierId
    bidSupplierPrice.submitDate = item.submitDate || new Date()
    bidSupplierPrice.submitType = item.submitType || 0
    await this.save(bidSupplierPrice)
  }
}
