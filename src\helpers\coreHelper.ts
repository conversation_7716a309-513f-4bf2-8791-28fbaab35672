import { enumData } from '../constants'
import { create, all } from 'mathjs'
const config = {}
const math = create(all, config) as any
import * as moment from 'moment'
import { customAlphabet } from 'nanoid'
class CoreHelper {
  // public newDateTZ() {
  //   var targetTime = new Date()
  //   var timeZoneFromDB = -7.0 //time zone value from database
  //   //get the timezone offset from local time in minutes
  //   var tzDifference = timeZoneFromDB * 60 + targetTime.getTimezoneOffset()
  //   //convert the offset to milliseconds, add to targetTime, and make a new Date
  //   var offsetTime = new Date(targetTime.getTime() + tzDifference * 60 * 1000)
  //   return offsetTime
  // }

  public newDateTZ() {
    const d = new Date()
    const offset = 7
    // convert to msec
    // add local time zone offset
    // get UTC time in msec
    const utc = d.getTime() + d.getTimezoneOffset() * 60000

    // create new Date object for different city
    // using supplied offset
    const nd = new Date(utc + 3600000 * offset)
    return nd
  }

  public dateToString(d: Date) {
    const offset = 7
    // convert to msec
    // add local time zone offset
    // get UTC time in msec
    const utc = d.getTime() + d.getTimezoneOffset() * 60000

    // create new Date object for different city
    // using supplied offset
    const nd = new Date(utc + 3600000 * offset)

    return moment(nd).format('DD/MM/YYYY HH:mm')
  }

  public stringInject(str: String, data: any) {
    if (typeof str === 'string' && data instanceof Array) {
      return str.replace(/({\d})/g, function (i: any) {
        return data[i.replace(/{/, '').replace(/}/, '')]
      })
    } else if (typeof str === 'string' && data instanceof Object) {
      if (Object.keys(data).length === 0) {
        return str
      }

      for (var key in data) {
        return str.replace(/({([^}]+)})/g, function (i) {
          var key = i.replace(/{/, '').replace(/}/, '')
          if (!data[key]) {
            return i
          }

          return data[key]
        })
      }
    } else if ((typeof str === 'string' && data instanceof Array === false) || (typeof str === 'string' && data instanceof Object === false)) {
      return str
    } else {
      return false
    }
  }

  //#region tính điểm giá
  public async callScore(list: any, scoreDLC: number) {
    // Tính điểm
    let scorePrice = 0
    let minValue = 100000000
    let lstValue = []
    //Tìm minValue và lưu điểm tạm
    for (const item of list) {
      const bidPrice = await item.bidPrice
      if (bidPrice && bidPrice.parentId === null && bidPrice.type === enumData.DataType.Number.code && item.value && `${item.value}`.trim() != '') {
        const temp = await this.calScorePriceItem(bidPrice.percent, `${item.value}`)
        if (temp < minValue) {
          minValue = temp
        }
        lstValue.push(temp)
        item.score = temp
      }
    }

    const dlc = await this.calDLC(lstValue)
    for (const item of list) {
      const bidPrice = await item.bidPrice
      if (bidPrice && bidPrice.parentId === null && bidPrice.type === enumData.DataType.Number.code) {
        if (dlc > 0) {
          item.score = scoreDLC - (item.score - minValue) / dlc
          scorePrice += item.score
        } else {
          item.score = scoreDLC
          scorePrice += item.score
        }
      }
    }

    if (isNaN(scorePrice)) {
      return { scorePrice: 0, list }
    } else if (!isFinite(scorePrice)) {
      return { scorePrice: 0, list }
    } else return { scorePrice, list }
  }

  /** Hàm tính độ lệch chuẩn */
  public calDLC(lstValue: number[]) {
    const n = lstValue.length
    if (n < 2) {
      return 0
    }
    let sum = 0
    for (const i of lstValue) {
      sum += i
    }
    const avg = sum / n
    let sum2 = 0
    for (const i of lstValue) {
      sum2 += Math.pow(i - avg, 2)
    }
    const variance = sum2 / (n - 1)
    const std = Math.sqrt(variance)
    return std
  }

  /** Tính điểm đánh giá giá */
  async calScorePriceItem(percent: number, value: string) {
    let score = 0
    if (value && value.trim() != '') score = (+value * percent) / 100

    if (isNaN(score) || !isFinite(score)) return 0

    return score
  }

  //#endregion

  async checkFomular(fomular: string, lstField: any[]) {
    let flag = true
    const lstCol = lstField.filter((c) => c.type === enumData.DataType.Number.code)
    let tempFomular = fomular
    for (const col of lstCol) {
      tempFomular = tempFomular.replace(`[${col.code}]`, '1.01')
    }
    // [qty] là trường "Số lượng" tĩnh của hạng mục
    tempFomular = tempFomular.replace(`[qty]`, '1.01')

    try {
      const ex = math.evaluate(tempFomular)
      if (typeof ex !== 'number') flag = false
    } catch {
      flag = false
      return flag
    } finally {
      return flag
    }
  }

  async calFomular(fomular: string, lstField: any[], item: any) {
    let value = null
    const lstCol = lstField.filter((c) => c.type === enumData.DataType.Number.code)
    let tempFomular = fomular
    for (const col of lstCol) {
      tempFomular = tempFomular.replace(`[${col.code}]`, item[col.id])
    }
    // [qty] là trường "Số lượng" tĩnh của hạng mục
    tempFomular = tempFomular.replace(`[qty]`, item.number)

    try {
      value = math.evaluate(tempFomular)
      if (typeof value !== 'number') value = null
    } catch {
      value = null
      return value
    } finally {
      return value
    }
  }

  convertObjToArray(obj: any) {
    const arr = []
    for (const key in obj) arr.push({ ...obj[key] })

    return arr
  }

  // Chuyển number thành định dạng tiền tệ
  formatMoney(n: number) {
    return n.toFixed(0).replace(/./g, function (c, i, a) {
      return i > 0 && c !== '.' && (a.length - i) % 3 === 0 ? ',' + c : c
    })
  }

  rankABCD(score: number) {
    if (score > 90) return 'A'
    if (score > 70) return 'B'
    if (score > 50) return 'C'
    return 'D'
  }

  /** Gen mã mặc định */
  codeDefaultItem() {
    const nanoid = customAlphabet('1234567890QWERTYUIOPASDFGHJKLZXCVBNM', 6)
    let sortString = nanoid()
    const code = moment(new Date()).format('YYYYMMDD')
    return code + sortString
  }

  /** Hàm tìm key trùng value */
  findDuplicates(arr: any[], key: string): string[] {
    const seen: { [key: string]: boolean } = {}
    const duplicates: string[] = []
    var array = []
    array = arr
    for (const prop of array) {
      if (seen[prop[key]]) {
        if (!duplicates.includes(prop[key])) {
          duplicates.push(prop[key])
        }
      } else {
        seen[prop[key]] = true
      }
    }
    return duplicates
  }
}

export const coreHelper = new CoreHelper()
