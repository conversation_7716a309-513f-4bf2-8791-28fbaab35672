import * as aws from 'aws-sdk'
import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'

@Injectable()
export class SQSService {
  private readonly sqs: aws.SQS
  private queueUrl = 'https://sqs.ap-southeast-1.amazonaws.com/590900909156/biddingonline-mock'

  constructor(private readonly configService: ConfigService) {
    this.queueUrl = this.configService.get<string>('AWS_SQS_URL') || 'https://sqs.ap-southeast-1.amazonaws.com/590900909156/biddingonline-mock'
    aws.config.update({
      region: this.configService.get<string>('AWS_SQS_REGION') || 'ap-southeast-1',
      accessKeyId: this.configService.get<string>('AWS_SQS_ACCESS_KEY_ID') || '********************',
      secretAccessKey: this.configService.get<string>('AWS_SQS_SECRET_ACCESS_KEY') || 'XmMoKqRHZ4cwl0sjFjwW7HfqDeL4NP3fZzAOEe8n',
    })
    const sqs = new aws.SQS({
      apiVersion: this.configService.get<string>('apiVersion') || '2023-03-09',
    })
    this.sqs = sqs
  }

  sendMessage(message: { type: string; data: any }, delaySeconds = 0) {
    if (this.sqs) {
      return new Promise((resolve, reject) => {
        try {
          const params = {
            DelaySeconds: delaySeconds,
            MessageBody: JSON.stringify(message),
            QueueUrl: this.queueUrl,
          }
          this.sqs.sendMessage(params, (err, data) => {
            if (err) {
              console.log('Error', err)
              reject(err)
            } else {
              console.log('SQS SendMessage Success!', data.MessageId)
              resolve(data)
            }
          })
        } catch (error) {
          reject(error)
        }
      })
    }
  }
}
