import { MigrationInterface, QueryRunner } from "typeorm";

export class updateTableSchemeDeleteForeignKey1673766886708 implements MigrationInterface {
    name = 'updateTableSchemeDeleteForeignKey1673766886708'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`scheme\` DROP FOREIGN KEY \`FK_7c9eb25163968be8b50d92d0fe2\``);
        await queryRunner.query(`ALTER TABLE \`pr\` DROP FOREIGN KEY \`FK_c5ea8ff2353ce3a8adf1cb2caf1\``);
        await queryRunner.query(`DROP INDEX \`REL_7c9eb25163968be8b50d92d0fe\` ON \`scheme\``);
        await queryRunner.query(`DROP INDEX \`REL_c5ea8ff2353ce3a8adf1cb2caf\` ON \`pr\``);
        await queryRunner.query(`ALTER TABLE \`scheme\` DROP COLUMN \`prId\``);
        await queryRunner.query(`ALTER TABLE \`pr\` DROP COLUMN \`schemeId\``);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`pr\` ADD \`schemeId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`scheme\` ADD \`prId\` varchar(36) NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`REL_c5ea8ff2353ce3a8adf1cb2caf\` ON \`pr\` (\`schemeId\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`REL_7c9eb25163968be8b50d92d0fe\` ON \`scheme\` (\`prId\`)`);
        await queryRunner.query(`ALTER TABLE \`pr\` ADD CONSTRAINT \`FK_c5ea8ff2353ce3a8adf1cb2caf1\` FOREIGN KEY (\`schemeId\`) REFERENCES \`scheme\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`scheme\` ADD CONSTRAINT \`FK_7c9eb25163968be8b50d92d0fe2\` FOREIGN KEY (\`prId\`) REFERENCES \`pr\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
