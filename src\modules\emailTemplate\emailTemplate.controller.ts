import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { enumProject } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { EmailTemplateCreateDto, EmailTemplateUpdateDto } from './dto'
import { EmailTemplateService } from './emailTemplate.service'

@ApiBearerAuth()
@ApiTags('EmailTemplate')
@Controller('emailTemplate')
export class EmailTemplateController {
  constructor(private readonly service: EmailTemplateService) {}

  @ApiOperation({ summary: 'Lấy danh sách template email phân trang' })
  @Roles(enumProject.Features.SETTING_014.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo template email' })
  @Roles(enumProject.Features.SETTING_014.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: EmailTemplateCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật template email' })
  @Roles(enumProject.Features.SETTING_014.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: EmailTemplateUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động template email' })
  @Roles(enumProject.Features.SETTING_014.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateIsDelete(user, data)
  }
}
