import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BidEntity } from './bid.entity'
import { BranchEntity } from './branch.entity'
import { ContractAppendixEntity } from './contractAppendix.entity'
import { ContractHistoryEntity } from './contractHistory.entity'
import { ContractMemberEntity } from './contractMember.entity'
import { InvoiceSuggestEntity } from './invoiceSuggest.entity'
import { ObjectEntity } from './object.entity'
import { PaymentProgressEntity } from './paymentProgress.entity'
import { POEntity } from './po.entity'
import { SupplierEntity } from './supplier.entity'
import { BillEntity } from './bill.entity'
import { PaymentContractEntity } from './paymentContract.entity'

/** Bảng hợp đồng */
@Entity({ name: 'contract' })
export class ContractEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.contracts)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.contracts)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileAttach: string

  /** Ngày hiệu lực */
  @Column({
    nullable: false,
  })
  effectiveDate: Date

  /** Ngày hết hạn */
  @Column({
    nullable: false,
  })
  expiredDate: Date

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /** Lý do Huỷ */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  reason: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  branchId: string
  @ManyToOne(() => BranchEntity, (p) => p.contracts)
  @JoinColumn({ name: 'branchId', referencedColumnName: 'id' })
  branch: Promise<BranchEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  createdBy: string

  /** Đối tượng */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  objectId: string
  @ManyToOne(() => ObjectEntity, (p) => p.contracts)
  @JoinColumn({ name: 'objectId', referencedColumnName: 'id' })
  object: Promise<ObjectEntity>

  /** Giá trị HĐ */
  @Column({
    type: 'bigint',
    default: 0,
    nullable: false,
  })
  value: number

  @Column({
    nullable: true,
    default: false,
  })
  isGenChild: boolean

  @OneToMany(() => ContractAppendixEntity, (p) => p.contract)
  appendixs: Promise<ContractAppendixEntity[]>

  /** Đề nghị thanh toán */
  @OneToMany(() => InvoiceSuggestEntity, (p) => p.contract)
  invoiceSuggests: Promise<InvoiceSuggestEntity[]>

  /** Danh sách PO theo HĐ */
  @OneToMany(() => POEntity, (p) => p.contract)
  pos: Promise<POEntity[]>

  @OneToMany(() => ContractMemberEntity, (p) => p.contract)
  contractMembers: Promise<ContractMemberEntity[]>

  @OneToMany(() => ContractHistoryEntity, (p) => p.contract)
  contractHistorys: Promise<ContractHistoryEntity[]>

  /** Tiến độ thanh toán */
  @OneToMany(() => PaymentProgressEntity, (p) => p.contract)
  paymentPlan: Promise<PaymentProgressEntity[]>

  /** Là PO con */
  @Column({
    nullable: true,
    default: false,
  })
  isChild: boolean

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  parentId: string
  // 1 PO có thể tách thành nhiều PO con
  @ManyToOne(() => ContractEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: ContractEntity
  @OneToMany(() => ContractEntity, (p) => p.parent)
  childs: Promise<ContractEntity[]>

  @OneToMany(() => BillEntity, (p) => p.contract)
  bills: Promise<BillEntity[]>

  @OneToMany(() => PaymentContractEntity, (p) => p.contract)
  paymentContracts: Promise<PaymentContractEntity[]>
}
