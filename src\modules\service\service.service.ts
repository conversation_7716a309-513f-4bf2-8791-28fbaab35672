import { Injectable, NotAcceptableException } from '@nestjs/common'
import { In, <PERSON>Than, Like } from 'typeorm'
import {
  CREATE_SUCCESS,
  enumData,
  ERROR_NOT_FOUND_DATA,
  ERROR_SERVICE_CANNOT_CREATE,
  ERROR_SERVICE_CANNOT_UPDATE,
  ERROR_YOU_DO_NOT_HAVE_PERMISSION,
  UPDATE_ACTIVE_SUCCESS,
  UPDATE_SUCCESS,
} from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { ServiceAccessEntity, ServiceEntity } from '../../entities'
import { EmployeeRepository, ServiceAccessRepository, ServiceRepository } from '../../repositories'
import { ServiceCreateDto, ServiceUpdateDto } from './dto'

@Injectable()
export class ServiceService {
  constructor(
    private readonly repo: ServiceRepository,
    private readonly serviceAccessRepo: ServiceAccessRepository,
    private readonly employeeRepo: EmployeeRepository,
  ) {}

  public async find(user: UserDto, data: { isLast?: boolean; level?: number; parentId?: string }) {
    const whereCon: any = { companyId: user.companyId, isDeleted: false }
    if (data.isLast != undefined) whereCon.isLast = data.isLast
    if (data.level > 0) whereCon.level = data.level
    if (data.parentId) whereCon.parentId = data.parentId
    return await this.repo.find({ where: whereCon, order: { name: 'ASC' } })
  }

  public async createData(user: UserDto, data: ServiceCreateDto) {
    const objCheckCode = await this.repo.findOne({ where: { code: data.code, companyId: user.companyId }, select: { id: true } })
    if (objCheckCode) throw new Error(`Lĩnh vực mua hàng có mã ${data.code} đã được sử dụng.`)

    let level = 1
    let parentId = undefined
    if (data.parent2) {
      level = 3
      parentId = data.parent2
      const objCheckParent = await this.isCanCreate(data.parent2)
      if (!objCheckParent) throw new Error(ERROR_SERVICE_CANNOT_CREATE)
    } else if (data.parent1) {
      level = 2
      parentId = data.parent1
      const objCheckParent = await this.isCanCreate(data.parent1)
      if (!objCheckParent) throw new Error(ERROR_SERVICE_CANNOT_CREATE)
    }

    const service = new ServiceEntity()
    service.companyId = user.companyId
    service.createdBy = user.id
    service.name = data.name || ''
    service.code = data.code
    service.level = level
    service.stockQuantity = data.stockQuantity || 0
    service.isLast = true
    service.parentId = parentId
    service.description = data.description

    // Nhân viên duyệt bước 1
    service.approveById = data.approveById

    // Cập nhật isLast = false cho parent
    if (service.parentId) {
      const parent = await this.repo.findOne({
        where: { id: service.parentId, companyId: user.companyId },
        select: { id: true, isLast: true },
      })
      if (!parent) throw new Error(ERROR_NOT_FOUND_DATA)

      if (parent.isLast) {
        await this.repo.update(parent.id, { isLast: false, updatedBy: user.id })
      }
    }
    const createdEntity = await service.save()

    data.serviceAccess = data.serviceAccess || []
    for (let employeeId of data.serviceAccess) {
      const serviceAccess = new ServiceAccessEntity()
      serviceAccess.companyId = user.companyId
      serviceAccess.createdBy = user.id
      serviceAccess.employeeId = employeeId
      serviceAccess.serviceId = createdEntity.id
      await serviceAccess.save()
    }

    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: ServiceUpdateDto) {
    const service = await this.repo.findOne({
      where: { id: data.id, companyId: user.companyId },
    })
    if (!service) throw new Error(ERROR_NOT_FOUND_DATA)

    const parentIdOld = service.parentId
    let level = 1
    let parentId = undefined
    if (data.parent2) {
      level = 3
      parentId = data.parent2
      const objCheckParent = await this.isCanCreate(data.parent2)
      if (!objCheckParent) throw new Error(ERROR_SERVICE_CANNOT_UPDATE)
    } else if (data.parent1) {
      level = 2
      parentId = data.parent1
      const objCheckParent = await this.isCanCreate(data.parent1)
      if (!objCheckParent) throw new Error(ERROR_SERVICE_CANNOT_UPDATE)
    }

    service.name = data.name
    service.description = data.description
    service.stockQuantity = data.stockQuantity || 0
    service.level = level
    service.parentId = parentId
    // Nhân viên duyệt bước 1
    service.approveById = data.approveById
    service.updatedBy = user.id
    await this.repo.save(service)

    // Cập nhật isLast = false cho parent
    if (parentId != parentIdOld) {
      const parentNew = await this.repo.findOne({
        where: { id: parentId, companyId: user.companyId },
        select: { id: true, isLast: true },
      })
      if (!parentNew) throw new Error(ERROR_NOT_FOUND_DATA)

      if (parentNew.isLast) await this.repo.update(parentNew.id, { isLast: false, updatedBy: user.id })

      // Check xem parent cũ còn service con không, nếu không thì update isLast = true
      const parentOld = await this.repo.findOne({
        where: { parentId: parentIdOld, companyId: user.companyId },
        select: { id: true },
      })
      if (!parentOld) await this.repo.update(parentIdOld, { isLast: true, updatedBy: user.id })
    }

    // xóa serviceAccess
    data.serviceAccess = data.serviceAccess || []
    const serviceAccessOld = await service.serviceAccess
    const lstOldId = serviceAccessOld.map((c) => c.employeeId)

    // lấy Nhân viên phụ trách cần xóa (lấy cái mới truyền xuống không có)
    const lstOldIdNeedDelete = lstOldId.filter((c) => !data.serviceAccess.includes(c))
    if (lstOldIdNeedDelete.length > 0) {
      await this.serviceAccessRepo.delete({ serviceId: data.id, employeeId: In(lstOldIdNeedDelete) })
    }

    // lấy Nhân viên phụ trách cần tạo (lấy cái cũ chưa có trước đó)
    const lstNewIdNeedCreate = data.serviceAccess.filter((c) => !lstOldId.includes(c))
    for (let employeeId of lstNewIdNeedCreate) {
      const serviceAccess = new ServiceAccessEntity()
      serviceAccess.companyId = user.companyId
      serviceAccess.createdBy = user.id
      serviceAccess.employeeId = employeeId
      serviceAccess.serviceId = data.id
      await serviceAccess.save()
    }

    return { message: UPDATE_SUCCESS }
  }

  /** Kiểm tra xem có được phép thêm không */
  private async isCanCreate(parentId: string) {
    const entity: any = await this.repo.findOne({
      where: { id: parentId, level: LessThan(3) },
      relations: { bids: true, supplierServices: true },
      select: { id: true, bids: { id: true }, supplierServices: { id: true } },
    })

    if (!entity) return false
    if (entity.__bids__.length > 0) return false
    if (entity.__supplierServices__.length > 0) return false

    return true
  }

  /** Phân trang cho IT cấu hình dịch vụ */
  public async pagination(user: UserDto, data: PaginationDto) {
    let whereCon: any = { companyId: user.companyId }
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.isLast != undefined) whereCon.isLast = data.where.isLast
    if (data.where.stockQuantity) whereCon.stockQuantity = data.where.stockQuantity
    if (data.where.parentId) {
      whereCon.parent = [{ id: data.where.parentId }, { parentId: data.where.parentId }]
    }
    // if (data.where.filterText) {
    //   whereCon = [
    //     { ...whereCon, code: Like(`%${data.where.filterText}%`) },
    //     { ...whereCon, name: Like(`%${data.where.filterText}%`) },
    //   ]
    // }
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { parent: { parent: true }, serviceAccess: true },
      order: { name: 'ASC' },
    })

    return res
  }

  public async managerPagination(data: PaginationDto, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    let whereCon: any[] = []
    const whereCommon: any = { companyId: user.companyId, isDeleted: false, isLast: true }
    if (data.where.code) whereCommon.code = Like(`%${data.where.code}%`)
    if (data.where.name) whereCommon.name = Like(`%${data.where.name}%`)
    if (data.where.lvmh) {
      whereCommon.code = Like(`%${data.where.lvmh}%`)
      whereCon.push({ ...whereCommon, approveById: user.employeeId })
      whereCommon.name = Like(`%${data.where.lvmh}%`)
      delete whereCommon.code
    } else {
      whereCon.push({ ...whereCommon, approveById: user.employeeId })
    }

    const listService = await this.serviceAccessRepo.find({
      where: { employeeId: user.employeeId, companyId: user.companyId, isDeleted: false },
      select: { id: true, serviceId: true },
    })
    if (listService.length > 0) {
      const listServiceId = listService.map((p) => p.serviceId)
      whereCon.push({ ...whereCommon, id: In(listServiceId) })
      if (data.where.lvmh) {
        whereCommon.code = Like(`%${data.where.lvmh}%`)
        delete whereCommon.name
        whereCon.push({ ...whereCommon, id: In(listServiceId) })
      }
    }
    const result: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { serviceAccess: true },
      order: { name: 'ASC' },
    })

    for (var item of result[0]) {
      item.isShowSendApproveCapacity = false
      item.isShowApproveCapacity = false
      item.__serviceAccess__ = item.__serviceAccess__ || []
      const lstEmployeeId = item.__serviceAccess__.map((c) => c.employeeId)
      if (item.statusCapacity == enumData.StatusServiceCapacity.ChuaDuyet.code && lstEmployeeId.length > 0) {
        item.isShowSendApproveCapacity = lstEmployeeId.includes(user.employeeId)
      }
      if (item.approveById == user.employeeId && item.statusCapacity == enumData.StatusServiceCapacity.GuiDuyet.code) {
        item.isShowApproveCapacity = true
      }
      item.statusCapacityName = enumData.StatusServiceCapacity[item.statusCapacity]?.name
      delete item.__serviceAccess__
    }

    return result
  }

  public async updateIsDelete(user: UserDto, data: { id: string }) {
    if (!data.id) throw new Error(ERROR_NOT_FOUND_DATA)
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  /** Kiểm tra trước khi vào transaction import */
  private async checkImportServices(user: UserDto, data: any) {
    const checkResult: any = {
      isCheckError: false,
      lstError: [],
      message: '',
    }

    // 2 list service check trùng
    const lstService = await this.repo.find({ where: { companyId: user.companyId }, select: { id: true, code: true } })
    const lstServiceCode = lstService.map((c) => c.code)
    const lstServiceInFile: any[] = []

    const lstEmployee = await this.employeeRepo.find({ where: { companyId: user.companyId, isDeleted: false } })

    // Kiểm tra từng row
    const lstErr = []
    for (const row of data.lstData) {
      // zenId: 'STT *' đã check ở FE

      // code: 'Mã Lĩnh vực mua hàng *'
      if (row.code != null) row.code = (row.code + '').trim()
      if (row.code == null || row.code === '') {
        const errorMessage = '[Mã Lĩnh vực mua hàng *] là bắt buộc, không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        if (lstService.some((c) => c.code === row.code)) {
          const errorMessage = `[Mã Lĩnh vực mua hàng *] là [${row.code}] đã tồn tại`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        if (lstServiceInFile.some((c) => c.code === row.code)) {
          const errorMessage = `[Mã Lĩnh vực mua hàng *] đã nhập [${row.code}] trùng trong file`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        if (row.code.length > 50) {
          const errorMessage = `[Mã Lĩnh vực mua hàng *] đã nhập [${row.code}] vượt quá 50 ký tự`
          lstErr.push({ ...row, errorMessage })
          continue
        }
      }

      // name: 'Tên Lĩnh vực mua hàng *'
      if (row.name != null) row.name = (row.name + '').trim()
      if (row.name == null || row.name === '') {
        const errorMessage = '[Tên Lĩnh vực mua hàng *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // accessByName: 'Nhân viên phụ trách *'
      if (row.accessByName != null) row.accessByName = (row.accessByName + '').trim()
      if (row.accessByName == null || row.accessByName === '') {
        const errorMessage = '[Nhân viên phụ trách *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const employee = lstEmployee.find((c: any) => c.name == row.accessByName)
        if (!employee) {
          const errorMessage = `[Nhân viên phụ trách *] có tên [${row.accessByName}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.accessById = employee.id
      }

      // approveByName: 'Người duyệt *'
      if (row.approveByName != null) row.approveByName = (row.approveByName + '').trim()
      if (row.approveByName == null || row.approveByName === '') {
        const errorMessage = '[Người duyệt *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const employee = lstEmployee.find((c: any) => c.name == row.approveByName)
        if (!employee) {
          const errorMessage = `[Người duyệt *] có tên [${row.approveByName}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.approveById = employee.id
      }

      // parentCode: 'Thuộc lĩnh vực mua hàng *'
      if (row.parentCode != null) row.parentCode = (row.parentCode + '').trim()
      if (row.parentCode != null && row.parentCode !== '') {
        if (!lstServiceCode.includes(row.parentCode)) {
          const errorMessage = `[Thuộc lĩnh vực mua hàng *] có tên [${row.parentCode}] không tồn tại trong hệ thống và file`
          lstErr.push({ ...row, errorMessage })
          continue
        }
      }

      lstServiceInFile.push(row)
      lstServiceCode.push(row.code)
    }

    if (lstErr.length > 0) {
      checkResult.lstError = lstErr
      checkResult.isCheckError = true
      checkResult.message = `Có ${checkResult.lstError.length} dòng lỗi, vui lòng xem chi tiết lỗi và kiểm tra lại file!`
    }

    return checkResult
  }

  /** Lưu data import */
  private async saveImportServices(user: UserDto, data: { lstData: any[] }) {
    return await this.repo.manager.transaction(async (manager) => {
      const checkResult: any = { isCheckError: false, lstError: [], message: '', lstSupplierId: [] }
      let numRowSuccess = 0

      const serviceRepo = manager.getRepository(ServiceEntity)
      const serviceAccessRepo = manager.getRepository(ServiceAccessEntity)

      // Lưu từng row
      const lstErr = []
      for (const row of data.lstData) {
        try {
          const serviceNew = new ServiceEntity()
          serviceNew.companyId = user.companyId
          serviceNew.createdBy = user.id
          serviceNew.code = row.code
          serviceNew.name = row.name
          serviceNew.approveById = row.approveById
          serviceNew.level = 1
          serviceNew.isLast = true
          const serviceEntity = await serviceRepo.save(serviceNew)
          if (row.accessById) {
            const serviceAccessEntity = new ServiceAccessEntity()
            serviceAccessEntity.companyId = user.companyId
            serviceAccessEntity.createdBy = user.id
            serviceAccessEntity.employeeId = row.accessById
            serviceAccessEntity.serviceId = serviceEntity.id
            await serviceAccessRepo.save(serviceAccessEntity)
          }

          // có parent
          if (row.parentCode != null && row.parentCode !== '') {
            // tìm parent & update isLast -> false
            const parent = await serviceRepo.findOne({ where: { code: row.parentCode, companyId: user.companyId, isDeleted: false } })
            if (parent) {
              await serviceRepo.update(parent.id, { isLast: false, updatedBy: user.id })

              // gán parentId & tăng level theo parent
              await serviceRepo.update(serviceEntity.id, { parentId: parent.id, level: parent.level + 1, updatedBy: user.id })
            }
          }

          numRowSuccess++
        } catch (error) {
          lstErr.push({ ...row, errorMessage: JSON.stringify(error) })
        }
      }

      if (lstErr.length > 0) {
        checkResult.lstError = lstErr
        checkResult.message = `Import thành công ${numRowSuccess}/${data.lstData.length} LVMH!`
      } else {
        checkResult.message = `Import thành công ${data.lstData.length} LVMH!`
      }

      return checkResult
    })
  }

  /** Import LVMH */
  public async importServices(user: UserDto, data: { lstData: any[] }) {
    // Check user
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    // Check & validate data
    const checkResult = await this.checkImportServices(user, data)
    if (checkResult.lstError.length > 0) {
      return checkResult
    }

    // trả về thông báo số LVMH được import thành công, và danh sách các dòng với chi tiết lỗi từng dòng
    return await this.saveImportServices(user, data)
  }

  /** Lấy tất cả LVMH có quyền */
  async getAllService(user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const whereCommon = { companyId: user.companyId, isDeleted: false, isLast: true }
    const whereCon = [
      { ...whereCommon, approveById: user.employeeId },
      { ...whereCommon, serviceAccess: { isDeleted: false, employeeId: user.employeeId } },
    ]

    const lstService: any[] = await this.repo.find({ where: whereCon, relations: { parent: { parent: { parent: true } } }, order: { name: 'ASC' } })
    const res: any[] = []
    for (const service of lstService) {
      res.push(service)
      if (service.parentId) {
        if (!res.some((c) => c.id == service.parentId)) {
          res.push(service.__parent__)
          if (service.__parent__.parentId) {
            if (!res.some((c) => c.id == service.__parent__.parentId)) {
              res.push(service.__parent__.__parent__)
              if (service.__parent__.__parent__.parentId) {
                if (!res.some((c) => c.id == service.__parent__.__parent__.parentId)) {
                  res.push(service.__parent__.__parent__.__parent__)
                }
              }
            }
          }
        }
      }
    }

    return res
  }
}
