import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsOptional } from 'class-validator'

export class PaymentCreateDto {
  @ApiProperty({ description: 'trạng thái thanh toán' })
  status: string

  @ApiProperty({ description: 'tê<PERSON> hồ sơ thanh toán' })
  name: string

  @ApiPropertyOptional()
  @IsOptional()
  fileAttach: string

  @ApiPropertyOptional()
  @IsOptional()
  filePaymentRequest: string

  @ApiPropertyOptional()
  @IsOptional()
  fileAcceptanceReport: string

  @ApiPropertyOptional()
  @IsOptional()
  note: string

  /** DS bill */
  @ApiProperty()
  billIds: any[]

  /** DS po */
  @ApiProperty()
  poIds: any[]

  /** DS contract */
  @ApiProperty()
  contractIds: any[]

  @ApiProperty()
  @IsOptional()
  supplierId: string

  @ApiPropertyOptional()
  @IsOptional()
  settingStringId: string

  @ApiPropertyOptional()
  moneyAdvance: number

  @ApiProperty({ description: 'Loại thanh toán' })
  paymentType: string

  @ApiProperty()
  @IsOptional()
  isSupplierCreate: boolean
}
