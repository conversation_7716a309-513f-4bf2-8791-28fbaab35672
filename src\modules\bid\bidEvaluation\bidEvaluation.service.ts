import { Injectable, MethodNotAllowedException, NotAcceptableException } from '@nestjs/common'
import { EmailService } from '../../email/email.service'
import { BidRepository, BidEmployeeAccessRepository, BidSupplierRepository } from '../../../repositories'
import { enumData, ERROR_YOU_DO_NOT_HAVE_PERMISSION } from '../../../constants'
import { UserDto } from '../../../dto'
import { In, Not } from 'typeorm'
import { BidHistoryEntity } from '../../../entities'
import { coreHelper } from '../../../helpers'

@Injectable()
export class BidEvaluationService {
  constructor(
    private readonly repo: BidRepository,
    private readonly bidEmployeeAccessRepo: BidEmployeeAccessRepository,
    private readonly bidSupplierRepo: BidSupplierRepository,
    private readonly emailService: EmailService,
  ) {}

  /** Tự động chọn NCC thắng thầu và kết thúc thầu */
  async autoBid(user: UserDto, data: { bidId: string }) {
    const bid: any = await this.repo.findOne({
      where: { id: data.bidId, companyId: user.companyId },
      relations: { childs: true },
      select: { id: true, isAutoBid: true, childs: true },
    })
    if (!bid) throw new Error('Gói thầu không còn tồn tại')
    if (bid.isDeleted) throw new Error('Gói thầu đã ngưng hoạt động')
    if (!bid.isAutoBid) throw new Error('Gói thầu không cấu hình tự động chọn NCC thắng thầu và kết thúc thầu')

    for (const item of bid.__childs__) {
      item.lstBidSupplier = await this.bidSupplierRepo.find({
        where: { bidId: item.id, companyId: user.companyId, isDeleted: false },
        relations: { supplier: true },
        order: { isSuccessBid: 'DESC' },
      })
      if (item.lstBidSupplier.length == 0) continue

      const lstValue = item.lstBidSupplier.map((c) => c.scorePrice)
      const maxValue = Math.max(...lstValue)
      const dlc = coreHelper.calDLC(lstValue)

      const lstValueManual = item.lstBidSupplier.map((c) => c.scoreManualPrice)
      const maxValueManual = Math.max(...lstValueManual)
      const dlcManual = coreHelper.calDLC(lstValueManual)

      for (const itemBidSupplier of item.lstBidSupplier) {
        itemBidSupplier.supplierName = itemBidSupplier.__supplier__.name
        delete itemBidSupplier.__supplier__
        itemBidSupplier.isChoose = itemBidSupplier.isSuccessBid == true
        let isHasTotal = false
        itemBidSupplier.scoreTotal = 0
        itemBidSupplier.scoreManualTotal = 0
        if (
          itemBidSupplier.statusTech === enumData.BidSupplierTechStatus.DaXacNhan.code ||
          itemBidSupplier.statusTech === enumData.BidSupplierTechStatus.DaDuyet.code
        ) {
          isHasTotal = true
          itemBidSupplier.scoreTotal += (itemBidSupplier.scoreTech * item.percentTech) / 100
          itemBidSupplier.scoreManualTotal += (itemBidSupplier.scoreManualTech * item.percentTech) / 100
        } else {
          itemBidSupplier.scoreTech = -1
          itemBidSupplier.scoreManualTech = -1
        }

        if (
          itemBidSupplier.statusTrade === enumData.BidSupplierTradeStatus.DaXacNhan.code ||
          itemBidSupplier.statusTrade === enumData.BidSupplierTradeStatus.DaDuyet.code
        ) {
          isHasTotal = true
          itemBidSupplier.scoreTotal += (itemBidSupplier.scoreTrade * item.percentTrade) / 100
          itemBidSupplier.scoreManualTotal += (itemBidSupplier.scoreManualTrade * item.percentTrade) / 100
        } else {
          itemBidSupplier.scoreTrade = -1
          itemBidSupplier.scoreManualTrade = -1
        }

        if (
          itemBidSupplier.statusPrice === enumData.BidSupplierPriceStatus.DaXacNhan.code ||
          itemBidSupplier.statusPrice === enumData.BidSupplierPriceStatus.DaDuyet.code
        ) {
          isHasTotal = true
          let priceScore = 0
          if (dlc > 0) {
            priceScore = item.percentPrice - (maxValue - itemBidSupplier.scorePrice) / dlc
          } else {
            priceScore = item.percentPrice
          }
          itemBidSupplier.scoreTotal += priceScore

          let priceManualScore = 0
          if (dlcManual > 0) {
            priceManualScore = item.percentPrice - (maxValueManual - itemBidSupplier.scoreManualPrice) / dlcManual
          } else {
            priceManualScore = item.percentPrice
          }
          itemBidSupplier.scoreManualTotal += priceManualScore
        } else {
          itemBidSupplier.scorePrice = -1
          itemBidSupplier.scoreManualPrice = -1
        }

        if (!isHasTotal) {
          itemBidSupplier.scoreTotal = -1
          itemBidSupplier.scoreManualTotal = -1
        }
      }

      item.lstBidSupplier.sort((a, b) => b.scoreTotal - a.scoreTotal)
      let rank = 1
      const total = item.lstBidSupplier.length
      item.lstBidSupplier.forEach((itemBidSupplier) => {
        itemBidSupplier.rank = rank + '/' + total
        rank++
      })
    }
  }

  /** Load ds Doanh nghiệp để chọn trúng thầu */
  async loadSupplierData(user: UserDto, data: { bidId: string }) {
    const res: any = await this.repo.getBid3(user, data.bidId)
    const dicStatusFile: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierFileStatus)
      lstStatus.forEach((c) => (dicStatusFile[c.code] = c.name))
    }
    for (const item of res.listItem) {
      item.lstBidSupplier = await this.bidSupplierRepo.find({
        where: { bidId: item.id, companyId: user.companyId, isDeleted: false },
        relations: { supplier: true },
        order: { isSuccessBid: 'DESC' },
      })
      if (item.lstBidSupplier.length == 0) continue

      const lstAll = await this.bidSupplierRepo.find({
        where: { bidId: item.id, companyId: user.companyId },
        select: { id: true, scorePrice: true, scoreManualPrice: true },
      })
      const lstValue = lstAll.map((c) => c.scorePrice)
      const maxValue = Math.max(...lstValue)
      const dlc = coreHelper.calDLC(lstValue)

      const lstValueManual = lstAll.map((c) => c.scoreManualPrice)
      const maxValueManual = Math.max(...lstValueManual)
      const dlcManual = coreHelper.calDLC(lstValueManual)

      for (const itemBidSupplier of item.lstBidSupplier) {
        itemBidSupplier.supplierName = itemBidSupplier.__supplier__.name
        delete itemBidSupplier.__supplier__
        itemBidSupplier.statusFileName = dicStatusFile[itemBidSupplier.statusFile]
        itemBidSupplier.isChoose = itemBidSupplier.isSuccessBid == true
        let isHasTotal = false
        itemBidSupplier.scoreTotal = 0
        itemBidSupplier.scoreManualTotal = 0
        if (
          itemBidSupplier.statusTech === enumData.BidSupplierTechStatus.DaXacNhan.code ||
          itemBidSupplier.statusTech === enumData.BidSupplierTechStatus.DaDuyet.code
        ) {
          isHasTotal = true
          itemBidSupplier.scoreTotal += (itemBidSupplier.scoreTech * item.percentTech) / 100
          itemBidSupplier.scoreManualTotal += (itemBidSupplier.scoreManualTech * item.percentTech) / 100
        } else {
          itemBidSupplier.scoreTech = -1
          itemBidSupplier.scoreManualTech = -1
        }

        if (
          itemBidSupplier.statusTrade === enumData.BidSupplierTradeStatus.DaXacNhan.code ||
          itemBidSupplier.statusTrade === enumData.BidSupplierTradeStatus.DaDuyet.code
        ) {
          isHasTotal = true
          itemBidSupplier.scoreTotal += (itemBidSupplier.scoreTrade * item.percentTrade) / 100
          itemBidSupplier.scoreManualTotal += (itemBidSupplier.scoreManualTrade * item.percentTrade) / 100
        } else {
          itemBidSupplier.scoreTrade = -1
          itemBidSupplier.scoreManualTrade = -1
        }

        if (
          itemBidSupplier.statusPrice === enumData.BidSupplierPriceStatus.DaXacNhan.code ||
          itemBidSupplier.statusPrice === enumData.BidSupplierPriceStatus.DaDuyet.code
        ) {
          isHasTotal = true
          let priceScore = 0
          if (dlc > 0) {
            priceScore = item.percentPrice - (maxValue - itemBidSupplier.scorePrice) / dlc
          } else {
            priceScore = item.percentPrice
          }
          itemBidSupplier.scoreTotal += priceScore

          let priceManualScore = 0
          if (dlcManual > 0) {
            priceManualScore = item.percentPrice - (maxValueManual - itemBidSupplier.scoreManualPrice) / dlcManual
          } else {
            priceManualScore = item.percentPrice
          }
          itemBidSupplier.scoreManualTotal += priceManualScore
        } else {
          itemBidSupplier.scorePrice = -1
          itemBidSupplier.scoreManualPrice = -1
        }

        if (!isHasTotal) {
          itemBidSupplier.scoreTotal = -1
          itemBidSupplier.scoreManualTotal = -1
        }
      }

      item.lstBidSupplier.sort((a, b) => b.scoreTotal - a.scoreTotal)
      let rank = 1
      const total = item.lstBidSupplier.length
      item.lstBidSupplier.forEach((itemBidSupplier) => {
        itemBidSupplier.rank = rank + '/' + total
        rank++
      })
    }

    return res
  }

  /** Hàm kiểm tra quyền xác nhận Doanh nghiệp trúng thầu */
  async checkPermissionEvaluation(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId }, select: { id: true, status: true } })
    if (!bid) return { hasPermission: result, message }
    if (
      bid.status === enumData.BidStatus.HoanTatDanhGia.code ||
      bid.status === enumData.BidStatus.DongDamPhanGia.code ||
      bid.status === enumData.BidStatus.DongDauGia.code
    ) {
      result = await this.bidEmployeeAccessRepo.isMPO(user, bidId)
      if (!result) message = 'Bạn không có quyền xác nhận Doanh nghiệp trúng thầu cho gói thầu.'
    } else message = 'Gói thầu đã được xác nhận Doanh nghiệp trúng thầu.'

    return { hasPermission: result, message }
  }

  /** Chọn Doanh nghiệp trúng thầu, trượt thầu theo từng Item */
  async evaluationBidSupplier(user: UserDto, data: { bidId: string; listItem: any[]; comment: string }) {
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionEvaluation(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const lstPromise = []
    for (const item of data.listItem) {
      const lstSupplierChoose = item.lstBidSupplier.filter((c) => c.isChoose)
      // Cập nhật trạng thái cho các Doanh nghiệp thắng thầu
      for (const supplierChoose of lstSupplierChoose) {
        lstPromise.push(
          this.bidSupplierRepo.update(supplierChoose.id, { isSuccessBid: true, noteSuccessBid: supplierChoose.noteSuccessBid, updatedBy: user.id }),
        )
      }
      const lstId = lstSupplierChoose.map((c) => c.id)
      lstPromise.push(this.bidSupplierRepo.update({ bidId: item.id, id: Not(In(lstId)) }, { isSuccessBid: false, updatedBy: user.id }))
    }
    await Promise.all(lstPromise)

    await this.repo.update(data.bidId, {
      status: enumData.BidStatus.DongThau.code,
      noteCloseBidMPO: data.comment,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.XacNhanNCCTrungThau.code
    bidHistory.save()

    // gửi email mpo leader duyệt
    this.emailService.GuiMpoDuyetNCCThangThau(data.bidId)

    return { message: 'Chọn doanh nghiệp thắng thầu thành công.' }
  }

  /** Phê duyệt Doanh nghiệp thắng thầu */
  async approveSupplierWinBid(user: UserDto, data: { bidId: string; comment: string }) {
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionApproveSupplierWinBid(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const todate = new Date()
    /* nếu như bid có skip thì chuyển sang đóng thầu luôn sau khi duyệt NCC thắng thầu */
    const bid = await this.repo.findOne({ where: { id: data.bidId } })

    if (bid.isSkipEnd) {
      await this.repo.update(data.bidId, {
        status: enumData.BidStatus.HoanTat.code,
        noteCloseBidMPOLeader: data.comment,
        approveChooseSupplierWinDate: todate,
        updatedBy: user.id,
      })
    } else {
      await this.repo.update(data.bidId, {
        status: enumData.BidStatus.DuyetNCCThangThau.code,
        noteCloseBidMPOLeader: data.comment,
        approveChooseSupplierWinDate: todate,
        updatedBy: user.id,
      })
    }

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.PheDuyetNCCThangThau.code
    bidHistory.save()

    // Gửi email thông báo nội bộ: tạm khóa
    // await this.emailService.ThongBaoNCCThangThauDuocDuyet(bidId)

    return { message: 'Phê duyệt Doanh nghiệp thắng thầu thành công.' }
  }

  /** Yêu cầu đánh giá và chọn lại Doanh nghiệp thắng thầu */
  async rejectSupplierWinBid(user: UserDto, data: { bidId: string; comment: string }) {
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionApproveSupplierWinBid(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(data.bidId, {
      status: enumData.BidStatus.DangDanhGia.code,
      statusRateTech: enumData.BidTechRateStatus.DangTao.code,
      statusRateTrade: enumData.BidTradeRateStatus.DangTao.code,
      statusRatePrice: enumData.BidPriceRateStatus.DangTao.code,
      noteCloseBidMPOLeader: data.comment,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.YeuCauKiemTraLai.code
    bidHistory.save()

    // Gửi email
    this.emailService.ThongBaoNCCThangThauBiTuChoi(data.bidId)

    return { message: 'Yêu cầu chọn lại Doanh nghiệp thắng thầu thành công.' }
  }

  /** Kiểm tra quyền Phê duyệt/Từ chối Doanh nghiệp thắng thầu */
  async checkPermissionApproveSupplierWinBid(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId }, select: { id: true, status: true } })
    if (!bid) return { hasPermission: result, message }

    if (bid.status === enumData.BidStatus.DongThau.code) {
      result = await this.bidEmployeeAccessRepo.isMPOLeader(user, bidId)
      if (!result) message = 'Bạn không có quyền phê duyệt Doanh nghiệp thắng thầu.'
    } else message = 'Gói thầu đã được phê duyệt Doanh nghiệp thắng thầu.'

    return { hasPermission: result, message }
  }
}
