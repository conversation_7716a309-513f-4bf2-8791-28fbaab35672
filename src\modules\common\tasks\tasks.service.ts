import { Injectable, Logger } from '@nestjs/common'
import { <PERSON>ron, CronExpression } from '@nestjs/schedule'
import { BidRepository, BidHistoryRepository, BidDealRepository, BidAuctionRepository, EmailHistoryRepository } from '../../../repositories'
import { enumData } from '../../../constants'
import { Raw, In, IsNull, LessThan } from 'typeorm'
import { EmailService } from '../../email/email.service'
import { BidAuctionEntity, BidDealEntity, BidEntity, BidHistoryEntity } from '../../../entities'
import { SQSService } from '../sqs/sqs.service'
import { PrService } from '../../pr/pr.service'
import { PurchasePlanService } from '../../purchasePlan/purchasePlan.service'
import { ContractService } from '../../contract/contract.service'
import { BidService } from '../../bid/bid.service'

@Injectable()
export class TasksService {
  private readonly logger = new Logger(TasksService.name)
  constructor(
    private readonly email: EmailService,
    private bidRepo: BidRepository,
    private bidDealRepo: BidDealRepository,
    private bidAuctionRepo: BidAuctionRepository,
    private bidHistoryRepo: BidHistoryRepository,
    private emailHistoryRepo: EmailHistoryRepository,
    private readonly sqsService: SQSService,
    private prService: PrService,
    private bidService: BidService,
    private contractService: ContractService,
    private purchasePlanService: PurchasePlanService,
  ) {}
  /** Hàm nhắc MPO mở thầu */
  @Cron(CronExpression.EVERY_DAY_AT_3AM)
  async handleCronRemindOpenBid() {
    const lstBid = await this.bidRepo.find({
      where: [
        {
          status: enumData.BidStatus.DangNhanBaoGia.code,
          startBidDate: Raw((alias) => `DATEDIFF(${alias}, CURDATE()) = 1`),
        },
        {
          status: enumData.BidStatus.DangNhanBaoGia.code,
          startBidDate: Raw((alias) => `DATEDIFF(CURDATE(), ${alias}) = 1`),
        },
      ],
      select: { id: true },
    })
    const length = lstBid.length
    for (let i = 0; i < length; i++) {
      const bidItem = lstBid[i]
      // Kiểm tra xem đã gửi email nhắc mở thầu chưa
      const history = await this.bidHistoryRepo.find({
        where: {
          bidId: bidItem.id,
          status: In([enumData.BidHistoryStatus.EmailNhacMoThauLan1.code, enumData.BidHistoryStatus.EmailNhacMoThauLan2.code]),
        },
      })

      if (history.length > 0) {
        // Nếu chưa gửi email nhắc lần 1
        if (!history.some((p) => p.status === enumData.BidHistoryStatus.EmailNhacMoThauLan1.code)) {
          this.email.ThongBaoSapMoThau(bidItem.id)

          // Lưu lịch sử
          const bidHistory = new BidHistoryEntity()
          bidHistory.companyId = bidItem.companyId
          bidHistory.bidId = bidItem.id
          bidHistory.status = enumData.BidHistoryStatus.EmailNhacMoThauLan1.code
          await this.bidHistoryRepo.save(bidHistory)
        }
        // Nếu chưa gửi email nhắc lần 2
        else if (!history.some((p) => p.status === enumData.BidHistoryStatus.EmailNhacMoThauLan2.code)) {
          this.email.ThongBaoQuaHanMoThau(bidItem.id)

          // Lưu lịch sử
          const bidHistory = new BidHistoryEntity()
          bidHistory.companyId = bidItem.companyId
          bidHistory.bidId = bidItem.id
          bidHistory.status = enumData.BidHistoryStatus.EmailNhacMoThauLan2.code
          await this.bidHistoryRepo.save(bidHistory)
        }
      }
    }
    // this.logger.debug('Called handleCronRemindOpenBid')
  }

  /** Hàm nhắc hạn duyệt hồ sơ kỹ thuật-chào giá */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async handleCronRemindRateBid() {
    //#region ThongBaoHanDuyetKyThuat3Ngay
    {
      const lstBid = await this.bidRepo.find({
        where: [
          {
            status: enumData.BidStatus.DangDanhGia.code,
            timeCheckTechDate: Raw((alias) => `DATEDIFF(${alias}, CURDATE()) = 3`),
          },
          {
            status: enumData.BidStatus.DangDuyetDanhGia.code,
            timeCheckTechDate: Raw((alias) => `DATEDIFF(${alias}, CURDATE()) = 3`),
          },
        ],
        select: { id: true },
      })
      for (let i = 0, lenBid = lstBid.length; i < lenBid; i++) {
        this.email.ThongBaoHanDuyetKyThuat3Ngay(lstBid[i].id)
      }
    }
    //#endregion

    //#region ThongBaoHetHanDuyetKyThuat1Ngay
    {
      const lstBid = await this.bidRepo.find({
        where: [
          {
            status: enumData.BidStatus.DangDanhGia.code,
            timeCheckTechDate: Raw((alias) => `DATEDIFF(${alias}, CURDATE()) = -1`),
          },
          {
            status: enumData.BidStatus.DangDuyetDanhGia.code,
            timeCheckTechDate: Raw((alias) => `DATEDIFF(${alias}, CURDATE()) = -1`),
          },
        ],
        select: { id: true },
      })
      for (let i = 0, lenBid = lstBid.length; i < lenBid; i++) {
        this.email.ThongBaoHetHanDuyetKyThuat1Ngay(lstBid[i].id)
      }
    }
    //#endregion

    //#region ThongBaoHanDuyetChaoGia3Ngay
    {
      const lstBid = await this.bidRepo.find({
        where: [
          {
            status: enumData.BidStatus.DangDanhGia.code,
            timeCheckPriceDate: Raw((alias) => `DATEDIFF(${alias}, CURDATE()) = 3`),
          },
          {
            status: enumData.BidStatus.DangDuyetDanhGia.code,
            timeCheckPriceDate: Raw((alias) => `DATEDIFF(${alias}, CURDATE()) = 3`),
          },
        ],
        select: { id: true },
      })
      for (let i = 0, lenBid = lstBid.length; i < lenBid; i++) {
        this.email.ThongBaoHanDuyetChaoGia3Ngay(lstBid[i].id)
      }
    }
    //#endregion

    //#region ThongBaoHetHanDuyetChaoGia1Ngay
    {
      const lstBid = await this.bidRepo.find({
        where: [
          {
            status: enumData.BidStatus.DangDanhGia.code,
            timeCheckPriceDate: Raw((alias) => `DATEDIFF(${alias}, CURDATE()) = -1`),
          },
          {
            status: enumData.BidStatus.DangDuyetDanhGia.code,
            timeCheckPriceDate: Raw((alias) => `DATEDIFF(${alias}, CURDATE()) = -1`),
          },
        ],
        select: { id: true },
      })
      for (let i = 0, lenBid = lstBid.length; i < lenBid; i++) {
        this.email.ThongBaoHetHanDuyetChaoGia1Ngay(lstBid[i].id)
      }
    }
    //#endregion
  }

  /** Hàm tự động gửi email nhắc Doanh nghiệp xác nhận tham gia đấu thầu nếu chưa xác nhận */
  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async handleCronRemindAcceptInvite() {
    const listBid = await this.bidRepo.find({
      where: {
        status: enumData.BidStatus.DangNhanBaoGia.code,
        submitEndDate: Raw((alias) => `DATEDIFF(${alias}, CURDATE()) = 1`),
      },
      select: { id: true },
    })
    for (let i = 0, lenBid = listBid.length; i < lenBid; i++) {
      this.email.GuiNccChuaXacNhanThamGiaThau(listBid[i].id)
    }
  }

  /** Hàm kiểm tra những mail fail và gửi lại */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async handleCronResendEmail() {
    const listMailFail = await this.emailHistoryRepo.find({
      where: { status: enumData.EmailStatus.Fail.code },
      select: {
        id: true,
        toAddresses: true,
        subject: true,
        ccAddresses: true,
        bccAddresses: true,
        body_text: true,
        body_html: true,
        type: true,
      },
    })

    for (let i = 0, length = listMailFail.length; i < length; i++) {
      const item = listMailFail[i]
      console.log(`Gửi email lại vào SQS: ${i}`)

      this.sqsService.sendMessage({
        type: enumData.SQSMessageType.Email,
        data: {
          toAddresses: item.toAddresses,
          subject: item.subject,
          ccAddresses: item.ccAddresses,
          bccAddresses: item.bccAddresses,
          body_text: item.body_text,
          body_html: item.body_html,
          type: item.type,
          isResend: true,
          historyId: item.id,
        },
      })
    }
  }

  /** <Mỗi 2 phút> kiểm tra và đóng các gói thầu đang đàm phán giá/ đấu giá */
  @Cron('0 */2 * * * *')
  async handleCronRemindClose() {
    //#region Close BidDeal
    {
      const listBid = await this.bidRepo.find({
        where: { status: enumData.BidStatus.HoanTatDanhGia.code, parentId: IsNull() },
        select: { id: true },
      })
      if (listBid.length == 0) return

      const lstBidId = listBid.map((p) => p.id)
      const today = new Date()
      const lstBidDeal = await this.bidDealRepo.find({
        where: { bidId: In(lstBidId), status: enumData.BidDealStatus.DangDamPhan.code, endDate: LessThan(today) },
        select: { id: true, bidId: true, companyId: true },
      })

      for (let i = 0, length = lstBidDeal.length; i < length; i++) {
        const bidDeal = lstBidDeal[i]
        await this.bidDealRepo.manager.transaction(async (manager) => {
          const bidRepo = manager.getRepository(BidEntity)
          const bidDealRepo = manager.getRepository(BidDealEntity)
          const bidHistoryRepo = manager.getRepository(BidHistoryEntity)

          // Đóng đàm phán giá
          await bidDealRepo.update(bidDeal.id, { status: enumData.BidDealStatus.DongDamPhanGia.code })
          // Đóng đàm phán giá các Item
          const lstDealItem = await bidDealRepo.find({ where: { parentId: bidDeal.id }, select: { id: true, bidId: true } })
          await bidDealRepo.update({ parentId: bidDeal.id }, { status: enumData.BidStatus.DongDamPhanGia.code })
          const lstBidItemId = lstDealItem.map((c) => c.bidId)
          if (lstBidItemId.length > 0) {
            await bidRepo.update({ id: In(lstBidItemId) }, { status: enumData.BidStatus.DongDamPhanGia.code })
          }

          // Lưu lịch sử
          const bidHistory = new BidHistoryEntity()
          bidHistory.companyId = bidDeal.companyId
          bidHistory.bidId = bidDeal.bidId
          bidHistory.status = enumData.BidHistoryStatus.DongDamPhanGia.code
          await bidHistoryRepo.save(bidHistory)
        })
      }
    }
    //#endregion

    //#region Close BidAuction
    {
      const listBid = await this.bidRepo.find({
        where: { status: enumData.BidStatus.HoanTatDanhGia.code, parentId: IsNull() },
        select: { id: true },
      })
      if (listBid.length == 0) return

      const lstBidId = listBid.map((p) => p.id)
      const today = new Date()
      const lstBidAuction = await this.bidAuctionRepo.find({
        where: { bidId: In(lstBidId), status: enumData.BidAuctionStatus.DangDauGia.code, endDate: LessThan(today) },
        select: { id: true, bidId: true, companyId: true },
      })

      for (let i = 0, length = lstBidAuction.length; i < length; i++) {
        const bidAuction = lstBidAuction[i]

        await this.bidAuctionRepo.manager.transaction(async (manager) => {
          const bidRepo = manager.getRepository(BidEntity)
          const bidAuctionRepo = manager.getRepository(BidAuctionEntity)
          const bidHistoryRepo = manager.getRepository(BidHistoryEntity)

          // Đóng đấu giá
          await bidAuctionRepo.update(bidAuction.id, { status: enumData.BidAuctionStatus.DongDauGia.code })
          // Đóng đấu giá các Item
          const lstAuctionItem = await bidAuctionRepo.find({ where: { parentId: bidAuction.id }, select: { id: true, bidId: true } })
          await bidAuctionRepo.update({ parentId: bidAuction.id }, { status: enumData.BidStatus.DongDauGia.code })
          const lstBidItemId = lstAuctionItem.map((c) => c.bidId)
          if (lstBidItemId.length > 0) {
            await bidRepo.update({ id: In(lstBidItemId) }, { status: enumData.BidStatus.DongDauGia.code })
          }

          // Lưu lịch sử
          const bidHistory = new BidHistoryEntity()
          bidHistory.companyId = bidAuction.companyId
          bidHistory.bidId = bidAuction.bidId
          bidHistory.status = enumData.BidHistoryStatus.DongDauGia.code
          await bidHistoryRepo.save(bidHistory)
        })
      }
    }
    //#endregion
  }

  /** Hàm tạo cảnh báo */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleCronCreateWarning() {
    this.prService.autoCreateWarningExpiry()
    this.contractService.autoCreateWarningComingExpiry()
    this.contractService.autoCreateWarningExpiryPayment()
    this.purchasePlanService.autoCreateWarningOverBudget()
    this.bidService.autoCreateWarningComingExpirySettingEvalution()
  }
}
