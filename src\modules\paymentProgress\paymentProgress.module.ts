import { Module } from '@nestjs/common'
import { PaymentProgressRepository } from '../../repositories'
import { TypeOrmExModule } from '../../typeorm'
import { PaymentProgressController } from './paymentProgress.controller'
import { PaymentProgressService } from './paymentProgress.service'
@Module({
  imports: [TypeOrmExModule.forCustomRepository([PaymentProgressRepository])],
  controllers: [PaymentProgressController],
  providers: [PaymentProgressService],
  exports: [PaymentProgressService],
})
export class PaymentProgressModule {}
