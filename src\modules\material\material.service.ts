import { ConflictException, Injectable } from '@nestjs/common'
import { MaterialCreateDto, MaterialUpdateDto } from './dto'
import { PaginationDto, UserDto } from '../../dto'
import { MaterialRepository } from '../../repositories'
import { Like } from 'typeorm'
import { ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS } from '../../constants'
import { MaterialEntity } from '../../entities'

@Injectable()
export class MaterialService {
  constructor(private readonly repo: MaterialRepository) {}

  public async find(user: UserDto, data: {}) {
    return await this.repo.find({ where: { isDeleted: false, companyId: user.companyId }, order: { name: 'ASC' } })
  }

  public async createData(user: UserDto, data: MaterialCreateDto) {
    return this.repo.manager.transaction(async (manager) => {
      try {
        const objectRepo = manager.getRepository(MaterialEntity)
        const objCheckCode = await objectRepo.findOne({ where: { code: data.code, companyId: user.companyId }, select: { id: true } })
        if (objCheckCode) throw new ConflictException(ERROR_CODE_TAKEN)

        const entity = new MaterialEntity()
        entity.name = data.name
        entity.code = data.code
        entity.companyId = user.companyId
        entity.description = data.description
        entity.createdBy = user.id
        await objectRepo.save(entity)

        return { message: 'Tạo mới thành công' }
      } catch (error) {
        throw error
      }
    })
  }

  public async updateData(user: UserDto, data: MaterialUpdateDto) {
    return this.repo.manager.transaction(async (manager) => {
      try {
        const objectRepo = manager.getRepository(MaterialEntity)
        const entity = await objectRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
        if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
        if (entity.code != data.code) {
          const objCheckCode = await objectRepo.findOne({ where: { code: data.code, companyId: user.companyId }, select: { id: true } })
          if (objCheckCode) throw new ConflictException(ERROR_CODE_TAKEN)
        }

        entity.name = data.name
        entity.code = data.code
        entity.description = data.description
        entity.updatedBy = user.id
        await objectRepo.save(entity)

        return { message: UPDATE_ACTIVE_SUCCESS }
      } catch (error) {
        throw error
      }
    })
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { companyId: user.companyId }
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { name: 'ASC' },
    })
  }

  public async updateIsDelete(user: UserDto, id: string) {
    const entity = await this.repo.findOne({ where: { id: id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(id, { isDeleted: !entity.isDeleted, updatedBy: user.id })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }
}
