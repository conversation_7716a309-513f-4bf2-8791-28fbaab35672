# env example cho onpremise (no saas)
NODE_ENV=development
PORT=3400
JWT_SECRET=<your_secret>
JWT_EXPIRY=10h

TYPEORM_HOST=ape-dev.c8fz4hdjihtv.ap-southeast-1.rds.amazonaws.com
TYPEORM_PORT=3306
TYPEORM_USERNAME=ape-auth
TYPEORM_PASSWORD='MPO123456!@#'
TYPEORM_DATABASE=ape-pms-dev
TYPEORM_LOGGING=false

CLIENT_URL=http://ape-pms-client.apetechs.co
ADMIN_URL=http://ape-pms-admin.apetechs.co

apiVersion=2023-03-09
AWS_S3_BUCKET_NAME=ape-devs
AWS_S3_ACCESS_KEY_ID=********************
AWS_S3_SECRET_ACCESS_KEY=yoZLjn9HP3t9a35gc7VTQ/sZC2vJVsp2WpO/EUAl

AWS_SMTP_END_POINT=smtp.gmail.com
AWS_SMTP_PORT=587
AWS_SMTP_SENDER_ADDRESS=<EMAIL>
AWS_SMTP_USERNAME=<EMAIL>
AWS_SMTP_PASSWORD=geppubclgzeddebc

AWS_SQS_URL=https://sqs.ap-southeast-1.amazonaws.com/077293829360/ape-devs
AWS_SQS_REGION=ap-southeast-1
AWS_SQS_ACCESS_KEY_ID=********************
AWS_SQS_SECRET_ACCESS_KEY=vv6cPjMv7FTKGPSmK6pUH6I/x0rrhcl2hzVF3785

LINK_UPLOAD_S3=ape-devs-pms
IS_PRODUCT=false