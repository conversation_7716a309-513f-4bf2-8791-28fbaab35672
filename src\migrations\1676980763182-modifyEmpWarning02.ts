import { MigrationInterface, QueryRunner } from "typeorm";

export class modifyEmpWarning021676980763182 implements MigrationInterface {
    name = 'modifyEmpWarning021676980763182'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`employee_warning\` ADD \`warningType\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`employee_warning\` DROP COLUMN \`dataType\``);
        await queryRunner.query(`ALTER TABLE \`employee_warning\` ADD \`dataType\` varchar(50) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`employee_warning\` DROP COLUMN \`dataType\``);
        await queryRunner.query(`ALTER TABLE \`employee_warning\` ADD \`dataType\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`employee_warning\` DROP COLUMN \`warningType\``);
    }

}
