import { Injectable, NotAcceptableException } from '@nestjs/common'
import { PaginationDto, UserDto } from '../../../dto'
import * as moment from 'moment'

import { CREATE_SUCCESS, enumData, ERROR_NOT_FOUND_DATA, UPDATE_SUCCESS } from '../../../constants'
import { Between, In, Like } from 'typeorm'
import { EmployeeRepository, PORepository, PrRepository, WarehouseRepository } from '../../../repositories'
import { InboundCreateDto, InboundUpdateDto, InboundUpdateStatusDto } from '../dto'
import { InboundContainerEntity, InboundEntity } from '../../../entities'
import { v4 as uuidv4 } from 'uuid'
import { InboundClientCreateDto } from '../dto/inboundClientCreate.dto'
import { coreHelper } from '../../../helpers'
import { InboundRepository } from '../../../repositories/inbound.repository'

@Injectable()
export class InboundClientService {
  constructor(
    private readonly repo: InboundRepository,
    private readonly employeeRepo: EmployeeRepository,
    private readonly prRepo: PrRepository,
    private readonly poRepo: PORepository,
    private readonly warehouseRepo: WarehouseRepository,
  ) {}

  /** lấy chi tiết */
  public async loadDetail(user: UserDto, data: { id: string }) {
    const res: any = await this.repo.findOne({
      where: { id: data.id, isDeleted: false },
      relations: {
        po: {
          supplier: true,
          products: true,
        },
        employeeIncharge: true,

        inboundContainers: true,
      },
    })

    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    res.contractName = res.__contract__?.name
    res.poNumber = res.__po__?.code
    res.expectWarehouseName = res.__expectWarehouse__?.name
    res.employeeInchargeName = res.__employeeIncharge__?.name
    res.statusName = enumData.InboundStatus[res.status]?.name
    res.dateArrivalPortFormat = moment(res.dateArrivalPort).format('DD/MM/YYYY')
    res.dateArrivalWarehouseFormat = moment(res.dateArrivalWarehouse).format('DD/MM/YYYY')
    res.dateExpiredFormat = moment(res.dateExpired).format('DD/MM/YYYY')
    res.listContainer = res.__inboundContainers__

    if (res.__po__) {
      res.prCode = res.__po__?.__pr__?.code
      res.lstProduct = res.__po__.__products__
    }

    delete res.__po___
    delete res.__po__
    delete res.__expectWarehouse__
    delete res.__employeeIncharge__
    delete res.__inboundItems__
    delete res.__inboundContainers__

    return res
  }

  // Tạo mã tự động cho yêu cầu chỉnh sửa
  async codeDefault() {
    const code = `IB${moment(new Date()).format('YYYYMM')}`
    const objData = await this.repo.findOne({
      where: { code: Like(`%${code}%`) },
      order: { code: 'DESC' },
    })
    let sortString = '0000'
    if (objData) {
      sortString = objData.code.substring(code.length, code.length + 4)
    }
    const lastSort = parseInt(sortString, 10)
    sortString = ('0000' + (lastSort + 1)).slice(-4)

    return code + sortString
  }

  /** Tạo mới inbound */
  public async createData(user: UserDto, data: InboundCreateDto) {
    // check user
    if (!user.supplierId) throw new NotAcceptableException('Bạn không có quyền thực hiện chức năng này!')
    const inboundCode = await this.codeDefault()

    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(InboundEntity)
      const inboundContainerRepo = trans.getRepository(InboundContainerEntity)

      const newInbound = repo.create({
        ...data,

        code: inboundCode,
        createdBy: user.id,
        createdAt: new Date(),
        status: enumData.InboundStatus.NEW.code,
      })

      const created = await repo.save(newInbound)

      if (data.listContainer && data.listContainer.length > 0) {
        const listContainerTask = []
        for (const itemContainer of data.listContainer) {
          const inboundContainer = inboundContainerRepo.create({
            ...itemContainer,
            id: uuidv4(),
            inboundId: created.id,
            createdBy: user.id,
            createdAt: new Date(),
          })
          listContainerTask.push(inboundContainer)
        }
        await inboundContainerRepo.insert(listContainerTask)
      }

      return { message: CREATE_SUCCESS }
    })
  }

  /** Hàm cập nhật */
  public async updateData(user: UserDto, data: InboundUpdateDto) {
    const found = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!found) throw new Error(ERROR_NOT_FOUND_DATA)

    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(InboundEntity)
      const inboundContainerRepo = trans.getRepository(InboundContainerEntity)

      const updateInbound = repo.create({
        ...data,
        updatedBy: user.id,
        updatedAt: new Date(),
      })
      const updated = await repo.save(updateInbound)

      const listContainerTask = []
      for (const itemContainer of data.listContainer) {
        const inboundContainer = inboundContainerRepo.create({
          ...itemContainer,
          inboundId: updated.id,
          updatedAt: new Date(),
          updatedBy: user.id,
        })
        listContainerTask.push(inboundContainer)
      }

      await inboundContainerRepo.save(listContainerTask)

      return { message: UPDATE_SUCCESS }
    })
  }

  /** Hàm cập nhật trạng thái */
  public async updateStatus(user: UserDto, data: InboundUpdateStatusDto) {
    const found = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!found) throw new Error(ERROR_NOT_FOUND_DATA)

    return this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(InboundEntity)

      if (!enumData.InboundStatus[data.status]) {
        throw new Error('Trạng thái không tồn tại')
      }

      found.status = data.status
      found.updatedAt = new Date()
      found.updatedBy = user.id
      await repo.save(found)
      return { message: UPDATE_SUCCESS }
    })
  }

  public convertStatus(
    data: any,
    enumStatus: any,
    statusStyleFieldName: string = 'statusStyle',
    dotStyleFieldName: string = 'dotStyle',
    tagColorFieldName: string = 'tagStatusColor',
  ) {
    // Style cho trạng thái
    data[statusStyleFieldName] = {
      color: enumStatus.color,
      borderColor: enumStatus.borderColor,
      width: '200px',
      fontWeight: '600',
      borderRadius: '30px',
    }
    data[dotStyleFieldName] = {
      width: '6px',
      height: '6px',
      borderRadius: '100%',
      backgroundColor: enumStatus.color,
    }
    data[tagColorFieldName] = enumStatus.bgColor
    return data
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}
    if (data.where?.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where?.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where?.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where?.supplierId) whereCon.supplierId = data.where.supplierId
    if (data.where?.poId) whereCon.poId = data.where.poId

    if (data.where.status) whereCon.status = data.where.status

    if (!whereCon.po) whereCon.po = {}
    whereCon.po.code = data.where.poCode

    if (!whereCon.po.supplier) whereCon.po.supplier = {}
    whereCon.po.supplier.code = data.where.supplierCode

    // whereCon.po.supplier.id = user.supplierId
    if (data.where.createdAtFrom && data.where.createdAtTo) {
      whereCon.createdAt = Between(
        moment(data.where.createdAtFrom).format('YYYY-MM-DD 00:00:00'),
        moment(data.where.createdAtTo).format('YYYY-MM-DD 23:59:59'),
      )
    }

    if (data.where.deliveryDateFrom && data.where.deliveryDateTo) {
      whereCon.createdAt = Between(
        moment(data.where.deliveryDateFrom).format('YYYY-MM-DD 00:00:00'),
        moment(data.where.deliveryDateTo).format('YYYY-MM-DD 23:59:59'),
      )
    }

    const res: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      relations: {
        po: {
          supplier: true,
        },
        employeeIncharge: true,
      },
    })

    const resEmployee = await this.employeeRepo.find({
      where: {
        isDeleted: false,
      },
    })

    const dictUser = resEmployee.reduce((acc: any, curr: any) => {
      acc[curr.userId] = curr
      return acc
    }, {})

    const resPR = await this.prRepo.find({
      where: {
        isDeleted: false,
      },
    })
    const dictPR = resPR.reduce((acc: any, curr: any) => {
      acc[curr.id] = curr
      return acc
    }, {})

    for (const item of res[0]) {
      item.contractName = item.__contract__?.name
      item.poNumber = item.__po__?.code
      item.expectWarehouseName = item.__expectWarehouse__?.name
      item.employeeInchargeName = item.__employeeIncharge__?.name
      item.statusName = enumData.InboundStatus[item.status]?.name
      this.convertStatus(item, enumData.InboundStatus[item.status], 'statusStyle', 'statusDotStyle', 'tagStatusColor')

      item.createdByName = dictUser[item.createdBy]?.name

      item.supplierName = item.__po__?.__supplier__?.name

      delete item.__po__
      delete item.__expectWarehouse__
      delete item.__employeeIncharge__
    }

    return res
  }

  public async createDataPoInbound(user: UserDto, data: InboundClientCreateDto) {
    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(InboundEntity)
      const inboundContainerRepo = trans.getRepository(InboundContainerEntity)

      const setDate = moment(new Date()).format('MMYYYY') + '_'
      const objData = await this.repo.findOne({
        where: { code: Like(`%${setDate}%`) },
        order: { code: 'DESC' },
      })
      let sortString = '000000'
      if (objData) {
        sortString = objData.code.substring(setDate.length, setDate.length + 6)
      }
      const lastSort = parseInt(sortString, 10)
      sortString = ('000000' + (lastSort + 1)).slice(-6)

      const code = setDate + sortString

      const newInbound = repo.create({
        ...data,
        createdBy: user.id,
        createdAt: new Date(),
        code: code,
        status: enumData.InboundStatus.NEW.code,
      })

      const created = await repo.save(newInbound)

      await inboundContainerRepo.delete({ inboundId: created.id })

      const listContainerTask = []
      for (const itemContainer of data.listContainer) {
        const inboundContainer = inboundContainerRepo.create({
          ...itemContainer,
          id: uuidv4(),
          inboundId: created.id,
          createdBy: user.id,
          createdAt: new Date(),
        })
        listContainerTask.push(inboundContainer)
      }

      await inboundContainerRepo.insert(listContainerTask)

      return { message: CREATE_SUCCESS }
    })
  }
}
