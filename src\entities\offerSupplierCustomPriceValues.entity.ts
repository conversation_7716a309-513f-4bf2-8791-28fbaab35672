import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { OfferSupplierEntity } from './offerSupplier.entity'

@Entity('offer_supplier_custom_price_value')
export class OfferSupplierCustomPriceValueEntity extends BaseEntity {
  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unit: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  currency: string

  /** Số lượng */
  @Column({
    nullable: true,
    default: 0,
  })
  number: number

  @Column({
    nullable: true,
    default: 0,
  })
  sort: number

  /** Tên  */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerSupplierId: string
  @ManyToOne(() => OfferSupplierEntity, (p) => p.offerSupplierCustomPriceValue)
  @JoinColumn({ name: 'offerSupplierId', referencedColumnName: 'id' })
  offerSupplier: Promise<OfferSupplierEntity>
}
