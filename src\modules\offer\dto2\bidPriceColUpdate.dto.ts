import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsNumber, IsBoolean } from 'class-validator'

export class BidPriceColUpdateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  bidId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiPropertyOptional()
  fomular: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  type: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  colType: string

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isRequired: boolean

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  sort: number
}
