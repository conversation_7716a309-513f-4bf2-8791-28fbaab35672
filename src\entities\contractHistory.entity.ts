import { Entity, Column, Jo<PERSON><PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { EmployeeEntity } from './employee.entity'
import { ContractEntity } from './contract.entity'
import { BaseEntity } from './base.entity'

/** B<PERSON>ng <PERSON> sử hợp đồng */
@Entity('contract_history')
export class ContractHistoryEntity extends BaseEntity {
  /** Trạng thái hiện tại */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  statusCurrent: string

  /** Trạng thái chuyển đổi */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  statusConvert: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  contractId: string
  @ManyToOne(() => ContractEntity, (p) => p.contractHistorys)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.contractHistorys)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  description: string
}
