import { MigrationInterface, QueryRunner } from "typeorm";

export class migrationMaterial1730693949431 implements MigrationInterface {
    name = 'migrationMaterial1730693949431'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid_item\` ADD \`productName\` varchar(500) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid_item\` DROP COLUMN \`productName\``);
    }

}
