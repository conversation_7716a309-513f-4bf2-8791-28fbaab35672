import { <PERSON>ti<PERSON>, Column, Join<PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierEntity } from './supplier.entity'
import { POEntity } from './po.entity'
import { ContractEntity } from './contract.entity'

import { BillLookupEntity } from './billLookup.entity'
import { PaymentBillEntity } from './paymentBill.entity'
/** Cấu hình hóa đơn */
@Entity('bill')
export class BillEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  code: string

  /** Mô tả hóa đơn */
  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  /** File xml */
  @Column({
    type: 'text',

    nullable: true,
  })
  fileXml: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Tình trạng thanh toán */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  paymentStatus: string

  /** nhà cung cấp */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.bills)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** PO */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.bills)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  contractId: string
  @ManyToOne(() => ContractEntity, (p) => p.bills)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  /** Trị giá hóa đơn */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  invoiceValue: number

  /** tổng trị giá hóa đơn */
  @Column({
    type: 'float',
    nullable: true,
  })
  totalInvoiceValue: number

  /** Thuế VAT */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  vat: number

  /** Nguồn tham chiếu hóa đơn(Theo hợp đồng hoặc theo PO)*/
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  referencesInvoice: string

  /** Đơn vị tiền tệ*/
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  currencyName: string

  /**Lưu id của công ty được chọn từ hệ thống hóa đơn bizzi */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  bizziCompanyId: string

  /** File đính kèm PDF */
  @Column({
    type: 'text',
    nullable: true,
  })
  fileAttach: string

  /**Mã tra cứu hóa đơn điện tử */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  billLookupCode: string

  /**Tra cứu hóa đơn */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  billLookupId: string
  @ManyToOne(() => BillLookupEntity, (p) => p.bills)
  @JoinColumn({ name: 'billLookupId', referencedColumnName: 'id' })
  billLookup: Promise<BillLookupEntity>

  @OneToMany(() => PaymentBillEntity, (p) => p.bill)
  paymentBills: Promise<PaymentBillEntity[]>
}
