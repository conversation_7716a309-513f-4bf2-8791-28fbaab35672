import { MigrationInterface, QueryRunner } from "typeorm";

export class dbBidManyItem1670387460067 implements MigrationInterface {
    name = 'dbBidManyItem1670387460067'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`pr\` ADD \`isAllowBid\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`pr_item\` ADD \`quantityBid\` int NOT NULL DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`pr_item\` DROP COLUMN \`quantityBid\``);
        await queryRunner.query(`ALTER TABLE \`pr\` DROP COLUMN \`isAllowBid\``);
    }

}
