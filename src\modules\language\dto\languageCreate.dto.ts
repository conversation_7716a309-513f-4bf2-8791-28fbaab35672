import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class LanguageCreateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiPropertyOptional()
  @IsOptional()
  avatarUrl: string

  @ApiPropertyOptional()
  @IsOptional()
  description: string
}
