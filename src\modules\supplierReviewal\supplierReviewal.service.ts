import { Injectable, NotFoundException } from '@nestjs/common'
import { PaginationDto, UserDto } from '../../dto'
import {
  SupplierRepository,
  ServiceRepository,
  SupplierServiceRepository,
  ServiceAccessRepository,
  DataHistoryRepository,
  SupplierExpertiseRepository,
} from '../../repositories'
import { EmailService } from '../email/email.service'
import { In, IsNull, Like, Not, Raw } from 'typeorm'
import { ERROR_NOT_FOUND_DATA, UPDATE_SUCCESS, enumData, DELETE_SUCCESS, ACTION_SUCCESS } from '../../constants'
import * as moment from 'moment'
import {
  BidSupplierEntity,
  EmployeeEntity,
  SupplierCapacityEntity,
  SupplierCapacityListDetailEntity,
  SupplierCapacityYearValueEntity,
  SupplierEntity,
  SupplierExpertiseEntity,
  SupplierHistoryEntity,
  SupplierNotifyEntity,
  SupplierServiceEntity,
  UserConfirmCodeEntity,
  UserEntity,
} from '../../entities'
import { coreHelper } from '../../helpers'

@Injectable()
export class SupplierReviewalService {
  constructor(
    private readonly repo: SupplierRepository,
    private readonly supplierExpertiseRepo: SupplierExpertiseRepository,
    private readonly supplierServiceRepo: SupplierServiceRepository,
    private readonly serviceRepo: ServiceRepository,
    private readonly dataHistoryRepo: DataHistoryRepository,
    private readonly serviceAccessRepo: ServiceAccessRepository,
    private readonly emailService: EmailService,
  ) {}

  // Danh sách dịch vụ mà bạn có quyền review
  private async getReviewableServices(user: UserDto, data: { parentId?: string; serviceId?: string; isDeleted?: boolean }) {
    if (!user.employeeId) return []

    const whereAccess: any = { employeeId: user.employeeId, companyId: user.companyId, isDeleted: false }
    if (data.serviceId) whereAccess.serviceId = data.serviceId
    const listServiceAccess = await this.serviceAccessRepo.find({
      where: whereAccess,
      select: { serviceId: true },
    })
    const lstServiceId = listServiceAccess.map((p) => p.serviceId)

    let whereCon: any = { isLast: true, approveById: user.employeeId, companyId: user.companyId } // Người duyệt
    if (data.serviceId) whereCon.id = data.serviceId
    if (data.parentId) whereCon.parentId = data.parentId
    if (data.isDeleted != undefined) whereCon.isDeleted = data.isDeleted

    if (lstServiceId.length > 0) {
      const where2: any = { isLast: true, id: In(lstServiceId) } // Người phụ trách
      whereCon = [whereCon, where2]
    }

    const lstService = await this.serviceRepo.find({ where: whereCon, select: { id: true } })
    return lstService.map((c) => c.id)
  }

  // Load danh sách Doanh nghiệp đăng ký cần duyệt
  public async pagination(user: UserDto, data: PaginationDto) {
    const services = await this.getReviewableServices(user, { serviceId: data.where.serviceId })
    if (services.length === 0) return [[], 0]

    const whereCon: any = { companyId: user.companyId }
    whereCon.serviceId = In(services)
    if (data.where.statusExpertise) whereCon.statusExpertise = data.where.statusExpertise

    if (data.where.status) whereCon.status = data.where.status
    else {
      whereCon.status = In([
        enumData.SupplierServiceStatus.MoiDangKy.code,
        enumData.SupplierServiceStatus.CapNhatThongTin.code,
        enumData.SupplierServiceStatus.PhuTrachDuyet.code,
        enumData.SupplierServiceStatus.PhuTrachKhongDuyet.code,
        enumData.SupplierServiceStatus.KhongDuyet.code,
        enumData.SupplierServiceStatus.DaDuyet.code,
        enumData.SupplierServiceStatus.NgungHoatDong.code,
      ])
    }

    if (data.where.fromDate && data.where.toDate) {
      whereCon.createdAt = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.fromDate).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.toDate).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.fromDate)
      whereCon.createdAt = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.fromDate).format('YYYY-MM-DD')}")`)
    else if (data.where.toDate) whereCon.createdAt = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.toDate).format('YYYY-MM-DD')}")`)

    if (data.where.supplierName) whereCon.supplier = [{ code: Like(`%${data.where.supplierName}%`) }, { name: Like(`%${data.where.supplierName}%`) }]

    const res: any[] = await this.supplierServiceRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { supplier: true, service: true },
      order: { createdAt: 'DESC' },
    })
    if (res[0].length == 0) return res
    const dicStatus: any = {}
    const dicStatusColor: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.SupplierServiceStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
      lstStatus.forEach((c) => (dicStatusColor[c.code] = c.color))
    }
    const lstServiceId = res[0].map((c) => c.serviceId).filter((value, index, self) => self.indexOf(value) === index)
    const lstApproveById = res[0].map((c) => c.__service__.approveById).filter((value, index, self) => value && self.indexOf(value) === index)
    const dicServiceAccessName: any = {}
    const dicServiceApproveName: any = {}
    {
      const lstServiceAccess: any[] = await this.serviceAccessRepo.find({
        where: {
          serviceId: In(lstServiceId),
          companyId: user.companyId,
          isDeleted: false,
        },
        relations: { employee: true },
        select: { id: true, serviceId: true, employee: { id: true, name: true } },
      })
      lstServiceAccess.forEach((c) => {
        if (!dicServiceAccessName[c.serviceId]) dicServiceAccessName[c.serviceId] = []
        dicServiceAccessName[c.serviceId].push(c.__employee__.name)
      })

      if (lstApproveById.length > 0) {
        const lstApprove = await this.repo.manager
          .getRepository(EmployeeEntity)
          .find({ where: { id: In(lstApproveById) }, select: { id: true, name: true } })
        lstApprove.forEach((c) => (dicServiceApproveName[c.id] = c.name))
      }
    }
    for (const item of res[0]) {
      const expertise = await this.supplierExpertiseRepo.findOne({
        where: { supplierServiceId: item.id, companyId: user.companyId, isDeleted: false },
        order: { createdAt: 'DESC' },
        select: { changeDate: true },
      })
      item.supplierName = item.__supplier__.name
      item.itemName = item.__service__.code + ' - ' + item.__service__.name
      const lstAccess = dicServiceAccessName[item.serviceId]
      if (lstAccess?.length > 0) item.acceptEmployeeName = lstAccess.join(', ')
      item.approveByName = ''
      if (item.__service__.approveById) {
        item.approveByName = dicServiceApproveName[item.__service__.approveById]
      }
      item.lastUpdateExpertise = expertise?.changeDate
      item.statusName = dicStatus[item.status]
      item.statusColor = dicStatusColor[item.status]
      delete item.__supplier__
      delete item.__service__
    }
    return res
  }

  // Lấy dữ liệu Doanh nghiệp đăng ký cần duyệt
  public async getReviewableCapacities(supplierServiceId: string, user: UserDto) {
    const services = await this.getReviewableServices(user, {})
    if (services.length === 0) return [[], 0]

    const data = await this.supplierServiceRepo.findOne({
      where: { id: supplierServiceId, serviceId: In(services), companyId: user.companyId },
      relations: {
        supplier: true,
        service: true,
        capacities: {
          supplierCapacityYearValues: true,
          supplierCapacityListDetails: true,
          childs: { supplierCapacityYearValues: true, supplierCapacityListDetails: true },
        },
      },
    })

    if (!data) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    return data
  }

  // Kiểm tra xem bạn có quyền của người phụ trách không
  public async checkPermissionAccess(user: UserDto, serviceId: string) {
    const serviceAccess = await this.serviceAccessRepo.find({
      where: { serviceId, companyId: user.companyId, employeeId: user.employeeId, isDeleted: false },
      select: { id: true },
    })
    return { isAccesser: !!serviceAccess }
  }

  // Kiểm tra xem bạn có quyền của người duyệt không
  public async checkPermissionApprove(user: UserDto, serviceId: string) {
    const service = await this.serviceRepo.findOne({
      where: { id: serviceId, companyId: user.companyId, approveById: user.employeeId },
      select: { id: true },
    })
    return { isApprover: !!service }
  }

  // Nhân viên phụ trách duyệt
  public async employeeApproveSupplier(user: UserDto, data: { supplierServiceId: string; comment?: string }) {
    const objCheck = await this.supplierServiceRepo.findOne({
      where: { id: data.supplierServiceId, companyId: user.companyId },
      select: { id: true },
    })
    if (!objCheck) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.supplierServiceRepo.update(data.supplierServiceId, {
      status: enumData.SupplierServiceStatus.PhuTrachDuyet.code,
      comment: data.comment || '',
      updatedBy: user.id,
    })
    await this.emailService.GuiNguoiDuyetDuyetNccDangKy(data.supplierServiceId)

    return { message: UPDATE_SUCCESS }
  }

  // Nhân viên phụ trách từ chối
  public async employeeRejectSupplier(user: UserDto, data: { supplierServiceId: string; comment?: string }) {
    const objCheck = await this.supplierServiceRepo.findOne({
      where: { id: data.supplierServiceId, companyId: user.companyId },
      select: { id: true },
    })
    if (!objCheck) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.supplierServiceRepo.update(data.supplierServiceId, {
      status: enumData.SupplierServiceStatus.PhuTrachKhongDuyet.code,
      comment: data.comment || '',
      updatedBy: user.id,
    })
    await this.emailService.GuiNccMPOTuChoiDangKy(data.comment || '', data.supplierServiceId)

    return { message: UPDATE_SUCCESS }
  }

  // Nhân viên duyệt duyệt
  public async approveSupplier(userC: UserDto, data: { supplierServiceId: string; comment?: string }) {
    // Approved vào duyệt
    const result = await this.repo.manager.transaction(async (manager) => {
      const repo = manager.getRepository(SupplierEntity)
      const userRepo = manager.getRepository(UserEntity)
      const supplierServiceRepo = new SupplierServiceRepository(SupplierServiceEntity, manager)

      const supplierService = await supplierServiceRepo.findOne({
        where: { id: data.supplierServiceId, companyId: userC.companyId },
        relations: { supplier: { user: true } },
      })
      if (!supplierService) throw new Error(ERROR_NOT_FOUND_DATA)

      const score = await supplierServiceRepo.calScore(data.supplierServiceId)
      const dtNow = new Date()
      await supplierServiceRepo.update(data.supplierServiceId, {
        status: enumData.SupplierServiceStatus.DaDuyet.code,
        approverComment: data.comment || '',
        approveDate: dtNow,
        score,
        updatedBy: userC.id,
      })

      const supplier = await supplierService.supplier
      if (supplier.status !== enumData.SupplierStatus.DaDuyet.code) {
        await repo.update(supplier.id, {
          status: enumData.SupplierStatus.DaDuyet.code,
          updatedBy: userC.id,
        })
      }

      const user = await supplier.user
      await userRepo.update({ username: user.username }, { isDeleted: false })
      return {
        supplierId: supplierService.supplierId,
        username: user.username,
        password: '***',
        updatedBy: userC.id,
      }
    })

    await this.emailService.GuiNccNguoiDuyetDuyetDangKy(data.supplierServiceId, result.username, 'Mật khẩu cũ')

    await this.emailService.GuiMpoNguoiDuyetDuyetDangKy(data.supplierServiceId)

    return { message: UPDATE_SUCCESS }
  }

  // Nhân viên duyệt từ chối
  public async rejectSupplier(user: UserDto, data: { supplierServiceId: string; comment?: string }) {
    // Approved vào từ chối
    await this.supplierServiceRepo.update(data.supplierServiceId, {
      status: enumData.SupplierServiceStatus.KhongDuyet.code,
      comment: data.comment || '',
      updatedBy: user.id,
    })

    await this.emailService.GuiMpoNguoiDuyetTuChoiDangKy(data.comment || '', data.supplierServiceId)

    return { message: UPDATE_SUCCESS }
  }

  // Lấy dữ liệu lịch sử chỉnh sửa thông tin pháp lý của Doanh nghiệp
  public async getSupplierLawHistoryEditData(user: UserDto, supplierId: string) {
    return await this.dataHistoryRepo.findOne({
      where: {
        relationId: supplierId,
        tableName: enumData.DataHistoryTable.Supplier,
        companyId: user.companyId,
      },
      order: { createdAt: 'DESC' },
    })
  }

  // Lấy dữ liệu lịch sử chỉnh sửa thông tin năng lực của Doanh nghiệp
  public async getSupplierCapacityHistoryEditData(user: UserDto, supplierServiceId: string) {
    return await this.dataHistoryRepo.findOne({
      where: {
        relationId: supplierServiceId,
        tableName: enumData.DataHistoryTable.SupplierCapacity,
        companyId: user.companyId,
      },
      order: { createdAt: 'DESC' },
    })
  }

  /** Xóa thông tin đăng ký */
  public async deleteSupplier(userC: UserDto, supplierServiceId: string) {
    const supplierService: any = await this.supplierServiceRepo.findOne({
      where: { id: supplierServiceId, companyId: userC.companyId },
      relations: { supplier: true },
      select: { id: true, serviceId: true, status: true, supplier: { id: true, userId: true, status: true } },
    })
    if (!supplierService) throw new Error(ERROR_NOT_FOUND_DATA)

    const supplier = supplierService.__supplier__
    if (!supplier) throw new Error(ERROR_NOT_FOUND_DATA)

    if (supplierService.status === enumData.SupplierServiceStatus.DaDuyet.code) {
      throw new Error('Item Nhà cung cấp đã được duyệt.')
    }

    const objCheckBidSupplier = await this.repo.manager.getRepository(BidSupplierEntity).findOne({
      where: { supplierId: supplier.id, serviceId: supplierService.serviceId },
      select: { id: true },
    })
    if (objCheckBidSupplier) {
      throw new Error('Nhà cung cấp đã tham gia đấu thầu Item này.')
    }

    await this.repo.manager.transaction(async (manager) => {
      const repo = manager.getRepository(SupplierEntity)
      const userRepo = manager.getRepository(UserEntity)
      const supplierServiceRepo = manager.getRepository(SupplierServiceEntity)
      const supplierCapacityRepo = manager.getRepository(SupplierCapacityEntity)
      const supplierNotifyRepo = manager.getRepository(SupplierNotifyEntity)
      const userConfirmCode = manager.getRepository(UserConfirmCodeEntity)
      const supplierCapacityYearValueRepo = manager.getRepository(SupplierCapacityYearValueEntity)
      const supplierCapacityListDetailRepo = manager.getRepository(SupplierCapacityListDetailEntity)
      const bidSupplierRepo = manager.getRepository(BidSupplierEntity)
      const supplierHistoryRepo = manager.getRepository(SupplierHistoryEntity)

      const lstSupplierService = await supplierServiceRepo.find({
        where: { supplierId: supplier.id, companyId: userC.companyId },
        select: { id: true },
      })

      const supplierCapacity = await supplierService?.capacities
      if (supplierCapacity?.length) {
        for (let i = 0, lenCapacity = supplierCapacity.length; i < lenCapacity; i++) {
          const item = supplierCapacity ? supplierCapacity[i] : null
          if (item) {
            // Xoá year value
            await supplierCapacityYearValueRepo.delete({ supplierCapacityId: item.id })

            // Xoá list value
            await supplierCapacityListDetailRepo.delete({ supplierCapacityId: item.id })
          }
        }
      }

      // Xoá capacity value
      await supplierCapacityRepo.delete({ supplierServiceId: supplierServiceId, parentId: Not(IsNull()) })
      await supplierCapacityRepo.delete({ supplierServiceId: supplierServiceId, parentId: IsNull() })

      // Xoá SupplierService
      await supplierServiceRepo.delete({ id: supplierServiceId })

      const supplierHistory = await supplierHistoryRepo.findOne({
        where: { supplierId: supplier.id },
        select: { id: true },
      })

      if (supplierHistory) {
        await supplierHistoryRepo.delete({ supplierId: supplier.id })
      }

      // xoá supplier nếu chỉ có 1 Item
      const objCheckBidSup = await bidSupplierRepo.findOne({ where: { supplierId: supplier.id }, select: { id: true } })
      if (lstSupplierService.length === 1 && !objCheckBidSup) {
        const user = await userRepo.findOne({
          where: { supplierId: supplier.id, companyId: userC.companyId },
          select: { id: true },
        })
        if (!user) throw new Error('Lỗi không tìm thấy thông tin tài khoản của nhà cung cấp.')

        await supplierNotifyRepo.delete({ supplierId: supplier.id })

        // xóa cái supplier đã có code quên mật khẩu
        await userConfirmCode.delete({ userId: supplier.userId })

        await userRepo.save({ supplierId: null, updatedBy: userC.id })

        await userRepo.delete({ id: user.id })
        await repo.delete({ id: supplier.id })
      }

      return { message: DELETE_SUCCESS }
    })
  }

  /** Ngưng hoạt động/Hoạt động lại item trong NCC */
  public async deActiveSupplier(user: UserDto, supplierServiceId: string) {
    const supplierService: any = await this.supplierServiceRepo.findOne({
      where: { id: supplierServiceId, companyId: user.companyId },
      relations: { supplier: true },
      select: { id: true, status: true, supplier: { id: true, status: true, userId: true } },
    })
    if (!supplierService) throw new Error(ERROR_NOT_FOUND_DATA)

    const supplier = supplierService.__supplier__
    if (!supplier) throw new Error(ERROR_NOT_FOUND_DATA)

    if (supplier.status !== enumData.SupplierStatus.DaDuyet.code || !supplier.userId)
      throw new Error('Nhà cung cấp chưa được duyệt trở thành thành viên.')

    if (supplierService.status === enumData.SupplierServiceStatus.NgungHoatDong.code) {
      await this.supplierServiceRepo.update(supplierServiceId, { status: enumData.SupplierServiceStatus.DaDuyet.code, updatedBy: user.id })
    } else {
      await this.supplierServiceRepo.update(supplierServiceId, { status: enumData.SupplierServiceStatus.NgungHoatDong.code, updatedBy: user.id })
    }

    return { message: ACTION_SUCCESS }
  }
}
