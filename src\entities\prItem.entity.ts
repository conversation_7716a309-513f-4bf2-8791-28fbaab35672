import { <PERSON><PERSON><PERSON>, <PERSON>umn, Jo<PERSON><PERSON><PERSON>umn, ManyTo<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BidEntity } from './bid.entity'
import { ItemTechEntity } from './itemTech.entity'
import { PrEntity } from './pr.entity'
import { PurchasePlanEntity } from './purchasePlan.entity'
import { ServiceEntity } from './service.entity'
import { MaterialEntity } from './material.entity'
import { OfferServiceEntity } from './offerService.entity'

/** Danh mục Item của YCMH */
@Entity('pr_item')
export class PrItemEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  prId: string
  @ManyToOne(() => PrEntity, (p) => p.prItems)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  serviceId: string
  @ManyToOne(() => ServiceEntity, (p) => p.prItems)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  itemId: string

  @ManyToOne(() => MaterialEntity, (p) => p.prItems)
  @JoinColumn({ name: 'itemId', referencedColumnName: 'id' })
  item: Promise<MaterialEntity>

  /** k/h (PR Plan) */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  purchasePlanId: string
  @ManyToOne(() => PurchasePlanEntity, (p) => p.prItems)
  @JoinColumn({ name: 'purchasePlanId', referencedColumnName: 'id' })
  purchasePlan: Promise<PurchasePlanEntity>

  /** số lượng cần đặt hàng */
  @Column({
    nullable: false,
  })
  quantity: number

  /** Tên hàng hóa  */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: false,
  })
  productName: string

  /** Lý do đề xuất */
  @Column({
    type: 'text',
    nullable: false,
  })
  suggestReason: string

  /** Ghi chú */
  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  /** Item code */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  code: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: false,
  })
  unit: string

  /** Số lượng đã tạo thầu = tổng quantityItem Các gói thầu Chi tiết tạo từ prItem này */
  @Column({
    nullable: false,
    default: 0,
  })
  quantityBid: number

  /** 1 item có nhiều yêu cầu kỹ thuật */
  @OneToMany(() => ItemTechEntity, (p) => p.prItem)
  itemTechs: Promise<ItemTechEntity[]>

  /** Các gói thầu Chi tiết trong gói thầu tổng của Pr */
  @OneToMany(() => BidEntity, (p) => p.prItem)
  bids: Promise<BidEntity[]>

  @OneToMany(() => OfferServiceEntity, (p) => p.prItem)
  offerService: Promise<OfferServiceEntity[]>
}
