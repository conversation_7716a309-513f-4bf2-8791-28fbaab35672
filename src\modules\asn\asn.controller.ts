import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { AsnService } from './asn.service'
import { AsnCreateDto, AsnUpdateDto } from './dto'

/** Quản lý nhập kho */
@ApiBearerAuth()
@ApiTags('ASN')
@Controller('asn')
export class AsnController {
  constructor(private readonly service: AsnService) {}

  @ApiOperation({ summary: 'Lấy ds ASN' })
  // @Roles(enumProject.Features.ASN_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: {}) {
    return await this.service.find(data, user)
  }

  @ApiOperation({ summary: 'DS ASN phân trang' })
  // @Roles(enumProject.Features.ASN_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo ASN' })
  // @Roles(enumProject.Features.ASN_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: AsnCreateDto) {
    return await this.service.createData(data, user)
  }

  @ApiOperation({ summary: 'Cập nhật thông tin ASN' })
  // @Roles(enumProject.Features.ASN_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: AsnUpdateDto) {
    return await this.service.updateData(data, user)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động' })
  // @Roles(enumProject.Features.ASN_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateIsDelete(data, user)
  }

  @ApiOperation({ summary: 'Tạo ASN bằng excel' })
  // @Roles(enumProject.Features.ASN_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data_excel')
  public async createDataExcel(@CurrentUser() user: UserDto, @Body() data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    return await this.service.createDataExcel(data, user)
  }

  @ApiOperation({ summary: 'Danh sách ASN của PO' })
  // @Roles(enumProject.Features.ASN_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_asn_po')
  public async findAsnPO(@CurrentUser() user: UserDto, @Body() data: { poId: string }) {
    return await this.service.findAsnPO(data, user)
  }

  @ApiOperation({ summary: 'Danh sách Item ASN' })
  // @Roles(enumProject.Features.ASN_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination_detail')
  public async paginationDetail(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.paginationDetail(user, data)
  }
}
