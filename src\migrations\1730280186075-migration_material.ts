import { MigrationInterface, QueryRunner } from "typeorm";

export class migrationMaterial1730280186075 implements MigrationInterface {
    name = 'migrationMaterial1730280186075'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid_price\` ADD \`itemId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`bid_price\` ADD CONSTRAINT \`FK_e6a75ce27597fcb955ad1f99dfe\` FOREIGN KEY (\`itemId\`) REFERENCES \`material\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid_price\` DROP FOREIGN KEY \`FK_e6a75ce27597fcb955ad1f99dfe\``);
        await queryRunner.query(`ALTER TABLE \`bid_price\` DROP COLUMN \`itemId\``);
    }

}
