import { Module } from '@nestjs/common'
import { InvoiceRepository, InvoiceSuggestRepository } from '../../repositories'
import { TypeOrmExModule } from '../../typeorm'
import { InvoiceController } from './invoice.controller'
import { InvoiceService } from './invoice.service'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([InvoiceRepository, InvoiceSuggestRepository])],
  controllers: [InvoiceController],
  providers: [InvoiceService],
})
export class InvoiceModule {}
