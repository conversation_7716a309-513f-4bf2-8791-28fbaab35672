import { Injectable, NotAcceptableException } from '@nestjs/common'
import { Equal, In, Like, Not, Raw } from 'typeorm'
import { CREATE_SUCCESS, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION, UPDATE_ACTIVE_SUCCESS } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import {
  AsnEntity,
  AsnItemEntity,
  BranchEntity,
  ObjectEntity,
  POEntity,
  POProductEntity,
  PurchasePlanEntity,
  ServiceEntity,
  WarehouseEntity,
} from '../../entities'
import { AsnRepository, PORepository } from '../../repositories'
import * as moment from 'moment'
import { AsnCreateDto, AsnUpdateDto } from './dto'

@Injectable()
export class AsnService {
  constructor(private readonly repo: AsnRepository, private readonly poRepo: PORepository) {}

  public async findDetail(user: UserDto, data: { id: string }) {
    const res = await this.repo.findOne({
      where: { id: data.id, companyId: user.companyId },
      relations: { object: true, warehouse: true, branch: true, purchasePlan: true, asnItems: { service: true, asn: true }, po: { pr: true } },
    })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    return res
  }

  public async find(data: {}, user: UserDto) {
    const res: any[] = await this.repo.find({
      where: { companyId: user.companyId, isDeleted: false },
      relations: { asnItems: true },
    })
    for (let e of res) {
      if (e.__asnItems__) {
        e.__details__ = e.__asnItems__
      }

      delete e.__asnItems__
    }

    return res
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const whereCon: any = { companyId: user.companyId }
    if (data.where.serviceId) whereCon.serviceId = data.where.serviceId
    if (data.where.purchasePlanId) whereCon.purchasePlanId = data.where.purchasePlanId
    if (data.where.warehouseId) whereCon.warehouseId = data.where.warehouseId
    if (data.where.branchId) whereCon.branchId = data.where.branchId
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.contractId) whereCon.po = { contractId: data.where.contractId }

    let check = Array.isArray(data.where.poId)
    if (check === true) {
      if (data.where.poId?.length > 0) whereCon.poId = In(data.where.poId)
    } else {
      if (data.where.poId) whereCon.poId = data.where.poId
    }
    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.createdAt = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    }

    return await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { warehouse: true, branch: true, po: { products: true }, asnItems: { service: true, asn: true }, purchasePlan: true },
      order: { createdAt: 'DESC' },
    })
  }

  public async createData(data: AsnCreateDto, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    await this.repo.manager.transaction(async (manager) => {
      // tạo mới thông tin cơ bản asn
      const asn = new AsnEntity()

      const branch = await manager.getRepository(BranchEntity).findOne({
        where: {
          id: data.branchId,
          companyId: user.companyId,
          isDeleted: false,
        },
      })
      let serviceLevel1: any
      const product = await manager.getRepository(POProductEntity).findOne({
        where: {
          poId: data.poId,
          companyId: user.companyId,
          isDeleted: false,
        },
      })
      if (product) {
        const service: any = await manager.getRepository(ServiceEntity).findOne({
          where: {
            id: product.serviceId,
            companyId: user.companyId,
            isDeleted: false,
          },
          relations: { parent: { parent: { parent: true } } },
        })
        if (!service) throw new Error(ERROR_NOT_FOUND_DATA)

        if (service.parentId && service.__parent__.parentId && service.__parent__.__parent__.parentId && service.__parent__.__parent__.__parent__) {
          serviceLevel1 = service.__parent__.__parent__.__parent__.code
        } else if (service.parentId && service.__parent__.parentId && service.__parent__.__parent__) {
          serviceLevel1 = service.__parent__.__parent__.code
        } else if (service.parentId && service.__parent__) {
          serviceLevel1 = service.__parent__.code
        } else {
          serviceLevel1 = service.code
        }
      }
      const object = await manager.getRepository(ObjectEntity).findOne({ where: { id: data.objectId, companyId: user.companyId } })
      let codeSC = '00'
      const asnLast = await manager.getRepository(AsnEntity).findOne({
        where: { code: Like(`%${codeSC}%`), companyId: user.companyId },
        order: { code: 'DESC' },
      })
      let sortString = '0'
      if (asnLast) {
        sortString = asnLast.code.substring(0, 4)
      }
      const lastSort = +sortString
      sortString = ('000' + (lastSort + 1)).slice(-4)
      let code = sortString // STT
      code += '/' + new Date().getFullYear().toString().slice(-2) // YY
      if (branch) {
        code += '/' + branch.code // XXX
      }
      if (object) {
        code += '.' + object.code // ZZZ
      }
      if (serviceLevel1) {
        code += '.' + serviceLevel1 // AAA
      }
      code += '-' + 'ASN'

      asn.code = code
      asn.branchId = data.branchId
      asn.companyId = user.companyId
      asn.warehouseId = data.warehouseId ? data.warehouseId : data.warehouseId
      asn.description = data.description
      asn.poId = data.poId
      asn.asnDate = data.asnDate
      asn.purchasePlanId = data.purchasePlanId
      asn.objectId = data.objectId
      const sum = data.__details__.reduce((sum: any, current: { quantityInbound: any }) => +sum + +current.quantityInbound, 0)
      asn.quantity = sum
      const asnQuantity = await manager.getRepository(AsnEntity).find({
        where: {
          poId: data.poId,
          companyId: user.companyId,
          isDeleted: false,
        },
      })
      if (asnQuantity.length > 0) {
        const check = asnQuantity.reduce((sum: any, current: any) => +sum + +current.quantity, 0)
        let total = check + sum
        const product = await manager.getRepository(POProductEntity).find({
          where: {
            poId: data.poId,
            companyId: user.companyId,
            isDeleted: false,
          },
        })
        if (product && product.length > 0) {
          const totalProduct = product.reduce((sum: any, current: any) => +sum + +current.quantity, 0)
          if (totalProduct < total) {
            throw new Error('Số lượng nhập kho vượt quá số lượng PO có thể nhập')
          }
        }
      }

      let purchaseService: any
      // Cập nhật lại quantityInbound kế hoạch mua hàng
      if (data.purchasePlanId) {
        const purchasePlan = await manager.getRepository(PurchasePlanEntity).findOne({
          where: {
            id: data.purchasePlanId,
            companyId: user.companyId,
            isDeleted: false,
          },
        })
        if (purchasePlan) {
          purchaseService = purchasePlan.serviceId
          if (purchasePlan.quantity - purchasePlan.quantityInbound < sum)
            throw new Error('Số lượng vượt quá số lượng trong kế hoạch. Vui lòng kiểm tra lại.')

          if (purchasePlan.quantityInbound <= 0) {
            purchasePlan.quantityInbound = sum
          } else {
            purchasePlan.quantityInbound = sum + purchasePlan.quantityInbound
          }
          await manager
            .getRepository(PurchasePlanEntity)
            .update(purchasePlan.id, { quantityInbound: purchasePlan.quantityInbound, updatedBy: user.id })
        }
      }

      asn.createdBy = user.id
      const entity = await manager.getRepository(AsnEntity).save(asn)
      // chi tiết của asn
      for (let item of data.__details__) {
        if (purchaseService) {
          if (item.serviceId !== purchaseService) {
            throw new Error('Vật tư trong kế hoạch khác với vật tư trong Item. Vui lòng kiểm tra lại')
          }
        }
        const asnItem = new AsnItemEntity()
        asnItem.asnId = entity.id
        asnItem.itemCode = item.itemCode
        asnItem.poId = item.poId
        asnItem.serviceId = item.serviceId
        asnItem.quantity = item.quantityInbound
        asnItem.description = item.description
        asnItem.branchId = data.branchId
        asnItem.companyId = user.companyId
        asnItem.createdBy = user.id
        await manager.getRepository(AsnItemEntity).save(asnItem)
      }
    })

    return { message: CREATE_SUCCESS }
  }

  public async updateData(data: AsnUpdateDto, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    await this.repo.manager.transaction(async (manager) => {
      const entity = await manager.getRepository(AsnEntity).findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      entity.description = data.description
      entity.poId = data.poId
      entity.branchId = data.branchId
      entity.warehouseId = data.warehouseId
      entity.purchasePlanId = data.purchasePlanId
      entity.quantity = data.quantity
      entity.asnDate = data.asnDate
      entity.objectId = data.objectId
      entity.description = data.description
      const sum = data.__details__.reduce((sum: any, current: { quantityInbound: any }) => +sum + +current.quantityInbound, 0)
      entity.quantity = sum
      const asnQuantity = await manager.getRepository(AsnEntity).find({
        where: {
          poId: data.poId,
          companyId: user.companyId,
          isDeleted: false,
        },
      })
      if (asnQuantity && asnQuantity.length > 0) {
        const check = asnQuantity.reduce((sum: any, current: any) => +sum + +current.quantity, 0)
        let total = check + sum
        const product = await manager.getRepository(POProductEntity).find({
          where: {
            poId: data.poId,
            companyId: user.companyId,
            isDeleted: false,
          },
        })
        if (product && product.length > 0) {
          const totalProduct = product.reduce((sum: any, current: any) => +sum + +current.quantity, 0)
          if (totalProduct < total) {
            throw new Error('Số lượng nhập kho vượt quá số lượng PO có thể nhập')
          }
        }
      }

      let purchaseService
      if (data.purchasePlanId !== null) {
        let quantityAvailable = 0
        const purchasePlan = await manager.getRepository(PurchasePlanEntity).findOne({
          where: {
            id: data.purchasePlanId,
            companyId: user.companyId,
            isDeleted: false,
          },
        })
        if (purchasePlan) {
          quantityAvailable = purchasePlan.quantity - purchasePlan.quantityInbound
          if (quantityAvailable < data.quantity) {
            throw new Error('Số lượng vượt quá số lượng trong kế hoạch. Vui lòng kiểm tra lại.')
          }
          if (purchasePlan.quantityInbound <= 0) {
            purchasePlan.quantityInbound = data.quantity
          } else {
            purchasePlan.quantityInbound = data.quantity + purchasePlan.quantityInbound
          }
          await manager
            .getRepository(PurchasePlanEntity)
            .update(purchasePlan.id, { quantityInbound: purchasePlan.quantityInbound, updatedBy: user.id })
        }
      }
      await manager.getRepository(AsnItemEntity).delete({ asnId: entity.id })
      for (let item of data.__details__) {
        if (purchaseService) {
          if (item.serviceId !== purchaseService) {
            throw new Error('Vật tư trong kế hoạch khác với vật tư trong Item. Vui lòng kiểm tra lại')
          }
        }
        const asnItem = new AsnItemEntity()
        asnItem.asnId = entity.id
        asnItem.itemCode = item.itemCode
        asnItem.poId = item.poId
        asnItem.serviceId = item.serviceId
        asnItem.quantity = item.quantityInbound
        asnItem.description = item.description
        asnItem.branchId = data.branchId
        asnItem.companyId = user.companyId
        asnItem.createdBy = user.id
        await manager.getRepository(AsnItemEntity).save(asnItem)
      }

      entity.updatedBy = user.id
      await manager.getRepository(AsnEntity).save(entity)
    })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async updateIsDelete(data: { id: string }, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    let quantityInvoice: any[] = await this.repo.find({
      where: {
        poId: entity.poId,
        companyId: user.companyId,
        isDeleted: false,
        id: Not(Equal(entity.id)),
      },
    })
    let sum = 0
    let sumPo = 0
    if (quantityInvoice.length > 0) {
      sum = quantityInvoice.reduce((sum, current) => +sum + +current.quantity, 0)
      let poQuantity: any
      poQuantity = await this.poRepo.findOne({
        where: {
          id: entity.poId,
          companyId: user.companyId,
          isDeleted: false,
        },
        relations: { products: true },
      })
      if (poQuantity) {
        sumPo = poQuantity.__products__.reduce((sum: any, current: any) => +sum + +current.quantity, 0)
      }
      if (entity.quantity + sum > sumPo) {
        throw new Error('Số lượng vượt quá tổng số lượng đã nhập kho. Vui lòng kiểm tra lại')
      }
    }
    if (entity.purchasePlanId !== null) {
      const purchasePlan = await this.repo.manager.getRepository(PurchasePlanEntity).findOne({
        where: {
          id: entity.purchasePlanId,
          companyId: user.companyId,
          isDeleted: false,
        },
      })
      if (purchasePlan) {
        if (entity.isDeleted === false) {
          purchasePlan.quantityInbound = purchasePlan.quantityInbound - entity.quantity
        } else {
          purchasePlan.quantityInbound = purchasePlan.quantityInbound + entity.quantity
        }
        await this.repo.manager
          .getRepository(PurchasePlanEntity)
          .update(purchasePlan.id, { quantityInbound: purchasePlan.quantityInbound, updatedBy: user.id })
      }
    }

    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async createDataExcel(data: { lstDataTable1: any[]; lstDataTable2: any[] }, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    return this.repo.manager.transaction(async (manager) => {
      let purchaseService
      if (data.lstDataTable1 && data.lstDataTable1.length > 0) {
        for (let item of data.lstDataTable1) {
          const asn = new AsnEntity()
          console.log(item.branch)
          const branchs = await manager.getRepository(BranchEntity).findOne({
            where: {
              code: item.branch.trim(),
              companyId: user.companyId,
              isDeleted: false,
            },
          })
          if (!branchs) throw new Error('Không tìm được chi nhánh')

          asn.branchId = branchs.id
          asn.companyId = user.companyId
          const purchaseOrder = await manager.getRepository(POEntity).findOne({
            where: {
              code: item.purchaseOrder,
              companyId: user.companyId,
              isDeleted: false,
            },
          })
          if (!purchaseOrder) throw new Error('Không tìm được PO')

          asn.poId = purchaseOrder.id
          const branch = await manager.getRepository(BranchEntity).findOne({
            where: {
              id: branchs.id,
              companyId: user.companyId,
              isDeleted: false,
            },
          })
          let serviceLevel1: any
          const product = await manager.getRepository(POProductEntity).findOne({
            where: {
              poId: purchaseOrder.id,
              companyId: user.companyId,
              isDeleted: false,
            },
          })
          if (product) {
            const service: any = await manager.getRepository(ServiceEntity).findOne({
              where: {
                id: product.serviceId,
                companyId: user.companyId,
                isDeleted: false,
              },
              relations: { parent: { parent: { parent: true } } },
            })
            if (!service) throw new Error('Không tìm được Lĩnh vực')

            if (
              service.parentId &&
              service.__parent__.parentId &&
              service.__parent__.__parent__.parentId &&
              service.__parent__.__parent__.__parent__
            ) {
              serviceLevel1 = service.__parent__.__parent__.__parent__.code
            } else if (service.parentId && service.__parent__.parentId && service.__parent__.__parent__) {
              serviceLevel1 = service.__parent__.__parent__.code
            } else if (service.parentId && service.__parent__) serviceLevel1 = service.__parent__.code
            else serviceLevel1 = service.code
          }
          const object = await manager.getRepository(ObjectEntity).findOne({ where: { id: purchaseOrder.objectId, companyId: user.companyId } })
          let codeSC = '00'
          const asnLast = await manager.getRepository(AsnEntity).findOne({
            where: { code: Like(`%${codeSC}%`), companyId: user.companyId },
            order: { code: 'DESC' },
          })
          let sortString = '0'
          if (asnLast) {
            sortString = asnLast.code.substring(0, 4)
          }
          const lastSort = +sortString
          sortString = ('000' + (lastSort + 1)).slice(-4)
          let code = sortString // STT
          code += '/' + new Date().getFullYear().toString().slice(-2) // YY
          if (branch) {
            code += '/' + branch.code // XXX
          }
          if (object) {
            code += '.' + object.code // ZZZ
          }
          if (serviceLevel1) {
            code += '.' + serviceLevel1 // AAA
          }
          code += '-' + 'ASN'
          asn.code = code

          const lst = data.lstDataTable2.filter((x: any) => x.zenListId == item.zenId)
          const sum = lst.reduce((sum: number, current: any) => {
            const quantity = +current.quantity // Chuyển đổi thành số
            return sum + Math.abs(quantity) // Cộng giá trị tuyệt đối của quantity vào sum
          }, 0)
          asn.quantity = sum
          if (item.purchasePlan !== null) {
            const purchasePlan = await manager.getRepository(PurchasePlanEntity).findOne({
              where: {
                code: item.purchasePlan,
                companyId: user.companyId,
                isDeleted: false,
              },
            })
            asn.purchasePlanId = purchasePlan.id
            asn.purchasePlan = Promise.resolve(purchasePlan)
            if (!purchasePlan) throw new Error('Không tìm được kế hoạch mua hàng')

            purchaseService = purchasePlan.serviceId
            if (purchasePlan.quantity - purchasePlan.quantityInbound < sum) {
              throw new Error('Số lượng vượt quá số lượng trong kế hoạch. Vui lòng kiểm tra lại.')
            }
            if (purchasePlan.quantityInbound <= 0) {
              purchasePlan.quantityInbound = sum
            } else {
              purchasePlan.quantityInbound = sum + purchasePlan.quantityInbound
            }
            await manager
              .getRepository(PurchasePlanEntity)
              .update(purchasePlan.id, { quantityInbound: purchasePlan.quantityInbound, updatedBy: user.id })
          }
          const asnQuantity = await manager.getRepository(AsnEntity).find({
            where: {
              poId: purchaseOrder.id,
              companyId: user.companyId,
              isDeleted: false,
            },
          })
          if (asnQuantity && asnQuantity.length > 0) {
            const check = asnQuantity.reduce((sum: any, current: any) => +sum + +current.quantity, 0)
            let total = check + sum
            const product = await manager.getRepository(POProductEntity).find({
              where: {
                poId: purchaseOrder.id,
                companyId: user.companyId,
                isDeleted: false,
              },
            })
            if (product && product.length > 0) {
              const totalProduct = product.reduce((sum: any, current: any) => +sum + +current.quantity, 0)
              if (totalProduct < total) {
                throw new Error('Số lượng nhập kho vượt quá số lượng PO có thể nhập')
              }
            }
          }
          const wh = await manager.getRepository(WarehouseEntity).findOne({
            where: {
              code: item.warehouse,
              companyId: user.companyId,
              isDeleted: false,
            },
          })
          if (!wh) throw new Error('Không tìm được kho')

          asn.warehouseId = wh.id
          // let dateEx: any
          // dateEx = moment(item.asnDate, ["MM-DD-YYYY", "YYYY-MM-DD"])
          // let date : any
          // date = moment(dateEx).format('YYYY-MM-DD')
          const [day, month, year] = item.asnDate.split('/').map(Number)

          asn.asnDate = new Date(year, month - 1, day)
          asn.createdBy = user.id

          let asnEntityNew = await manager.getRepository(AsnEntity).save(asn)
          for (let detail of data.lstDataTable2) {
            if (item.zenId === detail.zenListId) {
              const asn = await manager.getRepository(AsnEntity).findOne({
                where: {
                  code: asnEntityNew.code,
                  companyId: user.companyId,
                  isDeleted: false,
                },
              })
              if (!asn) throw new Error('Không tìm được ASN')
              const asnItem = new AsnItemEntity()

              asnItem.asnId = asn.id
              const service = await manager.getRepository(POProductEntity).findOne({
                where: {
                  poId: asn.poId,
                  companyId: user.companyId,
                  itemCode: detail.itemCode,
                  isDeleted: false,
                },
              })
              if (service) {
                asnItem.serviceId = service.serviceId
                if (purchaseService) {
                  if (asnItem.serviceId !== purchaseService) {
                    throw new Error('Vật tư trong kế hoạch khác với vật tư trong Item. Vui lòng kiểm tra lại')
                  }
                }
              }
              asnItem.quantity = detail.quantity
              asnItem.description = detail.note
              asnItem.itemCode = detail.itemCode
              asnItem.poId = asn.poId
              asnItem.branchId = asn.branchId
              asnItem.companyId = user.companyId
              asnItem.createdBy = user.id
              await manager.getRepository(AsnItemEntity).save(asnItem)
            }
          }
        }
      }
      return { message: CREATE_SUCCESS }
    })
  }

  public async findAsnPO(data: { poId: string }, user: UserDto) {
    const asnItemRepo = this.repo.manager.getRepository(AsnItemEntity)
    const po: any = await this.poRepo.findOne({
      where: { id: data.poId, companyId: user.companyId, isDeleted: false },
      relations: { products: { service: { parent: true } } },
    })
    if (!po) throw new Error(ERROR_NOT_FOUND_DATA)

    po.quantityPO = po.__products__.reduce((sum: any, current: any) => +sum + +current.quantity, 0)
    const lstASN: any[] = await this.repo.find({
      where: { poId: data.poId, companyId: user.companyId, isDeleted: false },
    })
    if (lstASN.length == 0) return po

    po.quantityAsn = lstASN.reduce((sum: any, current: any) => +sum + +current.quantity, 0)
    for (let asn of lstASN) {
      const detail = await asnItemRepo.find({
        where: { asnId: asn.id, companyId: user.companyId, isDeleted: false },
      })
      if (detail.length > 0) {
        for (let item of po.__products__) {
          const total = await asnItemRepo.find({
            where: { poId: item.poId, companyId: user.companyId, itemCode: item.itemCode, isDeleted: false },
          })
          const sumDetail = total.reduce((sum: any, current: any) => +sum + +current.quantity, 0)
          item.quantity_asn = sumDetail
          item.quantityInbound = item.quantity - sumDetail
        }
      }
    }
    po.asn = lstASN

    return po
  }

  public async paginationDetail(user: UserDto, data: PaginationDto) {
    const asnItemRepo = this.repo.manager.getRepository(AsnItemEntity)
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const whereCon: any = { companyId: user.companyId, isDeleted: false }
    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.createdAt = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD HH:MM:SS')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD HH:MM:SS',
          )}")`,
      )
    }

    if (data.where.purchasePlanId) whereCon.purchasePlanId = data.where.purchasePlanId
    if (data.where.warehouseId) whereCon.warehouseId = data.where.warehouseId
    if (data.where.branchId) whereCon.branchId = data.where.branchId

    const lstAsn = await this.repo.find({ where: whereCon, select: { id: true } })
    const lstAsnId = lstAsn.map((s) => s.id)
    if (lstAsnId.length == 0) return [[], 0]

    const whereItem: any = { asnId: In(lstAsnId), companyId: user.companyId, isDeleted: false }
    if (data.where.serviceId) whereItem.serviceId = data.where.serviceId

    const res: any[] = await asnItemRepo.findAndCount({
      where: whereItem,
      relations: { service: true, asn: { purchasePlan: true, branch: true, warehouse: true, po: true } },
      skip: data.skip,
      take: data.take,
    })

    for (const item of res[0]) {
      item.itemName = item.__service__?.name || ''
      delete item.__service__
      item.asnCode = item.__asn__.code
      item.purchasePlanCode = item.__asn__.__purchasePlan__?.code || ''
      item.warehouseName = item.__asn__.__warehouse__?.name || ''
      item.branchName = item.__asn__.__branch__?.name || ''
      item.poCode = item.__asn__.__po__?.code || ''

      delete item.__asn__
    }

    return res
  }
}
