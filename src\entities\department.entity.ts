import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany, JoinColumn, ManyToOne } from 'typeorm'
import { EmployeeEntity } from './employee.entity'
import { BranchEntity } from './branch.entity'
import { PurchasePlanEntity } from './purchasePlan.entity'

@Entity('department')
export class DepartmentEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  branchId: string
  @ManyToOne(() => BranchEntity, (p) => p.departments)
  @JoinColumn({ name: 'branchId', referencedColumnName: 'id' })
  branch: Promise<BranchEntity>

  @OneToMany(() => EmployeeEntity, (p) => p.department)
  employee: Promise<EmployeeEntity[]>

  @OneToMany(() => PurchasePlanEntity, (p) => p.department)
  purchasePlans: Promise<PurchasePlanEntity[]>
}
