import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class BidDealSupplierSaveDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  id: string

  @ApiProperty()
  listChild: BidDealItemDto[]
}

class BidDealItemDto {
  @ApiProperty()
  id: string
  @ApiProperty()
  bidId: string
  @ApiPropertyOptional()
  filePriceDetail?: string
  @ApiPropertyOptional()
  fileTechDetail?: string
  @ApiPropertyOptional()
  linkDriver?: string
  @ApiProperty()
  lstDealPrice: any[]
}
