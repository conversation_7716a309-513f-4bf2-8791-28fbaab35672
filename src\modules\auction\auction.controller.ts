import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { enumProject } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { AuctionService } from './auction.service'
import { AuctionAddSupplier, AuctionCancel, AuctionCreate, AuctionSubmit, AuctionUpdate } from './dto'

@ApiBearerAuth()
@ApiTags('Auction')
@Controller('auction')
export class AuctionController {
  constructor(private readonly service: AuctionService) {}

  @ApiOperation({ summary: 'Chi tiết đấu giá' })
  @Roles(enumProject.Features.BID_014.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_detail')
  public async findDetail(@Body() data: { id: string }, @CurrentUser() user: UserDto) {
    return await this.service.findDetail(data, user)
  }

  @ApiOperation({ summary: 'Tạo đấu giá' })
  @Roles(enumProject.Features.BID_014.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@Body() data: AuctionCreate, @CurrentUser() user: UserDto) {
    return await this.service.createData(data, user)
  }

  @ApiOperation({ summary: 'Chỉnh sửa đấu giá' })
  @Roles(enumProject.Features.BID_014.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@Body() data: AuctionUpdate, @CurrentUser() user: UserDto) {
    return await this.service.updateData(data, user)
  }

  @ApiOperation({ summary: 'Mời thêm NCC' })
  @Roles(enumProject.Features.BID_014.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('add_supplier')
  public async addSupplier(@Body() data: AuctionAddSupplier, @CurrentUser() user: UserDto) {
    return await this.service.addSupplier(data, user)
  }

  @ApiOperation({ summary: 'Hủy đấu giá' })
  @Roles(enumProject.Features.BID_014.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('cancel_data')
  public async cancelData(@Body() data: AuctionCancel, @CurrentUser() user: UserDto) {
    return await this.service.cancelData(data, user)
  }

  @ApiOperation({ summary: 'DS đấu giá có phân trang' })
  @Roles(enumProject.Features.BID_014.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@Body() data: PaginationDto, @CurrentUser() user: UserDto) {
    return await this.service.pagination(data, user)
  }

  //#region CLIENT

  @ApiOperation({ summary: 'Thông tin đấu giá của NCC' })
  @Roles(enumProject.Features.BID_014.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('get_detail')
  public async findDetailAuctionSupplier(@Body() data: { id: string }, @CurrentUser() user: UserDto) {
    return await this.service.findDetailAuctionSupplier(data, user)
  }

  @ApiOperation({ summary: 'Nộp đấu giá' })
  @Roles(enumProject.Features.BID_014.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('submit_data')
  public async submitData(@Body() data: AuctionSubmit, @CurrentUser() user: UserDto) {
    return await this.service.submitData(data, user)
  }

  //#endregion
}
