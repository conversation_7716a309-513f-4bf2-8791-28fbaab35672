import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { MaterialController } from './material.controller'
import { MaterialService } from './material.service'
import { MaterialRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([MaterialRepository])],
  controllers: [MaterialController],
  providers: [MaterialService],
})
export class MaterialModule {}
