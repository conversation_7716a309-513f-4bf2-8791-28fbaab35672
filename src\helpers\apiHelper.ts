import { HttpService } from '@nestjs/axios'
import { UnauthorizedException } from '@nestjs/common'
import { Request as IRequest } from 'express'
import { lastValueFrom } from 'rxjs'

class ApiHelper {
  constructor(private httpService: HttpService) {}

  /** T<PERSON>o bảng kê chủ hàng */
  public async callGeminiAI(text: any): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const header = { headers: {} }
      const request = this.httpService.post(`${process.env.BOT_API}/ai/gemini`, { text: text }, header)
      lastValueFrom(request)
        .then((res) => {
          resolve(res?.data)
        })
        .catch((err: any) => {
          reject(err?.response)
        })
    })
  }
}

export const apiHelper = new ApiHelper(new HttpService())
