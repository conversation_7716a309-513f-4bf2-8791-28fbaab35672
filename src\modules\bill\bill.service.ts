import { Injectable } from '@nestjs/common'
import { XMLParser } from 'fast-xml-parser'
import { PaginationDto, UserDto } from '../../dto'
import { CREATE_SUCCESS, enumData, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { BillEntity, ContractEntity, POEntity, SupplierEntity } from '../../entities'
import { In, Like } from 'typeorm'
import { BillCreateDto, BillUpdateDto } from './dto'
import { v4 as uuidv4 } from 'uuid'
import * as moment from 'moment'
import { apeAuthApiHelper } from '../../helpers'
import { HttpService } from '@nestjs/axios'
import { lastValueFrom } from 'rxjs'
import { BillRepository } from '../../repositories/bill.repository'
import { enumErrorInvoice } from '../../constants/enumBizzi'
@Injectable()
export class BillService {
  constructor(private httpService: HttpService, private readonly repo: BillRepository) {}

  private callApi(url: string, data: any, config: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const request = this.httpService.post(url, data, { headers: config })
      lastValueFrom(request)
        .then((res) => {
          resolve(res.data)
        })
        .catch((err: any) => {
          reject(err)
        })
    })
  }

  private loadApiData(url: string, config: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const request = this.httpService.get(url, { headers: config })
      lastValueFrom(request)
        .then((res) => {
          resolve(res.data)
        })
        .catch((err: any) => {
          reject(err)
        })
    })
  }

  public async find(user: UserDto, data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.name) whereCon.name = Like(`%${data.name}%`)
    return await this.repo.find({ where: whereCon, order: { createdAt: 'DESC' } })
  }

  async codeDefault() {
    const code = `HĐ${moment(new Date()).format('DDMMYY')}`
    const objData = await this.repo.findOne({
      where: { code: Like(`%${code}%`) },
      order: { code: 'DESC' },
    })
    let sortString = '0000'
    if (objData) {
      sortString = objData.code.substring(code.length, code.length + 4)
    }
    const lastSort = parseInt(sortString, 10)
    sortString = ('0000' + (lastSort + 1)).slice(-4)

    return code + sortString
  }

  public async createData(user: UserDto, data: BillCreateDto) {
    const newCode = await this.codeDefault()
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BillEntity)
      const contractRepo = trans.getRepository(ContractEntity)
      const poRepo = trans.getRepository(POEntity)
      const supplierRepo = trans.getRepository(SupplierEntity)

      const objCheckSupplier = await supplierRepo.findOne({ where: { id: user.supplierId }, select: { id: true } })
      if (!objCheckSupplier) throw new Error(`Nhà cung cấp không tồn tại. Vui lòng kiểm tra lại`)

      let checkContract: any
      if (data.contractId) {
        checkContract = await contractRepo.findOne({ where: { id: data.contractId }, select: { id: true, supplierId: true } })
        if (!checkContract) throw new Error(`Hợp đồng không tồn tại. Vui lòng kiểm tra lại`)
      }

      let checkPO: any
      if (data.poId) {
        checkPO = await poRepo.findOne({ where: { id: data.poId }, select: { id: true, supplierId: true } })
        if (!checkPO) throw new Error(`PO không tồn tại. Vui lòng kiểm tra lại`)
      }

      const bill = new BillEntity()
      bill.supplierId = user.supplierId || checkPO?.supplierId || checkContract?.supplierId // nếu thêm ở admin thì lấy nhà cung cấp của po
      bill.poId = data.poId
      bill.code = newCode
      bill.contractId = data.contractId
      bill.description = data.description
      bill.id = uuidv4()
      bill.fileXml = data.fileXml
      bill.fileXml = data.fileXml
      bill.fileAttach = data.fileAttach
      bill.billLookupCode = data.billLookupCode
      bill.billLookupId = data.billLookupId
      bill.status = enumData.BillStatus.NEW.code
      bill.paymentStatus = enumData.BillStatus.NEW.code
      bill.referencesInvoice = data.referencesInvoice
      bill.bizziCompanyId = data.bizziCompanyId

      bill.createdAt = new Date()

      await repo.insert(bill)

      /**
       * Gọi api thêm xml từ bên bizzi cung cấp api thêm hóa đơn bằng file xml(https://api-uat.bizzi.services/v1/invoices/xml)
       * Lấy thông tin(Đơn vị tiền tệ, Trị giá, Thuế VAT, Tổng giá trị) từ data trả về để thêm vào database ktg
       *
       *  */

      const firstFile: any = bill.fileXml
      let base64File: any
      if (firstFile) {
        try {
          base64File = await apeAuthApiHelper.downloadFileAsBase64(firstFile)
        } catch (error) {
          console.error('Error processing first file:', error.message)
        }
      }

      const config = {
        accept: 'application/json',
        'X-API-KEY': process.env.X_API_KEY,
      }

      const obj = {
        company_id: data.bizziCompanyId,
        xml_base64: base64File,
      }

      const url = process.env.URL_API_ADD_BILL
      let invoice: any

      try {
        invoice = await this.callApi(url, obj, config)
      } catch (error) {
        const errorMessage = error.response?.data?.message || ''
        if (errorMessage.includes('DUPLICATED_INVOICE')) {
          throw new Error(`Lỗi từ hệ thống Bizzi: ${enumErrorInvoice.DUPLICATED_INVOICE}`)
        } else if (errorMessage.includes('INTERNAL_SERVER_ERROR')) {
          throw new Error(`Lỗi từ hệ thống Bizzi: "${enumErrorInvoice.INTERNAL_SERVER_ERROR}"`)
        } else {
          throw new Error(`Lỗi không xác định: ${errorMessage}`)
        }
      }

      const {
        currency, // đơn vị tiền tệ
        total_amount_without_vat, // Trị giá
        total_vat_amount, // Thuế VAT
        total_amount_with_vat, //Tổng trị giá (+ VAT)
      } = invoice.data

      await repo.update(bill.id, {
        currencyName: currency,
        invoiceValue: total_amount_without_vat,
        vat: total_vat_amount,
        totalInvoiceValue: total_amount_with_vat,
      })
    })
    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: BillUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BillEntity)
      const contractRepo = trans.getRepository(ContractEntity)
      const poRepo = trans.getRepository(POEntity)
      const supplierRepo = trans.getRepository(SupplierEntity)

      const objCheckSupplier = await supplierRepo.findOne({ where: { id: user.supplierId }, select: { id: true } })
      if (!objCheckSupplier) throw new Error(`Nhà cung cấp không tồn tại. Vui lòng kiểm tra lại`)

      let checkContract: any
      if (data.contractId) {
        checkContract = await contractRepo.findOne({ where: { id: data.contractId }, select: { id: true, supplierId: true } })
        if (!checkContract) throw new Error(`Hợp đồng không tồn tại. Vui lòng kiểm tra lại`)
      }

      let checkPO: any
      if (data.poId) {
        checkPO = await poRepo.findOne({ where: { id: data.poId }, select: { id: true, supplierId: true } })
        if (!checkPO) throw new Error(`PO không tồn tại. Vui lòng kiểm tra lại`)
      }

      entity.supplierId = user.supplierId || checkPO?.supplierId || checkContract?.supplierId // nếu thêm ở admin thì lấy nhà cung cấp của po
      entity.poId = data.poId
      entity.contractId = data.contractId
      entity.description = data.description
      entity.referencesInvoice = data.referencesInvoice
      entity.bizziCompanyId = data.bizziCompanyId
      entity.fileAttach = data.fileAttach
      entity.billLookupCode = data.billLookupCode
      entity.billLookupId = data.billLookupId
      await repo.save(entity)
    })

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}

    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.poId) whereCon.poId = data.where.poId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.contractId) whereCon.contractId = data.where.contractId
    if (data.where.status) whereCon.status = data.where.status
    if (data.where.paymentStatus) whereCon.paymentStatus = data.where.paymentStatus
    whereCon.supplierId = user.supplierId
    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { po: true, contract: true },
      order: { createdAt: 'DESC' },
    })
    if (res[0].length == 0) return [[], 0]
    for (const item of res[0]) {
      item.poName = item?.__po__?.name
      item.poCode = item?.__po__?.code
      item.contractName = item?.__contract__?.name
      item.contractCode = item?.__contract__?.code
      item.statusName = enumData.BillStatus[item.status]?.name
      item.statusColor = enumData.BillStatus[item.status]?.color
      item.paymentStatusName = enumData.BillPaymentStatus[item.paymentStatus]?.name
      item.paymentStatusColor = enumData.BillPaymentStatus[item.paymentStatus]?.color
      delete item._po__
      delete item.__contract__
      delete item.__currency__
    }
    return res
  }

  public async updateIsDelete(data: { id: string }, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async findDetail(user: UserDto, data: { id: string }) {
    const res: any = await this.repo.findOne({
      where: { id: data.id },
      relations: {
        po: {
          products: true,
        },
        billLookup: true,
        contract: true,
        supplier: true,
      },
    })
    res.billLookupLink = res.__billLookup__.link
    res.billLookupName = res.__billLookup__.name

    res.supplierInfo = res?.__supplier__
    res.poName = res?.__po__?.title
    res.poCode = res?.__po__?.code
    res.contractName = res?.__contract__?.name
    res.contractCode = res?.__contract__?.code
    if (res?.__po__ && res?.__po__?.__products__ && res.referencesInvoice === enumData.referencesInvoice.P.code) {
      res.lstProduct = res?.__po__?.__products__ || []
    }

    delete res.__po__
    delete res.__supplier__
    delete res.__billLookup__
    delete res.__contract__

    return res
  }

  public async updateCancel(user: UserDto, data: { id: string }) {
    await this.repo.manager.transaction(async (transac) => {
      const repo = transac.getRepository(BillEntity)
      const entity = await repo.findOne({ where: { id: data.id } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
      if (entity.status !== enumData.BillStatus.NEW.code) {
        throw new Error(`Trạng thái không phù hợp. Vui lòng kiểm tra lại`)
      }
      entity.status = enumData.BillStatus.CANCEL.code
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
      await repo.save(entity)
    })
    return { message: UPDATE_SUCCESS }
  }

  public async updateSend(user: UserDto, data: { id: string }) {
    await this.repo.manager.transaction(async (transac) => {
      const repo = transac.getRepository(BillEntity)
      const entity = await repo.findOne({ where: { id: data.id } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
      if (entity.status !== enumData.BillStatus.NEW.code) {
        throw new Error(`Trạng thái không phù hợp. Vui lòng kiểm tra lại`)
      }
      entity.status = enumData.BillStatus.PROCESS.code
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
      await repo.save(entity)
    })
    return { message: UPDATE_SUCCESS }
  }

  public async updateConfirmed(user: UserDto, data: { id: string }) {
    await this.repo.manager.transaction(async (transac) => {
      const repo = transac.getRepository(BillEntity)
      const entity = await repo.findOne({ where: { id: data.id } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
      if (entity.status !== enumData.BillStatus.WAIT_CONFIRM.code) {
        throw new Error(`Trạng thái không phù hợp. Vui lòng kiểm tra lại`)
      }
      entity.status = enumData.BillStatus.CONFIRMED.code
      // entity.paymentStatus = enumData.BillPaymentStatus.PAID.code
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
    })
    return { message: UPDATE_SUCCESS }
  }

  public async findPOId(user: UserDto, data: { listBillId: string[] }) {
    const listBill = await this.repo.find({
      where: {
        id: In(data.listBillId),
        isDeleted: false,
      },
      select: { poId: true },
    })
    const poIds = listBill.map((item) => item.poId)
    return [...new Set(poIds)]
  }

  public async findContractId(user: UserDto, data: { listBillId: string[] }) {
    const listBill = await this.repo.find({
      where: {
        id: In(data.listBillId),
        isDeleted: false,
      },
      select: { contractId: true },
    })
    const contractIds = listBill.map((item) => item.contractId)
    return [...new Set(contractIds)]
  }

  public async findBillByStatus(user: UserDto, data: { status?: string }) {
    const whereCon: any = [{ paymentStatus: enumData.BillPaymentStatus.NEW.code, isDeleted: false, status: enumData.BillStatus.CONFIRMED.code }]

    const res: any = await this.repo.find({
      where: whereCon,
      order: { createdAt: 'DESC' },
      select: { code: true, id: true },
    })

    return res
  }

  public async loadCompany(user: UserDto) {
    const config = {
      accept: 'application/json',
      'X-API-KEY': process.env.X_API_KEY,
    }

    const url = process.env.URL_API_GET_COMPANY
    let invoice: any

    try {
      invoice = await this.loadApiData(url, config)
    } catch (error) {
      throw new Error(`Lỗi từ hệ thống Bizzi: " ${error.response.data.message}`)
    }

    return { listCompany: invoice.data }
  }

  public async findBillSupplier(user: UserDto, data: { status?: string }) {
    const whereCon: any = [
      {
        supplierId: user.supplierId,
        // paymentStatus: enumData.BillPaymentStatus.NEW.code,
        isDeleted: false,
        // status: enumData.BillStatus.CONFIRMED.code,
      },
    ]

    const res: any = await this.repo.find({
      where: whereCon,
      order: { createdAt: 'DESC' },
      select: { code: true, id: true },
    })

    return res
  }
}
