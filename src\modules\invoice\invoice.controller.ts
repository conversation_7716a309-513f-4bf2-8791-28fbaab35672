import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { enumProject } from '../../constants'
import { UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { InvoiceCreate } from './dto'
import { InvoiceService } from './invoice.service'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

/** <PERSON><PERSON><PERSON><PERSON> lý <PERSON> toán */
@ApiBearerAuth()
@ApiTags('Invoice')
@Controller('invoice')
export class InvoiceController {
  constructor(private readonly service: InvoiceService) {}

  @ApiOperation({ summary: 'Thanh toán' })
  @Roles(enumProject.Features.PAYMENT_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@Body() data: InvoiceCreate, @CurrentUser() user: UserDto) {
    return await this.service.createData(data, user)
  }
}
