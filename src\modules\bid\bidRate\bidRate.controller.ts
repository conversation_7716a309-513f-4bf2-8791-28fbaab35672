import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { enumProject } from '../../../constants'
import { PaginationDto, UserDto } from '../../../dto'
import { CurrentUser, Roles } from '../../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../../common/guards'
import { BidRateService } from './bidRate.service'

/** <PERSON><PERSON>h giá thầu */
@ApiBearerAuth()
@ApiTags('Bid')
@Controller('bidRates')
export class BidRateController {
  constructor(private readonly service: BidRateService) {}

  @ApiOperation({ summary: 'Danh sách gói thầu module đánh giá thầu phân trang' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  //#region bidTechRate

  @ApiOperation({ summary: 'Lấy ds Doanh nghiệp tham gia thầu và tính điểm' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('load_tech_rate/:id')
  public async loadTechRate(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.loadTechRate(user, bidId)
  }
  @ApiOperation({ summary: 'Lấy danh sách bidTech và điểm cao nhất tương ứng' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_best_tech_value')
  public async loadBestTechValue(@CurrentUser() user: UserDto, @Body() data: { bidId: string; bidSupplierId: string }) {
    return await this.service.loadBestTechValue(user, data)
  }
  @ApiOperation({ summary: 'Tạo đánh giá kỹ thuật' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_tech_rate')
  public async createTechRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[] }) {
    return await this.service.createTechRate(user, data)
  }
  @ApiOperation({ summary: 'Duyệt đánh giá kỹ thuật' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('approve_tech_rate')
  public async approveTechRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[] }) {
    return await this.service.approveTechRate(user, data)
  }
  @ApiOperation({ summary: 'Từ chối đánh giá kỹ thuật' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('reject_tech_rate')
  public async rejectTechRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[] }) {
    return await this.service.rejectTechRate(user, data)
  }

  //#endregion

  //#region bidTradeRate

  @ApiOperation({ summary: 'Lấy ds Doanh nghiệp tham gia thầu và tính điểm' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('load_trade_rate/:id')
  public async loadTradeRate(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.loadTradeRate(user, bidId)
  }
  @ApiOperation({ summary: 'Lấy danh sách sách bidTrade và điểm cao nhất tương ứng' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_best_trade_value')
  public async loadBestTradeValue(@CurrentUser() user: UserDto, @Body() data: { bidId: string; bidSupplierId: string }) {
    return await this.service.loadBestTradeValue(user, data)
  }
  @ApiOperation({ summary: 'Tạo đánh giá thương mại' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_trade_rate')
  public async createTradeRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[] }) {
    return await this.service.createTradeRate(user, data)
  }
  @ApiOperation({ summary: 'Duyệt đánh giá thương mại' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('approve_trade_rate')
  public async approveTradeRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[] }) {
    return await this.service.approveTradeRate(user, data)
  }
  @ApiOperation({ summary: 'Từ chối đánh giá thương mại' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('reject_trade_rate')
  public async rejectTradeRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[] }) {
    return await this.service.rejectTradeRate(user, data)
  }

  //#endregion

  //#region bidPriceRate

  @ApiOperation({ summary: 'Lấy ds Doanh nghiệp tham gia thầu và tính điểm' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('load_price_rate/:id')
  public async loadPriceRate(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.loadPriceRate(user, bidId)
  }
  @ApiOperation({ summary: 'Lấy danh sách sách bidPrice và điểm cao nhất tương ứng' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_best_price_value')
  public async loadBestPriceValue(@CurrentUser() user: UserDto, @Body() data: { bidId: string; bidSupplierId: string }) {
    return await this.service.loadBestPriceValue(user, data)
  }
  @ApiOperation({ summary: 'Tạo đánh giá giá' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_price_rate')
  public async createPriceRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[] }) {
    return await this.service.createPriceRate(user, data)
  }

  //#endregion

  //#region Phân tích giá

  @ApiOperation({ summary: 'Xếp hạng theo giá thấp nhất (Giá theo từng hạng mục)' })
  @Roles(enumProject.Features.BID_006.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_rank_by_min_price')
  public async loadRankByMinPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string; lstId: string[] }) {
    return await this.service.loadRankByMinPrice(user, data)
  }

  @ApiOperation({ summary: 'Xếp hạng theo tổng giá dạng 1 (Giá theo từng Doanh nghiệp)' })
  @Roles(enumProject.Features.BID_007.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_rank_by_sum_price')
  public async loadRankBySumPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string; lstId: string[] }) {
    return await this.service.loadRankBySumPrice(user, data)
  }

  @ApiOperation({ summary: 'Xếp hạng theo tổng giá dạng 2 (Giá theo từng Doanh nghiệp) (Mỗi dòng 1 Doanh nghiệp)' })
  @Roles(enumProject.Features.BID_007.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_supplier_rank_by_sum_price')
  public async loadSupplierRankBySumPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string; lstId: string[] }) {
    return await this.service.loadSupplierRankBySumPrice(user, data)
  }

  //#endregion

  @ApiOperation({ summary: 'Báo cáo kết quả đánh giá' })
  @Roles(enumProject.Features.BID_011.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_data_report_rate_bid/:bidid')
  public async getDataReportRateBid(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.service.getDataReportRateBid(user, bidId)
  }

  @ApiOperation({ summary: 'In kết quả đánh giá' })
  @Roles(enumProject.Features.BID_011.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_data_print_rate_bid/:bidid')
  public async getDataPrintRateBid(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.service.getDataPrintRateBid(user, bidId)
  }

  //#region Phê duyệt kết thúc thầu

  @ApiOperation({ summary: 'Gửi yêu cầu phê duyệt kết thúc thầu' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('send_request_finish_bid')
  public async sendRequestFinishBid(@CurrentUser() user: UserDto, @Body() data: { id: string; fileScan: string; noteFinishBidMPO: string }) {
    return await this.service.sendRequestFinishBid(user, data)
  }

  @ApiOperation({ summary: 'Phê duyệt kết thúc thầu' })
  @Roles(enumProject.Features.BID_005.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('approve_finish_bid')
  public async approveFinishBid(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.approveFinishBid(user, data)
  }

  //#endregion

  //#region Đàm phán/ Đấu giá

  @ApiOperation({ summary: 'DS item khi Đàm phán/ Đấu giá' })
  @Roles(enumProject.Features.BID_009.code, enumProject.Features.BID_010.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('item_pagination')
  public async itemPagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.itemPagination(user, data)
  }

  //#endregion

  //#region Truy vấn thông tin gói thầu

  @ApiOperation({ summary: 'Danh sách gói thầu đã hoàn tất' })
  @Roles(enumProject.Features.BID_012.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('result_pagination')
  public async resultPagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.resultPagination(user, data)
  }
  //#endregion
}
