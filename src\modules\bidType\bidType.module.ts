import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { BidTypeService } from './bidType.service'
import { BidTypeController } from './bidType.controller'
import { BidTypeRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BidTypeRepository])],
  controllers: [BidTypeController],
  providers: [BidTypeService],
})
export class BidTypeModule {}
