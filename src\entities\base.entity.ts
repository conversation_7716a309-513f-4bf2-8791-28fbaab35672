import { BaseEntity as Base, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm'

export abstract class BaseEntity extends Base {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @CreateDateColumn()
  createdAt: Date
  /** id của userEntity */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  createdBy: string

  @UpdateDateColumn({ nullable: true })
  updatedAt: Date
  /** id của userEntity */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  updatedBy: string

  @Column({
    name: 'isDeleted',
    nullable: false,
    default: false,
  })
  isDeleted: boolean

  /** id của company ở APE_Auth */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  companyId?: string
}
