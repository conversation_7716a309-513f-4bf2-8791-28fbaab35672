import { MigrationInterface, QueryRunner } from "typeorm";

export class createPrChildToPr1718682643135 implements MigrationInterface {
    name = 'createPrChildToPr1718682643135'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`pr_child_to_pr\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(36) NOT NULL, \`name\` varchar(250) NOT NULL, \`description\` text NULL, \`totalQuantity\` float NULL DEFAULT '0', \`prChildId\` varchar(36) NOT NULL, \`prId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`pr_child_to_pr\` ADD CONSTRAINT \`FK_b4472b399e2111eff898dfba80a\` FOREIGN KEY (\`prChildId\`) REFERENCES \`pr_child\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`pr_child_to_pr\` ADD CONSTRAINT \`FK_e753b2ddc7703ee999239a2be9f\` FOREIGN KEY (\`prId\`) REFERENCES \`pr\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`pr_child_to_pr\` DROP FOREIGN KEY \`FK_e753b2ddc7703ee999239a2be9f\``);
        await queryRunner.query(`ALTER TABLE \`pr_child_to_pr\` DROP FOREIGN KEY \`FK_b4472b399e2111eff898dfba80a\``);
        await queryRunner.query(`DROP TABLE \`pr_child_to_pr\``);
    }

}
