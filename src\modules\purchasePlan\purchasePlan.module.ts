import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { PurchasePlanService } from './purchasePlan.service'
import { PurchasePlanController } from './purchasePlan.controller'
import { PurchasePlanRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([PurchasePlanRepository])],
  controllers: [PurchasePlanController],
  providers: [PurchasePlanService],
  exports: [PurchasePlanService],
})
export class PurchasePlanModule {}
