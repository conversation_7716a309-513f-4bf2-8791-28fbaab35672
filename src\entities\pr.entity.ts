import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON>ne, <PERSON>in<PERSON><PERSON><PERSON>n, OneToMany, OneToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BranchEntity } from './branch.entity'
import { EmployeeEntity } from './employee.entity'
import { ObjectEntity } from './object.entity'
import { PrApproveEntity } from './prApprove.entity'
import { PrApproverEntity } from './prApprover.entity'
import { PrHistoryEntity } from './prHistory.entity'
import { PrItemEntity } from './prItem.entity'
import { POEntity } from './po.entity'
import { BidEntity } from './bid.entity'

/** YCMH */
@Entity('pr')
export class PrEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  branchId: string
  @ManyToOne(() => BranchEntity, (p) => p.prs)
  @JoinColumn({ name: 'branchId', referencedColumnName: 'id' })
  branch: Promise<BranchEntity>

  @Column({
    type: 'varchar',
    length: 1000,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  code: string

  /** NV để xuất */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  empProposerId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.proposerPRs)
  @JoinColumn({ name: 'empProposerId', referencedColumnName: 'id' })
  empProposer: Promise<EmployeeEntity>

  /** NV phụ trách */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  empInChargeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.inChargePRs)
  @JoinColumn({ name: 'empInChargeId', referencedColumnName: 'id' })
  empInCharge: Promise<EmployeeEntity>

  /** Ngày cần giao */
  @Column({
    nullable: false,
  })
  deliveryDate: Date

  /** Địa điểm giao hàng  */
  @Column({
    type: 'varchar',
    length: 1000,
    nullable: false,
  })
  deliveryAddress: string

  /** số lượng của pr */
  @Column({
    nullable: true,
  })
  quantity: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /** Type để phân biệt các loại PR */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  prType: string

  /** đối tượng */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  objectId: string
  @ManyToOne(() => ObjectEntity, (p) => p.prs)
  @JoinColumn({ name: 'objectId', referencedColumnName: 'id' })
  object: Promise<ObjectEntity>

  /** Ghi chú lý do từ chối */
  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  reason: string

  /** Cho phép tạo gói thầu, cập nhật false, nếu các PrItem đã tạo thầu hết */
  @Column({
    nullable: false,
    default: true,
  })
  isAllowBid: boolean

  /** Ghi chú yêu cầu kiểm tra lại */
  @Column({
    type: 'text',
    nullable: true,
  })
  noteReCheck: string

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  /** Đường dẫn file */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileUrl: string

  /** Đường dẫn file */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  imgUrl: string

  @OneToMany(() => PrApproveEntity, (p) => p.pr)
  prApproves: Promise<PrApproveEntity[]>

  @OneToMany(() => POEntity, (p) => p.pr)
  pos: Promise<POEntity[]>

  @OneToMany(() => PrItemEntity, (p) => p.pr)
  prItems: Promise<PrItemEntity[]>

  @OneToMany(() => PrApproverEntity, (p) => p.pr)
  prApprovers: Promise<PrApproverEntity[]>

  @OneToMany(() => PrHistoryEntity, (p) => p.pr)
  histories: Promise<PrHistoryEntity[]>

  /** Các gói thầu tổng và gói thầu chi tiết của Pr */
  @OneToMany(() => BidEntity, (p) => p.pr)
  bids: Promise<BidEntity[]>
}
