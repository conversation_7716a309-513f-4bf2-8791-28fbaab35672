import { MigrationInterface, QueryRunner } from 'typeorm'

export class addClm1724226414343 implements MigrationInterface {
  name = 'addClm1724226414343'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`pr\` ADD \`description\` text NULL`)
    await queryRunner.query(`ALTER TABLE \`pr\` ADD \`fileUrl\` varchar(250) NULL`)
    await queryRunner.query(`ALTER TABLE \`pr\` ADD \`imgUrl\` varchar(250) NULL`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`pr\` DROP COLUMN \`imgUrl\``)
    await queryRunner.query(`ALTER TABLE \`pr\` DROP COLUMN \`fileUrl\``)
    await queryRunner.query(`ALTER TABLE \`pr\` DROP COLUMN \`description\``)
  }
}
