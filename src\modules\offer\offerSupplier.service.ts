import { BadRequestException, Injectable, MethodNotAllowedException, NotAcceptableException, NotFoundException } from '@nestjs/common'
import {
  OfferCustomPriceRepository,
  OfferPriceColRepository,
  OfferPriceRepository,
  OfferPrItemRepository,
  OfferRepository,
  OfferSupplierRepository,
  OfferSupplierServiceRepository,
  ServiceCustomPriceRepository,
  ServicePriceRepository,
  SupplierRepository,
} from '../../repositories'
import { UserDto } from '../../dto'
import { enumData, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION, UPDATE_SUCCESS } from '../../constants'
import { In, Like } from 'typeorm'
import { coreHelper } from '../../helpers'
import { v4 as uuidv4 } from 'uuid'
import { OfferEntity, OfferSupplierEntity } from '../../entities'
import { OfferSupplierServiceEntity } from '../../entities/offerSupplierService.entity'

@Injectable()
export class OfferSupplerService {
  constructor(
    private readonly repo: OfferRepository,
    private readonly servicePriceRepo: ServicePriceRepository,
    private readonly serviceCustomPriceRepo: ServiceCustomPriceRepository,
    private readonly bidPriceRepo: OfferPriceRepository,
    private readonly bidPriceColRepo: OfferPriceColRepository,
    private readonly bidCustomPriceRepo: OfferCustomPriceRepository,
    private readonly bidPrItemRepository: OfferPrItemRepository,
    private readonly bidSupplierRepo: OfferSupplierRepository,
    private readonly supplierRepo: SupplierRepository,
    private readonly offerSupplierRepository: OfferSupplierRepository,
    private readonly offerSupplierService: OfferSupplierServiceRepository,
  ) {}

  /** Danh sách nhà cung cấp mời thầu */
  async loadSupplierInvite(
    user: UserDto,
    data: { bidId: string; supplierName?: string; lstStatus?: string[]; typeGetData: number; isMobile?: boolean },
  ) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new BadRequestException(`Vui lòng chọn Chào giá trước`)
    const bid: any = await this.repo.findOne({
      where: { id: data.bidId, isDeleted: false },
      relations: { offerService: true },
      select: {
        id: true,
        name: true,
        status: true,
        statusPrice: true,
        statusTrade: true,
        statusChooseSupplier: true,
        offerService: { id: true, serviceId: true },
      },
    })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const lstServiceId = bid.__offerService__.map((c) => c.serviceId).filter((value, index, self) => self.indexOf(value) === index)
    if (lstServiceId.length == 0) throw new Error('Chào giá chưa thiết lập danh sách Item')

    let whereCon: any = { isDeleted: false }
    // whereCon.supplierServices = { serviceId: In(lstServiceId), isDeleted: false }
    if (data.lstStatus?.length > 0) whereCon.supplierServices.statusExpertise = In(data.lstStatus)

    if (data.supplierName) {
      whereCon = [
        { ...whereCon, code: Like(`%${data.supplierName}%`) },
        { ...whereCon, name: Like(`%${data.supplierName}%`) },
      ]
    }

    let lstData: any[] = await this.supplierRepo.find({
      where: whereCon,
      order: { code: 'ASC' },
    })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.SupplierStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }

    let res: any[] = []
    for (const item of lstData) {
      const temp: any = {}
      temp.supplierId = item.id
      temp.supplierCode = item.code
      temp.supplierName = item.name
      temp.isChosen = false
      temp.statusName = dicStatus[item.status]
      temp.createdAt = item.createdAt
      res.push(temp)
    }

    if (bid.statusChooseSupplier != enumData.BidChooseSupplierStatus.ChuaChon.code) {
      const lstBidSupplier = await this.bidSupplierRepo.find({
        where: { offerId: data.bidId },
        select: { id: true, supplierId: true },
      })
      const lstSupplierInvitedID = lstBidSupplier.map((c) => c.supplierId)
      for (const item of res) item.isChosen = lstSupplierInvitedID.includes(item.supplierId)
    }

    // nếu 'Chọn cả doanh nghiệp ngoài LVMH' (không xét isChosen vì có isChosen thì đã gen data supplierService & bidSupplier)
    if (data.typeGetData == 2) {
      let lstSupplier = await this.supplierRepo.find({
        where: { isDeleted: false },
        select: { id: true, code: true, name: true },
      })

      if (lstSupplier.length > 0) {
        const lstSupplierId = lstData.map((c) => c.supplierId)
        if (lstSupplierId.length > 0) {
          lstSupplier = lstSupplier.filter((c) => lstSupplierId.indexOf(c.id) == -1)
        }
        for (const supplier of lstSupplier) {
          const temp: any = {}
          temp.supplierId = supplier.id
          temp.supplierCode = supplier.code
          temp.supplierName = supplier.name
          temp.isChosen = false
          temp.statusName = enumData.SupplierServiceStatus.ChuaDangKy.code
          temp.createdAt = supplier.createdAt
          res.push(temp)
        }
      }
    }

    const enumStatus = enumData.BidChooseSupplierStatus
    const status = bid.statusChooseSupplier
    const lstStatus = [enumStatus.DangChon.code, enumStatus.ChuaChon.code, enumStatus.TuChoi.code]
    const isShowChooseSupplier = lstStatus.includes(status)
    const isShowSendMPOLeaderCheck =
      status === enumStatus.DaChon.code &&
      bid.statusPrice === enumData.BidPriceStatus.DaTao.code &&
      bid.statusTech === enumData.BidTechStatus.DaTao.code &&
      bid.statusTrade === enumData.BidTradeStatus.DaTao.code
    const isShowAcceptChooseSupplier = status === enumStatus.GuiDuyet.code

    if (status === enumStatus.DaChon.code || status === enumStatus.DaDuyet.code || status === enumStatus.GuiDuyet.code) {
      res = res.filter((c) => c.isChosen)
    }

    if (data.isMobile) {
      return res
        .filter((item) => item.isChosen)
        .map((item) => {
          return {
            supplierId: item.supplierId ?? '',
            supplierCode: item.supplierCode ?? '',
            supplierName: item.supplierName ?? '',
            isChosen: item.isChosen ?? false,
            statusName: item.statusName ?? '',
            createdAt: item.createAt ?? '',
          }
        })
    }

    return { isShowChooseSupplier, isShowSendMPOLeaderCheck, isShowAcceptChooseSupplier, canAcceptSup: true, bidName: bid.name, lstData: res }
  }

  /** Mời thầu */
  async bidChooseSupplier(user: UserDto, data: { bidId: string; lstData: any[] }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bid: any = await this.repo.findOne({
      where: { id: data.bidId },
      relations: { offerService: true },
    })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    //tạo mới nhà cung câp( 1 - n)]
    data.lstData = data.lstData.filter((item) => item.isChosen)
    if (data.lstData && data.lstData.length > 0) {
      // Kiểm tra xem nhà cung cấp có tồn tại không
      const lstSupId = data.lstData.map((e) => e.supplierId)
      const lstSup = await this.supplierRepo.find({ where: { id: In(lstSupId), isDeleted: false } })

      // Tạo từ điển để dễ dàng truy cập
      const dicSup = Object.fromEntries(lstSup.map((item) => [item.id, item]))

      // Kiểm tra số lượng nhà cung cấp
      if (lstSup.length !== data.lstData.length) {
        throw new Error('Có NCC không tồn tại hoặc đã bị ngưng hoạt động!')
      }

      // Lưu thông tin nhà cung cấp đã chọn
      const offerSupplierPromises = data.lstData
        .filter((item) => item.isChosen)
        .map(async (item) => {
          const supplier = dicSup[item.supplierId]
          const offerPrice = new OfferSupplierEntity()
          offerPrice.id = uuidv4()
          offerPrice.offerId = bid.id
          offerPrice.supplierId = supplier.id
          offerPrice.supplierCode = supplier.code
          offerPrice.supplierName = supplier.name
          await this.offerSupplierRepository.save(offerPrice)
          // Lưu dịch vụ của nhà cung cấp
          const offerServicePromises = bid.__offerService__.map(async (supService) => {
            const offerService = new OfferSupplierServiceEntity()
            offerService.offerServiceId = supService.id
            offerService.offerSupplierId = offerPrice.id // Đảm bảo sử dụng id của offerPrice
            return await this.offerSupplierService.save(offerService)
          })

          // Chờ tất cả các dịch vụ được lưu
          await Promise.all(offerServicePromises)
        })

      // Chờ tất cả nhà cung cấp đã chọn được lưu
      await Promise.all(offerSupplierPromises)
    }

    await this.repo.update({ id: bid.id }, { statusChooseSupplier: enumData.OfferStatus.DaDuyet.code })
    /* chek nếu như cả 2 được cấu hình thì chuyển trạng thái của offer và công khai */
    if (bid.statusPrice === enumData.BidPriceStatus.DaDuyet.code && bid.statusTrade === enumData.BidTradeStatus.DaDuyet.code) {
      await this.repo.update({ id: bid.id }, { status: enumData.OfferStatus.DaCongKhai.code })
      // bid.status = enumData.OfferStatus.DaCongKhai.code
    }
    return { message: UPDATE_SUCCESS }
  }

  //#endregion

  //#region Accept all & send email invite supplier

  /** Kiểm tra quyền duyệt tất cả */
  async checkPermissionMpoAcceptAll(user: UserDto, bidId: string) {
    let result = false
    let message = 'Chào giá không còn tồn tại'
    const bid = await this.repo.findOne({
      where: [{ id: bidId }],
    })
    if (bid) {
      if (bid.status === enumData.BidStatus.DangDuyetGoiThau.code) {
        result = true
        if (!result) {
          message = 'Bạn không có quyền xét duyệt bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu.'
        }
      } else {
        result = false
        message =
          'Chào giá đã được xét duyệt bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu hoặc chưa khởi tạo xong yêu cầu kỹ thuật, bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Từ chối tất cả */
}
