import { Injectable } from '@nestjs/common'
import * as AWS from 'aws-sdk'
import { ConfigService } from '@nestjs/config'
import { customAlphabet } from 'nanoid'
import { coreHelper } from '../../helpers'
const nanoid = customAlphabet('0123456789abcdefghijklmnopqrstuvwxyz', 5)
import * as moment from 'moment'

@Injectable()
export class UploadFileService {
  AWS_S3_BUCKET_NAME: string
  s3: AWS.S3
  constructor(private readonly configService: ConfigService) {
    this.AWS_S3_BUCKET_NAME = this.configService.get<string>('AWS_S3_BUCKET_NAME') || ''
    const ACCESS_KEY_ID = this.configService.get<string>('AWS_S3_ACCESS_KEY_ID')
    const SECRET_ACCESS_KEY = this.configService.get<string>('AWS_S3_SECRET_ACCESS_KEY')

    this.s3 = new AWS.S3({
      accessKeyId: ACCESS_KEY_ID,
      secretAccessKey: SECRET_ACCESS_KEY,
    })
  }

  async uploadSingle(file: any) {
    let fileName = file.originalname
    let temp: string[] = fileName ? fileName.split('.') : []
    let ext = temp.length > 1 ? `.${temp[temp.length - 1]}` : ''
    if (ext) {
      const current = coreHelper.newDateTZ()
      const key = `-${moment(current).format('YYMMDD')}-${nanoid()}${ext}`
      fileName = fileName.replace(ext, key)
    }

    const params: AWS.S3.PutObjectRequest = {
      Bucket: this.AWS_S3_BUCKET_NAME,
      Key: `bid/${fileName}`, // File name you want to save as in S3
      Body: file.buffer,
      ACL: 'public-read',
    }
    return new Promise<string[]>((resolve, reject) => {
      this.s3.upload(params, (err: any, data: AWS.S3.ManagedUpload.SendData) => {
        if (err) {
          reject(err)
        } else {
          resolve([data.Location])
        }
      })
    })
  }
}
