import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { PurchasePlanService } from './purchasePlan.service'
import { PurchasePlanCreateDto } from './dto/purchasePlanCreate.dto'
import { PurchasePlanUpdateDto } from './dto/purchasePlanUpdate.dto'
import { CurrentUser, Roles } from '../common/decorators'
import { PaginationDto, UserDto } from '../../dto'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { enumProject } from '../../constants'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

/** Lập kế hoạch mua hàng */
@ApiBearerAuth()
@ApiTags('PurchasePlan')
@Controller('purchasePlan')
export class PurchasePlanController {
  constructor(private readonly service: PurchasePlanService) {}

  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách kế hoạch mua hàng' })
  @Roles(enumProject.Features.PR_002.code)
  // @Roles(enumProject.Features.ASN_001.code, enumProject.Features.PR_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: {}) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Lấy chi tiết kế hoạch mua hàng khi chỉnh sửa' })
  @Roles(enumProject.Features.PURCHASE_PLAN_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_detail_edit')
  public async findDetailEdit(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetailEdit(user, data)
  }

  @ApiOperation({ summary: 'Lấy chi tiết kế hoạch mua hàng' })
  @Roles(enumProject.Features.PURCHASE_PLAN_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetail(user, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách kế hoạch mua hàng phân trang' })
  @Roles(enumProject.Features.PURCHASE_PLAN_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo excel kế hoạch mua hàng' })
  @Roles(enumProject.Features.PURCHASE_PLAN_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data_list')
  public async createDataList(@CurrentUser() user: UserDto, @Body() data: PurchasePlanCreateDto[]) {
    return await this.service.createDataList(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới kế hoạch mua hàng' })
  @Roles(enumProject.Features.PURCHASE_PLAN_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: PurchasePlanCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật kế hoạch mua hàng' })
  @Roles(enumProject.Features.PURCHASE_PLAN_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: PurchasePlanUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động kế hoạch mua hàng' })
  @Roles(enumProject.Features.PURCHASE_PLAN_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    const warehouse = await this.service.updateIsDelete(user, data)
    return warehouse
  }
}
