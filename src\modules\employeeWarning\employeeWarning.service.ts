import { Injectable } from '@nestjs/common'
import { UserDto } from '../../dto'
import { EmployeeWarningRepository } from '../../repositories'

@Injectable()
export class EmployeeWarningService {
  constructor(private readonly repo: EmployeeWarningRepository) {}

  /** Lấy ds thông báo */
  public async loadEmployeeWarning(user: UserDto, data: { take: number }) {
    if (!user.employeeId) return { lstWarning: [], numWarningNew: 0 }

    const lstWarning = []
    const res1 = await this.repo.findAndCount({
      where: { employeeId: user.employeeId, companyId: user.companyId, isNew: true },
      order: { createdAt: 'DESC' },
      skip: 0,
      take: data.take,
    })
    lstWarning.push(...res1[0])

    // Lấy thêm thông báo đã đọc cho đủ số lượng
    if (res1[1] < data.take) {
      const res2 = await this.repo.findAndCount({
        where: { employeeId: user.employeeId, companyId: user.companyId, isNew: false },
        order: { createdAt: 'DESC' },
        skip: 0,
        take: data.take - res1[1],
      })
      lstWarning.push(...res2[0])
    }

    return {
      lstWarning, //ds thông báo
      numWarningNew: res1[1], //số thông báo mới
    }
  }

  /** Đọc thông báo */
  public async readEmployeeWarning(user: UserDto, data: { id: string }) {
    await this.repo.update(data.id, { isNew: false, updatedBy: user.id })
  }
}
