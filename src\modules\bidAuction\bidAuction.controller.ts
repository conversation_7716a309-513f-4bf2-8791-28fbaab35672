import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { enumProject } from '../../constants'
import { UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { BidAuctionService } from './bidAuction.service'
import { BidAuctionCreateDto } from './dto'

/** Đấu giá */
@ApiBearerAuth()
@ApiTags('Bid')
@Controller('bid_auction')
export class BidAuctionController {
  constructor(private readonly service: BidAuctionService) {}

  @ApiOperation({ summary: '<PERSON><PERSON>y danh sách hạng mục chào giá' })
  @Roles(enumProject.Features.BID_009.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_price/:bidId')
  public async getPrice(@CurrentUser() user: UserDto, @Param('bidId') bidId: string) {
    return await this.service.getPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Lấy ds NCC để mời tham gia đấu giá' })
  @Roles(enumProject.Features.BID_009.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_supplier_data')
  public async loadSupplierData(@CurrentUser() user: UserDto, @Body() data: { bidId: string; statusFile: string[]; name?: string }) {
    return await this.service.loadSupplierData(user, data)
  }

  @ApiOperation({ summary: 'Tạo đấu giá Item' })
  @Roles(enumProject.Features.BID_009.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('save_bid_auction')
  public async saveBidAuction(@CurrentUser() user: UserDto, @Body() data: BidAuctionCreateDto) {
    return await this.service.saveBidAuction(user, data)
  }
}
