import { Injectable, ConflictException, NotFoundException, NotAcceptableException, BadRequestException } from '@nestjs/common'
import {
  ERROR_PERCENT_SUM,
  ERROR_NOT_FOUND_DATA,
  ERROR_INVALID_FOMULAR,
  enumData,
  ERROR_YOU_DO_NOT_HAVE_PERMISSION,
  UPDATE_SUCCESS,
  CREATE_SUCCESS,
  UPDATE_ACTIVE_SUCCESS,
  DELETE_SUCCESS,
  DELETE_FAIL,
} from '../../constants'
import {
  ServiceCapacityCreateDto,
  ServiceCapacityUpdateDto,
  ServiceCapacityListDetailCreateDto,
  ServiceCapacityListDetailUpdateDto,
  ServiceTechCreateDto,
  ServiceTechUpdateDto,
  ServiceTechListDetailCreateDto,
  ServiceTechListDetailUpdateDto,
  ServiceTradeCreateDto,
  ServiceTradeUpdateDto,
  ServiceTradeListDetailCreateDto,
  ServiceTradeListDetailUpdateDto,
  ServicePriceCreateDto,
  ServicePriceUpdateDto,
  ServicePriceColCreateDto,
  ServicePriceColUpdateDto,
  ServicePriceListDetailUpdateDto,
  ServicePriceListDetailCreateDto,
  ServiceCustomPriceUpdateDto,
  ServiceCustomPriceCreateDto,
} from './dto'
import {
  ServiceCustomPriceRepository,
  ServiceRepository,
  ServiceCapacityRepository,
  ServicePriceColRepository,
  ServicePriceRepository,
  ServiceTechRepository,
  ServiceTradeRepository,
} from '../../repositories'
import { coreHelper } from '../../helpers'
import { PaginationDto, UserDto } from '../../dto'
import { Not, Equal, IsNull, Like } from 'typeorm'
import {
  BidPriceEntity,
  ServiceCapacityEntity,
  ServiceCapacityListDetailEntity,
  ServiceCustomPriceEntity,
  ServicePriceColEntity,
  ServicePriceColValueEntity,
  ServicePriceEntity,
  ServicePriceListDetailEntity,
  ServiceTechEntity,
  ServiceTechListDetailEntity,
  ServiceTradeEntity,
  ServiceTradeListDetailEntity,
  SupplierCapacityEntity,
} from '../../entities'

@Injectable()
export class ServiceSettingService {
  constructor(
    private readonly serviceRepo: ServiceRepository,

    private readonly capacityRepo: ServiceCapacityRepository,
    private readonly techRepo: ServiceTechRepository,
    private readonly tradeRepo: ServiceTradeRepository,
    private readonly priceRepo: ServicePriceRepository,
    private readonly priceColRepo: ServicePriceColRepository,
    private readonly customPriceRepo: ServiceCustomPriceRepository,
  ) {}

  //#region capacity

  public async capacity_getSumPercent(user: UserDto, data: { serviceId: string }) {
    const lstData = await this.capacityRepo.find({
      where: { parentId: IsNull(), serviceId: data.serviceId, companyId: user.companyId, isDeleted: false },
      select: { id: true, percent: true },
    })

    let sum = 0
    for (let item of lstData) {
      if (item.percent > 0) sum += item.percent
    }

    return sum
  }

  private async capacity_IsValidPercent(user: UserDto, data: any) {
    if (!data.percent || data.percent === 0) {
      return true
    }

    const lstDataSameParent = await this.capacityRepo.find({
      where: {
        parentId: data.parentId || IsNull(),
        serviceId: data.serviceId,
        id: Not(Equal(data.id || '')),
        companyId: user.companyId,
        isDeleted: false,
      },
    })
    let sum = 0
    for (const item of lstDataSameParent) {
      if (item.percent > 0) {
        sum += item.percent
      }
    }
    if (sum + data.percent > 100) {
      return false
    }
    return true
  }

  public async capacity_find(user: UserDto, data: { serviceId: string }) {
    return await this.capacityRepo.find({ where: { serviceId: data.serviceId, parentId: IsNull(), companyId: user.companyId, isDeleted: false } })
  }

  public async capacity_create_data(user: UserDto, data: ServiceCapacityCreateDto) {
    const isValidPercent = await this.capacity_IsValidPercent(user, data)
    if (!isValidPercent) throw new ConflictException(ERROR_PERCENT_SUM)

    const newEntity = this.capacityRepo.create(data)
    newEntity.companyId = user.companyId
    newEntity.createdBy = user.id
    await this.capacityRepo.save(newEntity)

    if (data.serviceId) {
      await this.serviceRepo.update(data.serviceId, {
        statusCapacity: enumData.StatusServiceCapacity.ChuaDuyet.code,
        updatedBy: user.id,
      })
    }

    return { message: CREATE_SUCCESS }
  }

  public async capacity_update_data(user: UserDto, data: ServiceCapacityUpdateDto) {
    const isValidPercent = await this.capacity_IsValidPercent(user, data)
    if (!isValidPercent) throw new ConflictException(ERROR_PERCENT_SUM)

    const entity = await this.capacityRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    entity.name = data.name
    if (data.sort !== null) {
      entity.sort = data.sort
    }
    entity.isRequired = data.isRequired

    const setType = new Set()
    setType.add(enumData.DataType.String.code)
    setType.add(enumData.DataType.Number.code)
    setType.add(enumData.DataType.File.code)
    entity.isChangeByYear = false
    if (setType.has(data.type)) entity.isChangeByYear = data.isChangeByYear

    entity.percent = data.percent
    entity.percentRule = data.percentRule
    entity.isCalUp = data.isCalUp
    entity.percentDownRule = data.percentDownRule
    entity.type = data.type
    entity.description = data.description
    entity.updatedBy = user.id
    await entity.save()

    if (data.serviceId) {
      await this.serviceRepo.update(data.serviceId, {
        statusCapacity: enumData.StatusServiceCapacity.ChuaDuyet.code,
        updatedBy: user.id,
      })
    }

    return { message: UPDATE_SUCCESS }
  }

  public async capacity_pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { parentId: IsNull(), companyId: user.companyId }
    if (data.where.serviceId) whereCon.serviceId = data.where.serviceId
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res: any[] = await this.capacityRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { serviceCapacityListDetails: true, childs: { serviceCapacityListDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })

    const config: any = {
      isShowSendApproveCapacity: false,
      isShowApproveCapacity: false,
    }

    if (data.where.serviceId) {
      const item: any = await this.serviceRepo.findOne({
        where: { id: data.where.serviceId },
        relations: { serviceAccess: true },
      })
      if (item) {
        item.__serviceAccess__ = item.__serviceAccess__ || []
        const lstEmployeeId = item.__serviceAccess__.map((c) => c.employeeId)
        if (item.statusCapacity == enumData.StatusServiceCapacity.ChuaDuyet.code && lstEmployeeId.length > 0) {
          config.isShowSendApproveCapacity = lstEmployeeId.includes(user.employeeId)
        }
        if (item.approveById == user.employeeId && item.statusCapacity == enumData.StatusServiceCapacity.GuiDuyet.code) {
          config.isShowApproveCapacity = true
        }
      }
    }

    return [...res, config]
  }

  public async capacity_update_active(user: UserDto, id: string) {
    const capacity = await this.capacityRepo.findOne({ where: { id, companyId: user.companyId } })
    if (!capacity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    if (capacity.isDeleted) {
      const isValidPercent = await this.capacity_IsValidPercent(user, capacity)
      if (!isValidPercent) throw new ConflictException(ERROR_PERCENT_SUM)
    }

    capacity.isDeleted = !capacity.isDeleted
    await this.capacityRepo.update(capacity.id, { isDeleted: capacity.isDeleted, updatedBy: user.id })
    if (!capacity.parentId) {
      let lstChild = await this.capacityRepo.find({ where: { parentId: capacity.id, companyId: user.companyId }, select: { id: true } })
      for (let child of lstChild) {
        await this.capacityRepo.update(child.id, { isDeleted: capacity.isDeleted, updatedBy: user.id })
      }
    }
    if (capacity.parentId && capacity.parentId !== '') {
      if (capacity.isDeleted === false) {
        let parent = await this.capacityRepo.findOne({ where: { id: capacity.parentId, companyId: user.companyId }, select: { id: true } })
        if (parent) {
          await this.capacityRepo.update(parent.id, { isDeleted: false, updatedBy: user.id })
        }
      }
    }

    if (capacity.serviceId) {
      await this.serviceRepo.update(capacity.serviceId, {
        statusCapacity: enumData.StatusServiceCapacity.ChuaDuyet.code,
        updatedBy: user.id,
      })
    }

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async capacity_deleteall(user: UserDto, serviceId: string) {
    if (!serviceId) throw new BadRequestException('Không xác định được Lĩnh vực mua hàng đã chọn!')
    const capacityListRepo = this.capacityRepo.manager.getRepository(ServiceCapacityListDetailEntity)
    const supplierCapacityRepo = this.capacityRepo.manager.getRepository(SupplierCapacityEntity)

    const lstServiceCapacityLv1 = await this.capacityRepo.find({
      where: { parentId: IsNull(), serviceId, companyId: user.companyId },
      select: { id: true },
    })
    if (lstServiceCapacityLv1.length > 0) {
      for (const serviceCapacityLv1 of lstServiceCapacityLv1) {
        const lstServiceCapacityLv2 = await serviceCapacityLv1.childs
        if (lstServiceCapacityLv2.length > 0) {
          for (const serviceCapacityLv2 of lstServiceCapacityLv2) {
            try {
              // update supplierCapacity lv2
              await supplierCapacityRepo.update(
                { serviceCapacityId: serviceCapacityLv2.id },
                {
                  serviceCapacityId: null,
                  updatedBy: user.id,
                },
              )
            } catch {
              throw new Error(DELETE_FAIL)
            }

            // xóa cấu hình danh sách lv2
            await capacityListRepo.delete({ serviceCapacityId: serviceCapacityLv2.id })
          }
          // xóa lv2
          await this.capacityRepo.delete({ parentId: serviceCapacityLv1.id })
        }

        // update supplierCapacity lv1
        try {
          await supplierCapacityRepo.update(
            { serviceCapacityId: serviceCapacityLv1.id },
            {
              serviceCapacityId: null,
              updatedBy: user.id,
            },
          )
        } catch (err: any) {
          throw new Error(DELETE_FAIL)
        }

        // xóa cấu hình danh sách lv1
        await capacityListRepo.delete({ serviceCapacityId: serviceCapacityLv1.id })
      }
      // xóa lv1
      try {
        await this.capacityRepo.delete({ serviceId })
      } catch (err: any) {
        throw new Error(DELETE_FAIL)
      }
    }

    await this.serviceRepo.update(serviceId, {
      statusCapacity: enumData.StatusServiceCapacity.ChuaDuyet.code,
      updatedBy: user.id,
    })

    return { message: DELETE_SUCCESS }
  }

  public async capacity_import(user: UserDto, serviceId: string, data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    const setType = new Set()
    setType.add(enumData.DataType.String.code)
    setType.add(enumData.DataType.Number.code)
    setType.add(enumData.DataType.File.code)

    await this.capacity_deleteall(user, serviceId)

    const res = await this.capacityRepo.manager.transaction(async (manager) => {
      const serviceCapacityRepo = manager.getRepository(ServiceCapacityEntity)
      const serviceCapacityListDetailRepo = manager.getRepository(ServiceCapacityListDetailEntity)

      // add lv1
      var lstDataLv1 = data.lstDataTable1.filter((c: any) => c.level == 1)
      for (const item of lstDataLv1) {
        const objServiceCapacityNew = new ServiceCapacityEntity()
        objServiceCapacityNew.companyId = user.companyId
        objServiceCapacityNew.createdBy = user.id
        objServiceCapacityNew.serviceId = serviceId
        objServiceCapacityNew.sort = item.sort || 0
        objServiceCapacityNew.name = item.name
        objServiceCapacityNew.percent = item.percent
        objServiceCapacityNew.percentRule = item.percentRule
        objServiceCapacityNew.type = item.type
        objServiceCapacityNew.isRequired = item.isRequired

        objServiceCapacityNew.isChangeByYear = false
        if (setType.has(objServiceCapacityNew.type)) objServiceCapacityNew.isChangeByYear = item.isChangeByYear

        objServiceCapacityNew.isCalUp = item.isCalUp
        objServiceCapacityNew.percentDownRule = item.percentDownRule

        const objServiceCapacity = await serviceCapacityRepo.save(objServiceCapacityNew)
        item.id = objServiceCapacity.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new ServiceCapacityListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.serviceCapacityId = item.id
            detailNew.name = detail.nameList
            detailNew.value = detail.valueList
            await serviceCapacityListDetailRepo.save(detailNew)
          }
        }
      }

      // add lv2
      var lstDataLv2 = data.lstDataTable1.filter((c: any) => c.level == 2)
      for (const item of lstDataLv2) {
        const objServiceCapacityNew = new ServiceCapacityEntity()
        objServiceCapacityNew.companyId = user.companyId
        objServiceCapacityNew.createdBy = user.id
        objServiceCapacityNew.serviceId = serviceId
        objServiceCapacityNew.sort = item.sort || 0
        objServiceCapacityNew.name = item.name
        objServiceCapacityNew.percent = item.percent
        objServiceCapacityNew.percentRule = item.percentRule
        objServiceCapacityNew.type = item.type
        objServiceCapacityNew.isRequired = item.isRequired

        objServiceCapacityNew.isChangeByYear = false
        if (setType.has(item.type)) objServiceCapacityNew.isChangeByYear = item.isChangeByYear

        objServiceCapacityNew.isCalUp = item.isCalUp
        objServiceCapacityNew.percentDownRule = item.percentDownRule

        const parent = lstDataLv1.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objServiceCapacityNew.parentId = parent.id

        const objServiceCapacity = await serviceCapacityRepo.save(objServiceCapacityNew)
        item.id = objServiceCapacity.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new ServiceCapacityListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.serviceCapacityId = item.id
            detailNew.name = detail.nameList
            detailNew.value = detail.valueList
            await serviceCapacityListDetailRepo.save(detailNew)
          }
        }
      }
    })

    if (serviceId) {
      await this.serviceRepo.update(serviceId, {
        statusCapacity: enumData.StatusServiceCapacity.ChuaDuyet.code,
        updatedBy: user.id,
      })
    }

    return res
  }

  public async capacity_send_approve(user: UserDto, serviceId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const service: any = await this.serviceRepo.findOne({
      where: { id: serviceId, companyId: user.companyId },
      relations: { serviceAccess: true },
      select: { id: true, serviceAccess: { id: true, employeeId: true } },
    })
    if (!service) throw new NotFoundException('Lĩnh vực mua hàng không còn tồn tại.')
    if (service.isDeleted) throw new NotFoundException('Lĩnh vực mua hàng đã ngưng hoạt động.')

    if (!service.__serviceAccess__.some((c) => c.employeeId == user.employeeId)) {
      throw new NotAcceptableException(
        ERROR_YOU_DO_NOT_HAVE_PERMISSION + ' Vui lòng liên hệ người phụ trách mua hàng của lĩnh vực mua hàng này để thực hiện thao tác này!',
      )
    }

    await this.serviceRepo.update(serviceId, {
      statusCapacity: enumData.StatusServiceCapacity.GuiDuyet.code,
      updatedBy: user.id,
    })
  }

  public async capacity_approve(user: UserDto, serviceId: string, skipCheck = false) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const service = await this.serviceRepo.findOne({
      where: { id: serviceId, companyId: user.companyId },
      select: { id: true, approveById: true, parentId: true },
    })
    if (!service) throw new NotFoundException('Lĩnh vực mua hàng không còn tồn tại.')
    if (service.isDeleted) throw new NotFoundException('Lĩnh vực mua hàng đã ngưng hoạt động.')

    if (!skipCheck && user.employeeId != service.approveById) {
      throw new NotAcceptableException(
        ERROR_YOU_DO_NOT_HAVE_PERMISSION + ' Vui lòng liên hệ người duyệt mua hàng của lĩnh vực mua hàng này để thực hiện thao tác này!',
      )
    }

    await this.serviceRepo.update(serviceId, {
      statusCapacity: enumData.StatusServiceCapacity.DaDuyet.code,
      updatedBy: user.id,
    })

    // Duyệt năng lực cho cấp cha, nếu cấp cha chưa duyệt năng lực
    if (service.parentId) {
      const parent = await this.serviceRepo.findOne({ where: { id: service.parentId, companyId: user.companyId } })
      if (parent && parent.statusCapacity != enumData.StatusServiceCapacity.DaDuyet.code) {
        await this.capacity_approve(user, service.parentId, true)
      }
    }
  }

  public async capacity_recheck(user: UserDto, serviceId: string, isCheckApprove = true) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const service = await this.serviceRepo.findOne({ where: { id: serviceId, companyId: user.companyId } })
    if (!service) throw new NotFoundException('Lĩnh vực mua hàng không còn tồn tại.')

    if (user.employeeId != service.approveById && isCheckApprove) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    await this.serviceRepo.update(serviceId, {
      statusCapacity: enumData.StatusServiceCapacity.ChuaDuyet.code,
      updatedBy: user.id,
    })

    // Hủy duyệt năng lực cấp cha, nếu cấp cha đã duyệt nhưng không còn cấp con đã duyệt
    if (service.parentId) {
      const parent = await this.serviceRepo.findOne({ where: { id: service.parentId, companyId: user.companyId } })
      if (parent?.statusCapacity == enumData.StatusServiceCapacity.DaDuyet.code) {
        const lstChild = await parent.childs
        if (!lstChild.some((c) => c.statusCapacity == enumData.StatusServiceCapacity.DaDuyet.code)) {
          await this.capacity_recheck(user, service.parentId, false)
        }
      }
    }
  }

  //#region capacityServiceListDetail
  public async capacitylistdetail_create_data(user: UserDto, data: ServiceCapacityListDetailCreateDto) {
    const capacity = await this.capacityRepo.findOne({ where: { id: data.serviceCapacityId, companyId: user.companyId } })
    if (!capacity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const capacityListRepo = this.capacityRepo.manager.getRepository(ServiceCapacityListDetailEntity)
    const entity = capacityListRepo.create(data)
    entity.companyId = user.companyId
    entity.createdBy = user.id
    await capacityListRepo.save(entity)

    if (capacity.serviceId) {
      await this.serviceRepo.update(capacity.serviceId, {
        statusCapacity: enumData.StatusServiceCapacity.ChuaDuyet.code,
        updatedBy: user.id,
      })
    }

    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async capacitylistdetail_update_data(user: UserDto, data: ServiceCapacityListDetailUpdateDto) {
    const entity = await this.capacityRepo.manager
      .getRepository(ServiceCapacityListDetailEntity)
      .findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.value = data.value
    entity.updatedBy = user.id
    await entity.save()

    const capacity = await entity.serviceCapacity
    if (capacity.serviceId) {
      await this.serviceRepo.update(capacity.serviceId, {
        statusCapacity: enumData.StatusServiceCapacity.ChuaDuyet.code,
        updatedBy: user.id,
      })
    }

    return { id: entity.id, message: UPDATE_SUCCESS }
  }

  public async capacitylistdetail_pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { companyId: user.companyId }
    if (data.where.serviceCapacityId) whereCon.serviceCapacityId = data.where.serviceCapacityId
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.capacityRepo.manager.getRepository(ServiceCapacityListDetailEntity).findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { value: 'DESC' },
    })
  }

  public async capacitylistdetail_update_active(user: UserDto, id: string) {
    const capacityListRepo = this.capacityRepo.manager.getRepository(ServiceCapacityListDetailEntity)

    const entity = await capacityListRepo.findOne({ where: { id, companyId: user.companyId }, select: { id: true, isDeleted: true } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await capacityListRepo.update(id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    const capacity = await entity.serviceCapacity
    await this.serviceRepo.update(capacity.serviceId, {
      statusCapacity: enumData.StatusServiceCapacity.ChuaDuyet.code,
      updatedBy: user.id,
    })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }
  //#endregion

  //#endregion

  //#region tech

  public async tech_getSumPercent(user: UserDto, data: { serviceId: string }) {
    let lstData = await this.techRepo.find({
      where: {
        parentId: IsNull(),
        serviceId: data.serviceId,
        companyId: user.companyId,
        isDeleted: false,
      },
    })
    let sum = 0
    for (let item of lstData) {
      if (item.percent > 0) {
        sum += item.percent
      }
    }
    return sum
  }

  private async tech_IsValidPercent(user: UserDto, data: any) {
    if (!data.percent || data.percent === 0) {
      return true
    }
    let lstDataSameParent = await this.techRepo.find({
      where: {
        parentId: data.parentId || IsNull(),
        serviceId: data.serviceId,
        id: Not(Equal(data.id || '')),
        companyId: user.companyId,
        isDeleted: false,
      },
    })
    let sum = 0
    for (let item of lstDataSameParent) {
      if (item.percent > 0) {
        sum += item.percent
      }
    }
    if (sum + data.percent > 100) {
      return false
    }
    return true
  }

  public async tech_find(user: UserDto, data: { serviceId: string; isGetRelation: boolean }) {
    const whereCon: any = { serviceId: data.serviceId, level: 1, companyId: user.companyId, isDeleted: false }
    let objRelation = {}
    if (data.isGetRelation) {
      objRelation = { serviceTechListDetails: true, childs: { serviceTechListDetails: true } }
    }
    const res = await this.techRepo.find({ where: whereCon, order: { sort: 'ASC', createdAt: 'ASC' }, relations: objRelation })

    return res
  }

  public async tech_create_data(user: UserDto, data: ServiceTechCreateDto) {
    const isValidPercent = await this.tech_IsValidPercent(user, data)
    if (!isValidPercent) throw new ConflictException(ERROR_PERCENT_SUM)

    const newEntity = this.techRepo.create(data)
    newEntity.companyId = user.companyId
    newEntity.createdBy = user.id
    await this.techRepo.save(newEntity)

    return { message: CREATE_SUCCESS }
  }

  public async tech_update_data(user: UserDto, data: ServiceTechUpdateDto) {
    const isValidPercent = await this.tech_IsValidPercent(user, data)
    if (!isValidPercent) throw new ConflictException(ERROR_PERCENT_SUM)

    const entity = await this.techRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    if (data.sort !== null) {
      entity.sort = data.sort
    }
    entity.isHighlight = data.isHighlight
    entity.hightlightValue = data.hightlightValue
    entity.isRequired = data.isRequired
    entity.type = data.type
    entity.percent = data.percent
    entity.percentRule = data.percentRule
    entity.isCalUp = data.isCalUp
    entity.percentDownRule = data.percentDownRule
    entity.level = data.level
    entity.description = data.description
    entity.scoreDLC = data.scoreDLC
    entity.requiredMin = data.requiredMin
    entity.updatedBy = user.id
    await entity.save()

    return { message: UPDATE_SUCCESS }
  }

  public async tech_pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { parentId: IsNull(), companyId: user.companyId }
    if (data.where.name) data.where.name = Like(`%${data.where.name}%`)
    if (data.where.serviceId) whereCon.serviceId = data.where.serviceId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.techRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { serviceTechListDetails: true, childs: { serviceTechListDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })
  }

  public async tech_update_active(user: UserDto, id: string) {
    const entity = await this.techRepo.findOne({ where: { id, companyId: user.companyId } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    if (entity.isDeleted) {
      const isValidPercent = await this.tech_IsValidPercent(user, entity)
      if (!isValidPercent) throw new ConflictException(ERROR_PERCENT_SUM)
    }

    entity.isDeleted = !entity.isDeleted
    await this.techRepo.update(entity.id, { isDeleted: entity.isDeleted, updatedBy: user.id })
    if (entity.level === 1) {
      let lstChild = await this.techRepo.find({ where: { parentId: entity.id }, select: { id: true } })
      for (let child of lstChild) {
        await this.techRepo.update(child.id, { isDeleted: entity.isDeleted, updatedBy: user.id })
      }
    }
    if (entity.level === 2) {
      if (entity.isDeleted === false) {
        let parent = await this.techRepo.findOne({ where: { id: entity.parentId }, select: { id: true } })
        if (parent) {
          await this.techRepo.update(parent.id, { isDeleted: false, updatedBy: user.id })
        }
      }
    }
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async tech_deleteall(user: UserDto, serviceId: string) {
    return this.techRepo.manager.transaction(async (manager) => {
      const serviceTechListDetailRepo = manager.getRepository(ServiceTechListDetailEntity)
      const serviceTechRepo = manager.getRepository(ServiceTechEntity)

      const level = 1
      const lstServiceTechLv1 = await serviceTechRepo.find({ where: { serviceId, level, companyId: user.companyId } })
      if (lstServiceTechLv1.length > 0) {
        for (const serviceTechLv1 of lstServiceTechLv1) {
          const lstServiceTechLv2 = await serviceTechLv1.childs
          if (lstServiceTechLv2.length > 0) {
            for (const serviceTechLv2 of lstServiceTechLv2) {
              // xóa cấu hình danh sách lv2
              await serviceTechListDetailRepo.delete({ serviceTechId: serviceTechLv2.id })
            }
          }
          // xóa lv2
          await serviceTechRepo.delete({ parentId: serviceTechLv1.id })

          // xóa cấu hình danh sách lv1
          await serviceTechListDetailRepo.delete({ serviceTechId: serviceTechLv1.id })
        }
        // xóa lv1
        await serviceTechRepo.delete({ serviceId })
      }

      return { message: DELETE_SUCCESS }
    })
  }

  public async tech_import(user: UserDto, serviceId: string, data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    await this.tech_deleteall(user, serviceId)

    return this.techRepo.manager.transaction(async (manager) => {
      const serviceTechRepo = manager.getRepository(ServiceTechEntity)
      const serviceTechListDetailRepo = manager.getRepository(ServiceTechListDetailEntity)

      // add lv1
      var lstDataLv1 = data.lstDataTable1.filter((c: any) => c.level == 1)
      for (const item of lstDataLv1) {
        const objServiceTechNew = new ServiceTechEntity()
        objServiceTechNew.companyId = user.companyId
        objServiceTechNew.createdBy = user.id
        objServiceTechNew.serviceId = serviceId
        objServiceTechNew.level = 1
        objServiceTechNew.sort = item.sort || 0
        objServiceTechNew.name = item.name
        objServiceTechNew.percent = item.percent
        objServiceTechNew.percentRule = item.percentRule
        objServiceTechNew.requiredMin = item.requiredMin
        objServiceTechNew.type = item.type
        objServiceTechNew.isRequired = item.isRequired
        objServiceTechNew.isHighlight = item.isHighlight
        objServiceTechNew.hightlightValue = item.hightlightValue
        objServiceTechNew.isCalUp = item.isCalUp
        objServiceTechNew.percentDownRule = item.percentDownRule

        const objServiceTech = await serviceTechRepo.save(objServiceTechNew)
        item.id = objServiceTech.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new ServiceTechListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.serviceTechId = item.id
            detailNew.name = detail.nameList
            detailNew.value = detail.valueList
            await serviceTechListDetailRepo.save(detailNew)
          }
        }
      }

      // add lv2
      var lstDataLv2 = data.lstDataTable1.filter((c: any) => c.level == 2)
      for (const item of lstDataLv2) {
        const objServiceTechNew = new ServiceTechEntity()
        objServiceTechNew.companyId = user.companyId
        objServiceTechNew.createdBy = user.id
        objServiceTechNew.serviceId = serviceId
        objServiceTechNew.level = 2
        objServiceTechNew.sort = item.sort || 0
        objServiceTechNew.name = item.name
        objServiceTechNew.percent = item.percent
        objServiceTechNew.percentRule = item.percentRule
        objServiceTechNew.requiredMin = item.requiredMin
        objServiceTechNew.type = item.type
        objServiceTechNew.isRequired = item.isRequired
        objServiceTechNew.isHighlight = item.isHighlight
        objServiceTechNew.hightlightValue = item.hightlightValue
        objServiceTechNew.isCalUp = item.isCalUp
        objServiceTechNew.percentDownRule = item.percentDownRule
        const parent = lstDataLv1.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objServiceTechNew.parentId = parent.id

        const objServiceTech = await serviceTechRepo.save(objServiceTechNew)
        item.id = objServiceTech.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new ServiceTechListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.serviceTechId = item.id
            detailNew.name = detail.nameList
            detailNew.value = detail.valueList
            await serviceTechListDetailRepo.save(detailNew)
          }
        }
      }
    })
  }

  //#region techServiceListDetail
  public async techlistdetail_create_data(user: UserDto, data: ServiceTechListDetailCreateDto) {
    const techListRepo = this.techRepo.manager.getRepository(ServiceTechListDetailEntity)
    const entity = techListRepo.create(data)
    entity.companyId = user.companyId
    entity.createdBy = user.id
    await techListRepo.save(entity)

    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async techlistdetail_update_data(user: UserDto, data: ServiceTechListDetailUpdateDto) {
    const entity = await this.techRepo.manager
      .getRepository(ServiceTechListDetailEntity)
      .findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.value = data.value
    entity.updatedBy = user.id
    await entity.save()

    return { id: entity.id, message: UPDATE_SUCCESS }
  }

  public async techlistdetail_pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { companyId: user.companyId }
    if (data.where.serviceTechId) whereCon.serviceTechId = data.where.serviceTechId
    if (data.where.name) data.where.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.techRepo.manager.getRepository(ServiceTechListDetailEntity).findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { value: 'DESC' },
    })
  }

  public async techlistdetail_update_active(user: UserDto, id: string) {
    const techListRepo = this.techRepo.manager.getRepository(ServiceTechListDetailEntity)
    const entity = await techListRepo.findOne({ where: { id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await techListRepo.update(id, { isDeleted: !entity.isDeleted, updatedBy: user.id })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }
  //#endregion

  //#endregion

  //#region trade

  public async trade_getSumPercent(user: UserDto, data: { serviceId: string }) {
    let lstData = await this.tradeRepo.find({
      where: {
        parentId: IsNull(),
        serviceId: data.serviceId,
        companyId: user.companyId,
        isDeleted: false,
      },
    })
    let sum = 0
    for (let item of lstData) {
      if (item.percent > 0) {
        sum += item.percent
      }
    }
    return sum
  }

  private async trade_IsValidPercent(user: UserDto, data: any) {
    if (!data.percent || data.percent === 0) {
      return true
    }
    let lstDataSameParent = await this.tradeRepo.find({
      where: {
        parentId: data.parentId || IsNull(),
        serviceId: data.serviceId,
        id: Not(Equal(data.id || '')),
        companyId: user.companyId,
        isDeleted: false,
      },
    })
    let sum = 0
    for (let item of lstDataSameParent) {
      if (item.percent > 0) {
        sum += item.percent
      }
    }
    if (sum + data.percent > 100) {
      return false
    }
    return true
  }

  public async trade_find(user: UserDto, data: { serviceId: string }) {
    return await this.tradeRepo.find({ where: { serviceId: data.serviceId, level: 1, companyId: user.companyId, isDeleted: false } })
  }

  public async trade_create_data(user: UserDto, data: ServiceTradeCreateDto) {
    const isValidPercent = await this.trade_IsValidPercent(user, data)
    if (!isValidPercent) throw new ConflictException(ERROR_PERCENT_SUM)

    const newEntity = this.tradeRepo.create(data)
    newEntity.companyId = user.companyId
    newEntity.createdBy = user.id
    await this.tradeRepo.save(newEntity)

    return { message: CREATE_SUCCESS }
  }

  public async trade_update_data(user: UserDto, data: ServiceTradeUpdateDto) {
    const isValidPercent = await this.trade_IsValidPercent(user, data)
    if (!isValidPercent) throw new ConflictException(ERROR_PERCENT_SUM)

    const entity = await this.tradeRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    if (data.sort !== null) {
      entity.sort = data.sort
    }
    entity.isRequired = data.isRequired
    entity.type = data.type
    entity.percent = data.percent
    entity.percentRule = data.percentRule
    entity.isCalUp = data.isCalUp
    entity.percentDownRule = data.percentDownRule
    entity.level = data.level
    entity.description = data.description
    entity.scoreDLC = data.scoreDLC
    entity.requiredMin = data.requiredMin
    entity.updatedBy = user.id
    await entity.save()

    return { message: UPDATE_SUCCESS }
  }

  public async trade_pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { parentId: IsNull(), companyId: user.companyId }
    if (data.where.name) data.where.name = Like(`%${data.where.name}%`)
    if (data.where.serviceId) whereCon.serviceId = data.where.serviceId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.tradeRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { serviceTradeListDetails: true, childs: { serviceTradeListDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })
  }

  public async trade_update_active(user: UserDto, id: string) {
    const entity = await this.tradeRepo.findOne({ where: { id, companyId: user.companyId } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    if (entity.isDeleted) {
      const isValidPercent = await this.trade_IsValidPercent(user, entity)
      if (!isValidPercent) throw new ConflictException(ERROR_PERCENT_SUM)
    }

    entity.isDeleted = !entity.isDeleted
    await this.tradeRepo.update(entity.id, { isDeleted: entity.isDeleted, updatedBy: user.id })
    if (entity.level === 1) {
      let lstChild = await this.tradeRepo.find({ where: { parentId: entity.id, companyId: user.companyId }, select: { id: true } })
      for (let child of lstChild) {
        await this.tradeRepo.update(child.id, { isDeleted: entity.isDeleted, updatedBy: user.id })
      }
    }
    if (entity.level === 2) {
      if (entity.isDeleted === false) {
        let parent = await this.tradeRepo.findOne({ where: { id: entity.parentId }, select: { id: true } })
        if (parent) {
          await this.tradeRepo.update(parent.id, { isDeleted: false, updatedBy: user.id })
        }
      }
    }
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async trade_deleteall(user: UserDto, serviceId: string) {
    return this.tradeRepo.manager.transaction(async (manager) => {
      const serviceTradeListDetailRepo = manager.getRepository(ServiceTradeListDetailEntity)
      const serviceTradeRepo = manager.getRepository(ServiceTradeEntity)

      const lstServiceTradeLv1 = await serviceTradeRepo.find({
        where: { parentId: IsNull(), serviceId: serviceId, companyId: user.companyId },
      })
      if (lstServiceTradeLv1.length > 0) {
        for (const serviceTradeLv1 of lstServiceTradeLv1) {
          const lstServiceTradeLv2 = await serviceTradeLv1.childs
          if (lstServiceTradeLv2.length > 0) {
            for (const serviceTradeLv2 of lstServiceTradeLv2) {
              // xóa cấu hình danh sách lv2
              await serviceTradeListDetailRepo.delete({ serviceTradeId: serviceTradeLv2.id })
            }
          }
          // xóa lv2
          await serviceTradeRepo.delete({ parentId: serviceTradeLv1.id })

          // xóa cấu hình danh sách lv1
          await serviceTradeListDetailRepo.delete({ serviceTradeId: serviceTradeLv1.id })
        }
        // xóa lv1
        await serviceTradeRepo.delete({ serviceId })
      }

      return { message: DELETE_SUCCESS }
    })
  }

  public async trade_import(user: UserDto, serviceId: string, data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    await this.trade_deleteall(user, serviceId)

    return this.tradeRepo.manager.transaction(async (manager) => {
      const serviceTradeRepo = manager.getRepository(ServiceTradeEntity)
      const serviceTradeListDetailRepo = manager.getRepository(ServiceTradeListDetailEntity)

      // add lv1
      var lstDataLv1 = data.lstDataTable1.filter((c: any) => c.level == 1)
      for (const item of lstDataLv1) {
        const objServiceTradeNew = new ServiceTradeEntity()
        objServiceTradeNew.companyId = user.companyId
        objServiceTradeNew.createdBy = user.id
        objServiceTradeNew.serviceId = serviceId
        objServiceTradeNew.level = 1
        objServiceTradeNew.sort = item.sort || 0
        objServiceTradeNew.name = item.name
        objServiceTradeNew.percent = item.percent
        objServiceTradeNew.percentRule = item.percentRule
        objServiceTradeNew.type = item.type
        objServiceTradeNew.isRequired = item.isRequired
        objServiceTradeNew.isCalUp = item.isCalUp
        objServiceTradeNew.percentDownRule = item.percentDownRule

        const objServiceTrade = await serviceTradeRepo.save(objServiceTradeNew)
        item.id = objServiceTrade.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new ServiceTradeListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.serviceTradeId = item.id
            detailNew.name = detail.nameList
            detailNew.value = detail.valueList
            await serviceTradeListDetailRepo.save(detailNew)
          }
        }
      }

      // add lv2
      var lstDataLv2 = data.lstDataTable1.filter((c: any) => c.level == 2)
      for (const item of lstDataLv2) {
        const objServiceTradeNew = new ServiceTradeEntity()
        objServiceTradeNew.companyId = user.companyId
        objServiceTradeNew.createdBy = user.id
        objServiceTradeNew.serviceId = serviceId
        objServiceTradeNew.level = 2
        objServiceTradeNew.sort = item.sort || 0
        objServiceTradeNew.name = item.name
        objServiceTradeNew.percent = item.percent
        objServiceTradeNew.percentRule = item.percentRule
        objServiceTradeNew.type = item.type
        objServiceTradeNew.isRequired = item.isRequired
        objServiceTradeNew.isCalUp = item.isCalUp
        objServiceTradeNew.percentDownRule = item.percentDownRule

        const parent = lstDataLv1.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objServiceTradeNew.parentId = parent.id

        const objServiceTrade = await serviceTradeRepo.save(objServiceTradeNew)
        item.id = objServiceTrade.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new ServiceTradeListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.serviceTradeId = item.id
            detailNew.name = detail.nameList
            detailNew.value = detail.valueList
            await serviceTradeListDetailRepo.save(detailNew)
          }
        }
      }
    })
  }

  //#region tradeServiceListDetail
  public async tradelistdetail_create_data(user: UserDto, data: ServiceTradeListDetailCreateDto) {
    const tradeListRepo = this.tradeRepo.manager.getRepository(ServiceTradeListDetailEntity)
    const entity = tradeListRepo.create(data)
    entity.companyId = user.companyId
    entity.createdBy = user.id
    await tradeListRepo.save(entity)

    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async tradelistdetail_update_data(user: UserDto, data: ServiceTradeListDetailUpdateDto) {
    const entity = await this.tradeRepo.manager
      .getRepository(ServiceTradeListDetailEntity)
      .findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.value = data.value
    entity.updatedBy = user.id
    await entity.save()

    return { id: entity.id, message: UPDATE_SUCCESS }
  }

  public async tradelistdetail_pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { companyId: user.companyId }
    if (data.where.name) data.where.name = Like(`%${data.where.name}%`)
    if (data.where.serviceTradeId) whereCon.serviceTradeId = data.where.serviceTradeId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.tradeRepo.manager.getRepository(ServiceTradeListDetailEntity).findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { value: 'DESC' },
    })
  }

  public async tradelistdetail_update_active(user: UserDto, id: string) {
    const tradeListRepo = this.tradeRepo.manager.getRepository(ServiceTradeListDetailEntity)
    const entity = await tradeListRepo.findOne({ where: { id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await tradeListRepo.update(id, { isDeleted: !entity.isDeleted, updatedBy: user.id })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }
  //#endregion

  //#endregion

  //#region price

  public async price_find(user: UserDto, data: { serviceId: string }) {
    return await this.priceRepo.find({
      where: { serviceId: data.serviceId, level: 1, companyId: user.companyId, isDeleted: false },
      relations: { childs: true },
    })
  }

  public async price_create_data(user: UserDto, data: ServicePriceCreateDto) {
    const newEntity = this.priceRepo.create(data)
    newEntity.companyId = user.companyId
    newEntity.createdBy = user.id
    await this.priceRepo.save(newEntity)

    return { message: CREATE_SUCCESS }
  }

  public async price_update_data(user: UserDto, data: ServicePriceUpdateDto) {
    const entity = await this.priceRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.isRequired = data.isRequired
    entity.isSetup = data.isSetup
    entity.type = data.type
    entity.unit = data.unit
    entity.currency = data.currency
    if (data.sort !== null) {
      entity.sort = data.sort
    }
    entity.percent = data.percent
    entity.level = data.level
    entity.description = data.description
    entity.scoreDLC = data.scoreDLC
    entity.requiredMin = data.requiredMin
    entity.number = data.number
    entity.isTemplate = data.isTemplate
    entity.updatedBy = user.id
    await entity.save()

    return { message: UPDATE_SUCCESS }
  }

  public async price_deleteall(user: UserDto, serviceId: string) {
    return this.priceRepo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(BidPriceEntity)
      const servicePriceListDetailRepo = manager.getRepository(ServicePriceListDetailEntity)
      const servicePriceColValueRepo = manager.getRepository(ServicePriceColValueEntity)
      const servicePriceRepo = manager.getRepository(ServicePriceEntity)

      const level = 1
      const lstServicePriceLv1 = await servicePriceRepo.find({ where: { serviceId, level, companyId: user.companyId } })
      if (lstServicePriceLv1.length > 0) {
        for (const servicePriceLv1 of lstServicePriceLv1) {
          const lstServicePriceLv2 = await servicePriceLv1.childs
          if (lstServicePriceLv2.length > 0) {
            for (const servicePriceLv2 of lstServicePriceLv2) {
              const lstServicePriceLv3 = await servicePriceLv2.childs
              if (lstServicePriceLv3.length > 0) {
                for (const servicePriceLv3 of lstServicePriceLv3) {
                  // update bidPriceLv3
                  await bidPriceRepo.update(
                    { servicePriceId: servicePriceLv3.id },
                    {
                      servicePriceId: null,
                      updatedBy: user.id,
                    },
                  )
                  // xóa thông tin mở rộng lv3
                  await servicePriceListDetailRepo.delete({ servicePriceId: servicePriceLv3.id })
                  // xóa giá trị cột động lv3
                  await servicePriceColValueRepo.delete({ servicePriceId: servicePriceLv3.id })
                }
              }
              // xóa lv3
              await servicePriceRepo.delete({ parentId: servicePriceLv2.id })

              // update bidPriceLv2
              await bidPriceRepo.update(
                { servicePriceId: servicePriceLv2.id },
                {
                  servicePriceId: null,
                  updatedBy: user.id,
                },
              )
              // xóa thông tin mở rộng lv2
              await servicePriceListDetailRepo.delete({ servicePriceId: servicePriceLv2.id })
              // xóa giá trị cột động lv2
              await servicePriceColValueRepo.delete({ servicePriceId: servicePriceLv2.id })
            }
          }
          // xóa lv2
          await servicePriceRepo.delete({ parentId: servicePriceLv1.id })

          // update bidPriceLv1
          await bidPriceRepo.update(
            { servicePriceId: servicePriceLv1.id },
            {
              servicePriceId: null,
              updatedBy: user.id,
            },
          )
          // xóa thông tin mở rộng lv1
          await servicePriceListDetailRepo.delete({ servicePriceId: servicePriceLv1.id })
          // xóa giá trị cột động lv1
          await servicePriceColValueRepo.delete({ servicePriceId: servicePriceLv1.id })
        }
        // xóa lv1
        await servicePriceRepo.delete({ serviceId })
      }

      return { message: DELETE_SUCCESS }
    })
  }

  public async setting_fomular(user: UserDto, data: { id: string; fomular: string }) {
    const lstField = await this.priceColRepo.find({
      where: { serviceId: data.id, companyId: user.companyId, isDeleted: false },
      order: { sort: 'ASC', createdAt: 'ASC' },
    })
    const isValidFomular = await coreHelper.checkFomular(data.fomular, lstField)
    if (!isValidFomular) throw new NotAcceptableException(ERROR_INVALID_FOMULAR)

    const objService = await this.serviceRepo.findOne({ where: { id: data.id }, select: { id: true } })
    if (!objService) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.serviceRepo.update(data.id, { fomular: data.fomular, updatedBy: user.id })

    return { message: UPDATE_SUCCESS }
  }

  /** Setup cách tính điểm giá của Item */
  public async saveSettingPriceCalWay(user: UserDto, data: { id: string; wayCalScorePrice: string }) {
    await this.serviceRepo.update(data.id, { wayCalScorePrice: data.wayCalScorePrice, updatedBy: user.id })

    return { message: UPDATE_SUCCESS }
  }

  public async price_import(user: UserDto, serviceId: string, data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    await this.price_deleteall(user, serviceId)

    return this.priceRepo.manager.transaction(async (manager) => {
      const servicePriceColValueRepo = manager.getRepository(ServicePriceColValueEntity)
      const servicePriceRepo = manager.getRepository(ServicePriceEntity)
      const servicePriceColRepo = manager.getRepository(ServicePriceColEntity)
      const servicePriceListDetailRepo = manager.getRepository(ServicePriceListDetailEntity)

      const lstServicePriceCol = await servicePriceColRepo.find({
        where: { serviceId, companyId: user.companyId, colType: enumData.ColType.MPO.code, isDeleted: false },
        order: { sort: 'ASC', createdAt: 'ASC' },
      })

      // add lv1
      var lstDataLv1 = data.lstDataTable1.filter((c: any) => c.level == 1)
      for (const item of lstDataLv1) {
        const objServicePriceNew = new ServicePriceEntity()
        objServicePriceNew.companyId = user.companyId
        objServicePriceNew.createdBy = user.id
        objServicePriceNew.serviceId = serviceId
        objServicePriceNew.name = item.name
        objServicePriceNew.sort = item.sort || 0
        objServicePriceNew.unit = item.unit
        objServicePriceNew.currency = item.currency
        objServicePriceNew.number = item.number
        objServicePriceNew.isRequired = item.isRequired
        objServicePriceNew.level = 1
        objServicePriceNew.type = enumData.DataType.Number.code
        objServicePriceNew.isTemplate = false
        objServicePriceNew.isSetup = false

        const objServicePrice = await servicePriceRepo.save(objServicePriceNew)
        item.id = objServicePrice.id

        for (const col of lstServicePriceCol) {
          if (item[col.id] != null) {
            const objServicePriceColValueNew = new ServicePriceColValueEntity()
            objServicePriceColValueNew.companyId = user.companyId
            objServicePriceColValueNew.createdBy = user.id
            objServicePriceColValueNew.servicePriceId = item.id
            objServicePriceColValueNew.servicePriceColId = col.id
            objServicePriceColValueNew.value = item[col.id]
            await servicePriceColValueRepo.save(objServicePriceColValueNew)
          }
        }

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new ServicePriceListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.servicePriceId = item.id
            detailNew.name = detail.nameList
            detailNew.type = detail.typeDataList
            detailNew.value = detail.valueList
            await servicePriceListDetailRepo.save(detailNew)
          }
        }
      }

      // add lv2
      var lstDataLv2 = data.lstDataTable1.filter((c: any) => c.level == 2)
      for (const item of lstDataLv2) {
        const objServicePriceNew = new ServicePriceEntity()
        objServicePriceNew.companyId = user.companyId
        objServicePriceNew.createdBy = user.id
        objServicePriceNew.serviceId = serviceId
        objServicePriceNew.name = item.name
        objServicePriceNew.sort = item.sort || 0
        objServicePriceNew.unit = item.unit
        objServicePriceNew.currency = item.currency
        objServicePriceNew.number = item.number
        objServicePriceNew.isRequired = item.isRequired
        objServicePriceNew.level = 2
        objServicePriceNew.type = enumData.DataType.Number.code
        objServicePriceNew.isTemplate = false
        objServicePriceNew.isSetup = false
        const parent = lstDataLv1.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objServicePriceNew.parentId = parent.id

        const objServicePrice = await servicePriceRepo.save(objServicePriceNew)
        item.id = objServicePrice.id

        for (const col of lstServicePriceCol) {
          if (item[col.id] != null) {
            const objServicePriceColValueNew = new ServicePriceColValueEntity()
            objServicePriceColValueNew.companyId = user.companyId
            objServicePriceColValueNew.createdBy = user.id
            objServicePriceColValueNew.servicePriceId = item.id
            objServicePriceColValueNew.servicePriceColId = col.id
            objServicePriceColValueNew.value = item[col.id]

            await servicePriceColValueRepo.save(objServicePriceColValueNew)
          }
        }

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new ServicePriceListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.servicePriceId = item.id
            detailNew.name = detail.nameList
            detailNew.type = detail.typeDataList
            detailNew.value = detail.valueList
            await servicePriceListDetailRepo.save(detailNew)
          }
        }
      }

      // add lv3
      var lstDataLv3 = data.lstDataTable1.filter((c: any) => c.level == 3)
      for (const item of lstDataLv3) {
        const objServicePriceNew = new ServicePriceEntity()
        objServicePriceNew.companyId = user.companyId
        objServicePriceNew.createdBy = user.id
        objServicePriceNew.serviceId = serviceId
        objServicePriceNew.name = item.name
        objServicePriceNew.sort = item.sort || 0
        objServicePriceNew.unit = item.unit
        objServicePriceNew.currency = item.currency
        objServicePriceNew.number = item.number
        objServicePriceNew.isRequired = item.isRequired
        objServicePriceNew.level = 3
        objServicePriceNew.type = enumData.DataType.Number.code
        objServicePriceNew.isTemplate = false
        objServicePriceNew.isSetup = false
        const parent = lstDataLv2.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objServicePriceNew.parentId = parent.id

        const objServicePrice = await servicePriceRepo.save(objServicePriceNew)
        item.id = objServicePrice.id

        for (const col of lstServicePriceCol) {
          if (item[col.id] != null) {
            const objServicePriceColValueNew = new ServicePriceColValueEntity()
            objServicePriceColValueNew.companyId = user.companyId
            objServicePriceColValueNew.createdBy = user.id
            objServicePriceColValueNew.servicePriceId = item.id
            objServicePriceColValueNew.servicePriceColId = col.id
            objServicePriceColValueNew.value = item[col.id]
            await servicePriceColValueRepo.save(objServicePriceColValueNew)
          }
        }

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new ServicePriceListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.servicePriceId = item.id
            detailNew.name = detail.nameList
            detailNew.type = detail.typeDataList
            detailNew.value = detail.valueList
            await servicePriceListDetailRepo.save(detailNew)
          }
        }
      }
    })
  }

  public async price_pagination(user: UserDto, data: PaginationDto) {
    if (!data.where.serviceId) throw new BadRequestException(`Vui lòng chọn Item trước!`)

    const whereCon: any = { serviceId: data.where.serviceId, parentId: IsNull(), companyId: user.companyId }
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res1: any[] = await this.priceRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: {
        servicePriceListDetails: true,
        servicePriceColValues: true,
        childs: {
          servicePriceListDetails: true,
          servicePriceColValues: true,
          childs: {
            servicePriceListDetails: true,
            servicePriceColValues: true,
          },
        },
      },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } } },
    })

    const lstCol = await this.priceColRepo.find({
      where: { serviceId: data.where.serviceId, companyId: user.companyId, isDeleted: false },
      order: { sort: 'ASC', createdAt: 'ASC' },
    })

    const getDataCell = (row, col) => {
      row[col.id] = ''
      row.__servicePriceColValues__ = row.__servicePriceColValues__ || []
      const cell = row.__servicePriceColValues__.find((c) => c.servicePriceColId === col.id)
      if (cell) row[col.id] = cell.value
    }

    // Lọc trạng thái hoạt động cho các cấp con
    if (data.where.isDeleted != undefined) {
      for (const data1 of res1[0]) {
        for (const col of lstCol) getDataCell(data1, col)
        // Lọc trạng thái cho lv2
        data1.__childs__ = data1.__childs__.filter((c: any) => c.isDeleted === data.where.isDeleted)
        for (const data2 of data1.__childs__) {
          for (const col of lstCol) getDataCell(data2, col)
          // Lọc trạng thái cho lv3
          data2.__childs__ = data2.__childs__.filter((c: any) => c.isDeleted === data.where.isDeleted)
          for (const data3 of data2.__childs__) {
            for (const col of lstCol) getDataCell(data3, col)
          }
        }
      }
    }

    return [...res1, lstCol]
  }

  public async price_update_active(user: UserDto, id: string) {
    const entity = await this.priceRepo.findOne({ where: { id, companyId: user.companyId } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    entity.isDeleted = !entity.isDeleted
    await this.priceRepo.update(entity.id, { isDeleted: entity.isDeleted, updatedBy: user.id })

    const lstChild1 = await entity.childs
    if (lstChild1.length > 0) {
      for (let child1 of lstChild1) {
        const lstChild2 = await child1.childs
        if (lstChild2.length > 0) {
          for (let child2 of lstChild2) {
            await this.priceRepo.update(child2.id, { isDeleted: entity.isDeleted, updatedBy: user.id })
          }
        }
        await this.priceRepo.update(child1.id, { isDeleted: entity.isDeleted, updatedBy: user.id })
      }
    }

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  //#region priceServiceListDetail
  public async pricelistdetail_create_data(user: UserDto, data: ServicePriceListDetailCreateDto) {
    const priceListRepo = this.priceRepo.manager.getRepository(ServicePriceListDetailEntity)
    const entity = priceListRepo.create(data)
    entity.companyId = user.companyId
    entity.createdBy = user.id
    await priceListRepo.save(entity)

    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async pricelistdetail_update_data(user: UserDto, data: ServicePriceListDetailUpdateDto) {
    const entity = await this.priceRepo.manager
      .getRepository(ServicePriceListDetailEntity)
      .findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.type = data.type
    entity.value = data.value
    entity.updatedBy = user.id
    await entity.save()

    return { id: entity.id, message: UPDATE_SUCCESS }
  }

  public async pricelistdetail_pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { companyId: user.companyId }
    if (data.where.servicePriceId) whereCon.servicePriceId = data.where.servicePriceId
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res: any[] = await this.priceRepo.manager.getRepository(ServicePriceListDetailEntity).findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'ASC' },
    })

    return res
  }

  public async pricelistdetail_update_active(user: UserDto, id: string) {
    const priceListRepo = this.priceRepo.manager.getRepository(ServicePriceListDetailEntity)
    const entity = await priceListRepo.findOne({ where: { id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await priceListRepo.update(id, { isDeleted: !entity.isDeleted, updatedBy: user.id })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }
  //#endregion

  //#region priceCol
  public async pricecol_create_data(user: UserDto, data: ServicePriceColCreateDto) {
    if (data.fomular?.length > 0) {
      const lstField = await this.priceColRepo.find({
        where: { serviceId: data.serviceId, companyId: user.companyId, isDeleted: false },
        order: { sort: 'ASC', createdAt: 'ASC' },
      })
      const isValidFomular = await coreHelper.checkFomular(data.fomular, lstField)
      if (!isValidFomular) throw new NotAcceptableException(ERROR_INVALID_FOMULAR)
    }

    const entity = this.priceColRepo.create(data)
    entity.companyId = user.companyId
    entity.createdBy = user.id
    await this.priceColRepo.save(entity)

    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async pricecol_update_data(user: UserDto, data: ServicePriceColUpdateDto) {
    const entity = await this.priceColRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    if (data.fomular !== entity.fomular) {
      // Nếu update công thức thì kiểm tra lại
      if (data.fomular?.length > 0) {
        const lstField = await this.priceColRepo.find({
          where: { serviceId: entity.serviceId, companyId: user.companyId, isDeleted: false },
          order: { sort: 'ASC', createdAt: 'ASC' },
        })
        const isValidFomular = await coreHelper.checkFomular(data.fomular, lstField)
        if (!isValidFomular) throw new Error(ERROR_INVALID_FOMULAR)
      }

      entity.fomular = data.fomular
    }
    entity.code = data.code
    entity.name = data.name
    entity.type = data.type
    entity.colType = data.colType
    entity.isRequired = data.isRequired
    entity.sort = data.sort || 0
    entity.updatedBy = user.id
    await entity.save()

    return { id: entity.id, message: UPDATE_SUCCESS }
  }

  public async pricecol_pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { companyId: user.companyId }
    if (data.where.serviceId) whereCon.serviceId = data.where.serviceId
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res: any[] = await this.priceColRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { name: 'ASC' },
    })
    if (res[0].length == 0) return res

    for (const item of res[0]) {
      item.typeName = enumData.DataType[item.type]?.name || ''
      item.colTypeName = enumData.ColType[item.colType]?.name || ''
    }

    return res
  }

  public async pricecol_update_active(user: UserDto, id: string) {
    const entity = await this.priceColRepo.findOne({ where: { id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.priceColRepo.update(id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }
  //#endregion

  //#endregion

  //#region customprice

  public async customprice_create_data(user: UserDto, data: ServiceCustomPriceCreateDto) {
    const newEntity = this.customPriceRepo.create(data)
    newEntity.companyId = user.companyId
    newEntity.createdBy = user.id
    await this.customPriceRepo.save(newEntity)

    return { message: CREATE_SUCCESS }
  }

  public async customprice_update_data(user: UserDto, data: ServiceCustomPriceUpdateDto) {
    const entity = await this.customPriceRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.isRequired = data.isRequired
    entity.type = data.type
    entity.unit = data.unit
    entity.currency = data.currency
    if (data.sort !== null) {
      entity.sort = data.sort
    }
    entity.number = data.number
    entity.updatedBy = user.id

    await entity.save()

    return { message: UPDATE_SUCCESS }
  }

  public async customprice_pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { companyId: user.companyId }
    if (data.where.serviceId) whereCon.serviceId = data.where.serviceId
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    return await this.customPriceRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { sort: 'ASC', createdAt: 'ASC' },
    })
  }

  public async customprice_update_active(user: UserDto, id: string) {
    const entity = await this.customPriceRepo.findOne({ where: { id, companyId: user.companyId } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    await this.customPriceRepo.update(id, { isDeleted: !entity.isDeleted, updatedBy: user.id })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async customprice_import(user: UserDto, serviceId: string, data: { lstData: any[] }) {
    await this.customPriceRepo.delete({ serviceId })

    for (const item of data.lstData) {
      const objServiceCustomPriceNew = new ServiceCustomPriceEntity()
      objServiceCustomPriceNew.companyId = user.companyId
      objServiceCustomPriceNew.createdBy = user.id
      objServiceCustomPriceNew.serviceId = serviceId
      objServiceCustomPriceNew.name = item.name
      objServiceCustomPriceNew.sort = item.sort || 0
      objServiceCustomPriceNew.unit = item.unit
      objServiceCustomPriceNew.currency = item.currency
      objServiceCustomPriceNew.number = item.number
      objServiceCustomPriceNew.isRequired = item.isRequired
      objServiceCustomPriceNew.type = enumData.DataType.Number.code
      await this.customPriceRepo.save(objServiceCustomPriceNew)
    }
  }

  //#endregion

  //#region Rate
  public async rateUpdate(user: UserDto, data: { id: string; percentTech: number; percentPrice: number; percentTrade: number }) {
    if (!data.id) throw new Error(ERROR_NOT_FOUND_DATA)
    const objService = await this.serviceRepo.findOne({ where: { id: data.id, companyId: user.companyId }, select: { id: true } })
    if (!objService) throw new Error(ERROR_NOT_FOUND_DATA)
    if (data.percentTech + data.percentPrice + data.percentTrade != 100) throw new Error('Tổng % các tỉ lệ nên là 100%')

    await this.serviceRepo.update(data.id, {
      percentTech: data.percentTech,
      percentPrice: data.percentPrice,
      percentTrade: data.percentTrade,
      updatedBy: user.id,
    })

    return { message: UPDATE_SUCCESS }
  }
  //#endregion
}
