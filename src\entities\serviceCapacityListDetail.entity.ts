import { BaseEntity } from './base.entity'
import { En<PERSON>ty, Column, ManyToOne, JoinColumn } from 'typeorm'
import { ServiceCapacityEntity } from './serviceCapacity.entity'

@Entity('service_capacity_list_detail')
export class ServiceCapacityListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
  })
  value: number

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  serviceCapacityId: string
  @ManyToOne(() => ServiceCapacityEntity, (p) => p.serviceCapacityListDetails)
  @JoinColumn({ name: 'serviceCapacityId', referencedColumnName: 'id' })
  serviceCapacity: Promise<ServiceCapacityEntity>
}
