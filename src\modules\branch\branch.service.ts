import { ConflictException, Injectable } from '@nestjs/common'
import { BranchCreateDto, BranchUpdateDto } from './dto'
import { PaginationDto, UserDto } from '../../dto'
import { BranchRepository, EmployeeRepository } from '../../repositories'
import { CREATE_SUCCESS, ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { BranchEntity } from '../../entities'
import { Like } from 'typeorm'

@Injectable()
export class BranchService {
  constructor(private readonly repo: BranchRepository, private employeeRepo: EmployeeRepository) {}

  public async find(user: UserDto, data: { level?: number; parentId?: string }) {
    const whereCon: any = { companyId: user.companyId, isDeleted: false }

    if (data.parentId) whereCon.parentId = data.parentId
    if (data.level > 0) whereCon.level = data.level

    return await this.repo.find({ where: whereCon, order: { name: 'ASC' } })
  }

  public async findSelection(user: UserDto, data: { level?: number; parentId?: string }) {
    const whereCon: any = { companyId: user.companyId, isDeleted: false }
    const employee: any = await this.employeeRepo.findOne({
      where: { id: user.employeeId, companyId: user.companyId },
      relations: { branch: true },
    })
    if (employee && employee.__branch__) {
      whereCon.id = employee.__branch__?.id
    }
    if (data.parentId) whereCon.parentId = data.parentId
    if (data.level > 0) whereCon.level = data.level

    return await this.repo.find({ where: whereCon, order: { name: 'ASC' } })
  }

  public async createData(user: UserDto, data: BranchCreateDto) {
    const objCheckCode = await this.repo.findOne({ where: { code: data.code, companyId: user.companyId }, select: { id: true } })
    if (objCheckCode) throw new ConflictException(ERROR_CODE_TAKEN)

    data.level = 1
    if (data.parent2) {
      data.level = 3
    } else if (data.parent1) {
      data.level = 2
    }

    const branch = new BranchEntity()
    branch.companyId = user.companyId
    branch.name = data.name || ''
    branch.code = data.code
    branch.level = data.level
    branch.type = data.type
    branch.description = data.description
    branch.parentId = data.parent2 || data.parent1
    branch.createdBy = user.id
    await branch.save()

    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: BranchUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    data.level = 1
    if (data.parent2) {
      data.level = 3
    } else if (data.parent1) {
      data.level = 2
    }

    entity.name = data.name
    entity.description = data.description
    entity.type = data.type
    entity.parentId = data.parent2 || data.parent1
    if (entity.parentId == entity.id) throw new Error('Vui lòng chọn cấp 1 hoặc cấp 2 khác chi nhánh hiện tại!')
    entity.level = data.level
    entity.updatedBy = user.id
    await entity.save()

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { companyId: user.companyId }
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.parentId) whereCon.parentId = data.where.parentId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { parent: { parent: true } },
      order: { createdAt: 'DESC' },
    })

    for (const item of res[0]) {
      item.parent1 = null
      item.parent2 = null
      if (item.level == 2) {
        item.parent1 = item.parentId
        item.branchLv1Code = item.__parent__.code
      } else if (item.level == 3) {
        item.branchLv1Code = item.__parent__.__parent__.code
        item.branchLv2Code = item.__parent__.code
        item.parent1 = item.__parent__.parentId
        item.parent2 = item.parentId
      }

      delete item.__parent__
    }

    return res
  }

  public async updateIsDelete(data: { id: string }, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }
}
