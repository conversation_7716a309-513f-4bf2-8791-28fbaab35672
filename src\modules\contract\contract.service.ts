import { Injectable, MethodNotAllowedException, NotAcceptableException } from '@nestjs/common'
import * as moment from 'moment'
import { In, IsNull, Like, Not, Raw } from 'typeorm'
import { enumData, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import {
  ContractEntity,
  ContractHistoryEntity,
  ContractMemberEntity,
  EmailTemplateEntity,
  EmployeeWarningEntity,
  POHistoryEntity,
  PaymentProgressEntity,
  UserEntity,
} from '../../entities'
import { coreHelper } from '../../helpers'
import {
  AsnRepository,
  BidRepository,
  ContractRepository,
  EmployeeRepository,
  InvoiceSuggestRepository,
  ObjectRepository,
  PORepository,
  PaymentProgressRepository,
  ServiceRepository,
} from '../../repositories'
import { ContractCreate, ContractUpdate } from './dto'
import { use } from 'passport'

@Injectable()
export class ContractService {
  constructor(
    private readonly repo: ContractRepository,
    private readonly employeeRepo: EmployeeRepository,
    private readonly objectRepo: ObjectRepository,
    private readonly bidRepo: BidRepository,
    private readonly serviceRepo: ServiceRepository,
    private readonly invoiceSuggestRepo: InvoiceSuggestRepository,
    private readonly paymentProgressRepo: PaymentProgressRepository,
    private readonly poRepository: PORepository,
    private readonly asnRepository: AsnRepository,
  ) {}

  public async findDetail2(user: UserDto, data: { id: string }) {
    if (!data.id) throw new Error('Vui lòng chọn hợp đồng trước!')
    const res: any = await this.repo.findOne({
      where: { id: data.id, companyId: user.companyId },
      relations: {
        contractMembers: { employee: true },
        paymentPlan: true,
        bid: true,
        supplier: true,
        childs: { bid: true, supplier: true, pos: true, branch: true },
        parent: true,
      },
    })
    if (!res) throw new Error('Hợp đồng không tồn tại!')
    const lstEmployee: any = await this.employeeRepo.find({ where: { isDeleted: false } })
    const lstMember = res.__contractMembers__ || []
    res.anotherRoleIds = lstMember.filter((c: any) => c.contractRoleCode === enumData.ContractRoleCode.View.code).map((c: any) => c.employeeId)
    let arrTemp = []
    let lstView = res.anotherRoleIds
    lstEmployee.map((item) => {
      const newItem = lstView.find((c) => c === item.id)
      if (newItem) {
        arrTemp.push(item.name)
      }
    })

    res.listContractView = arrTemp
    const objManagement = lstMember.find((c: any) => c.contractRoleCode === enumData.ContractRoleCode.Management.code)
    if (objManagement) {
      res.manageContractId = objManagement.employeeId
      let manageContractName = lstEmployee.find((x) => x.id === objManagement.employeeId)
      if (manageContractName) {
        res.manageContractName = manageContractName?.name
      }
    }

    const objConfirm = lstMember.find((c: any) => c.contractRoleCode === enumData.ContractRoleCode.Confirm.code)
    if (objConfirm) {
      res.confirmContractId = objConfirm.employeeId
      let confirmContractName = lstEmployee.find((x) => x.id === objConfirm.employeeId)
      if (confirmContractName) {
        res.confirmContractName = confirmContractName?.name
      }
    }
    delete res.__contractMembers__

    res.lstPaymentProgress = res.__paymentPlan__ || []
    delete res.__paymentPlan__

    if (res.__bid__) {
      res.bidName = res.__bid__.name
      delete res.__bid__
    }
    if (res.__childs__) {
      res.child = res.__childs__
      const dicStatus: any = {}
      const dicStatusColor: any = {}
      {
        const lstContractStatus = coreHelper.convertObjToArray(enumData.ContractStatus)
        lstContractStatus.forEach((c) => (dicStatus[c.code] = c.name))
        lstContractStatus.forEach((c) => (dicStatusColor[c.code] = c.color))
      }

      for (let item of res.__childs__) {
        const invoiceByContract = await this.invoiceSuggestRepo.find({
          where: {
            contractId: item.id,
            companyId: user.companyId,
            isDeleted: false,
          },
        })

        if (invoiceByContract && invoiceByContract.length > 0) {
          item.isInvoice = true
        }

        if (item.__bid__) {
          item.bidName = item.__bid__.name
          delete item.__bid__
        }
        if (item.__branch__) {
          item.branchName = item.__branch__?.name
          delete item.__branch__
        }
        if (item.__supplier__) {
          item.supplierName = item.__supplier__.name
          delete item.__supplier__
        }
        item.statusName = dicStatus[item.status]
        item.statusColor = dicStatusColor[item.status]
      }
      delete res.__childs__
    }
    if (res.__supplier__) {
      res.supplierName = res.__supplier__.name
      delete res.__supplier__
    }
    res.parentName = res.parent?.name

    const lstContractStatus = coreHelper.convertObjToArray(enumData.ContractStatus)
    const objStatus = lstContractStatus.find((c) => c.code == res.status)
    res.statusName = objStatus?.name || ''
    res.statusColor = objStatus?.color || ''
    return res
  }

  public async findDetail(dataId: any, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    let result: any = await this.repo.findOne({
      where: { id: dataId, companyId: user.companyId },
      relations: { bid: true, supplier: true, pos: true, paymentPlan: true },
    })
    if (result) {
      let contractApproved = this.checkPermissionPOContractApproved(user, result.id)
      result.isAllowApprovedContract = (await contractApproved).hasPermission
      let poContractView = this.checkPermissionPOContractView(user, result.id)
      result.isAllowViewContract = (await poContractView).hasPermission
      let contractManagerView = this.checkPermissionPOContractManager(user, result.id)
      result.isAllowContractManagerView = (await contractManagerView).hasPermission
    }
    result.lstPaymentProgress = result.__paymentPlan__ || []
    const lstPO = await result.pos
    if (lstPO.length > 0) {
      let lstArrId = lstPO.map((x) => x.id)
      if (lstArrId && lstArrId.length > 0) {
        const lstAns = await this.asnRepository.find({
          where: {
            poId: In(lstArrId),
            isDeleted: false,
          },
        })
        result.lstAns = lstAns || []
      }
    } else {
      result.lstAns = []
    }

    delete result.__paymentPlan__
    return result
  }

  public async find(user: UserDto, data: { status?: string }) {
    const whereCon: any = { companyId: user.companyId, isDeleted: false }
    const employee = await this.employeeRepo.find({ where: { userId: user.id } })
    if (employee[0].branchId) {
      whereCon.branchId = employee[0].branchId
    }

    if (data.status) {
      if (typeof data.status == 'string') {
        whereCon.status = data.status
      } else {
        whereCon.status = In(data.status)
      }
    }
    const res: any[] = await this.repo.find({
      where: whereCon,
      relations: { supplier: true },
      order: { name: 'ASC' },
    })

    for (const item of res) {
      if (item.supplierId) {
        item.supplierName = item.__supplier__.name
        item.supplierBankName = item.__supplier__.bankname
        item.supplierBankNumber = item.__supplier__.bankNumber
      }
      delete item.__supplier__
    }

    return res
  }

  public async findMirror(user: UserDto, data: { status?: string }) {
    const whereCon: any = { companyId: user.companyId, isDeleted: false }

    whereCon.branchId = IsNull()

    const res: any[] = await this.repo.find({
      where: whereCon,
      relations: { supplier: true },
      order: { name: 'ASC' },
    })

    for (const item of res) {
      if (item.supplierId) {
        item.supplierName = item.__supplier__.name
        item.supplierBankName = item.__supplier__.bankname
        item.supplierBankNumber = item.__supplier__.bankNumber
      }
      delete item.__supplier__
    }

    return res
  }

  public async createData(data: ContractCreate, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const employee = await this.employeeRepo.findOne({
      where: { id: user.employeeId, companyId: user.companyId },
      relations: { branch: true },
    })
    const object = await this.objectRepo.findOne({ where: { id: data.objectId, companyId: user.companyId } })

    const bidService: any = await this.bidRepo.findOne({
      where: {
        id: data.bidId,
        companyId: user.companyId,
        isDeleted: false,
      },
      relations: { childs: true, pr: true },
      select: { id: true, code: true, childs: { id: true, serviceId: true } },
    })
    if (!bidService) throw new Error(ERROR_NOT_FOUND_DATA)
    const lstChild = bidService.__childs__ || []
    if (lstChild.length == 0) throw new Error(`Gói thầu [${bidService.code}] không có Item!`)

    const service: any = await this.serviceRepo.findOne({
      where: { id: lstChild[0].serviceId, companyId: user.companyId, isDeleted: false },
      relations: { parent: { parent: { parent: true } } },
    })
    if (!service) throw new Error(ERROR_NOT_FOUND_DATA)

    let serviceLevel1: any
    if (service.parentId && service.__parent__.parentId && service.__parent__.__parent__.parentId && service.__parent__.__parent__.__parent__) {
      serviceLevel1 = service.__parent__.__parent__.__parent__.code
    } else if (service.parentId && service.__parent__.parentId && service.__parent__.__parent__) {
      serviceLevel1 = service.__parent__.__parent__.code
    } else if (service.parentId && service.__parent__) {
      serviceLevel1 = service.__parent__.code
    } else {
      serviceLevel1 = service.code
    }
    let codeSC = '00'
    const contractLast = await this.repo.findOne({
      where: { code: Like(`%${codeSC}%`), companyId: user.companyId },
      order: { code: 'DESC' },
    })
    let sortString = '0'
    if (contractLast) {
      sortString = contractLast.code.substring(0, 4)
    }
    const lastSort = +sortString
    sortString = ('000' + (lastSort + 1)).slice(-4)
    let code = sortString // STT
    code += '/' + new Date().getFullYear().toString().slice(-2) // YY
    if (employee && (await employee.branch)) {
      code += '/' + (await employee.branch).code // XXX
    }
    if (object) {
      code += '.' + object.code // ZZZ
    }
    if (serviceLevel1) {
      code += '.' + serviceLevel1 // AAA
    }
    code += '-' + 'HĐ'

    const contract = new ContractEntity()
    const employeeCheck = await this.employeeRepo.findOne({ where: { userId: user.id } })

    if (employeeCheck && employeeCheck.branchId) {
      contract.isChild = true
    } else {
      contract.isChild = false
    }
    contract.companyId = user.companyId
    contract.createdBy = user.id
    contract.supplierId = data.supplierId
    contract.bidId = data.bidId
    contract.code = code
    contract.name = data.name
    contract.isGenChild = false
    if (data.contractMirrorId) {
      // contract.isChild = true
      contract.parentId = data.contractMirrorId
    }

    contract.fileAttach = data.fileAttach
    contract.description = data.description
    contract.effectiveDate = data.effectiveDate
    contract.expiredDate = data.expiredDate
    contract.status = enumData.ContractStatus.Open.code
    contract.objectId = data.objectId
    if (employee.branchId) contract.branchId = employee.branchId
    contract.value = data.value || 0
    const newEntity = await this.repo.save(contract)

    // người duyệt contract
    const contractConfirmMember = new ContractMemberEntity()
    contractConfirmMember.companyId = user.companyId
    contractConfirmMember.createdBy = user.id
    contractConfirmMember.employeeId = data.confirmContractId
    contractConfirmMember.contractRoleCode = enumData.ContractRoleCode.Confirm.code
    contractConfirmMember.description = enumData.ContractRoleCode.Confirm.description
    contractConfirmMember.contractId = newEntity.id
    await contractConfirmMember.save()

    const findContractConfirmMember = await this.employeeRepo.findOne({
      where: {
        id: data.confirmContractId,
        companyId: user.companyId,
        isDeleted: false,
      },
    })
    if (!findContractConfirmMember) {
      throw new Error(` Nhân viên duyệt không tồn tại. Vui lòng kiểm tra lại`)
    }

    const contractConfirmViewMember = new ContractMemberEntity()
    contractConfirmViewMember.companyId = user.companyId
    contractConfirmViewMember.createdBy = user.id
    contractConfirmViewMember.employeeId = data.confirmContractId
    contractConfirmViewMember.contractRoleCode = enumData.ContractRoleCode.View.code
    contractConfirmViewMember.description = enumData.ContractRoleCode.View.description
    contractConfirmViewMember.contractId = newEntity.id
    await contractConfirmViewMember.save()

    // người chỉnh sửa contract
    const contractEditMember = new ContractMemberEntity()
    contractEditMember.companyId = user.companyId
    contractEditMember.createdBy = user.id
    contractEditMember.employeeId = data.manageContractId
    contractEditMember.contractRoleCode = enumData.ContractRoleCode.Management.code
    contractEditMember.description = enumData.ContractRoleCode.Edit.description
    contractEditMember.contractId = newEntity.id
    await contractEditMember.save()

    const contractEditViewMember = new ContractMemberEntity()
    contractEditViewMember.companyId = user.companyId
    contractEditViewMember.createdBy = user.id
    contractEditViewMember.employeeId = data.manageContractId
    contractEditViewMember.contractRoleCode = enumData.ContractRoleCode.View.code
    contractEditViewMember.description = enumData.ContractRoleCode.View.description
    contractEditViewMember.contractId = newEntity.id
    await contractEditViewMember.save()

    // người xem contract
    for (const item of data.anotherRoleIds) {
      const contractMember = new ContractMemberEntity()
      contractMember.companyId = user.companyId
      contractMember.createdBy = user.id
      contractMember.employeeId = item
      contractMember.contractRoleCode = enumData.ContractRoleCode.View.code
      contractMember.description = enumData.ContractRoleCode.View.description
      contractMember.contractId = newEntity.id
      await contractMember.save()
    }

    //#region Thêm tiến độ thanh toán

    if (data.isPaymentProgress)
      for (const progress of data.lstPaymentProgress) {
        const new1 = new PaymentProgressEntity()
        new1.companyId = user.companyId
        new1.createdBy = user.id
        new1.name = progress.name
        new1.percent = +progress.percent
        new1.time = progress.time
        new1.description = progress.description
        new1.contractId = newEntity.id
        new1.money = (new1.percent * +newEntity.value) / 100
        await new1.save()
      }
    //#endregion

    const contractHistory = new ContractHistoryEntity()
    contractHistory.companyId = user.companyId
    contractHistory.createdBy = user.id
    contractHistory.contractId = newEntity.id
    contractHistory.employeeId = user.employeeId ? user.employeeId : ''
    contractHistory.statusConvert = enumData.ContractStatus.Open.code
    contractHistory.statusCurrent = enumData.ContractStatus.Open.code

    const description = `Nhân viên [${user.username}] - tạo mới hợp đồng có mã là : [${contract.code}]`
    contractHistory.description = description
    await contractHistory.save()

    return { message: 'Tạo Mới Hợp Đồng Thành Công', success: true }
  }

  public async updateData(data: ContractUpdate, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.code = data.code
    entity.name = data.name
    entity.objectId = data.objectId
    entity.bidId = data.bidId
    entity.supplierId = data.supplierId
    entity.fileAttach = data.fileAttach
    entity.expiredDate = data.expiredDate
    entity.effectiveDate = data.effectiveDate
    entity.value = data.value
    entity.description = data.description

    // Xóa ds nhân viên cũ trong HĐ
    await this.repo.manager.getRepository(ContractMemberEntity).delete({ contractId: entity.id })

    // người duyệt contract
    const contractConfirmMember = new ContractMemberEntity()
    contractConfirmMember.companyId = user.companyId
    contractConfirmMember.createdBy = user.id
    contractConfirmMember.employeeId = data.confirmContractId
    contractConfirmMember.contractRoleCode = enumData.ContractRoleCode.Confirm.code
    contractConfirmMember.description = enumData.ContractRoleCode.Confirm.description
    contractConfirmMember.contractId = entity.id
    await contractConfirmMember.save()

    const findContractConfirmMember = await this.employeeRepo.findOne({
      where: {
        id: data.confirmContractId,
        companyId: user.companyId,
        isDeleted: false,
      },
    })
    if (!findContractConfirmMember) {
      throw new Error(` Nhân viên duyệt không tồn tại. Vui lòng kiểm tra lại`)
    }

    const contractConfirmViewMember = new ContractMemberEntity()
    contractConfirmViewMember.companyId = user.companyId
    contractConfirmViewMember.createdBy = user.id
    contractConfirmViewMember.employeeId = data.confirmContractId
    contractConfirmViewMember.contractRoleCode = enumData.ContractRoleCode.View.code
    contractConfirmViewMember.description = enumData.ContractRoleCode.View.description
    contractConfirmViewMember.contractId = entity.id
    await contractConfirmViewMember.save()

    // người chỉnh sửa contract
    const contractEditMember = new ContractMemberEntity()
    contractEditMember.companyId = user.companyId
    contractEditMember.createdBy = user.id
    contractEditMember.employeeId = data.manageContractId
    contractEditMember.contractRoleCode = enumData.ContractRoleCode.Management.code
    contractEditMember.description = enumData.ContractRoleCode.Edit.description
    contractEditMember.contractId = entity.id
    await contractEditMember.save()

    const contractEditViewMember = new ContractMemberEntity()
    contractEditViewMember.companyId = user.companyId
    contractEditViewMember.createdBy = user.id
    contractEditViewMember.employeeId = data.manageContractId
    contractEditViewMember.contractRoleCode = enumData.ContractRoleCode.View.code
    contractEditViewMember.description = enumData.ContractRoleCode.View.description
    contractEditViewMember.contractId = entity.id
    await contractEditViewMember.save()

    // người xem contract
    for (const item of data.anotherRoleIds) {
      const contractMember = new ContractMemberEntity()
      contractMember.companyId = user.companyId
      contractMember.createdBy = user.id
      contractMember.employeeId = item
      contractMember.contractRoleCode = enumData.ContractRoleCode.View.code
      contractMember.description = enumData.ContractRoleCode.View.description
      contractMember.contractId = entity.id
      await contractMember.save()
    }

    //#region Thêm tiến độ thanh toán
    if (data.isChangePaymentProgress) {
      // Xóa tiến độ thanh toán cũ
      await this.paymentProgressRepo.delete({ contractId: entity.id })
      for (const progress of data.lstPaymentProgress) {
        const new1 = new PaymentProgressEntity()
        new1.companyId = user.companyId
        new1.createdBy = user.id
        new1.name = progress.name
        new1.percent = +progress.percent
        new1.time = progress.time
        new1.description = progress.description
        new1.contractId = entity.id
        new1.money = (new1.percent * +entity.value) / 100
        await this.paymentProgressRepo.save(new1)
      }
    }
    //#endregion

    //#region Lịch sử HĐ
    const contractHistory = new ContractHistoryEntity()
    contractHistory.companyId = user.companyId
    contractHistory.createdBy = user.id
    contractHistory.contractId = entity.id
    contractHistory.employeeId = user.employeeId ? user.employeeId : ''
    contractHistory.statusConvert = enumData.ContractStatus.Open.code
    contractHistory.statusCurrent = enumData.ContractStatus.Open.code

    const description = `Nhân viên [${user.username}] - chỉnh sửa thông tin hợp đồng có mã là : [${entity.code}]`
    contractHistory.description = description
    await contractHistory.save()
    //#endregion

    entity.updatedBy = user.id
    await this.repo.save(entity)

    return { message: 'Hợp đồng đã được chỉnh sửa thành công.', success: true }
  }

  public async pagination(data: PaginationDto, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const whereCon: any = { companyId: user.companyId, isDeleted: false }

    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.supplierId) whereCon.supplierId = data.where.supplierId
    if (data.where.bidId) whereCon.bidId = data.where.bidId
    if (data.where.status) whereCon.status = data.where.status

    if (data.where.createdAtFrom && data.where.createdAtTo) {
      whereCon.createdAt = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.createdAtFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.createdAtTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    }
    if (data.where.effectiveDateFrom && data.where.effectiveDateTo) {
      whereCon.effectiveDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.effectiveDateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(
            data.where.effectiveDateTo,
          ).format('YYYY-MM-DD')}")`,
      )
    }
    if (data.where.expiredDateFrom && data.where.expiredDateTo) {
      whereCon.expiredDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.expiredDateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(
            data.where.expiredDateTo,
          ).format('YYYY-MM-DD')}")`,
      )
    }

    // const lstBranch = await this.employeeRepo.getListBranchView(user)
    // if (lstBranch?.length > 0) {
    //   whereCon.branchId = In(lstBranch)
    // }

    const result: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { bid: true, supplier: true, pos: true },
      order: { createdAt: 'DESC' },
    })

    const dicStatus: any = {}
    const dicStatusColor: any = {}
    {
      const lstContractStatus = coreHelper.convertObjToArray(enumData.ContractStatus)
      lstContractStatus.forEach((c) => (dicStatus[c.code] = c.name))
      lstContractStatus.forEach((c) => (dicStatusColor[c.code] = c.color))
    }

    for (let item of result[0]) {
      const invoiceByContract = await this.invoiceSuggestRepo.find({
        where: {
          contractId: item.id,
          companyId: user.companyId,
          isDeleted: false,
        },
      })

      if (invoiceByContract && invoiceByContract.length > 0) {
        item.isInvoice = true
      }

      if (item.__bid__) {
        item.bidName = item.__bid__.name
        delete item.__bid__
      }
      if (item.__supplier__) {
        item.supplierName = item.__supplier__.name
        delete item.__supplier__
      }
      item.statusName = dicStatus[item.status]
      item.statusColor = dicStatusColor[item.status]
    }

    for (const item of result[0]) {
      const contractApproved = await this.checkPermissionPOContractApproved(user, item.id)
      item.isAllowApprovedContract = contractApproved.hasPermission
      const poContractView = await this.checkPermissionPOContractView(user, item.id)
      item.isAllowViewContract = poContractView.hasPermission
      const contractManagerView = await this.checkPermissionPOContractManager(user, item.id)
      item.isAllowContractManagerView = contractManagerView.hasPermission
    }
    return result
  }

  public async updateStatus(data: { id: string; reason?: string }, statusConvert: any, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status === enumData.ContractStatus.Complete.code) {
      throw new Error(`Hợp Đồng đang ở trạng thái [${enumData.ContractStatus.Complete.name}] không thể cập nhật tiếp !`)
    }
    if (entity.status === enumData.ContractStatus.Cancel.code) {
      throw new Error(`Hợp Đồng đang ở trạng thái [${enumData.ContractStatus.Cancel.name}] không thể cập nhật tiếp !`)
    }
    if (entity.status !== enumData.ContractStatus.Processing.code) {
      const objPermission = await this.checkPermissionPOContractApproved(user, data.id)
      if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    } else {
      const objPermission = await this.checkPermissionPOContractManager(user, data.id)
      if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    }

    let lstStatusCurrent = coreHelper.convertObjToArray(enumData.ContractStatus)
    const statusCurrent = lstStatusCurrent.find((s) => s.code === entity.status)

    if (statusConvert.code === enumData.ContractStatus.Cancel.code && data.reason) {
      entity.reason = data.reason

      const lstPO = await this.poRepository.find({ where: { contractId: entity.id, isDeleted: false } })
      if (lstPO && lstPO.length > 0) {
        const lstStatus = [
          enumData.PurchaseOrderStatus.Approved.code,
          enumData.PurchaseOrderStatus.Confirm.code,
          enumData.PurchaseOrderStatus.Delivery.code,
          enumData.PurchaseOrderStatus.DeliveryRefuse.code,
          enumData.PurchaseOrderStatus.Complete.code,
        ]
        const checkPO = lstPO.some((r) => lstStatus.includes(r.status))
        if (checkPO) {
          throw new Error(
            `Không thể hủy hợp đồng do các PO liên quan đang ở trạng thái khác mới [${enumData.PurchaseOrderStatus.Open.code}] và [${enumData.PurchaseOrderStatus.Cancel.code}]. Vui lòng kiểm tra lại PO liên quan`,
          )
        } else {
          const lstPOCancel = lstPO.filter((x) => x.status === enumData.PurchaseOrderStatus.Open.code)
          if (lstPOCancel && lstPOCancel.length > 0) {
            for (let item of lstPOCancel) {
              item.status = enumData.PurchaseOrderStatus.Cancel.code
              item.updatedAt = new Date()
              item.updatedBy = user.id
              await this.poRepository.save(item)
              const historyObj = new POHistoryEntity()
              historyObj.companyId = user.companyId
              historyObj.createdBy = user.id
              historyObj.poId = item.id
              historyObj.statusConvert = enumData.PurchaseOrderStatus.Open.code
              historyObj.statusCurrent = enumData.PurchaseOrderStatus.Cancel.code
              historyObj.description = `Nhân viên [${user.username}] - Vừa Hủy PO có mã là [${item.code}] - PO bị hủy vì hợp đồng liên quan PO đã bị hủy`
              historyObj.employeeId = user.employeeId
              await historyObj.save()
            }
          }
        }
      }
    }
    entity.status = statusConvert.code

    const poContractHistory = new ContractHistoryEntity()
    poContractHistory.companyId = user.companyId
    poContractHistory.createdBy = user.id
    poContractHistory.contractId = entity.id
    poContractHistory.employeeId = user.employeeId ? user.employeeId : ''

    let lstStatus = coreHelper.convertObjToArray(enumData.ContractStatus)
    const statusCurrentName = lstStatus.find((s) => s.code === statusCurrent.code)
    const statusname = lstStatus.find((s) => s.code === entity.status)
    poContractHistory.statusCurrent = statusCurrent.code
    poContractHistory.statusConvert = entity.status

    let description = `Nhân viên [${user.username}] - cập nhật trạng thái hợp đồng có mã [${entity.code}] - Trạng thái cũ [${statusCurrentName.name}] - Trạng thái mới [${statusname.name}]`
    if (entity.status === enumData.ContractStatus.Cancel.code && data.reason) {
      description = `${description} - Lý do: ${data.reason}`
    }
    poContractHistory.description = description
    await poContractHistory.save()

    entity.updatedBy = user.id
    await entity.save()

    return { message: 'Cập nhật trạng thái hợp đồng thành công' }
  }

  /** Kiểm tra quyền duyệt hợp đồng */
  async checkPermissionPOContractApproved(user: UserDto, contractId: string) {
    const objContract = await this.repo.findOne({ where: { id: contractId, companyId: user.companyId } })
    if (objContract) {
      const objCheck = await this.repo.manager.getRepository(ContractMemberEntity).findOne({
        where: {
          employeeId: user.employeeId,
          contractId,
          companyId: user.companyId,
          contractRoleCode: In([enumData.PORoleCode.Confirm.code]),
        },
      })
      if (!objCheck) return { hasPermission: false, message: 'Bạn không có quyền duyệt hợp đồng.' }
      return { hasPermission: true, message: '' }
    }
    return { hasPermission: false, message: '' }
  }

  /** Kiểm tra quyền xem thông tin hợp đồng */
  async checkPermissionPOContractView(user: UserDto, contractId: string) {
    const objContract = await this.repo.findOne({ where: { id: contractId, companyId: user.companyId } })
    if (objContract) {
      const objCheck = await this.repo.manager.getRepository(ContractMemberEntity).findOne({
        where: {
          employeeId: user.employeeId,
          contractId,
          companyId: user.companyId,
          contractRoleCode: enumData.ContractRoleCode.View.code,
        },
      })
      if (!objCheck) return { hasPermission: false, message: 'Bạn không có quyền xem thông tin PO.' }
      return { hasPermission: true, message: '' }
    }
    return { hasPermission: false, message: '' }
  }

  /** Kiểm tra quyền quản lý thông tin hợp đồng */
  async checkPermissionPOContractManager(user: UserDto, contractId: string) {
    const objContract = await this.repo.findOne({ where: { id: contractId, companyId: user.companyId } })
    if (objContract) {
      const objCheck = await this.repo.manager.getRepository(ContractMemberEntity).findOne({
        where: {
          employeeId: user.employeeId,
          contractId,
          companyId: user.companyId,
          contractRoleCode: enumData.ContractRoleCode.Management.code,
        },
      })
      if (!objCheck) return { hasPermission: false, message: 'Bạn không có quyền quản lý hợp đồng.' }
      return { hasPermission: true, message: '' }
    }
    return { hasPermission: false, message: '' }
  }

  /** Lịch sử HĐ */
  public async paginationHistory(user: UserDto, data: PaginationDto) {
    let whereCon: any = { companyId: user.companyId }
    if (data.where.contractId) whereCon.contractId = data.where.contractId

    const res: any[] = await this.repo.manager.getRepository(ContractHistoryEntity).findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { contract: true, employee: true },
      order: { createdAt: 'DESC' },
    })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.ContractStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }
    for (const item of res[0]) {
      item.contractCode = ''
      item.employeeName = ''
      if (item.contractId) item.contractCode = item.__contract__.code
      if (item.employeeId) item.employeeName = item.__employee__.name
      item.statusCurrentName = dicStatus[item.statusCurrent]
      item.statusConvertName = dicStatus[item.statusConvert]

      delete item.__contract__
      delete item.__employee__
    }

    return res
  }

  /** Cảnh báo khi gần hết hạn hợp đồng */
  public async autoCreateWarningComingExpiry() {
    return this.repo.manager.transaction(async (manager) => {
      try {
        const dataType = enumData.DataWarningType.Contract.code
        const warningType = enumData.WarningType.Contract_Coming_Expiry
        await manager.getRepository(EmployeeWarningEntity).delete({
          dataId: Not(IsNull()),
          dataType: dataType,
          warningType: warningType.code,
        })

        const dFrom = new Date(new Date().setHours(0, 0, 0, 0))
        let dTo = new Date(new Date().setDate(new Date().getDate() + 7))
        dTo = new Date(new Date(dTo).setHours(23, 59, 59, 59))

        const lstContract = await manager.getRepository(ContractEntity).find({
          where: {
            isDeleted: false,
            status: In([enumData.ContractStatus.Open.code, enumData.ContractStatus.Processing.code]),
            expiredDate: Raw(
              (alias) =>
                `DATE(${alias}) BETWEEN DATE("${moment(dFrom).format('YYYY-MM-DD HH:MM:SS')}") AND DATE("${moment(dTo).format(
                  'YYYY-MM-DD HH:MM:SS',
                )}")`,
            ),
          },
        })
        if (lstContract.length > 0) {
          let html = warningType.default
          let subject = warningType.name
          const template = await manager.getRepository(EmailTemplateEntity).findOne({ where: { code: warningType.code, isDeleted: false } })
          if (template) {
            html = template.description
            subject = template.name
          }

          let lstUserId = lstContract.map((s: any) => s.createdBy)
          lstUserId = Array.from(new Set(lstUserId))
          const lstUser = await manager.getRepository(UserEntity).find({
            where: { id: In(lstUserId), isDeleted: false },
            relations: ['employee'],
          })

          for await (const contract of lstContract) {
            let createdBy = lstUser.find((s: any) => s.id == contract.createdBy)
            if (createdBy && createdBy.employeeId) {
              let emp = await createdBy.employee

              const subject_text = coreHelper.stringInject(subject, [contract.code])
              // let link = `&nbsp; <button onclick="showDataDetail('${dataType}', '${pp.id}')">Xem Chi Tiết</button>`

              let content = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [emp.name, contract.name, contract.code])

              const w1 = new EmployeeWarningEntity()
              w1.warningType = warningType.code
              w1.dataType = dataType
              w1.dataId = contract.id
              w1.message = subject_text || ''
              w1.messageFull = content || ''
              w1.employeeId = createdBy.employeeId
              await manager.getRepository(EmployeeWarningEntity).save(w1)
            }
          }
        }
      } catch (error) {
        throw error
      }
    })
  }

  /** Cảnh báo khi hết hạn thanh toán */
  public async autoCreateWarningExpiryPayment() {
    return this.repo.manager.transaction(async (manager) => {
      try {
        const dataType = enumData.DataWarningType.Contract.code
        const warningType = enumData.WarningType.Contract_Expiry_Payment
        await manager.getRepository(EmployeeWarningEntity).delete({
          dataId: Not(IsNull()),
          dataType: dataType,
          warningType: warningType.code,
        })
        const dFrom = new Date(new Date().setHours(0, 0, 0, 0))

        const lstContract = await manager.getRepository(ContractEntity).find({
          where: {
            isDeleted: false,
            status: In([enumData.ContractStatus.Open.code, enumData.ContractStatus.Processing.code]),
          },
        })
        if (lstContract.length > 0) {
          let lstContractId = lstContract.map((s: any) => s.id)
          lstContractId = Array.from(new Set(lstContractId))
          const lstProgress = await manager.getRepository(PaymentProgressEntity).find({
            where: {
              isDeleted: false,
              contractId: In(lstContractId),
              paymentStatus: Not(enumData.PaymentProgressStatus.Paid.code),
              time: Raw((alias) => `DATE(${alias}) <= DATE("${moment(dFrom).format('YYYY-MM-DD')}")`),
            },
          })
          if (lstProgress.length > 0) {
            let lstExpiryId = lstProgress.map((s: any) => s.contractId)
            lstExpiryId = Array.from(new Set(lstExpiryId))

            let lstExpiry = lstContract.filter((s: any) => lstExpiryId.includes(s.id))

            let html = warningType.default
            let subject = warningType.name
            const template = await manager.getRepository(EmailTemplateEntity).findOne({ where: { code: warningType.code, isDeleted: false } })
            if (template) {
              html = template.description
              subject = template.name
            }

            let lstUserId = lstExpiry.map((s: any) => s.createdBy)
            lstUserId = Array.from(new Set(lstUserId))
            const lstUser = await manager.getRepository(UserEntity).find({
              where: { id: In(lstUserId), isDeleted: false },
              relations: ['employee'],
            })

            for await (const contract of lstExpiry) {
              let createdBy = lstUser.find((s: any) => s.id == contract.createdBy)
              if (createdBy && createdBy.employeeId) {
                let emp = await createdBy.employee

                const subject_text = coreHelper.stringInject(subject, [contract.code])
                // let link = `&nbsp; <button onclick="showDataDetail('${dataType}', '${pp.id}')">Xem Chi Tiết</button>`

                let content = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [emp.name, contract.name, contract.code])

                const w1 = new EmployeeWarningEntity()
                w1.warningType = warningType.code
                w1.dataType = dataType
                w1.dataId = contract.id
                w1.message = subject_text || ''
                w1.messageFull = content || ''
                w1.employeeId = createdBy.employeeId
                await manager.getRepository(EmployeeWarningEntity).save(w1)
              }
            }
          }
        }
      } catch (error) {
        throw error
      }
    })
  }

  public async findContractClient(user: UserDto, data: { status?: string }) {
    const whereCon: any = { isDeleted: false }

    if (data.status) {
      if (typeof data.status == 'string') {
        whereCon.status = data.status
      } else {
        whereCon.status = In(data.status)
      }
    }
    const res: any[] = await this.repo.find({
      where: whereCon,
      order: { name: 'ASC' },
    })

    return res
  }

  public async loadContractBySupplier(user: UserDto, data: { status?: any }) {
    if (!user.supplierId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    // let whereCon: any = { isDeleted: false, supplierId: user.supplierId }
    let whereCon: any = { isDeleted: false }

    if (data.status) {
      whereCon.status = data.status
    }
    const res: any[] = await this.repo.find({
      where: whereCon,
    })
    return res
  }
}
