import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsString } from 'class-validator'

export class AsnCreateDto {
  @ApiProperty()
  code: string

  @ApiPropertyOptional()
  poId: string

  @ApiPropertyOptional()
  branchId: string

  @ApiPropertyOptional()
  warehouseId: string

  @ApiPropertyOptional()
  asnDate: Date

  @ApiPropertyOptional()
  quantity: number

  @ApiPropertyOptional()
  purchasePlanId: string

  @ApiPropertyOptional()
  description: string

  @ApiPropertyOptional()
  objectId: string

  @ApiPropertyOptional()
  __details__: AsnItemDto[]

  @ApiPropertyOptional()
  serviceLevel1: string
}

export class AsnItemDto {
  @ApiProperty()
  @IsString()
  poId: string

  @ApiPropertyOptional()
  asnId: string

  @ApiPropertyOptional()
  serviceId: string

  @ApiPropertyOptional()
  quantity: number

  @ApiPropertyOptional()
  quantityInbound: number

  @ApiPropertyOptional()
  description: string

  @ApiPropertyOptional()
  itemCode: string
}
