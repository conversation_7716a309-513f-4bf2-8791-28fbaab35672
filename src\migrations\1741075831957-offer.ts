import { MigrationInterface, QueryRunner } from "typeorm";

export class offer1741075831957 implements MigrationInterface {
    name = 'offer1741075831957'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`nameCol\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`nameCol\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`price\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`offerPriceName\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`offerPriceLevel\` int NULL DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`offerId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`serviceId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`supplierId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`submitDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`submitType\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`number\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`unitPrice\` bigint NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`code\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`fomular\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`isRequired\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`sort\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`name\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`description\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`unit\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`currency\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`percent\` float NULL DEFAULT '100'`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`level\` int NULL DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`parentId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`type\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`colType\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`offerItemId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`scoreDLC\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`requiredMin\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`number\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`isSetup\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`isTemplate\` tinyint NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`itemNo\` bigint NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`materialGroupName\` varchar(100) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`categoryName\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`category\` varchar(1) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`shortText\` varchar(1000) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`quantity\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`status\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`unitCode\` varchar(10) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`prItemId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`percentTech\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`percentTrade\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`percentPrice\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`scoreDLC\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`fomular\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`isExGr\` tinyint NOT NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`shipmentPriceId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`conditionType\` varchar(150) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`description\` varchar(4000) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`amount\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`crcy\` varchar(150) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`per\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`conditionValue\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`curr\` varchar(150) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`cConDe\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`numCCo\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD \`deliveryDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`title\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`effectiveDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`endDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`currency\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`timePeriod\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`condition\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`scoreDLC\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`address\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`isHaveVat\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`isCompleteAll\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`isNotConfigTrade\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`isGetFromPr\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`description\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`refType\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`isLoadFromItem\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`publicDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`statusRateTech\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`statusRateTrade\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`statusRatePrice\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`statusResetPrice\` varchar(50) NULL DEFAULT 'ChuaTao'`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`isRequestDelete\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`noteRequestDelete\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`fileScan\` varchar(500) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`noteFinishBidMPO\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`statusTrade\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`resetPriceEndDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`statusPrice\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`isRequireFileTechDetail\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`isRequireFilePriceDetail\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`wayCalScorePrice\` varchar(50) NULL DEFAULT 'SumScore'`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`fomular\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`statusChooseSupplier\` varchar(50) NULL DEFAULT 'ChuaChon'`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`noteTrade\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`fileAttach\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`notePrice\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`approveChooseSupplierWinDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`bidCloseDate\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`noteTechLeader\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`noteMPOLeader\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`shipmentId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` ADD \`offerTypeCode\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`isSuccessBid\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`scoreTech\` float NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`scorePrice\` float NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`scoreTrade\` float NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`scoreManualTech\` float NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`scoreManualPrice\` float NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`scoreManualTrade\` float NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`parentId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`statusFile\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`statusTech\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`statusTrade\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`isPriceValid\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`isJoin\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`statusResetPrice\` varchar(50) NOT NULL DEFAULT 'KhongYeuCau'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`noteMPOLeader\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`noteTrade\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`isTradeValid\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`status\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`notePrice\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`statusPrice\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`fileAttach\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`fileTech\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`filePrice\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`linkDrive\` varchar(400) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD \`note\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_custom_price\` CHANGE \`isRequired\` \`isRequired\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` CHANGE \`serviceName\` \`serviceName\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` CHANGE \`dateStart\` \`dateStart\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` CHANGE \`dateEnd\` \`dateEnd\` datetime NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD CONSTRAINT \`FK_bb4bacf4af6f704d19e6f4c4edb\` FOREIGN KEY (\`parentId\`) REFERENCES \`offer_price\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD CONSTRAINT \`FK_f9fe2bd7a8fb496d41d4bb20282\` FOREIGN KEY (\`offerItemId\`) REFERENCES \`offer_service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` ADD CONSTRAINT \`FK_929cb4b9994dfaba179b6f8ae88\` FOREIGN KEY (\`prItemId\`) REFERENCES \`pr_item\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` ADD CONSTRAINT \`FK_d19b3ad6ea4d242e224e79d1a81\` FOREIGN KEY (\`parentId\`) REFERENCES \`offer_supplier\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP FOREIGN KEY \`FK_d19b3ad6ea4d242e224e79d1a81\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP FOREIGN KEY \`FK_929cb4b9994dfaba179b6f8ae88\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP FOREIGN KEY \`FK_f9fe2bd7a8fb496d41d4bb20282\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP FOREIGN KEY \`FK_bb4bacf4af6f704d19e6f4c4edb\``);
        await queryRunner.query(`ALTER TABLE \`offer\` CHANGE \`dateEnd\` \`dateEnd\` datetime NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`offer\` CHANGE \`dateStart\` \`dateStart\` datetime NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_service\` CHANGE \`serviceName\` \`serviceName\` varchar(250) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_custom_price\` CHANGE \`isRequired\` \`isRequired\` tinyint NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`note\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`linkDrive\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`filePrice\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`fileTech\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`fileAttach\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`statusPrice\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`notePrice\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`status\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`isTradeValid\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`noteTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`noteMPOLeader\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`statusResetPrice\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`isJoin\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`isPriceValid\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`statusTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`statusTech\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`statusFile\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`parentId\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`scoreManualTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`scoreManualPrice\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`scoreManualTech\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`scoreTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`scorePrice\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`scoreTech\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier\` DROP COLUMN \`isSuccessBid\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`offerTypeCode\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`shipmentId\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`noteMPOLeader\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`noteTechLeader\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`bidCloseDate\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`approveChooseSupplierWinDate\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`notePrice\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`fileAttach\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`noteTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`statusChooseSupplier\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`fomular\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`wayCalScorePrice\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`isRequireFilePriceDetail\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`isRequireFileTechDetail\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`statusPrice\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`resetPriceEndDate\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`statusTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`noteFinishBidMPO\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`fileScan\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`noteRequestDelete\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`isRequestDelete\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`statusResetPrice\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`statusRatePrice\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`statusRateTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`statusRateTech\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`publicDate\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`isLoadFromItem\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`refType\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`description\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`isGetFromPr\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`isNotConfigTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`isCompleteAll\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`isHaveVat\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`address\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`scoreDLC\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`condition\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`timePeriod\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`currency\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`endDate\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`effectiveDate\``);
        await queryRunner.query(`ALTER TABLE \`offer\` DROP COLUMN \`title\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`deliveryDate\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`numCCo\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`cConDe\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`curr\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`conditionValue\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`per\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`crcy\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`amount\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`description\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`conditionType\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`shipmentPriceId\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`isExGr\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`fomular\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`scoreDLC\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`percentPrice\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`percentTrade\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`percentTech\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`prItemId\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`unitCode\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`status\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`quantity\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`shortText\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`category\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`categoryName\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`materialGroupName\``);
        await queryRunner.query(`ALTER TABLE \`offer_service\` DROP COLUMN \`itemNo\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`isTemplate\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`isSetup\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`number\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`requiredMin\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`scoreDLC\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`offerItemId\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`colType\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`type\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`parentId\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`level\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`percent\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`currency\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`unit\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`description\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`name\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`sort\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`isRequired\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`fomular\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` DROP COLUMN \`code\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`unitPrice\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`number\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`submitType\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`submitDate\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`supplierId\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`serviceId\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`offerId\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`offerPriceLevel\``);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` DROP COLUMN \`offerPriceName\``);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`price\` bigint NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_price\` ADD \`nameCol\` varchar(250) NULL`);
        await queryRunner.query(`ALTER TABLE \`offer_supplier_price\` ADD \`nameCol\` varchar(250) NULL`);
    }

}
