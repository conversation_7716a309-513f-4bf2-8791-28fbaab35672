import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { EmployeeWarningRepository } from '../../repositories'
import { EmployeeWarningService } from './employeeWarning.service'
import { EmployeeWarningController } from './employeeWarning.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([EmployeeWarningRepository])],
  controllers: [EmployeeWarningController],
  providers: [EmployeeWarningService],
})
export class EmployeeWarningModule {}
