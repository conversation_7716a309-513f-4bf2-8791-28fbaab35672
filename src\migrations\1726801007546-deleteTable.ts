import { MigrationInterface, QueryRunner } from 'typeorm'

export class deleteTable1726801007546 implements MigrationInterface {
  name = 'deleteTable1726801007546'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE \`ward\``)
    await queryRunner.query(`DROP TABLE \`district\``)
    await queryRunner.query(`DROP TABLE \`city\``)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
