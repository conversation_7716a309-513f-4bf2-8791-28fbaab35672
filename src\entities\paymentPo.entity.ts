import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, JoinColum<PERSON>, ManyToOne } from 'typeorm'
import { POEntity } from './po.entity'
import { PaymentEntity } from './payment.entity'

@Entity('payment_po')
export class PaymentPoEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.paymentPos)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  paymentId: string
  @ManyToOne(() => PaymentEntity, (p) => p.paymentPos)
  @JoinColumn({ name: 'paymentId', referencedColumnName: 'id' })
  payment: Promise<PaymentEntity>
}
