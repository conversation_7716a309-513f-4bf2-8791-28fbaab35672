import { Repository } from 'typeorm'
import { UserDto } from '../dto'
import { EmployeeEntity, BranchEntity } from '../entities'
import { CustomRepository } from '../typeorm'

@CustomRepository(EmployeeEntity)
export class EmployeeRepository extends Repository<EmployeeEntity> {
  // Lấy danh sách các chi nhánh mà nhân viên có quyền
  async getListBranchView(user: UserDto) {
    // nếu k truyền employeeId thì trả về []
    if (!user.employeeId) return []

    const employee = await this.findOne({ where: { id: user.employeeId, companyId: user.companyId } })
    // nếu truyền employeeId k tồn tại thì trả về []
    if (!employee) return []

    const branch = await employee.branch
    // nếu employee chưa xác định chi nhánh thì trả về []
    if (!branch) return []
    const branchMember = await branch.branchMembers

    // nếu chưa setup branchMember thì trả về []
    if (branchMember?.length == 0) return []

    // nếu employ k có trong branchMember thì trả về []
    if (!branchMember.some((c) => c.employeeId == user.employeeId)) return []

    // hàm local lấy tất cả chi nhánh cấp dưới
    const getListBrachChild = async (branch: BranchEntity) => {
      const res: BranchEntity[] = []
      const lstBranchChild = await branch.childs
      if (lstBranchChild.length == 0) return res
      res.push(...lstBranchChild)
      for (const child of lstBranchChild) {
        const lstBranchChild2 = await getListBrachChild(child)
        res.push(...lstBranchChild2)
      }
      return res
    }

    const lstBranchId = []
    lstBranchId.push(branch.id)
    const lstBranchChild = await getListBrachChild(branch)
    if (lstBranchChild.length > 0) {
      const lstBranchChildId = lstBranchChild.map((c) => c.id)
      lstBranchId.push(...lstBranchChildId)
    }
    return lstBranchId
  }
}
