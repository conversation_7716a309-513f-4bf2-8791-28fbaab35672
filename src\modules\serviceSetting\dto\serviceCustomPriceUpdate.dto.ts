import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsBoolean, IsNumber } from 'class-validator'

export class ServiceCustomPriceUpdateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiPropertyOptional()
  sort: number

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isRequired: boolean

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  type: string

  @ApiPropertyOptional()
  unit: string
  @ApiPropertyOptional()
  currency: string
  @ApiPropertyOptional()
  number: number
  // percent: number
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  serviceId: string
}
