import { Repository, In } from 'typeorm'
import {
  EmployeeEntity,
  BidEntity,
  BidHistoryEntity,
  BidTypeEntity,
  BidAuctionEntity,
  BidCustomPriceEntity,
  BidDealEntity,
  BidDealSupplierEntity,
  BidSupplierEntity,
  BidPriceEntity,
} from '../entities'
import { enumData } from '../constants/enumData'
import { CustomRepository } from '../typeorm'
import { UserDto } from '../dto'
import { BidItemEntity } from '../entities/bidItem.entity'

@CustomRepository(BidEntity)
export class BidRepository extends Repository<BidEntity> {
  //#region get data
  async getBid1(user: UserDto, bidId: string, isGetListAccess = false) {
    const res: any = await this.findOne({
      where: { id: bidId, companyId: user.companyId },
      relations: { employeeAccess: true, childs: { service: true }, bidItems: true },
    })
    if (!res) throw new Error('<PERSON><PERSON><PERSON> thầu không còn tồn tại')
    if (res.isDeleted) throw new Error('<PERSON><PERSON><PERSON> thầu đã ngưng hoạt động')

    //#region PERMISSION
    const lstAccess = res.__employeeAccess__ || []
    if (isGetListAccess && lstAccess.length > 0) {
      res.listAccess = []
      const lstEmployeeId = lstAccess.map((c) => c.employeeId).filter((value, index, self) => self.indexOf(value) === index)
      const lstEmployee: any[] = await this.manager.getRepository(EmployeeEntity).find({
        where: { id: In(lstEmployeeId), companyId: user.companyId },
        relations: { department: true },
        select: { id: true, name: true, email: true, department: { name: true } },
      })
      for (const access of lstEmployee) {
        res.listAccess.push({
          employeeName: access.name,
          employeeEmail: access.email,
          departmentName: access.__department__.name,
        })
        delete access.__department__
      }
    }

    const ruleType = enumData.BidRuleType
    res.isMPO = lstAccess.some((p) => p.employeeId == user.employeeId && p.type === ruleType.MPO.code)
    res.isMPOLeader = lstAccess.some((p) => p.employeeId == user.employeeId && p.type === ruleType.MPOLeader.code)
    res.isTech = lstAccess.some((p) => p.employeeId == user.employeeId && p.type === ruleType.Tech.code)
    res.isTechLeader = lstAccess.some((p) => p.employeeId == user.employeeId && p.type === ruleType.TechLeader.code)
    res.isMember = lstAccess.some((p) => p.employeeId == user.employeeId && p.type === ruleType.Memmber.code)
    delete res.__employeeAccess__

    res.isTech = res.isTech || res.isMPO
    res.isTechLeader = res.isTechLeader || res.isMPOLeader
    res.isShowCreateTech = res.statusTech === enumData.BidTechStatus.DangTao.code && res.isTech
    res.isShowAcceptTech = res.statusTech === enumData.BidTechStatus.DaTao.code && res.isTechLeader
    res.isShowCreateTrade = res.statusTrade === enumData.BidTradeStatus.DangTao.code && res.isMPO
    res.isShowCreatePrice = res.statusPrice === enumData.BidPriceStatus.DangTao.code && res.isMPO

    //#endregion

    res.listItem = res.__childs__ || []
    for (const item of res.listItem) {
      item.itemName = item.__service__?.code + ' - ' + item.__service__?.name
    }
    delete res.__childs__

    return res
  }

  /**
   * Lấy danh sách Item của gói thầu khi đánh giá
   * @param bidId id gói thầu
   */
  async getBid2(user: UserDto, bidId: string) {
    const res: any = await this.findOne({
      where: { id: bidId, companyId: user.companyId },
      relations: { childs: { service: true }, bidItems: true },
      select: {
        id: true,
        childs: {
          id: true,
          percentTech: true,
          percentTrade: true,
          percentPrice: true,
          scoreDLC: true,
          service: { id: true, code: true, name: true },
        },
      },
    })
    if (!res) throw new Error('Gói thầu không còn tồn tại')
    if (res.isDeleted) throw new Error('Gói thầu đã ngưng hoạt động')

    res.listItem = res.__childs__ || []
    for (const item of res.listItem) {
      item.itemName = item.__service__?.code + ' - ' + item.__service__?.name
      delete item.__service__
    }
    delete res.__childs__

    return res
  }

  /**
   * Lấy danh sách Item của gói thầu khi chọn NCC thắng thầu
   * @param bidId id gói thầu
   */
  async getBid3(user: UserDto, bidId: string) {
    const res: any = await this.findOne({
      where: { id: bidId, companyId: user.companyId },
      relations: { childs: { service: true } },
      select: { id: true, noteCloseBidMPO: true, noteCloseBidMPOLeader: true, childs: true, bidItems: true },
    })
    if (!res) throw new Error('Gói thầu không còn tồn tại')
    if (res.isDeleted) throw new Error('Gói thầu đã ngưng hoạt động')

    res.listItem = res.__childs__ || []
    for (const item of res.listItem) {
      item.itemName = item.__service__?.code + ' - ' + item.__service__?.name
      delete item.__service__
    }
    delete res.__childs__

    return res
  }

  //#endregion
}

@CustomRepository(BidHistoryEntity)
export class BidHistoryRepository extends Repository<BidHistoryEntity> {}

@CustomRepository(BidTypeEntity)
export class BidTypeRepository extends Repository<BidTypeEntity> {}

@CustomRepository(BidItemEntity)
export class BidItemRepository extends Repository<BidItemEntity> {}

@CustomRepository(BidAuctionEntity)
export class BidAuctionRepository extends Repository<BidAuctionEntity> {}

@CustomRepository(BidCustomPriceEntity)
export class BidCustomPriceRepository extends Repository<BidCustomPriceEntity> {}

@CustomRepository(BidDealEntity)
export class BidDealRepository extends Repository<BidDealEntity> {
  /** Lấy giá mới nhất các Doanh nghiệp chào (cùng số lượng) */
  async getPriceValueNewest(user: UserDto, bidId: string) {
    const bidDealSupplierRepo = this.manager.getRepository(BidDealSupplierEntity)
    const bidSupplierRepo = this.manager.getRepository(BidSupplierEntity)
    const bidPriceRepo = this.manager.getRepository(BidPriceEntity)

    const lstResult: any[] = []
    const lstBidDeal = await this.find({ where: { bidId, companyId: user.companyId }, order: { createdAt: 'DESC' } })

    // TH: Có đàm phán
    if (lstBidDeal && lstBidDeal.length > 0) {
      const bidDealLast = lstBidDeal[0]
      const lstDealLastPrice = await bidDealLast.bidDealPrices

      // biến kiểm tra có thay đổi số lượng khi đàm phán
      let isChangeNumber = false
      // lấy các lần đàm phán gần nhất cùng số lượng
      const lstBidDealId = []
      lstBidDealId.push(bidDealLast.id)
      // quét ngược các lần đàm phán
      for (let i = 1; i < lstBidDeal.length; i++) {
        const bidDealCurrent = lstBidDeal[i]
        const lstDealCurrentPrice = await bidDealCurrent.bidDealPrices
        // Kiểm tra lần đàm phán có thay đổi số lượng hay không
        for (const price of lstDealLastPrice) {
          const priceCurrent = lstDealCurrentPrice.find((c) => c.bidPriceId == price.bidPriceId)
          if (!priceCurrent || priceCurrent.number != price.number) {
            isChangeNumber = true
            break
          }
        }
        if (isChangeNumber) break
        else lstBidDealId.push(bidDealCurrent.id)
      }

      // template bảng giá
      const bidPrices = await bidPriceRepo.find({ where: { bidId, companyId: user.companyId, isDeleted: false } })
      if (!isChangeNumber) {
        // Kiểm tra lần có thay đổi số lượng với template bảng giá hay không
        for (const price of lstDealLastPrice) {
          const priceCurrent = bidPrices.find((c) => c.id == price.bidPriceId)
          if (!priceCurrent || priceCurrent.number != price.number) {
            isChangeNumber = true
            break
          }
        }
      }

      // Lấy những Doanh nghiệp đã nộp giá mới
      const lstBidDealSupplier = await bidDealSupplierRepo.find({
        where: {
          bidDealId: In(lstBidDealId),
          companyId: user.companyId,
          status: enumData.BidDealSupplierStatus.DaGuiGiaMoi.code,
        },
        order: { createdAt: 'DESC' },
      })
      // Khi isChangeNumber = false thì kiểm tra thêm hồ sơ chào giá các Doanh nghiệp không tham gia đàm phán

      // Danh sách các Doanh nghiệp tham gia đấu thầu
      const lstBidSupplier = await bidSupplierRepo.find({ where: { bidId, companyId: user.companyId } })

      // Lấy dữ liệu giá cho mỗi Doanh nghiệp tham gia đấu thầu
      for (const bidSupplier of lstBidSupplier) {
        // Kiểm tra Doanh nghiệp có đàm phán k
        const bidDealSupplier = lstBidDealSupplier.find((c) => c.supplierId == bidSupplier.supplierId)
        if (bidDealSupplier) {
          const supplier = await bidDealSupplier.supplier
          const lstValue = await bidDealSupplier.bidDealSupplierPriceValue
          for (const item of lstValue) {
            const itemRes: any = {}
            const bidDealPrice = lstDealLastPrice.find((c) => c.bidPriceId === item.bidPriceId)

            itemRes.bidPriceId = item.bidPriceId

            if (bidDealPrice) {
              itemRes.number = bidDealPrice.number
            }
            if (itemRes.number == null || itemRes.number <= 0) {
              const bidPrice = bidPrices.find((c) => c.id === item.bidPriceId)
              itemRes.number = bidPrice?.number
            }

            itemRes.value = item.value
            itemRes.supplierId = supplier.id
            itemRes.supplierName = supplier.name
            itemRes.supplierScorePrice = bidSupplier?.scorePrice
            lstResult.push(itemRes)
          }
        }
        // Nếu không thay đổi số lượng so với template thì lấy giá nộp chào giá
        else if (!isChangeNumber) {
          if (bidSupplier.statusFile == enumData.BidSupplierFileStatus.HopLe.code) {
            const supplier = await bidSupplier.supplier
            const lstBidSupplierPriceValue = await bidSupplier.bidSupplierPriceValue
            for (const item2 of lstBidSupplierPriceValue) {
              const itemRes: any = {}
              const bidPrice = bidPrices.find((c) => c.id === item2.bidPriceId)

              itemRes.bidPriceId = item2.bidPriceId
              itemRes.number = bidPrice?.number
              itemRes.value = item2.value
              itemRes.supplierId = supplier.id
              itemRes.supplierName = supplier.name
              itemRes.supplierScorePrice = bidSupplier.scorePrice
              lstResult.push(itemRes)
            }
          }
        }
      }
    }
    // TH: Không có đàm phán
    else {
      const statusFile = enumData.BidSupplierFileStatus.HopLe.code
      const lstBidSupplier = await bidSupplierRepo.find({ where: { bidId, companyId: user.companyId, statusFile } })
      if (lstBidSupplier.length == 0) return lstResult

      for (const bidSupplier of lstBidSupplier) {
        const supplier = await bidSupplier.supplier
        const lstBidSupplierPriceValue = await bidSupplier.bidSupplierPriceValue
        for (const item2 of lstBidSupplierPriceValue) {
          const itemRes: any = {}
          const bidPrice = await item2.bidPrice

          itemRes.bidPriceId = item2.bidPriceId
          itemRes.number = bidPrice.number
          itemRes.value = item2.value
          itemRes.supplierId = supplier.id
          itemRes.supplierName = supplier.name
          itemRes.supplierScorePrice = bidSupplier.scorePrice
          lstResult.push(itemRes)
        }
      }
    }

    return lstResult
  }
}
