import { MigrationInterface, QueryRunner } from "typeorm";

export class modifyEmpWarning1676964289328 implements MigrationInterface {
    name = 'modifyEmpWarning1676964289328'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`employee_warning\` ADD \`dataType\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`employee_warning\` ADD \`dataId\` varchar(36) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`employee_warning\` DROP COLUMN \`dataId\``);
        await queryRunner.query(`ALTER TABLE \`employee_warning\` DROP COLUMN \`dataType\``);
    }

}
