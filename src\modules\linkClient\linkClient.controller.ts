import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { LinkClientService } from './linkClient.service'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { PaginationDto, UserDto } from '../../dto'
import { enumProject } from '../../constants'
import { CurrentUser, Roles } from '../common/decorators'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { LinkClientCreateDto, LinkClientUpdateDto } from './dto'

@ApiBearerAuth()
@ApiTags('Client')
@Controller('linkClients')
export class LinkClientController {
  constructor(private readonly service: LinkClientService) {}

  @ApiOperation({ summary: 'Danh sách link phân trang' })
  @Roles(enumProject.Features.SETTING_021.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo link' })
  @Roles(enumProject.Features.SETTING_021.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: LinkClientCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật link' })
  @Roles(enumProject.Features.SETTING_021.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: LinkClientUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động link' })
  @Roles(enumProject.Features.SETTING_021.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateIsDelete(user, data.id)
  }
}
