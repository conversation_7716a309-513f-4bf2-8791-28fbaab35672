import { Injectable } from '@nestjs/common'
import { HttpService } from '@nestjs/axios'
import { lastValueFrom } from 'rxjs'
import { apiHelper } from '../../helpers/apiHelper'
import { BidAuctionSupplierPriceValueRepository, BidPriceRepository } from '../../repositories'
import { Like } from 'typeorm'
@Injectable()
export class AiService {
  constructor(
    private readonly bidPriceRepository: BidPriceRepository,
    private readonly bidAuctionSupplierPriceValueRepository: BidAuctionSupplierPriceValueRepository,
  ) {}

  async callOpenAI(text: string) {}

  async callGeminiAI(text: any) {
    const lstResult = await apiHelper.callGeminiAI(text)
    return lstResult || ''
  }
  async getProductPrice(productName: string) {
    let isHaveRs = false
    let message = ''
    /* lấy tên theo bảng giá */
    const priceCol: any = await this.bidPriceRepository.findOne({
      where: { name: Like(`%${productName}%`) },
      relations: { bidAuctionSupplierPriceValue: { bidAuctionSupplier: { supplier: true } } },
    })
    /* nếu như có priceCol thì tìm ra giá và   */
    if (priceCol) {
      isHaveRs = true
      message = `Hệ thống đang có sản phẩm ${priceCol.name} của nhà cung cấp ${priceCol?.__bidAuctionSupplierPriceValue__[0]?.bidAuctionSupplier?.__supplier__?.name} với giá là ${priceCol?.__bidAuctionSupplierPriceValue__[0]?.value} VND `
    }
    /* nếu có thì trả về tên à giá tốt nhất và NCC */
    return { isHaveRs, message }
  }
}
