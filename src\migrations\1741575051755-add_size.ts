import { MigrationInterface, QueryRunner } from 'typeorm'

export class addSize1741575051755 implements MigrationInterface {
  name = 'addSize1741575051755'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`service_price_col\` CHANGE \`name\` \`name\` varchar(500) NOT NULL`)
    await queryRunner.query(`ALTER TABLE \`service_capacity\` CHANGE \`name\` \`name\` varchar(500) NOT NULL`)
    await queryRunner.query(`ALTER TABLE \`service_tech\` CHANGE \`name\` \`name\` varchar(500) NOT NULL`)
    await queryRunner.query(`ALTER TABLE \`service_trade\` CHANGE \`name\` \`name\` varchar(500) NOT NULL`)
    await queryRunner.query(`ALTER TABLE \`service_custom_price\` CHANGE \`name\` \`name\` varchar(500) NOT NULL`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`service_custom_price\` CHANGE \`name\` \`name\` varchar(250) NOT NULL`)
    await queryRunner.query(`ALTER TABLE \`service_trade\` CHANGE \`name\` \`name\` varchar(250) NOT NULL`)
    await queryRunner.query(`ALTER TABLE \`service_tech\` CHANGE \`name\` \`name\` varchar(250) NOT NULL`)
    await queryRunner.query(`ALTER TABLE \`service_capacity\` CHANGE \`name\` \`name\` varchar(250) NOT NULL`)
    await queryRunner.query(`ALTER TABLE \`service_price_col\` CHANGE \`name\` \`name\` varchar(250) NOT NULL`)
  }
}
