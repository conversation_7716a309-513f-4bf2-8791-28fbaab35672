import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { DepartmentRepository } from '../../repositories'
import { DepartmentService } from './department.service'
import { DepartmentController } from './department.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([DepartmentRepository])],
  controllers: [DepartmentController],
  providers: [DepartmentService],
})
export class DepartmentModule {}
