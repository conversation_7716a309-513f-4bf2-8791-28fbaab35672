import { Column, <PERSON>tity, Jo<PERSON><PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { AsnEntity } from './asn.entity'
import { BaseEntity } from './base.entity'
import { ServiceEntity } from './service.entity'

/** B<PERSON>ng Danh sách ITEM ASN */
@Entity('asn_item')
export class AsnItemEntity extends BaseEntity {
  /** Nhập kho */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  asnId: string
  @ManyToOne(() => AsnEntity, (p) => p.asnItems)
  @JoinColumn({ name: 'asnId', referencedColumnName: 'id' })
  asn: Promise<AsnEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  poId: string

  /** Vật tư */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  serviceId: string
  @ManyToOne(() => ServiceEntity, (p) => p.asnItems)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  @Column({
    nullable: false,
    default: 0,
  })
  quantity: number

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  itemCode: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  branchId: string
}
