import { IsNull, Repository } from 'typeorm'
import { UserDto } from '../dto'
import { CustomRepository } from '../typeorm'
import { OfferTechEntity } from '../entities/offerTech.entity'
import { OfferTechListDetailEntity } from '../entities/offerTechDetail.entity'

@CustomRepository(OfferTechEntity)
export class OfferTechRepository extends Repository<OfferTechEntity> {
  // Lấy danh sách yêu cầu kỹ thuật của gói thầu
  async getTech(user: UserDto, offerItemId: string) {
    return await this.find({
      where: { offerServiceId: offerItemId, companyId: user.companyId, isDeleted: false, parentId: IsNull() },
      relations: { offerTechListDetails: true, childs: { offerTechListDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })
  }
}
@CustomRepository(OfferTechListDetailEntity)
export class OfferTechListDetailRepository extends Repository<OfferTechListDetailEntity> {}
