import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PurchasePlanEntity } from './purchasePlan.entity'

/** Tiến độ mua hàng */
@Entity('purchase_plan_progress')
export class PurchasePlanProgressEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  purchasePlanId: string
  @ManyToOne(() => PurchasePlanEntity, (p) => p.progresss)
  @JoinColumn({ name: 'purchasePlanId', referencedColumnName: 'id' })
  purchasePlan: Promise<PurchasePlanEntity>

  @Column({
    nullable: false,
    default: 1,
  })
  sort: number

  @Column({
    nullable: false,
  })
  progressDate: Date

  @Column({
    nullable: false,
    default: 0,
  })
  quantity: number

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string
}
