import { BadRequestException, ConflictException, Injectable, NotAcceptableException, NotFoundException, UnauthorizedException } from '@nestjs/common'
import { In, IsNull, Like, Not, Raw } from 'typeorm'
import {
  ERROR_YOU_DO_NOT_HAVE_PERMISSION,
  ERROR_VALIDATE,
  UPDATE_SUCCESS,
  enumData,
  ERROR_NOT_FOUND_DATA,
  ERROR_EMAIL_TAKEN,
  PWD_SALT_ROUNDS,
  ACTION_SUCCESS,
} from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import {
  UserRepository,
  ServiceRepository,
  SupplierServiceRepository,
  SupplierExpertiseRepository,
  ServiceAccessRepository,
  EmployeeRepository,
  ServiceCapacityRepository,
  SupplierRepository,
  BidSupplierRepository,
} from '../../repositories'
import { EmailService } from '../email/email.service'
import { ConfigService } from '@nestjs/config'
import * as moment from 'moment'
import { CreateExpertiseDto, SupplierCreateDto, SupplierUpdateLawDto } from './dto'
import { apeAuthApiHelper, coreHelper, enumApeAuth } from '../../helpers'
import {
  BidAuctionSupplierEntity,
  BidDealSupplierEntity,
  BidSupplierEntity,
  DataHistoryEntity,
  ServiceCapacityEntity,
  SupplierCapacityEntity,
  SupplierCapacityListDetailEntity,
  SupplierCapacityYearValueEntity,
  SupplierEntity,
  SupplierExpertiseDetailEntity,
  SupplierExpertiseEntity,
  SupplierExpertiseLawDetailEntity,
  SupplierExpertiseMemberEntity,
  SupplierExpertiseYearDetailEntity,
  SupplierHistoryEntity,
  SupplierNotifyEntity,
  SupplierServiceEntity,
  UserConfirmCodeEntity,
  UserEntity,
} from '../../entities'
import { Request as IRequest } from 'express'
import { hash } from 'bcrypt'

@Injectable()
export class SupplierService {
  constructor(
    private readonly repo: SupplierRepository,
    private readonly bidSupplierRepo: BidSupplierRepository,
    private readonly userRepo: UserRepository,
    private readonly serviceRepo: ServiceRepository,
    private readonly serviceCapacityRepo: ServiceCapacityRepository,
    private readonly supplierServiceRepo: SupplierServiceRepository,
    private readonly supplierExpertiseRepo: SupplierExpertiseRepository,
    private readonly serviceAccessRepo: ServiceAccessRepository,
    private readonly employeeRepo: EmployeeRepository,
    private readonly emailService: EmailService,
    private readonly configService: ConfigService,
  ) {}

  //#region Quản lý NCC

  async findDetail(user: UserDto, data: { id: string }) {
    const res = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    return res
  }

  /** Phân trang Danh sách NCC */
  async supplierPagination(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data) throw new NotFoundException()

    let whereCon: any = { companyId: user.companyId }
    if (data.where.status) whereCon.status = data.where.status
    else whereCon.status = In([enumData.SupplierStatus.MoiDangKy.code, enumData.SupplierStatus.DaDuyet.code])

    if (data.where.fromDate && data.where.toDate) {
      whereCon.createdAt = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.fromDate).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.toDate).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.fromDate && !data.where.toDate)
      whereCon.createdAt = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.fromDate).format('YYYY-MM-DD')}")`)
    else if (data.where.toDate && !data.where.fromDate)
      whereCon.createdAt = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.toDate).format('YYYY-MM-DD')}")`)

    if (data.where.supplierCode) whereCon.code = Like(`%${data.where.supplierCode}%`)

    // nếu lọc service
    if (data.where.serviceId) {
      let where2: any = { serviceId: data.where.serviceId, companyId: user.companyId, isDeleted: false }
      if (data.where.supplierType) {
        where2.supplierType = data.where.supplierType
      }
      const lstSupplierService = await this.supplierServiceRepo.find({ where: where2, select: { id: true, supplierId: true } })

      if (lstSupplierService.length == 0) return [[], 0]

      const lstSupplierId = lstSupplierService.map((p) => p.supplierId)
      whereCon.id = In(lstSupplierId)
    }

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { user: true, supplierServices: { service: true } },
      order: { createdAt: 'DESC' },
    })
    const dicStatus: any = {}
    const dicStatusColor: any = {}
    {
      const lstPOStatus = coreHelper.convertObjToArray(enumData.SupplierStatus)
      lstPOStatus.forEach((c) => (dicStatus[c.code] = c.name))
      lstPOStatus.forEach((c) => (dicStatusColor[c.code] = c.color))
    }
    for (const item of res[0]) {
      item.username = item.__user__.username
      item.statusName = dicStatus[item.status]
      item.statusColor = dicStatusColor[item.status]
      delete item.__user__

      item.listSupplierService = []
      const lstSupplierService = item.__supplierServices__ || []
      delete item.__supplierServices__
      for (const supplierService of lstSupplierService) {
        const itemName = supplierService.__service__.code + ' - ' + supplierService.__service__.name
        delete supplierService.__service__

        item.listSupplierService.push({
          ...supplierService,
          itemName,
        })
      }
    }

    return res
  }

  public async loadDataSelectOffer(user: UserDto, data: any) {
    let whereCon: any = { companyId: user?.companyId, status: enumData.SupplierStatus.DaDuyet.code, isDeleted: false }
    // whereCon.supplierServices = { serviceId: In(lstServiceId), isDeleted: false }
    if (data.lstStatus?.length > 0) whereCon.supplierServices.statusExpertise = In(data.lstStatus)

    if (data.supplierName) {
      whereCon = [
        { ...whereCon, code: Like(`%${data.supplierName}%`) },
        { ...whereCon, name: Like(`%${data.supplierName}%`) },
      ]
    }
    const res: any = await this.repo.find({
      where: whereCon,
      select: { id: true, code: true, name: true },
    })
    for (const item of res) {
      item.statusName = enumData.SupplierStatus[item.status]?.code
    }
    return res
  }

  /** Thêm NCC */
  public async createSupplier(user: UserDto, data: SupplierCreateDto, req: IRequest) {
    // check user
    if (!user.employeeId) throw new Error('Bạn không có quyền thực hiện chức năng này!')

    try {
      const objCheckCode = await this.repo.findOne({ where: { code: data.code, companyId: user.companyId }, select: { id: true } })
      if (objCheckCode) throw new ConflictException(`Mã số doanh nghiệp [${data.code}] đã được sử dụng.`)

      const objCheckEmail = await this.repo.findOne({ where: { email: data.email, companyId: user.companyId }, select: { id: true } })
      if (objCheckEmail) throw new ConflictException(ERROR_EMAIL_TAKEN)

      if (data.password != data.confirmPassword) {
        throw new Error('Mật khẩu không trùng khớp.')
      }

      return await this.repo.manager.transaction('READ UNCOMMITTED', async (manager) => {
        const userRepo = manager.getRepository(UserEntity)
        const supplierRepo = manager.getRepository(SupplierEntity)

        const checkUser = await userRepo.findOne({ where: { username: data.username, companyId: user.companyId } })
        if (checkUser) throw new Error('Tài khoản đã tồn tại.')

        const newEntity = supplierRepo.create(data)
        newEntity.companyId = user.companyId
        newEntity.createdBy = user.id
        newEntity.status = enumData.SupplierStatus.DaDuyet.code
        // Lưu lại id của nhân viên giới thiệu
        if (user.employeeId) newEntity.introducerId = user.employeeId
        const supplierEntity = await supplierRepo.save(newEntity)

        const isProduct = process.env.IS_PRODUCT == 'true'
        if (isProduct) {
          if (!req || !req.headers || !req.headers.authorization)
            throw new UnauthorizedException('Không có quyền truy cập! (code: BEARER_TOKEN_ERROR)')
          await apeAuthApiHelper.createUserPMS(
            req.headers.authorization,
            user.companyId,
            enumApeAuth.UserType.CompanyPackageSupplier,
            data.username,
            data.password,
          )
        }

        const newUserEntity = userRepo.create({
          companyId: user.companyId,
          createdBy: user.id,
          username: data.username,
          password: !isProduct ? data.password : '',
          type: enumData.UserType.Supplier.code,
          supplierId: supplierEntity.id,
        })
        const createdUser = await userRepo.save(newUserEntity)
        await supplierRepo.update(supplierEntity.id, {
          userId: createdUser.id,
        })
        // supplierEntity
        if (supplierEntity) await this.emailService.GuiNccNguoiDuyetDuyetDangKy2(supplierEntity.id, data.username, data.password)

        // Supplier History
        const suplierHistory = new SupplierHistoryEntity()
        suplierHistory.companyId = user.companyId
        suplierHistory.createdBy = user.id
        suplierHistory.supplierId = supplierEntity.id
        suplierHistory.description = `Admin tạo NCC`
        await manager.getRepository(SupplierHistoryEntity).save(suplierHistory)
      })
    } catch (error) {
      console.log(error)
      throw error
    }
  }

  /** Sửa NCC */
  public async updateSupplier(user: UserDto, data: SupplierUpdateLawDto) {
    // check user
    if (!user.employeeId) throw new NotAcceptableException('Bạn không có quyền thực hiện chức năng này!')

    const check = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (check == null) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    if (check.isDeleted) {
      throw new NotAcceptableException(`Nhà cung cấp [${check.name}] đã ngưng hoạt động, vui lòng thay đổi trạng thái để tiếp tục thao tác`)
    }

    return await this.repo.manager.transaction(async (manager) => {
      const supplierRepo = manager.getRepository(SupplierEntity)
      let supplierEntity = await supplierRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!supplierEntity) throw new Error(ERROR_NOT_FOUND_DATA)

      if (supplierEntity.code !== data.code) {
        const objCheckCode = await this.repo.findOne({ where: { code: data.code, companyId: user.companyId }, select: { id: true } })
        if (objCheckCode) throw new ConflictException(`Mã số doanh nghiệp [${data.code}] đã được sử dụng.`)
      }

      if (supplierEntity.email !== data.email) {
        const objCheckEmail = await this.repo.findOne({ where: { email: data.email, companyId: user.companyId }, select: { id: true } })
        if (objCheckEmail) throw new ConflictException(ERROR_EMAIL_TAKEN)
      }

      const historyRepo = manager.getRepository(DataHistoryEntity)
      const history = historyRepo.create({
        companyId: user.companyId,
        createdBy: user.id,
        relationId: data.id,
        tableName: enumData.DataHistoryTable.Supplier,
        dataJson: supplierEntity as any,
        description: 'Admin cập nhật thông tin pháp lý.',
      })
      await historyRepo.save(history)

      supplierEntity.name = data.name
      supplierEntity.code = data.code
      supplierEntity.description = data.description
      supplierEntity.dealName = data.dealName
      supplierEntity.address = data.address
      supplierEntity.dealAddress = data.dealAddress
      supplierEntity.fileMST = data.fileMST
      supplierEntity.represen = data.represen
      supplierEntity.chief = data.chief
      supplierEntity.bankNumber = data.bankNumber
      supplierEntity.bankname = data.bankname
      supplierEntity.bankBrand = data.bankBrand
      supplierEntity.fileAccount = data.fileAccount
      supplierEntity.contactName = data.contactName
      supplierEntity.email = data.email
      supplierEntity.phone = data.phone
      supplierEntity.createYear = data.createYear
      supplierEntity.capital = data.capital
      supplierEntity.assets = data.assets
      supplierEntity.fileBill = data.fileBill
      supplierEntity.fileInfoBill = data.fileInfoBill
      supplierEntity.updatedBy = user.id

      await supplierRepo.update(data.id, supplierEntity)

      // Supplier History
      const suplierHistory = new SupplierHistoryEntity()
      suplierHistory.companyId = user.companyId
      suplierHistory.createdBy = user.id
      suplierHistory.supplierId = supplierEntity.id
      suplierHistory.description = `Admin cập nhật thông tin NCC`
      await manager.getRepository(SupplierHistoryEntity).save(suplierHistory)

      return { message: UPDATE_SUCCESS }
    })
  }

  /** Lấy thông tin năng lực Item của NCC */
  async loadCapacity(user: UserDto, data: { supplierServiceId: string }) {
    if (!user.employeeId) if (!user.employeeId) throw new NotAcceptableException('Bạn không có quyền thực hiện chức năng này!')
    if (!data.supplierServiceId) throw new BadRequestException('Không xác định Item NCC!')

    const supService = await this.supplierServiceRepo.findOne({
      where: { id: data.supplierServiceId },
      select: { id: true, serviceId: true, supplierId: true, status: true },
    })
    if (!supService) throw new NotFoundException('Item NCC không tồn tại!')
    if (supService.status == enumData.SupplierServiceStatus.NgungHoatDong.code) {
      throw new Error('Item NCC đã ngưng hoạt động!')
    }

    if (supService.status == enumData.SupplierServiceStatus.ChuaDangKy.code) {
      const res: any[] = await this.serviceCapacityRepo.find({
        where: { serviceId: supService.serviceId, parentId: IsNull(), companyId: user.companyId, isDeleted: false },
        relations: { serviceCapacityListDetails: true, childs: { serviceCapacityListDetails: true } },
        order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
      })

      for (const item of res) {
        item.supplierId = supService.supplierId
        item.serviceId = supService.serviceId
        item.serviceCapacityId = item.id
        item.__childs__ = item.__childs__.filter((c: any) => !c.isDeleted)
        item.listDetail = item.__serviceCapacityListDetails__ || []
        delete item.__serviceCapacityListDetails__
        if (item.isChangeByYear) item.listDetailYear = []

        for (const itemChild of item.__childs__) {
          itemChild.supplierId = supService.supplierId
          itemChild.serviceId = supService.serviceId
          itemChild.serviceCapacityId = itemChild.id
          itemChild.listDetail = itemChild.__serviceCapacityListDetails__ || []
          delete itemChild.__serviceCapacityListDetails__
          if (itemChild.isChangeByYear) itemChild.listDetailYear = []
        }
      }

      return res
    } else {
      const res: any[] = await this.repo.manager.getRepository(SupplierCapacityEntity).find({
        where: { supplierServiceId: supService.id, parentId: IsNull(), companyId: user.companyId, isDeleted: false },
        relations: {
          supplierCapacityListDetails: true,
          supplierCapacityYearValues: true,
          childs: { supplierCapacityListDetails: true, supplierCapacityYearValues: true },
        },
        order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
      })

      for (const item of res) {
        item.__childs__ = item.__childs__.filter((c: any) => !c.isDeleted)
        item.listDetail = item.__supplierCapacityListDetails__ || []
        delete item.__supplierCapacityListDetails__
        item.listDetailYear = item.__supplierCapacityYearValues__ || []
        delete item.__supplierCapacityYearValues__

        for (const itemChild of item.__childs__) {
          itemChild.listDetail = itemChild.__supplierCapacityListDetails__ || []
          delete itemChild.__supplierCapacityListDetails__
          itemChild.listDetailYear = itemChild.__supplierCapacityYearValues__ || []
          delete itemChild.__supplierCapacityYearValues__
        }
      }

      return res
    }
  }

  /** Admin Lưu thông tin năng lực item NCC */
  public async saveSupplierServiceCapacity(user: UserDto, data: { supplierServiceId: string; lstCapacity: any[] }) {
    // check user
    if (!user.employeeId) throw new NotAcceptableException('Bạn không có quyền thực hiện chức năng này!')

    // Item NCC
    const supplierService: any = await this.supplierServiceRepo.findOne({
      where: { id: data.supplierServiceId, companyId: user.companyId },
      relations: { service: true },
      select: { id: true, serviceId: true, supplierId: true, status: true, service: { code: true, name: true } },
    })
    if (!supplierService) throw new Error(ERROR_NOT_FOUND_DATA)
    if (supplierService.status == enumData.SupplierServiceStatus.NgungHoatDong.code) {
      throw new Error('Item NCC đã ngưng hoạt động!')
    }
    const setType = new Set()
    setType.add(enumData.DataType.String.code)
    setType.add(enumData.DataType.Number.code)
    setType.add(enumData.DataType.File.code)
    return await this.repo.manager.transaction('READ UNCOMMITTED', async (manager) => {
      const supplierServiceRepo = new SupplierServiceRepository(SupplierServiceEntity, manager)

      if (supplierService.status == enumData.SupplierServiceStatus.ChuaDangKy.code) {
        const supplierCapacityRepo = manager.getRepository(SupplierCapacityEntity)
        const supplierCapacityYearValueRepo = manager.getRepository(SupplierCapacityYearValueEntity)
        const supplierCapacityListDetailRepo = manager.getRepository(SupplierCapacityListDetailEntity)
        const serviceCapacityRepo = manager.getRepository(ServiceCapacityEntity)

        // Danh sách tiêu chí năng lực
        const serviceCapacity = await serviceCapacityRepo.find({
          where: {
            parentId: IsNull(),
            serviceId: supplierService.serviceId,
            companyId: user.companyId,
            isDeleted: false,
          },
        })

        for (let i = 0, lenCapacity = serviceCapacity.length; i < lenCapacity; i++) {
          const item = serviceCapacity[i]
          const findValue = data.lstCapacity.find((p) => p.serviceCapacityId === item.id)
          if (!findValue) throw new Error(`Thông tin năng lực không trùng khớp với dữ liệu được cấu hình. Vui lòng tải lại trang và thử lại.`)

          let supplierCapacity = new SupplierCapacityEntity()
          supplierCapacity.companyId = user.companyId
          supplierCapacity.createdBy = user.id
          supplierCapacity.supplierServiceId = supplierService.id
          supplierCapacity.name = item.name
          supplierCapacity.sort = item.sort
          supplierCapacity.type = item.type
          supplierCapacity.isRequired = item.isRequired
          supplierCapacity.percent = item.percent
          supplierCapacity.percentRule = item.percentRule
          supplierCapacity.isCalUp = item.isCalUp
          supplierCapacity.percentDownRule = item.percentDownRule
          supplierCapacity.description = item.description

          supplierCapacity.isChangeByYear = false
          if (setType.has(supplierCapacity.type)) supplierCapacity.isChangeByYear = item.isChangeByYear

          supplierCapacity.value = findValue.value
          supplierCapacity.serviceId = item.serviceId
          supplierCapacity.supplierId = supplierService.supplierId
          supplierCapacity.serviceCapacityId = item.id
          supplierCapacity = await supplierCapacityRepo.save(supplierCapacity)

          // Nếu type là List thì thêm cấu hình danh sách list
          if (item.type === enumData.DataType.List.code) {
            const lstDetail = await item.serviceCapacityListDetails
            for (let j = 0, lenListDetail = lstDetail.length; j < lenListDetail; j++) {
              const itemListDetail = lstDetail[j]
              let listDetail = new SupplierCapacityListDetailEntity()
              listDetail.companyId = user.companyId
              listDetail.createdBy = user.id
              listDetail.supplierCapacityId = supplierCapacity.id
              listDetail.isDeleted = itemListDetail.isDeleted
              listDetail.name = itemListDetail.name
              listDetail.value = itemListDetail.value
              listDetail.isChosen = false
              if (findValue.value) {
                listDetail.isChosen = findValue.value == itemListDetail.id
              }
              listDetail = await supplierCapacityListDetailRepo.save(listDetail)

              if (listDetail.isChosen) {
                await supplierCapacityRepo.update(supplierCapacity.id, { value: listDetail.id })
              }
            }
          }

          // Nếu type là loại theo năm thì lưu danh sách giá trị
          if (item.isChangeByYear) {
            const lstYear = findValue.listDetailYear || []
            for (let j = 0, lenYear = lstYear.length; j < lenYear; j++) {
              const itemYear = lstYear[j]
              const listDetail = new SupplierCapacityYearValueEntity()
              listDetail.companyId = user.companyId
              listDetail.createdBy = user.id
              listDetail.supplierCapacityId = supplierCapacity.id
              listDetail.isDeleted = false
              listDetail.value = itemYear.value
              listDetail.year = itemYear.year
              await supplierCapacityYearValueRepo.save(listDetail)
            }
          }

          // Lọc qua danh sách con, Nếu có value con thì copy. Không có thì thôi
          const lstChild = (await item.childs).filter((p) => !p.isDeleted)
          for (let i2 = 0, lenChild = lstChild.length; i2 < lenChild; i2++) {
            const itemChild = lstChild[i2]
            const findValueChild = findValue.__childs__.find((c: any) => c.serviceCapacityId === itemChild.id)
            if (!findValueChild) throw new Error(ERROR_NOT_FOUND_DATA)

            let child = new SupplierCapacityEntity()
            child.companyId = user.companyId
            child.createdBy = user.id
            child.supplierServiceId = supplierService.id

            child.name = itemChild.name
            child.sort = itemChild.sort
            child.type = itemChild.type
            child.isRequired = itemChild.isRequired
            child.percent = itemChild.percent
            child.percentRule = itemChild.percentRule
            child.isCalUp = itemChild.isCalUp
            child.percentDownRule = itemChild.percentDownRule
            child.description = itemChild.description

            child.isChangeByYear = false
            if (setType.has(child.type)) child.isChangeByYear = itemChild.isChangeByYear

            child.value = findValueChild.value

            child.serviceId = supplierService.serviceId
            child.supplierId = supplierService.supplierId
            child.serviceCapacityId = itemChild.id
            child.parentId = supplierCapacity.id
            child = await supplierCapacityRepo.save(child)

            if (itemChild.type === enumData.DataType.List.code) {
              const lstDetail = await itemChild.serviceCapacityListDetails
              for (let j = 0, lenListDetail = lstDetail.length; j < lenListDetail; j++) {
                const itemListDetail = lstDetail[j]
                let listDetail = new SupplierCapacityListDetailEntity()
                listDetail.companyId = user.companyId
                listDetail.createdBy = user.id
                listDetail.supplierCapacityId = child.id
                listDetail.isDeleted = itemListDetail.isDeleted
                listDetail.name = itemListDetail.name
                listDetail.value = itemListDetail.value
                listDetail.isChosen = false
                if (findValueChild.value) {
                  listDetail.isChosen = findValueChild.value == itemListDetail.id
                }
                listDetail = await supplierCapacityListDetailRepo.save(listDetail)

                if (listDetail.isChosen) {
                  await supplierCapacityRepo.update(child.id, { value: listDetail.id })
                }
              }
            }

            if (itemChild.isChangeByYear) {
              const lstYear = findValueChild.listDetailYear || []
              for (let j = 0, lenYear = lstYear.length; j < lenYear; j++) {
                const itemYear = lstYear[j]
                const listDetail = new SupplierCapacityYearValueEntity()
                listDetail.companyId = user.companyId
                listDetail.createdBy = user.id
                listDetail.supplierCapacityId = child.id
                listDetail.isDeleted = false
                listDetail.value = itemYear.value
                listDetail.year = itemYear.year
                await supplierCapacityYearValueRepo.save(listDetail)
              }
            }
          }
        }
      } else {
        const supplierCapacityRepo = manager.getRepository(SupplierCapacityEntity)
        const supplierCapacityYearRepo = manager.getRepository(SupplierCapacityYearValueEntity)
        const supplierCapacityListRepo = manager.getRepository(SupplierCapacityListDetailEntity)

        const supplierExpertiseRepo = manager.getRepository(SupplierExpertiseEntity)
        const expertise = await supplierExpertiseRepo.findOne({
          where: { supplierServiceId: supplierService.id, companyId: user.companyId, isDeleted: false },
          order: { createdAt: 'DESC' },
          select: { id: true, status: true, createdAt: true },
        })
        if (expertise?.status === enumData.SupplierExpertiseStatus.DangThamDinh.code) {
          throw new Error('Hệ thống đang thẩm định! Vui lòng cập nhật sau!')
        }

        const lstCapacityOld = await supplierService.capacities
        const historyRepo = manager.getRepository(DataHistoryEntity)
        const history = historyRepo.create({
          companyId: user.companyId,
          createdBy: user.id,
          relationId: data.supplierServiceId,
          tableName: enumData.DataHistoryTable.SupplierCapacity,
          dataJson: lstCapacityOld,
          description: 'Admin cập nhật thông tin năng lực Item NCC',
        })
        await historyRepo.save(history)

        const lenCapacity = data.lstCapacity?.length || 0
        for (let i = 0; i < lenCapacity; i++) {
          const item = data.lstCapacity[i]

          const itemEntity = await supplierCapacityRepo.findOne({
            where: { id: item.id, companyId: user.companyId, isDeleted: false },
            select: { id: true },
          })
          if (!itemEntity) throw new Error(ERROR_NOT_FOUND_DATA)

          await supplierCapacityRepo.update(item.id, {
            value: item.value,
            updatedBy: user.id,
          })

          if (item.type === enumData.DataType.List.code) {
            // Gán ischosen về false hết và chọn lại
            await supplierCapacityListRepo.update(
              {
                supplierCapacityId: item.id,
                isDeleted: false,
                isChosen: true,
              },
              { isChosen: false, updatedBy: user.id },
            )

            if (item.value) {
              await supplierCapacityListRepo.update(
                {
                  id: item.value,
                  isDeleted: false,
                },
                { isChosen: true, updatedBy: user.id },
              )
            }
          }

          // Xoá hết data cũ và thêm lại
          await supplierCapacityYearRepo.delete({ supplierCapacityId: item.id })
          if (item.listDetailYear?.length > 0) {
            for (let j = 0, lenYear = item.listDetailYear.length; j < lenYear; j++) {
              let itemYear = item.listDetailYear[j]
              const listDetail = new SupplierCapacityYearValueEntity()
              listDetail.companyId = user.companyId
              listDetail.createdBy = user.id
              listDetail.supplierCapacityId = itemEntity.id
              listDetail.isDeleted = false
              listDetail.value = itemYear.value
              listDetail.year = itemYear.year
              await supplierCapacityYearRepo.save(listDetail)
            }
          }

          if (!item.childs?.length) continue
          for (let i2 = 0, lenChild = item.childs.length; i2 < lenChild; i2++) {
            const child = item.childs[i2]

            const childEntity = await supplierCapacityRepo.findOne({
              where: { id: child.id, companyId: user.companyId, isDeleted: false },
              select: { id: true },
            })
            if (!childEntity) throw new Error(ERROR_NOT_FOUND_DATA)

            await supplierCapacityRepo.update(child.id, {
              value: child.value,
              updatedBy: user.id,
            })

            if (child.type === enumData.DataType.List.code) {
              // Gán ischosen về false hết và chọn lại
              await supplierCapacityListRepo.update(
                {
                  supplierCapacityId: child.id,
                  isDeleted: false,
                  isChosen: true,
                },
                { isChosen: false, updatedBy: user.id },
              )

              if (child.value) {
                await supplierCapacityListRepo.update(
                  {
                    id: child.value,
                    isDeleted: false,
                  },
                  { isChosen: true, updatedBy: user.id },
                )
              }
            }

            // Xoá hết data cũ và thêm lại
            await supplierCapacityYearRepo.delete({ supplierCapacityId: child.id })
            if (child.listDetailYear?.length > 0) {
              for (let j = 0, lenYear = child.listDetailYear.length; j < lenYear; j++) {
                const itemYear = child.listDetailYear[j]
                const listDetail = new SupplierCapacityYearValueEntity()
                listDetail.companyId = user.companyId
                listDetail.createdBy = user.id
                listDetail.supplierCapacityId = childEntity.id
                listDetail.isDeleted = false
                listDetail.value = itemYear.value
                listDetail.year = itemYear.year
                await supplierCapacityYearRepo.save(listDetail)
              }
            }
          }
        }
      }

      // Supplier History
      const suplierHistory = new SupplierHistoryEntity()
      suplierHistory.companyId = user.companyId
      suplierHistory.createdBy = user.id
      suplierHistory.supplierId = supplierService.supplierId
      suplierHistory.description = `Admin cập nhật thông tin năng lực NCC Item [${
        supplierService.__service__.code + ' - ' + supplierService.__service__.name
      }]`
      await manager.getRepository(SupplierHistoryEntity).save(suplierHistory)

      // Tính lại điểm
      const score = await supplierServiceRepo.calScore(supplierService.id)
      await supplierServiceRepo.update(supplierService.id, { score, status: enumData.SupplierServiceStatus.DaDuyet.code, updatedBy: user.id })
    })
  }

  /** Tải lại template năng lực Item của NCC */
  async deleteAllCapacity(user: UserDto, data: { supplierServiceId: string }) {
    if (!user.employeeId) if (!user.employeeId) throw new NotAcceptableException('Bạn không có quyền thực hiện chức năng này!')
    if (!data.supplierServiceId) throw new BadRequestException('Không xác định Item NCC!')

    const supService: any = await this.supplierServiceRepo.findOne({
      where: { id: data.supplierServiceId, companyId: user.companyId },
      relations: { service: true },
      select: { id: true, supplierId: true, service: { code: true, name: true } },
    })
    if (!supService) throw new NotFoundException('Item NCC không tồn tại!')

    await this.repo.manager.transaction(async (manager) => {
      const lstSupplierCapacity = await manager
        .getRepository(SupplierCapacityEntity)
        .find({ where: { supplierServiceId: data.supplierServiceId }, select: { id: true } })
      if (lstSupplierCapacity.length > 0) {
        for (const supplierCapacity of lstSupplierCapacity) {
          const lstExpertiseCapacity = await manager
            .getRepository(SupplierExpertiseDetailEntity)
            .find({ where: { supplierCapacityId: supplierCapacity.id }, select: { id: true } })
          if (lstExpertiseCapacity.length > 0) {
            const lstExpertiseCapacityId = lstExpertiseCapacity.map((c) => c.id)
            // xóa thẩm định tiêu chí theo năm
            await manager.getRepository(SupplierExpertiseYearDetailEntity).delete({ supplierExpertiseDetailId: In(lstExpertiseCapacityId) })
            // xóa thẩm định tiêu chí khác
            await manager.getRepository(SupplierExpertiseDetailEntity).delete({ supplierCapacityId: supplierCapacity.id })
          }

          // xóa giá trị tiêu chí theo năm
          await manager.getRepository(SupplierCapacityYearValueEntity).delete({ supplierCapacityId: supplierCapacity.id })
          // xóa giá trị tiêu chí List
          await manager.getRepository(SupplierCapacityListDetailEntity).delete({ supplierCapacityId: supplierCapacity.id })
        }
        // xóa các tiêu chí cấp 2
        await manager.getRepository(SupplierCapacityEntity).delete({ supplierServiceId: data.supplierServiceId, parentId: Not(IsNull()) })
        // xóa các tiêu chí còn lại
        await manager.getRepository(SupplierCapacityEntity).delete({ supplierServiceId: data.supplierServiceId })
      }

      // Cập nhật trạng thái và trạng thái thẩm định của Item NCC
      await manager.getRepository(SupplierServiceEntity).update(data.supplierServiceId, {
        status: enumData.SupplierServiceStatus.ChuaDangKy.code,
        statusExpertise: enumData.SupplierServiceExpertiseStatus.ChuaDangKy.code,
        updatedBy: user.id,
      })

      // Supplier History
      const suplierHistory = new SupplierHistoryEntity()
      suplierHistory.companyId = user.companyId
      suplierHistory.createdBy = user.id
      suplierHistory.supplierId = supService.supplierId
      suplierHistory.description = `Tải lại template năng lực Item [${supService.__service__.code + ' - ' + supService.__service__.name}]`
      await manager.getRepository(SupplierHistoryEntity).save(suplierHistory)
    })

    return { message: ACTION_SUCCESS }
  }

  /** Kiểm tra trước khi vào transaction import */
  private async checkImportSuppliers(user: UserDto, data: any) {
    const checkResult: any = {
      isCheckError: false,
      lstError: [],
      message: '',
    }

    // 2 list supplier check trùng
    const lstSupplier = await this.repo.find({
      where: { companyId: user.companyId },
      select: { id: true, code: true },
    })
    const lstSupplierInFile: any[] = []

    const lstUser = await this.userRepo.find({
      where: { companyId: user.companyId, isDeleted: false },
      select: { id: true, username: true },
    })

    // Kiểm tra từng row
    const lstErr = []
    for (const row of data.lstData) {
      // zenId: 'STT *' đã check ở FE

      // code: 'Mã số doanh nghiệp *'
      if (row.code != null) row.code = (row.code + '').trim()
      if (row.code == null || row.code === '') {
        const errorMessage = '[Mã số doanh nghiệp *] là bắt buộc, không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        if (lstSupplier.some((c) => c.code === row.code)) {
          const errorMessage = `[Mã số doanh nghiệp *] là [${row.code}] đã tồn tại`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        if (lstSupplierInFile.some((c) => c.code === row.code)) {
          const errorMessage = `[Mã số doanh nghiệp *] đã nhập [${row.code}] trùng trong file`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        if (row.code.length > 50) {
          const errorMessage = `[Mã số doanh nghiệp *] đã nhập [${row.code}] vượt quá 50 ký tự`
          lstErr.push({ ...row, errorMessage })
          continue
        }
      }

      // name: 'Tên doanh nghiệp *'
      if (row.name != null) row.name = (row.name + '').trim()
      if (row.name == null || row.name === '') {
        const errorMessage = '[Tên doanh nghiệp *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // dealName: 'Tên giao dịch *'
      if (row.dealName != null) row.dealName = (row.dealName + '').trim()
      if (row.dealName == null || row.dealName === '') {
        const errorMessage = '[Tên giao dịch *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // createYear: 'Năm thành lập công ty *'
      if (row.createYear != null) row.createYear = (row.createYear + '').trim()
      if (row.createYear == null || row.createYear === '') {
        const errorMessage = '[Năm thành lập công ty *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const createYear = +row.createYear
        const currentYear = new Date().getFullYear()
        if (isNaN(createYear) || createYear < 1900 || createYear > currentYear) {
          const errorMessage = `[Năm thành lập công ty] đã nhập [${row.createYear}] không hợp lệ`
          lstErr.push({ ...row, errorMessage })
          continue
        }
      }

      // address: 'Địa chỉ trụ sở *'
      if (row.address != null) row.address = (row.address + '').trim()
      if (row.address == null || row.address === '') {
        const errorMessage = '[Địa chỉ trụ sở *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // dealAddress: 'Địa chỉ giao dịch *'
      if (row.dealAddress != null) row.dealAddress = (row.dealAddress + '').trim()
      if (row.dealAddress == null || row.dealAddress === '') {
        const errorMessage = '[Địa chỉ giao dịch *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // represen: 'Người đại diện pháp luật *'
      if (row.represen != null) row.represen = (row.represen + '').trim()
      if (row.represen == null || row.represen === '') {
        const errorMessage = '[Người đại diện pháp luật *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // chief: 'Tên giám đốc *'
      if (row.chief != null) row.chief = (row.chief + '').trim()
      if (row.chief == null || row.chief === '') {
        const errorMessage = '[Tên giám đốc *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // capital: 'Vốn điều lệ (tỷ đồng) *'
      if (row.capital != null) row.capital = (row.capital + '').trim()
      if (row.capital == null || row.capital === '') {
        const errorMessage = '[Vốn điều lệ (tỷ đồng) *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        row.capital = +row.capital
        if (isNaN(row.capital) || row.capital < 0) {
          const errorMessage = `[Vốn điều lệ (tỷ đồng) *] đã nhập [${row.capital}] không hợp lệ`
          lstErr.push({ ...row, errorMessage })
          continue
        }
      }

      // assets: 'Tài sản cố định (tỷ đồng) *'
      if (row.assets != null) row.assets = (row.assets + '').trim()
      if (row.assets == null || row.assets === '') {
        const errorMessage = '[Tài sản cố định (tỷ đồng) *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        row.assets = +row.assets
        if (isNaN(row.assets) || row.assets < 0) {
          const errorMessage = `[Tài sản cố định (tỷ đồng) *] đã nhập [${row.assets}] không hợp lệ`
          lstErr.push({ ...row, errorMessage })
          continue
        }
      }

      // bankNumber: 'Số tài khoản ngân hàng *'
      if (row.bankNumber != null) row.bankNumber = (row.bankNumber + '').trim()
      if (row.bankNumber == null || row.bankNumber === '') {
        const errorMessage = '[Số tài khoản ngân hàng *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // bankname: 'Tên ngân hàng *'
      if (row.bankname != null) row.bankname = (row.bankname + '').trim()
      if (row.bankname == null || row.bankname === '') {
        const errorMessage = '[Tên ngân hàng *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // bankBrand: 'Tên chi nhánh ngân hàng *'
      if (row.bankBrand != null) row.bankBrand = (row.bankBrand + '').trim()
      if (row.bankBrand == null || row.bankBrand === '') {
        const errorMessage = '[Tên chi nhánh ngân hàng *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // description: 'Mô tả về doanh nghiệp *'
      if (row.description != null) row.description = (row.description + '').trim()
      if (row.description == null || row.description === '') {
        const errorMessage = '[Mô tả về doanh nghiệp *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // contactName: 'Người liên hệ *'
      if (row.contactName != null) row.contactName = (row.contactName + '').trim()
      if (row.contactName == null || row.contactName === '') {
        const errorMessage = '[Người liên hệ *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // phone: 'Số điện thoại *'
      if (row.phone != null) row.phone = (row.phone + '').trim()
      if (row.phone == null || row.phone === '') {
        const errorMessage = '[Số điện thoại *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        //validate phone
        const regularExpression = /^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./0-9]*$/
        const isValidPhone = regularExpression.test(String(row.phone).toLowerCase())
        if (!isValidPhone) {
          const errorMessage = `[Số điện thoại *] đã nhập [${row.phone}] không hợp lệ`
          lstErr.push({ ...row, errorMessage })
          continue
        }
      }

      // email: 'Email *'
      if (row.email != null) row.email = (row.email + '').trim()
      if (row.email == null || row.email === '') {
        const errorMessage = '[Email *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        //validate email
        const regularExpression =
          /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        const isValidEmail = regularExpression.test(String(row.email).toLowerCase())
        if (!isValidEmail) {
          const errorMessage = `[Email *] đã nhập [${row.email}] không hợp lệ`
          lstErr.push({ ...row, errorMessage })
          continue
        }
      }

      // username: 'Tên đăng nhập *'
      if (row.username != null) row.username = (row.username + '').trim()
      if (row.username == null || row.username === '') {
        const errorMessage = '[Tên đăng nhập *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        if (lstUser.some((c) => c.username === row.username)) {
          const errorMessage = `[Tên đăng nhập *] là [${row.username}] đã tồn tại`
          lstErr.push({ ...row, errorMessage })
          continue
        }
      }

      // password: 'Mật khẩu *'
      if (row.password != null) row.password = (row.password + '').trim()
      if (row.password == null || row.password === '') {
        const errorMessage = '[Mật khẩu *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      lstSupplierInFile.push(row)
    }

    if (lstErr.length > 0) {
      checkResult.lstError = lstErr
      checkResult.isCheckError = true
      checkResult.message = `Có ${checkResult.lstError.length} dòng lỗi, vui lòng xem chi tiết lỗi và kiểm tra lại file!`
    }

    return checkResult
  }

  /** Import NCC */
  public async importSuppliers(user: UserDto, data: { lstData: any[] }, req: IRequest) {
    // Check user
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    // Check & validate data
    const checkResult = await this.checkImportSuppliers(user, data)
    if (checkResult.lstError.length > 0) {
      return checkResult
    }

    // trả về thông báo số NCC được import thành công, và danh sách các dòng với chi tiết lỗi từng dòng
    return await this.repo.manager.transaction(async (manager) => {
      const checkResult: any = { isCheckError: false, lstError: [], message: '', lstSupplierId: [] }
      let numRowSuccess = 0

      const supplierRepo = manager.getRepository(SupplierEntity)
      const userRepo = manager.getRepository(UserEntity)

      // Lưu từng row
      const lstErr = []
      const isProduct = process.env.IS_PRODUCT == 'true'
      for (const row of data.lstData) {
        try {
          const supplierEntity = new SupplierEntity()
          supplierEntity.companyId = user.companyId
          supplierEntity.createdBy = user.id
          supplierEntity.code = row.code
          supplierEntity.name = row.name
          supplierEntity.dealName = row.dealName
          supplierEntity.createYear = row.createYear
          supplierEntity.address = row.address
          supplierEntity.dealAddress = row.dealAddress
          supplierEntity.represen = row.represen
          supplierEntity.chief = row.chief
          supplierEntity.capital = row.capital
          supplierEntity.assets = row.assets
          supplierEntity.bankNumber = row.bankNumber
          supplierEntity.bankname = row.bankname
          supplierEntity.bankBrand = row.bankBrand
          supplierEntity.description = row.description
          supplierEntity.contactName = row.contactName
          supplierEntity.phone = row.phone
          supplierEntity.email = row.email
          supplierEntity.status = enumData.SupplierStatus.DaDuyet.code
          supplierEntity.email = row.email
          supplierEntity.fileAccount = ''
          supplierEntity.fileBill = ''
          supplierEntity.fileInfoBill = ''
          supplierEntity.fileMST = ''
          if (user.employeeId) supplierEntity.introducerId = user.employeeId
          const supplier = await supplierRepo.save(supplierEntity)

          if (isProduct) {
            if (!req || !req.headers || !req.headers.authorization)
              throw new UnauthorizedException('Không có quyền truy cập! (code: BEARER_TOKEN_ERROR)')
            await apeAuthApiHelper.createUserPMS(
              req.headers.authorization,
              user.companyId,
              enumApeAuth.UserType.CompanyPackageSupplier,
              row.username,
              row.password,
            )
          }

          const newUserEntity = userRepo.create({
            companyId: user.companyId,
            createdBy: user.id,
            username: row.username,
            password: !isProduct ? row.password : '',
            type: enumData.UserType.Supplier.code,
            supplierId: supplier.id,
            isDeleted: false,
          })
          const createdUser = await userRepo.save(newUserEntity)
          await supplierRepo.update(supplier.id, { userId: createdUser.id })

          // Supplier History
          const suplierHistory = new SupplierHistoryEntity()
          suplierHistory.companyId = user.companyId
          suplierHistory.createdBy = user.id
          suplierHistory.supplierId = supplier.id
          suplierHistory.description = `Tạo NCC bằng import excel`
          await manager.getRepository(SupplierHistoryEntity).save(suplierHistory)

          checkResult.lstSupplierId.push(supplier.id)

          numRowSuccess++
        } catch (error) {
          lstErr.push({ ...row, errorMessage: JSON.stringify(error) })
        }
      }

      if (lstErr.length > 0) {
        checkResult.lstError = lstErr
        checkResult.message = `Import thành công ${numRowSuccess}/${data.lstData.length} supplier!`
      } else {
        checkResult.message = `Import thành công ${data.lstData.length} supplier!`
      }

      return checkResult
    })
  }

  /** Lưu thông tin supplier service */
  public async saveSupplierService(user: UserDto, data: { id: string; supplierType: string }) {
    // Check user
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const supplierService: any = await this.supplierServiceRepo.findOne({
      where: { id: data.id, companyId: user.companyId },
      relations: { service: true },
      select: { id: true, supplierId: true, supplierType: true, service: { code: true, name: true } },
    })
    if (!supplierService) throw new Error(ERROR_NOT_FOUND_DATA)

    if (data.supplierType == supplierService.supplierType) {
      throw new Error('Vui lòng thay đổi dữ liệu trước khi lưu.')
    }

    // lưu data
    await this.supplierServiceRepo.update(data.id, {
      supplierType: data.supplierType,
      updatedBy: user.id,
    })

    // Supplier History
    const suplierHistory = new SupplierHistoryEntity()
    suplierHistory.companyId = user.companyId
    suplierHistory.createdBy = user.id
    suplierHistory.supplierId = supplierService.supplierId
    suplierHistory.description = `Sửa phân loại NCC của Item [${supplierService.__service__.code + ' - ' + supplierService.__service__.name}]`
    await suplierHistory.save()

    return { message: ACTION_SUCCESS }
  }

  //#endregion

  /** Lấy thông tin năng lực Item của NCC */
  public async getSupplierCapacity(user: UserDto, supplierServiceId: string) {
    return await this.repo.manager.getRepository(SupplierCapacityEntity).find({
      where: { supplierServiceId, parentId: IsNull(), companyId: user.companyId, isDeleted: false },
      relations: {
        supplierCapacityListDetails: true,
        supplierCapacityYearValues: true,
        childs: { supplierCapacityListDetails: true, supplierCapacityYearValues: true },
      },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })
  }

  /** Danh sách Item mà bạn có quyền access */
  private async getAccessAbleServices(user: UserDto, serviceId?: string) {
    const whereAccess: any = { employeeId: user.employeeId, companyId: user.companyId, isDeleted: false }
    if (serviceId) whereAccess.serviceId = serviceId
    const listServiceAccess = await this.serviceAccessRepo.find({ where: whereAccess, select: { serviceId: true } })
    const lstServiceId = listServiceAccess.map((p) => p.serviceId)
    let whereCon: any = { isLast: true, approveById: user.employeeId, companyId: user.companyId } // Người duyệt
    if (serviceId) whereCon.id = serviceId

    if (lstServiceId.length > 0) {
      const where2: any = { isLast: true, id: In(lstServiceId) } // Người phụ trách
      whereCon = [whereCon, where2]
    }

    const lstService = await this.serviceRepo.find({ where: whereCon, select: { id: true } })
    return lstService.map((c) => c.id)
  }

  /** Lấy ds supplierService để tạo YC thẩm định */
  public async supplierServicePagination(user: UserDto, data: PaginationDto) {
    const services = await this.getAccessAbleServices(user, data.where.serviceId)
    if (services.length === 0) return [[], 0]

    const whereCon: any = { serviceId: In(services), status: enumData.SupplierServiceStatus.DaDuyet.code, companyId: user.companyId }
    if (data.where.statusExpertise) whereCon.statusExpertise = data.where.statusExpertise
    if (data.where.supplierName) whereCon.supplier = [{ code: Like(`%${data.where.supplierName}%`) }, { name: Like(`%${data.where.supplierName}%`) }]

    const res: any[] = await this.supplierServiceRepo.findAndCount({
      where: whereCon,
      relations: { supplier: true, service: true },
      skip: data.skip,
      take: data.take,
      order: { score: 'DESC' },
    })
    if (res[0].length == 0) return res

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.SupplierServiceExpertiseStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }
    for (const item of res[0]) {
      item.itemName = item.__service__?.code + ' - ' + item.__service__?.name
      delete item.__service__
      item.supplierName = await item.__supplier__.name
      delete item.__supplier__

      item.statusExpertiseName = dicStatus[item.statusExpertise]
      const objExpertise = await this.supplierExpertiseRepo.findOne({
        where: { supplierServiceId: item.id, companyId: user.companyId, isDeleted: false },
        order: { createdAt: 'DESC' },
        select: { id: true, changeDate: true },
      })
      if (objExpertise) item.lastUpdateExpertise = objExpertise.changeDate
    }

    return res
  }

  /** Lấy các LVMH mà NCC có thể đăng ký thêm */
  public async getServicesCanAdd(user: UserDto, data: { supplierId: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.supplierId) throw new BadRequestException('Không xác định nhà cung cấp!')

    const whereCommon: any = { companyId: user.companyId, isDeleted: false }
    {
      const lstSupplierService = await this.supplierServiceRepo.find({ where: { supplierId: data.supplierId }, select: { serviceId: true } })
      const lstServiceId = lstSupplierService.map((c) => c.serviceId)
      if (lstServiceId.length > 0) {
        whereCommon.id = Not(In(lstServiceId))
      }
    }
    const whereCommon1: any = { ...whereCommon, isLast: true, statusCapacity: enumData.StatusServiceCapacity.DaDuyet.code }

    const whereCon: any = [
      { ...whereCommon, isLast: false, childs: [whereCommon1, { ...whereCommon, isLast: false, childs: whereCommon1 }] },
      whereCommon1,
    ]

    return await this.serviceRepo.find({
      where: whereCon,
      order: { code: 'ASC' },
    })
  }

  /** Thêm Item vào NCC */
  public async addSupplierService(user: UserDto, data: { supplierId: string; serviceId: string }) {
    if (!data.serviceId) throw new BadRequestException('Không xác định lĩnh vực mua hàng!')
    if (!data.supplierId) throw new BadRequestException('Không xác định nhà cung cấp!')

    const supplierService: any = await this.supplierServiceRepo.findOne({
      where: { supplierId: data.supplierId, serviceId: data.serviceId },
      select: { id: true },
    })
    if (supplierService) throw new ConflictException('Lĩnh vực mua hàng đã tồn tại trong nhà cung cấp.')

    const service = await this.serviceRepo.findOne({ where: { id: data.serviceId, isDeleted: false }, select: { id: true, code: true, name: true } })
    if (!service) throw new ConflictException('Lĩnh vực mua hàng không tồn tại.')

    const newSupplierService = new SupplierServiceEntity()
    newSupplierService.companyId = user.companyId
    newSupplierService.createdBy = user.id
    newSupplierService.supplierId = data.supplierId
    newSupplierService.serviceId = data.serviceId
    newSupplierService.status = enumData.SupplierServiceStatus.ChuaDangKy.code
    newSupplierService.statusExpertise = enumData.SupplierServiceExpertiseStatus.ChuaDangKy.code
    await newSupplierService.save()

    // Supplier History
    const suplierHistory = new SupplierHistoryEntity()
    suplierHistory.companyId = user.companyId
    suplierHistory.createdBy = user.id
    suplierHistory.supplierId = data.supplierId
    suplierHistory.description = `Thêm Item [${service.code + ' - ' + service.name}] vào NCC`
    await suplierHistory.save()

    return { message: ACTION_SUCCESS }
  }

  /**  Phân trang Danh sách NCC theo dịch vụ */
  public async loadSupplierService(user: UserDto, data: { supplierId: string }) {
    if (!data.supplierId) throw new BadRequestException('Không xác định nhà cung cấp!')

    const res: any[] = await this.supplierServiceRepo.find({
      where: { supplierId: data.supplierId },
      relations: { supplier: true, service: true },
      order: { service: { code: 'ASC' } },
    })

    for (const item of res) {
      item.itemName = item.__service__.code + ' - ' + item.__service__.name
      delete item.__service__
      item.supplierName = await item.__supplier__.name
      delete item.__supplier__

      item.statusExpertiseName = enumData.SupplierServiceExpertiseStatus.ChuaThamDinh.name
      const objExpertise = await this.supplierExpertiseRepo.findOne({
        where: { supplierServiceId: item.id, companyId: user.companyId, isDeleted: false },
        order: { createdAt: 'DESC' },
        select: { id: true, changeDate: true, status: true },
      })

      if (objExpertise) {
        item.statusExpertiseName = enumData.SupplierExpertiseStatus[objExpertise.status]?.name || ''
      }
      item.statusName = enumData.SupplierServiceStatus[item.status]?.name || ''
    }

    return res
  }

  /** Tạo yêu cầu thẩm định */
  public async supplierServiceToExpertise(user: UserDto, data: CreateExpertiseDto) {
    if (data.isCheckLaw && !data.employeeLawId) {
      throw new Error('Vui lòng chọn thành viên thẩm định thông tin pháp lý.')
    }

    const lstSupplierExpertiseId = []
    await this.repo.manager.transaction(async (manager) => {
      const supplierServiceRepo = manager.getRepository(SupplierServiceEntity)
      const supplierExpertiseRepo = manager.getRepository(SupplierExpertiseEntity)
      const supplierExpertiseMemberRepo = manager.getRepository(SupplierExpertiseMemberEntity)
      for (let supplierServiceId of data.supplierServiceIds) {
        const supplierService: any = await supplierServiceRepo.findOne({
          where: { id: supplierServiceId, companyId: user.companyId },
          relations: { service: { serviceAccess: true } },
          select: { id: true, serviceId: true, supplierId: true, service: { id: true, serviceAccess: { id: true } } },
        })
        if (!supplierService) throw new Error(ERROR_NOT_FOUND_DATA)

        // Nhân viên duyệt năng lực
        if (supplierService.__service__.__serviceAccess__.length == 0) {
          throw new Error('Lĩnh vực mua hàng chưa cấu hình nhân viên phụ trách.')
        }

        const expertise = new SupplierExpertiseEntity()
        expertise.companyId = user.companyId
        expertise.createdBy = user.id
        expertise.supplierServiceId = supplierService.id
        expertise.serviceId = supplierService.serviceId
        expertise.supplierId = supplierService.supplierId

        // Nhân viên duyệt pháp lý
        if (data.employeeLawId) expertise.approvedLawId = data.employeeLawId

        expertise.note = data.note
        expertise.isCheckLaw = data.isCheckLaw
        expertise.isCheckCapacity = data.isCheckCapacity
        expertise.changeDate = data.changeDate
        expertise.status = enumData.SupplierExpertiseStatus.DangThamDinh.code

        if (data.isCheckCapacity) {
          expertise.statusCapacity = enumData.SupplierExpertiseCapacityStatus.ChuaThamDinh.code
        } else {
          expertise.statusCapacity = enumData.SupplierExpertiseCapacityStatus.KhongThamDinh.code
        }

        if (data.isCheckLaw) {
          expertise.statusLaw = enumData.SupplierExpertiseLawStatus.ChuaThamDinh.code
        } else {
          expertise.statusLaw = enumData.SupplierExpertiseLawStatus.KhongThamDinh.code
        }

        const createdEntity = await supplierExpertiseRepo.save(expertise)

        for (const employeeId of data.members) {
          const expertiseMember = new SupplierExpertiseMemberEntity()
          expertiseMember.companyId = user.companyId
          expertiseMember.createdBy = user.id
          expertiseMember.employeeId = employeeId
          expertiseMember.supplierExpertiseId = createdEntity.id
          await supplierExpertiseMemberRepo.save(expertiseMember)
        }

        lstSupplierExpertiseId.push(createdEntity.id)
      }
    })

    for (const supplierExpertiseId of lstSupplierExpertiseId) {
      this.emailService.GuiThongBaoThamDinhNcc(supplierExpertiseId)
    }

    return { message: UPDATE_SUCCESS }
  }

  /** Đổi mk NCC */
  public async updatePassword(user: UserDto, data: { id: string; newPassword: string }) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    const userEntity = await this.userRepo.findOne({ where: { id: entity.userId, companyId: user.companyId } })
    if (!userEntity) throw new Error(ERROR_NOT_FOUND_DATA)

    // Update user bên auth
    const isProduct = process.env.IS_PRODUCT == 'true'
    if (isProduct) {
      await apeAuthApiHelper.updatePasswordSecret(user.companyId, userEntity.username, data.newPassword)
    } else {
      const hashedPassword = await hash(data.newPassword, PWD_SALT_ROUNDS)
      await this.userRepo.update(data.id, { password: hashedPassword, updatedBy: user.id })
    }

    return { message: 'Cập nhật mật khẩu thành công!' }
  }

  /** Xoá NCC (chức năng bảo mật) */
  public async deleteSuppliers(user: UserDto, data: { confirmCode: string; lstSupplierId: string[] }) {
    const pw = this.configService.get<string>('JWT_SECRET') || 'ape-bidding'
    if (data.confirmCode != pw) throw new NotAcceptableException('Mã xác nhận không đúng, bạn đang sử dụng chức năng không được cho phép!')
    if (data.lstSupplierId == null || data.lstSupplierId.length == 0) throw new NotAcceptableException('Vui lòng ghi rõ danh sách NCC cần xoá trước')

    // check user
    if (!user.employeeId) {
      throw new NotAcceptableException('Bạn đang sử dụng chức năng không được cho phép!')
    }

    const emp = await this.employeeRepo.findOne({ where: { id: user.employeeId, companyId: user.companyId } })
    if (emp) {
      const department = await emp.department
      if (department.code != 'IT') {
        throw new NotAcceptableException('Bạn đang sử dụng chức năng không được cho phép!')
      }
    } else {
      throw new NotAcceptableException('Bạn đang sử dụng chức năng không được cho phép!')
    }

    return await this.repo.manager.transaction(async (manager) => {
      const supplierRepo = manager.getRepository(SupplierEntity)
      const bidSupplierRepo = manager.getRepository(BidSupplierEntity)
      const bidDealSupplierRepo = manager.getRepository(BidDealSupplierEntity)
      const bidAutionSupplierRepo = manager.getRepository(BidAuctionSupplierEntity)

      const supplierNotifyRepo = manager.getRepository(SupplierNotifyEntity)

      const supplierExpertiseYearDetailRepo = manager.getRepository(SupplierExpertiseYearDetailEntity)
      const supplierExpertiseDetailRepo = manager.getRepository(SupplierExpertiseDetailEntity)
      const supplierExpertiseLawDetailRepo = manager.getRepository(SupplierExpertiseLawDetailEntity)
      const supplierExpertiseMemberRepo = manager.getRepository(SupplierExpertiseMemberEntity)
      const supplierExpertiseRepo = manager.getRepository(SupplierExpertiseEntity)

      const supplierCapacityListDetailRepo = manager.getRepository(SupplierCapacityListDetailEntity)
      const supplierCapacityYearValueRepo = manager.getRepository(SupplierCapacityYearValueEntity)
      const supplierCapacityRepo = manager.getRepository(SupplierCapacityEntity)

      const supplierServiceRepo = manager.getRepository(SupplierServiceEntity)

      const userConfirmRepo = manager.getRepository(UserConfirmCodeEntity)
      const userRepo = manager.getRepository(UserEntity)

      const res = []
      for (const supplierId of data.lstSupplierId) {
        //#region Check data
        const supplier = await supplierRepo.findOne({ where: { id: supplierId, companyId: user.companyId } })
        if (!supplier) {
          res.push(`NCC [${supplierId}] không tồn tại!`)
          continue
        }
        const bidSupplier = await bidSupplierRepo.findOne({ where: { supplierId, companyId: user.companyId } })
        if (bidSupplier) {
          res.push(`NCC [${supplierId}] đã tham gia thầu. Không được xoá!`)
          continue
        }
        const bidDealSupplier = await bidDealSupplierRepo.findOne({ where: { supplierId, companyId: user.companyId } })
        if (bidDealSupplier) {
          res.push(`NCC [${supplierId}] đã tham gia đàm phán. Không được xoá!`)
          continue
        }
        const bidAutionSupplier = await bidAutionSupplierRepo.findOne({ where: { supplierId, companyId: user.companyId } })
        if (bidAutionSupplier) {
          res.push(`NCC [${supplierId}] đã tham gia đấu giá. Không được xoá!`)
          continue
        }
        //#endregion

        //#region xoá SupplierNotifyEntity
        var lstSupplierNotify = await supplier.supplierNotifys
        if (lstSupplierNotify.length > 0) {
          await supplierNotifyRepo.delete({ supplierId })
        }
        //#endregion

        //#region xoá supplierExpertiseRepository
        var lstSuppierExpertise = await supplier.supplierExpertise
        if (lstSuppierExpertise.length > 0) {
          for (const supplierExpertise of lstSuppierExpertise) {
            // xoá thẩm định năng lực
            var lstSupplierExpertiseDetail = await supplierExpertise.supplierExpertiseDetails
            if (lstSupplierExpertiseDetail.length > 0) {
              for (const supplierExpertiseDetail of lstSupplierExpertiseDetail) {
                var lstSupplierExpertiseYearDetail = await supplierExpertiseDetail.supplierExpertiseYearDetails
                if (lstSupplierExpertiseYearDetail.length > 0) {
                  await supplierExpertiseYearDetailRepo.delete({
                    supplierExpertiseDetailId: supplierExpertiseDetail.id,
                  })
                }
              }
              await supplierExpertiseDetailRepo.delete({
                supplierExpertiseId: supplierExpertise.id,
              })
            }

            // xoá thẩm định pháp lý NCC
            var lstSupplierExpertiseLawDetail = await supplierExpertise.supplierExpertiseLawDetails
            if (lstSupplierExpertiseLawDetail.length > 0) {
              await supplierExpertiseLawDetailRepo.delete({
                supplierExpertiseId: supplierExpertise.id,
              })
            }

            // xoá thành viên hội đồng thẩm định
            var lstSupplierExpertiseMember = await supplierExpertise.members
            if (lstSupplierExpertiseMember.length > 0) {
              await supplierExpertiseMemberRepo.delete({
                supplierExpertiseId: supplierExpertise.id,
              })
            }
          }
          await supplierExpertiseRepo.delete({ supplierId })
        }
        //#endregion

        //#region Xoá supplierCapacityRepository
        var lstSupplierCapacity = await supplier.supplierCapacities
        if (lstSupplierCapacity.length > 0) {
          for (const supplierCapacity of lstSupplierCapacity) {
            // xoá dữ liệu danh sách
            const lstSupplierCapacityListDetail = await supplierCapacity.supplierCapacityListDetails
            if (lstSupplierCapacityListDetail.length > 0) {
              await supplierCapacityListDetailRepo.delete({
                supplierCapacityId: supplierCapacity.id,
              })
            }

            // xoá dữ liệu theo năm
            const lstSupplierCapacityYearValue = await supplierCapacity.supplierCapacityYearValues
            if (lstSupplierCapacityYearValue.length > 0) {
              await supplierCapacityYearValueRepo.delete({
                supplierCapacityId: supplierCapacity.id,
              })
            }
            // xoá child
            if (supplierCapacity.parentId) await supplierCapacityRepo.delete(supplierCapacity.id)
          }
          await supplierCapacityRepo.delete({ supplierId })
        }
        //#endregion

        //#region Xoá supplierServiceRepository
        const lstSupplierService = await supplier.supplierServices
        if (lstSupplierService.length > 0) {
          await supplierServiceRepo.delete({ supplierId })
        }
        //#endregion

        //#region Cập nhật userRepo
        const userUpdate = await supplier.user
        let userId = ''
        if (userUpdate) {
          userId = userUpdate.id
          await userRepo.update(userId, { supplierId: null })
        }
        //#endregion

        // xoá NCC
        await supplierRepo.delete(supplierId)

        //#region Xoá supplierServiceRepository
        if (userId != '') {
          const user = await userRepo.findOne({ where: { id: userId } })
          if (user) {
            const lstUserConfirm = await user.userConfirm
            if (lstUserConfirm.length > 0) {
              await userConfirmRepo.delete({ userId })
            }

            await userRepo.delete(userId)
          }
        }
        //#endregion

        res.push(`NCC [${supplierId}] đã được xoá.`)
      }

      return res
    })
  }

  /** Lấy ds NCC */
  public async find(user: UserDto, data: { bidId?: string; isSuccessBid?: boolean }) {
    const whereCon: any = { companyId: user.companyId, isDeleted: false }
    // Lấy các NCC được mời tham gia gói thầu bidId
    if (data.bidId) {
      whereCon.bidSupplier = { bidId: data.bidId, isDeleted: false }
      if (data.isSuccessBid) {
        delete whereCon.bidSupplier
        const lstBidSupplierSuccess = await this.bidSupplierRepo.find({
          where: {
            bid: { parentId: data.bidId, isDeleted: false },
            isSuccessBid: true,
            isDeleted: false,
          },
          select: { id: true, supplierId: true },
        })
        if (lstBidSupplierSuccess.length == 0) return []
        const lstId = lstBidSupplierSuccess.map((c) => c.supplierId).filter((value, idx, self) => self.indexOf(value) == idx)
        whereCon.id = In(lstId)
      }
    }

    return await this.repo.find({ where: whereCon, select: { id: true, code: true, name: true } })
  }

  /** Lịch sử đấu thầu của NCC */
  public async paginationBidHistory(user: UserDto, data: PaginationDto) {
    if (!data.where.supplierId) return [[], 0]

    const res: any[] = await this.bidSupplierRepo.findAndCount({
      where: { supplierId: data.where.supplierId, companyId: user.companyId, isDeleted: false, bid: { parentId: IsNull(), isDeleted: false } },
      relations: { bid: true },
      skip: data.skip,
      take: data.take,
      select: { id: true, status: true, bid: { code: true, name: true, status: true } },
    })

    const dicBidStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicBidStatus[c.code] = c.name))
    }

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }
    for (const item of res[0]) {
      item.bidCode = item.__bid__.code
      item.bidName = item.__bid__.name
      item.bidStatus = dicBidStatus[item.__bid__.status]
      delete item.__bid__

      item.statusName = dicStatus[item.status]
    }

    return res
  }

  //#region CLIENT

  /** Kiểm tra quyền xem kết quả thẩm định */
  public async checkIsYourExpertise(user: UserDto, data: { supplierExpertiseId: string }) {
    if (!user.supplierId) return { isSupplierExpertise: false }
    if (!data || !data.supplierExpertiseId) return { isSupplierExpertise: false }

    const supplierExpertiseRepo = this.repo.manager.getRepository(SupplierExpertiseEntity)
    const entity = await supplierExpertiseRepo.findOne({
      where: {
        supplierId: user.supplierId,
        id: data.supplierExpertiseId,
        status: enumData.SupplierExpertiseStatus.KhongDuyetQT2.code,
        companyId: user.companyId,
      },
      select: { id: true },
    })
    if (entity) {
      return { isSupplierExpertise: true }
    } else {
      return { isSupplierExpertise: false }
    }
  }

  /** Lấy dữ liệu mà MPO đề xuất cho NCC */
  public async getDataSuggest(user: UserDto, data: { supplierExpertiseId: string }) {
    if (!user.supplierId) throw new NotFoundException('Không có quyền truy cập')
    if (!data?.supplierExpertiseId) throw new NotAcceptableException(ERROR_VALIDATE)

    return await this.supplierExpertiseRepo.findOne({
      where: { supplierId: user.supplierId, id: data.supplierExpertiseId, companyId: user.companyId },
      relations: {
        service: true,
        supplier: true,
        supplierExpertiseLawDetails: true,
        supplierExpertiseDetails: { supplierExpertiseYearDetails: true },
        supplierService: {
          capacities: {
            supplierCapacityListDetails: true,
            supplierCapacityYearValues: true,
            childs: { supplierCapacityListDetails: true, supplierCapacityYearValues: true },
          },
        },
      },
    })
  }

  /** NCC chấp nhận thay đổi dữ liệu theo yêu cầu của MPO */
  public async supplierAcceptChangeData(user: UserDto, data: { supplierExpertiseId: string }) {
    if (!user.supplierId) throw new NotFoundException('Không có quyền truy cập')
    if (!data?.supplierExpertiseId) throw new NotAcceptableException(ERROR_VALIDATE)

    await this.repo.manager.transaction(async (manager) => {
      const supplierServiceRepo = new SupplierServiceRepository(SupplierServiceEntity, manager)
      const supplierRepo = manager.getRepository(SupplierEntity)
      const supplierCapacityRepo = manager.getRepository(SupplierCapacityEntity)
      const supplierCapacityListRepo = manager.getRepository(SupplierCapacityListDetailEntity)
      const supplierCapacityYearRepo = manager.getRepository(SupplierCapacityYearValueEntity)

      const supplierExpertiseRepo = manager.getRepository(SupplierExpertiseEntity)
      const supplierExpertiseLawDetailRepo = manager.getRepository(SupplierExpertiseLawDetailEntity)
      const supplierExpertiseDetailRepo = manager.getRepository(SupplierExpertiseDetailEntity)
      let supplierEntity = await supplierRepo.findOne({
        where: { id: user.supplierId, companyId: user.companyId, isDeleted: false },
      })
      let supplierLawEntity = await supplierExpertiseLawDetailRepo.findOne({
        where: { supplierExpertiseId: data.supplierExpertiseId, companyId: user.companyId, isDeleted: false },
      })
      if (!supplierEntity) throw new Error(ERROR_NOT_FOUND_DATA)

      // Gán giá trị Law
      if (supplierLawEntity) {
        supplierEntity.name = supplierLawEntity.name ? supplierLawEntity.name : supplierEntity.name
        supplierEntity.code = supplierLawEntity.code ? supplierLawEntity.code : supplierEntity.code
        supplierEntity.dealName = supplierLawEntity.dealName ? supplierLawEntity.dealName : supplierEntity.dealName
        supplierEntity.address = supplierLawEntity.address ? supplierLawEntity.address : supplierEntity.address
        supplierEntity.dealAddress = supplierLawEntity.dealAddress ? supplierLawEntity.dealAddress : supplierEntity.dealAddress
        supplierEntity.fileMST = supplierLawEntity.fileMST ? supplierLawEntity.fileMST : supplierEntity.fileMST
        supplierEntity.represen = supplierLawEntity.represen ? supplierLawEntity.represen : supplierEntity.represen
        supplierEntity.chief = supplierLawEntity.chief ? supplierLawEntity.chief : supplierEntity.chief
        supplierEntity.bankNumber = supplierLawEntity.bankNumber ? supplierLawEntity.bankNumber : supplierEntity.bankNumber
        supplierEntity.bankname = supplierLawEntity.bankname ? supplierLawEntity.bankname : supplierEntity.bankname
        supplierEntity.bankBrand = supplierLawEntity.bankBrand ? supplierLawEntity.bankBrand : supplierEntity.bankBrand
        supplierEntity.fileAccount = supplierLawEntity.fileAccount ? supplierLawEntity.fileAccount : supplierEntity.fileAccount
        supplierEntity.contactName = supplierLawEntity.contactName ? supplierLawEntity.contactName : supplierEntity.contactName
        supplierEntity.email = supplierLawEntity.email ? supplierLawEntity.email : supplierEntity.email
        supplierEntity.phone = supplierLawEntity.phone ? supplierLawEntity.phone : supplierEntity.phone
        supplierEntity.createYear = supplierLawEntity.createYear ? supplierLawEntity.createYear : supplierEntity.createYear
        supplierEntity.capital = supplierLawEntity.capital ? supplierLawEntity.capital : supplierEntity.capital
        supplierEntity.assets = supplierLawEntity.assets ? supplierLawEntity.assets : supplierEntity.assets
        supplierEntity.fileBill = supplierLawEntity.fileBill ? supplierLawEntity.fileBill : supplierEntity.fileBill
        supplierEntity.fileInfoBill = supplierLawEntity.fileInfoBill ? supplierLawEntity.fileInfoBill : supplierEntity.fileInfoBill
        supplierEntity.updatedBy = user.id

        // console.log(supplierEntity)

        await supplierRepo.update(supplierEntity.id, supplierEntity)
      }

      // Danh sách giá trị capacity gợi ý
      let supplierExpertiseDetailEntity = await supplierExpertiseDetailRepo.find({
        where: { supplierExpertiseId: data.supplierExpertiseId, companyId: user.companyId, isDeleted: false },
      })
      const length = supplierExpertiseDetailEntity.length
      // Lọc qua danh sách và Gán gía trị capacity
      // Hàm không cần lọc lại danh sách con vì chỉ cập nhật value nên không cần
      for (let i = 0; i < length; i++) {
        let item = supplierExpertiseDetailEntity[i]
        // Tìm supplierCapacity và gắn giá trị vào
        let supplierCapacityEntity = await item.supplierCapacity
        if (!supplierCapacityEntity) throw new Error(ERROR_NOT_FOUND_DATA)

        // Cập nhật giá trị mới
        await supplierCapacityRepo.update(supplierCapacityEntity.id, {
          value: item.value,
          updatedBy: user.id,
        })

        // Nếu kiểu list
        if (item.type === enumData.DataType.List.code) {
          // Gán ischosen về false hết và chọn lại
          await supplierCapacityListRepo.update(
            {
              supplierCapacityId: supplierCapacityEntity.id,
              isDeleted: false,
            },
            { isChosen: false, updatedBy: user.id },
          )
          await supplierCapacityListRepo.update(
            {
              id: item.value,
              isDeleted: false,
            },
            { isChosen: true, updatedBy: user.id },
          )
        }

        // Nếu có data year
        let yearData = await item.supplierExpertiseYearDetails
        const yearLength = yearData.length
        if (yearLength > 0) {
          // Xoá hết data cũ và thêm lại
          await supplierCapacityYearRepo.delete({
            supplierCapacityId: item.supplierCapacityId,
          })
          for (let y = 0; y < yearLength; y++) {
            let itemYear = yearData[y]
            const listDetail = new SupplierCapacityYearValueEntity()
            listDetail.companyId = user.companyId
            listDetail.createdBy = user.id
            listDetail.supplierCapacityId = supplierCapacityEntity.id
            listDetail.isDeleted = false
            listDetail.value = itemYear.value
            listDetail.year = itemYear.year
            await supplierCapacityYearRepo.save(listDetail)
          }
        }
      }

      // Đổi trạng thái thẩm định thành đã duyệt
      const supplierExpertise = await supplierExpertiseRepo.findOne({ where: { id: data.supplierExpertiseId, companyId: user.companyId } })
      await supplierExpertiseRepo.update(data.supplierExpertiseId, {
        status: enumData.SupplierExpertiseStatus.DaThamDinh.code,
        updatedBy: user.id,
      })

      // Đổi trạng thái Năng lực thành đã thẩm định
      const supplierServiceId = supplierExpertise?.supplierServiceId
      if (!supplierServiceId) throw new Error(ERROR_NOT_FOUND_DATA)
      await supplierServiceRepo.update(supplierServiceId, {
        statusExpertise: enumData.SupplierServiceExpertiseStatus.DaThamDinh.code,
        updatedBy: user.id,
      })

      // Tính lại điểm
      const score = await supplierServiceRepo.calScore(supplierServiceId)
      await supplierServiceRepo.update(supplierServiceId, {
        score,
        updatedBy: user.id,
      })
    })

    await this.emailService.supplierAcceptChangeData(user.supplierId, data.supplierExpertiseId)

    return { message: UPDATE_SUCCESS }
  }
  //#endregion
}
