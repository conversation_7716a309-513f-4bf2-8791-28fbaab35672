import { Repository, IsNull } from 'typeorm'
import { enumData } from '../constants'
import { UserDto } from '../dto'
import { BidAuctionSupplierPriceValueEntity, BidPriceColEntity, BidPriceEntity } from '../entities'
import { CustomRepository } from '../typeorm'

@CustomRepository(BidPriceEntity)
export class BidPriceRepository extends Repository<BidPriceEntity> {
  // Lấy danh sách yêu cầu giá của gói thầu
  async getPrice(user: UserDto, bidId: string) {
    return await this.find({
      where: { bidId, companyId: user.companyId, parentId: IsNull() },
      relations: {
        bidPriceListDetails: true,
        bidPriceColValue: true,
        childs: { bidPriceListDetails: true, bidPriceColValue: true, childs: { bidPriceListDetails: true, bidPriceColValue: true } },
      },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } } },
    })
  }
}

@CustomRepository(BidPriceColEntity)
export class BidPriceColRepository extends Repository<BidPriceColEntity> {
  /** Lấy các cột động "nhân viên nhập" gói thầu */
  async getBidPriceColMPO(user: UserDto, bidId: string) {
    return await this.find({
      where: { bidId, companyId: user.companyId, isDeleted: false, colType: enumData.ColType.MPO.code },
      order: { sort: 'ASC', createdAt: 'ASC' },
    })
  }

  /** Lấy tất cả cột động gói thầu */
  async getBidPriceColAll(user: UserDto, bidId: string) {
    return await this.find({
      where: { bidId, companyId: user.companyId, isDeleted: false },
      order: { sort: 'ASC', createdAt: 'ASC' },
    })
  }
}
@CustomRepository(BidAuctionSupplierPriceValueEntity)
export class BidAuctionSupplierPriceValueRepository extends Repository<BidAuctionSupplierPriceValueEntity> {}
