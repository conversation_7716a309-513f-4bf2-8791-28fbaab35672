import { ConflictException, Injectable, NotAcceptableException, NotFoundException } from '@nestjs/common'
import {
  ERROR_CODE_TAKEN,
  ERROR_EMAIL_TAKEN,
  ERROR_NOT_FOUND_DATA,
  ERROR_VALIDATE,
  ERROR_YOU_DO_NOT_HAVE_PERMISSION,
  UPDATE_SUCCESS,
} from '../../constants'
import { ServiceRepository, UserRepository, SupplierServiceRepository, ServiceCapacityRepository, SupplierRepository } from '../../repositories'
import { enumData } from '../../constants/enumData'
import { UserDto } from '../../dto'
import { SupplierUpdateLawDto, SupplierUpdateCapacityDto, SupplierRegisterDto, SupplierAddCapacitiesDto } from '../supplier/dto'
import { EmailService } from '../email/email.service'
import { In, IsNull, Not } from 'typeorm'
import {
  DataHistoryEntity,
  EmployeeEntity,
  ServiceCapacityEntity,
  ServiceEntity,
  SupplierCapacityEntity,
  SupplierCapacityListDetailEntity,
  SupplierCapacityYearValueEntity,
  SupplierEntity,
  SupplierExpertiseEntity,
  SupplierHistoryEntity,
  SupplierServiceEntity,
  UserEntity,
} from '../../entities'
import { apeAuthApiHelper, enumApeAuth } from '../../helpers'

@Injectable()
export class SupplierRegistrationService {
  constructor(
    private readonly serviceRepo: ServiceRepository,
    private readonly serviceCapacityRepo: ServiceCapacityRepository,
    private readonly supplierRepo: SupplierRepository,
    private readonly userRepo: UserRepository,
    private readonly supplierServiceRepo: SupplierServiceRepository,
    private readonly emailService: EmailService,
  ) {}

  /** Lấy các LVMH */
  public async getServices(req: Request) {
    const companyId = await apeAuthApiHelper.getCompanyId(req)
    const whereCommon: any = { companyId, isDeleted: false }
    const whereCommon1: any = { ...whereCommon, isLast: true, statusCapacity: enumData.StatusServiceCapacity.DaDuyet.code }
    const whereCon: any = [
      { ...whereCommon, isLast: false, childs: [whereCommon1, { ...whereCommon, isLast: false, childs: whereCommon1 }] },
      whereCommon1,
    ]
    return await this.serviceRepo.find({
      where: whereCon,
      order: { code: 'ASC' },
    })
  }

  /** Lấy các LVMH mà Doanh nghiệp có thể đăng ký thêm */
  public async getServicesCanAdd(user: UserDto) {
    if (!user.supplierId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const whereCommon: any = { companyId: user.companyId, isDeleted: false }
    {
      const lstSupplierService = await this.supplierServiceRepo.find({ where: { supplierId: user.supplierId }, select: { serviceId: true } })
      const lstServiceId = lstSupplierService.map((c) => c.serviceId)
      if (lstServiceId.length > 0) {
        whereCommon.id = Not(In(lstServiceId))
      }
    }
    const whereCommon1: any = { ...whereCommon, isLast: true, statusCapacity: enumData.StatusServiceCapacity.DaDuyet.code }

    const whereCon: any = [
      { ...whereCommon, isLast: false, childs: [whereCommon1, { ...whereCommon, isLast: false, childs: whereCommon1 }] },
      whereCommon1,
    ]

    return await this.serviceRepo.find({
      where: whereCon,
      order: { code: 'ASC' },
    })
  }

  public async getServiceCapacity(req: Request, data: { serviceId: string }) {
    const companyId = await apeAuthApiHelper.getCompanyId(req)
    if (!data.serviceId) throw new NotAcceptableException(ERROR_VALIDATE)

    return await this.serviceCapacityRepo.find({
      where: { serviceId: data.serviceId, parentId: IsNull(), companyId, isDeleted: false },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
      relations: {
        serviceCapacityListDetails: true,
        childs: { serviceCapacityListDetails: true },
      },
    })
  }

  /** Hàm đăng ký Doanh nghiệp mới */
  async supplierRegistration(req: Request, data: SupplierRegisterDto) {
    const companyId = await apeAuthApiHelper.getCompanyId(req)

    try {
      const objCheckCode = await this.supplierRepo.findOne({ where: { code: data.code, companyId }, select: { id: true } })
      if (objCheckCode) throw new ConflictException(ERROR_CODE_TAKEN)

      const objCheckEmail = await this.supplierRepo.findOne({ where: { email: data.email, companyId }, select: { id: true } })
      if (objCheckEmail) throw new ConflictException(ERROR_EMAIL_TAKEN)

      if (data.password != data.confirmPassword) throw new Error('Mật khẩu không trùng khớp.')
      if (!data.services?.length) throw new Error('Vui lòng chọn lĩnh vực kinh doanh.')

      const setType = new Set()
      setType.add(enumData.DataType.String.code)
      setType.add(enumData.DataType.Number.code)
      setType.add(enumData.DataType.File.code)

      const result = await this.supplierRepo.manager.transaction('READ UNCOMMITTED', async (manager) => {
        const userRepo = manager.getRepository(UserEntity)
        const supplierRepo = manager.getRepository(SupplierEntity)
        const supplierServiceRepo = manager.getRepository(SupplierServiceEntity)
        const supplierCapacityRepo = manager.getRepository(SupplierCapacityEntity)
        const supplierCapacityYearValueRepo = manager.getRepository(SupplierCapacityYearValueEntity)
        const supplierCapacityListDetailRepo = manager.getRepository(SupplierCapacityListDetailEntity)
        const serviceCapacityRepo = manager.getRepository(ServiceCapacityEntity)
        const employeeRepo = manager.getRepository(EmployeeEntity)

        const checkUser = await userRepo.findOne({ where: { username: data.username, companyId }, select: { id: true } })
        if (checkUser) throw new Error('Tài khoản đã tồn tại.')

        // check mã giới thiệu
        // if (data.introducerCode == null || data.introducerCode === '') throw new Error('Vui lòng nhập mã giới thiệu.')
        let introducerId = null
        if (data.introducerCode != null && data.introducerCode.length > 0) {
          const employee = await employeeRepo.findOne({ where: { code: data.introducerCode, companyId, isDeleted: false }, select: { id: true } })
          if (!employee) throw new Error('Mã giới thiệu không hợp lệ.')

          introducerId = employee.id
        }

        const newEntity = supplierRepo.create(data)
        newEntity.companyId = companyId
        newEntity.status = enumData.SupplierStatus.MoiDangKy.code
        // Lưu lại id của nhân viên giới thiệu
        if (introducerId) newEntity.introducerId = introducerId
        const supplierEntity = await supplierRepo.save(newEntity)

        const isProduct = process.env.IS_PRODUCT == 'true'
        if (isProduct) {
          await apeAuthApiHelper.registerUserPMS(companyId, enumApeAuth.UserType.CompanyPackageSupplier, data.username, data.password)
        }

        const newUserEntity = userRepo.create({
          companyId,
          username: data.username,
          password: !isProduct ? data.password : '',
          type: enumData.UserType.Supplier.code,
          supplierId: supplierEntity.id,
          isDeleted: true,
        })
        const createdUser = await userRepo.save(newUserEntity)
        await supplierRepo.update(supplierEntity.id, {
          userId: createdUser.id,
          createdBy: createdUser.id,
        })
        await userRepo.update(createdUser.id, { createdBy: createdUser.id }) // Supplier History

        const suplierHistory = new SupplierHistoryEntity()
        suplierHistory.companyId = companyId
        suplierHistory.createdBy = createdUser.id
        suplierHistory.supplierId = supplierEntity.id
        suplierHistory.description = `NCC đăng ký mới`
        await manager.getRepository(SupplierHistoryEntity).save(suplierHistory)

        const listResult = []
        // Mapping data value vào danh sách supplierCapacity
        // Lọc qua danh sách các dịch vụ Doanh nghiệp đăng ký
        for (let itemService of data.services) {
          // Dịch vụ Doanh nghiệp đăng ký
          const serviceRegister: any = await manager
            .getRepository(ServiceEntity)
            .findOne({ where: { id: itemService.serviceId, companyId }, select: { id: true, code: true, name: true } })

          if (!serviceRegister) throw new Error('Lĩnh vực kinh doanh không còn tồn tại.')
          // Danh sách tiêu chí năng lực của dịch vụ
          const serviceCapacity = await serviceCapacityRepo.find({
            where: { parentId: IsNull(), serviceId: itemService.serviceId, companyId, isDeleted: false },
          })

          // Lọc qua danh sách tiêu chí của dịch vụ và copy vào supplierCapacity mới
          if (!serviceCapacity || serviceCapacity.length === 0) {
            throw new Error(`Không tìm thấy yêu cầu năng lực.`)
          }

          let supplierService = new SupplierServiceEntity()
          supplierService.companyId = companyId
          supplierService.createdBy = createdUser.id
          supplierService.status = enumData.SupplierServiceStatus.MoiDangKy.code
          supplierService.serviceId = itemService.serviceId
          supplierService.supplierId = supplierEntity.id
          const supplierServiceEntity = await supplierServiceRepo.save(supplierService)

          listResult.push({ supplierServiceId: supplierServiceEntity.id })
          // console.log('save supplier service success')

          const dataServiceCapacityLength = serviceCapacity.length
          for (let iServiceCapacity = 0; iServiceCapacity < dataServiceCapacityLength; iServiceCapacity++) {
            // const used = process.memoryUsage().heapUsed / 1024 / 1024
            // console.log(`The script uses approximately ${Math.round(used * 100) / 100} MB`)
            const item = serviceCapacity[iServiceCapacity]

            // Tìm giá trị tương ứng vs id
            let findValue = itemService.serviceCapacity.find((p) => p.id === item.id)
            if (!findValue) {
              throw new Error(`Thông tin năng lực không trùng khớp với dữ liệu được cấu hình. Vui lòng tải lại trang và đăng ký lại.`)
            }

            let supplierCapacity = new SupplierCapacityEntity()
            supplierCapacity.companyId = companyId
            supplierCapacity.createdBy = createdUser.id
            supplierCapacity.supplierServiceId = supplierServiceEntity.id
            supplierService.supplierId = supplierEntity.id
            supplierCapacity.name = item.name
            supplierCapacity.sort = item.sort
            supplierCapacity.type = item.type
            supplierCapacity.isRequired = item.isRequired
            supplierCapacity.percent = item.percent
            supplierCapacity.percentRule = item.percentRule
            supplierCapacity.isCalUp = item.isCalUp
            supplierCapacity.percentDownRule = item.percentDownRule
            supplierCapacity.description = item.description

            supplierCapacity.isChangeByYear = false
            if (setType.has(supplierCapacity.type)) supplierCapacity.isChangeByYear = item.isChangeByYear

            supplierCapacity.value = findValue.value

            supplierCapacity.serviceId = item.serviceId

            supplierCapacity.supplierId = supplierEntity.id

            supplierCapacity.serviceCapacityId = item.id
            const supplierCapacityEntity = await supplierCapacityRepo.save(supplierCapacity)

            // Nếu type là List thì thêm cấu hình danh sách list
            if (item.type === enumData.DataType.List.code) {
              const lstDetail = await item.serviceCapacityListDetails
              for (let i = 0, lenListDetail = lstDetail.length; i < lenListDetail; i++) {
                const itemListDetail = lstDetail[i]

                let listDetail = new SupplierCapacityListDetailEntity()
                listDetail.companyId = companyId
                listDetail.createdBy = createdUser.id
                listDetail.supplierCapacityId = supplierCapacityEntity.id
                listDetail.isDeleted = itemListDetail.isDeleted
                listDetail.name = itemListDetail.name
                listDetail.value = itemListDetail.value
                listDetail.isChosen = false
                if (findValue.value) {
                  listDetail.isChosen = findValue.value === itemListDetail.id
                }
                listDetail = await supplierCapacityListDetailRepo.save(listDetail)

                if (listDetail.isChosen) {
                  await supplierCapacityRepo.update(supplierCapacityEntity.id, { value: listDetail.id })
                }
              }
            }

            // Nếu type là loại theo năm thì lưu danh sách giá trị
            if (item.isChangeByYear) {
              const lstYear = findValue.listDetailYear || []
              for (let i = 0, lenYear = lstYear.length; i < lenYear; i++) {
                const itemYear = lstYear[i]

                const listDetail = new SupplierCapacityYearValueEntity()
                listDetail.companyId = companyId
                listDetail.createdBy = createdUser.id
                listDetail.supplierCapacityId = supplierCapacityEntity.id
                listDetail.isDeleted = false
                listDetail.value = itemYear.value
                listDetail.year = itemYear.year
                await supplierCapacityYearValueRepo.save(listDetail)
              }
            }

            // Lọc qua danh sách con, Nếu có value con thì copy. Không có thì thôi
            const lstChild = (await item.childs).filter((p) => !p.isDeleted)
            for (let iChild = 0, lenLstChild = lstChild.length; iChild < lenLstChild; iChild++) {
              let itemChild = lstChild[iChild]

              let findValueChild = findValue.__childs__.find((p) => p.id === itemChild.id)
              if (findValueChild) {
                let child = new SupplierCapacityEntity()
                child.companyId = companyId
                child.createdBy = createdUser.id
                child.supplierServiceId = supplierServiceEntity.id

                child.name = itemChild.name
                child.sort = itemChild.sort
                child.type = itemChild.type
                child.isRequired = itemChild.isRequired
                child.percent = itemChild.percent
                child.percentRule = itemChild.percentRule
                child.isCalUp = itemChild.isCalUp
                child.percentDownRule = itemChild.percentDownRule
                child.description = itemChild.description

                child.isChangeByYear = false
                if (setType.has(child.type)) child.isChangeByYear = itemChild.isChangeByYear

                child.value = findValueChild.value
                child.serviceId = itemChild.serviceId

                child.supplierId = supplierEntity.id
                child.serviceCapacityId = itemChild.id
                child.parentId = supplierCapacityEntity.id
                child = await supplierCapacityRepo.save(child)
                // Nếu type là List thì thêm cấu hình danh sách list
                if (itemChild.type === enumData.DataType.List.code) {
                  const lstDetail = await itemChild.serviceCapacityListDetails
                  for (let i = 0, lenListDetail = lstDetail.length; i < lenListDetail; i++) {
                    const itemListDetail = lstDetail[i]
                    let listDetail = new SupplierCapacityListDetailEntity()
                    listDetail.companyId = companyId
                    listDetail.createdBy = createdUser.id
                    listDetail.supplierCapacityId = child.id
                    listDetail.isDeleted = itemListDetail.isDeleted
                    listDetail.name = itemListDetail.name
                    listDetail.value = itemListDetail.value
                    if (findValueChild.value) {
                      listDetail.isChosen = findValueChild.value === itemListDetail.id
                    }
                    listDetail = await supplierCapacityListDetailRepo.save(listDetail)

                    if (listDetail.isChosen) {
                      await supplierCapacityRepo.update(child.id, { value: listDetail.id })
                    }
                  }
                }

                if (itemChild.isChangeByYear) {
                  const lstYear = findValueChild.listDetailYear || []
                  for (let i = 0, lenYear = lstYear.length; i < lenYear; i++) {
                    const itemYear = lstYear[i]
                    const listDetail = new SupplierCapacityYearValueEntity()
                    listDetail.companyId = companyId
                    listDetail.createdBy = createdUser.id
                    listDetail.supplierCapacityId = child.id
                    listDetail.isDeleted = false
                    listDetail.value = itemYear.value
                    listDetail.year = itemYear.year
                    await supplierCapacityYearValueRepo.save(listDetail)
                  }
                }
              } else {
                throw new Error(ERROR_NOT_FOUND_DATA)
              }
            }
          }

          const suplierHistory = new SupplierHistoryEntity()
          suplierHistory.companyId = companyId
          suplierHistory.createdBy = createdUser.id
          suplierHistory.supplierId = supplierEntity.id
          suplierHistory.description = `NCC đăng ký Item mới [${serviceRegister.code + ' - ' + serviceRegister.name}]`
          await manager.getRepository(SupplierHistoryEntity).save(suplierHistory)
        }
        // const used1 = process.memoryUsage().heapUsed / 1024 / 1024
        // console.log(`Trước khi chạy: ${Math.round(used1 * 100) / 100} MB`)
        // console.log(`success`)
        return listResult
      })

      for (let item of result) {
        this.emailService.GuiMpoDuyetNccDangKy(item.supplierServiceId)
      }
      return result
    } catch (error) {
      console.log(error)
      throw error
    }
  }

  async supplierAddCapacities(user: UserDto, data: SupplierAddCapacitiesDto) {
    const setType = new Set()
    setType.add(enumData.DataType.String.code)
    setType.add(enumData.DataType.Number.code)
    setType.add(enumData.DataType.File.code)

    try {
      return await this.supplierRepo.manager.transaction('READ UNCOMMITTED', async (manager) => {
        const serviceRepo = manager.getRepository(ServiceEntity)
        const supplierRepo = manager.getRepository(SupplierEntity)
        const supplierServiceRepo = manager.getRepository(SupplierServiceEntity)
        const supplierCapacityRepo = manager.getRepository(SupplierCapacityEntity)
        const supplierCapacityYearValueRepo = manager.getRepository(SupplierCapacityYearValueEntity)
        const supplierCapacityListDetailRepo = manager.getRepository(SupplierCapacityListDetailEntity)
        const serviceCapacityRepo = manager.getRepository(ServiceCapacityEntity)

        const supplier = await supplierRepo.findOne({ where: { id: user.supplierId, companyId: user.companyId }, select: { id: true } })
        if (!supplier) throw new Error('Không tìm thấy nhà cung cấp.')

        // Mapping data value vào danh sách supplierCapacity
        // Lọc qua danh sách các dịch vụ Doanh nghiệp đăng ký
        if (!data.services?.length) throw new Error('Vui lòng chọn lĩnh vực kinh doanh trước.')

        for (const itemService of data.services) {
          // Dịch vụ Doanh nghiệp đăng ký
          const serviceRegister = await serviceRepo.findOne({
            where: { id: itemService.serviceId, companyId: user.companyId },
            select: { id: true, code: true, name: true },
          })
          if (!serviceRegister) throw new Error('Lĩnh vực kinh doanh không còn tồn tại.')

          // Danh sách tiêu chí năng lực của dịch vụ
          const lstServiceCapacity = await serviceCapacityRepo.find({
            where: {
              parentId: IsNull(),
              serviceId: itemService.serviceId,
              companyId: user.companyId,
              isDeleted: false,
            },
          })

          // Lọc qua danh sách tiêu chí của dịch vụ và copy vào supplierCapacity mới
          if (!lstServiceCapacity?.length) throw new Error(`Không tìm thấy yêu cầu năng lực.`)

          let supplierService = new SupplierServiceEntity()
          supplierService.companyId = user.companyId
          supplierService.createdBy = user.id

          const existItem = await supplierServiceRepo.findOne({
            where: {
              serviceId: itemService.serviceId,
              supplierId: supplier.id,
              companyId: user.companyId,
              isDeleted: false,
            },
          })
          if (existItem != null && existItem.status != enumData.SupplierServiceStatus.ChuaDangKy.code) {
            throw new Error(`Dịch vụ đã được đăng ký. Vui lòng kiểu tra lại!`)
          } else if (existItem != null) {
            supplierService = existItem
          }

          supplierService.status = enumData.SupplierServiceStatus.MoiDangKy.code
          supplierService.statusExpertise = enumData.SupplierServiceExpertiseStatus.ChuaThamDinh.code
          supplierService.serviceId = itemService.serviceId
          supplierService.supplierId = supplier.id

          supplierService = await supplierServiceRepo.save(supplierService)

          for (
            let iServiceCapacity = 0, dataServiceCapacityLength = lstServiceCapacity.length;
            iServiceCapacity < dataServiceCapacityLength;
            iServiceCapacity++
          ) {
            // const used = process.memoryUsage().heapUsed / 1024 / 1024
            // console.log(`The script uses approximately ${Math.round(used * 100) / 100} MB`)
            const item = lstServiceCapacity[iServiceCapacity]

            // Tìm giá trị tương ứng vs id
            const findValue = itemService.serviceCapacity.find((p) => p.id === item.id)
            if (!findValue) {
              throw new Error(`Thông tin năng lực không trùng khớp với template năng lực được cấu hình. Vui lòng tải lại trang và đăng ký lại.`)
            }

            const supplierCapacity = new SupplierCapacityEntity()
            supplierCapacity.companyId = user.companyId
            supplierCapacity.createdBy = user.id
            supplierCapacity.supplierServiceId = supplierService.id
            supplierCapacity.name = item.name
            supplierCapacity.sort = item.sort
            supplierCapacity.type = item.type
            supplierCapacity.isRequired = item.isRequired
            supplierCapacity.percent = item.percent
            supplierCapacity.percentRule = item.percentRule
            supplierCapacity.isCalUp = item.isCalUp
            supplierCapacity.percentDownRule = item.percentDownRule
            supplierCapacity.description = item.description

            supplierCapacity.isChangeByYear = false
            if (setType.has(supplierCapacity.type)) supplierCapacity.isChangeByYear = item.isChangeByYear

            supplierCapacity.value = findValue.value

            // supplierCapacity.service = serviceRegister
            supplierCapacity.serviceId = item.serviceId

            supplierCapacity.supplierId = supplier.id

            supplierCapacity.serviceCapacityId = item.id
            const supplierCapacityEntity = await supplierCapacityRepo.save(supplierCapacity)
            // console.log('save supplier capacity success')

            // Nếu type là List thì thêm cấu hình danh sách list
            if (item.type === enumData.DataType.List.code) {
              const lstDetail = await item.serviceCapacityListDetails
              for (let i = 0, lenListDetail = lstDetail.length; i < lenListDetail; i++) {
                const itemListDetail = lstDetail[i]
                let listDetail = new SupplierCapacityListDetailEntity()
                listDetail.companyId = user.companyId
                listDetail.createdBy = user.id
                listDetail.supplierCapacityId = supplierCapacityEntity.id
                listDetail.isDeleted = itemListDetail.isDeleted
                listDetail.name = itemListDetail.name
                listDetail.value = itemListDetail.value
                listDetail.isChosen = false
                if (findValue.value) {
                  listDetail.isChosen = findValue.value === itemListDetail.id
                }
                listDetail = await supplierCapacityListDetailRepo.save(listDetail)

                if (listDetail.isChosen) {
                  await supplierCapacityRepo.update(supplierCapacityEntity.id, { value: listDetail.id })
                }
              }
            }

            // Nếu type là loại tăng theo năm thì lư danh sách giá trị
            if (item.isChangeByYear) {
              const lstYear = findValue.listDetailYear || []
              for (let i = 0, lenYear = lstYear.length; i < lenYear; i++) {
                const itemYear = lstYear[i]
                const listDetail = new SupplierCapacityYearValueEntity()
                listDetail.companyId = user.companyId
                listDetail.createdBy = user.id
                listDetail.supplierCapacityId = supplierCapacityEntity.id
                listDetail.isDeleted = false
                listDetail.value = itemYear.value
                listDetail.year = itemYear.year
                await supplierCapacityYearValueRepo.save(listDetail)
              }
            }

            // Lọc qua danh sách con, Nếu có value con thì copy. Không có thì thôi
            const lstChild = (await item.childs).filter((p) => !p.isDeleted)
            for (let iChild = 0, lenChild = lstChild.length; iChild < lenChild; iChild++) {
              const itemChild = lstChild[iChild]

              const findValueChild = findValue.__childs__.find((p) => p.id === itemChild.id)
              if (!findValueChild) {
                throw new Error(`Thông tin năng lực không trùng khớp với template năng lực được cấu hình. Vui lòng tải lại trang và đăng ký lại.`)
              }

              let child = new SupplierCapacityEntity()
              child.companyId = user.companyId
              child.createdBy = user.id
              child.supplierServiceId = supplierService.id

              child.name = itemChild.name
              child.sort = itemChild.sort
              child.type = itemChild.type
              child.isRequired = itemChild.isRequired
              child.percent = itemChild.percent
              child.percentRule = itemChild.percentRule
              child.isCalUp = itemChild.isCalUp
              child.percentDownRule = itemChild.percentDownRule
              child.description = itemChild.description

              child.isChangeByYear = false
              if (setType.has(child.type)) child.isChangeByYear = itemChild.isChangeByYear

              child.value = findValueChild.value
              child.serviceId = itemChild.serviceId

              child.supplierId = supplier.id
              child.serviceCapacityId = itemChild.id
              child.parentId = supplierCapacityEntity.id

              // console.log('save supplier capacity child')
              child = await supplierCapacityRepo.save(child)
              // console.log('save supplier capacity child success')
              // Nếu type là List thì thêm cấu hình danh sách list
              if (itemChild.type === enumData.DataType.List.code) {
                const lstDetail = await itemChild.serviceCapacityListDetails
                for (let i = 0, lenListDetail = lstDetail.length; i < lenListDetail; i++) {
                  const itemListDetail = lstDetail[i]
                  let listDetail = new SupplierCapacityListDetailEntity()
                  listDetail.companyId = user.companyId
                  listDetail.createdBy = user.id
                  listDetail.supplierCapacityId = child.id
                  listDetail.isDeleted = itemListDetail.isDeleted
                  listDetail.name = itemListDetail.name
                  listDetail.value = itemListDetail.value
                  if (findValueChild.value) {
                    listDetail.isChosen = findValueChild.value === itemListDetail.id
                  }
                  listDetail = await supplierCapacityListDetailRepo.save(listDetail)

                  if (listDetail.isChosen) {
                    await supplierCapacityRepo.update(child.id, { value: listDetail.id })
                  }
                }
              }

              if (itemChild.isChangeByYear) {
                const lstYear = findValueChild.listDetailYear || []
                for (let i = 0, lenYear = lstYear.length; i < lenYear; i++) {
                  const itemYear = lstYear[i]
                  const listDetail = new SupplierCapacityYearValueEntity()
                  listDetail.companyId = user.companyId
                  listDetail.createdBy = user.id
                  listDetail.supplierCapacityId = child.id
                  listDetail.isDeleted = false
                  listDetail.value = itemYear.value
                  listDetail.year = itemYear.year
                  await supplierCapacityYearValueRepo.save(listDetail)
                }
              }
            }
          }

          const suplierHistory = new SupplierHistoryEntity()
          suplierHistory.companyId = user.companyId
          suplierHistory.createdBy = user.id
          suplierHistory.supplierId = supplier.id
          suplierHistory.description = `NCC đăng ký Item mới [${serviceRegister.code + ' - ' + serviceRegister.name}]`
          await manager.getRepository(SupplierHistoryEntity).save(suplierHistory)
        }
        // const used1 = process.memoryUsage().heapUsed / 1024 / 1024
        // console.log(`Trước khi chạy: ${Math.round(used1 * 100) / 100} MB`)
        // console.log(`success`)
        return { message: 'Thêm năng lực thành công.' }
      })
    } catch (error) {
      console.log(error)
      throw error
    }
  }

  async updateLaw(user: UserDto, data: SupplierUpdateLawDto) {
    return await this.supplierRepo.manager.transaction(async (manager) => {
      const supplierRepo = manager.getRepository(SupplierEntity)
      let supplierEntity = await supplierRepo.findOne({ where: { id: data.id, companyId: user.companyId } })
      if (!supplierEntity) throw new Error(ERROR_NOT_FOUND_DATA)

      if (supplierEntity.code !== data.code) {
        const objCheckCode = await supplierRepo.findOne({ where: { code: data.code, companyId: user.companyId }, select: { id: true } })
        if (objCheckCode) throw new ConflictException(ERROR_CODE_TAKEN)
      }

      if (supplierEntity.email !== data.email) {
        const objCheckEmail = await supplierRepo.findOne({ where: { email: data.email, companyId: user.companyId }, select: { id: true } })
        if (objCheckEmail) throw new ConflictException(ERROR_EMAIL_TAKEN)
      }

      const historyRepo = manager.getRepository(DataHistoryEntity)
      const history = historyRepo.create({
        companyId: user.companyId,
        createdBy: user.id,
        relationId: data.id,
        tableName: enumData.DataHistoryTable.Supplier,
        dataJson: supplierEntity as any,
        description: 'Nhà cung cấp cập nhật thông tin pháp lý.',
      })
      await historyRepo.save(history)

      supplierEntity.name = data.name
      supplierEntity.code = data.code
      supplierEntity.description = data.description
      supplierEntity.dealName = data.dealName
      supplierEntity.address = data.address
      supplierEntity.dealAddress = data.dealAddress
      supplierEntity.fileMST = data.fileMST
      supplierEntity.represen = data.represen
      supplierEntity.chief = data.chief
      supplierEntity.bankNumber = data.bankNumber
      supplierEntity.bankname = data.bankname
      supplierEntity.bankBrand = data.bankBrand
      supplierEntity.fileAccount = data.fileAccount
      supplierEntity.contactName = data.contactName
      supplierEntity.email = data.email
      supplierEntity.phone = data.phone
      supplierEntity.createYear = data.createYear
      supplierEntity.capital = data.capital
      supplierEntity.assets = data.assets
      supplierEntity.fileBill = data.fileBill
      supplierEntity.fileInfoBill = data.fileInfoBill
      supplierEntity.updatedBy = user.id

      await supplierRepo.update(data.id, supplierEntity)

      // Supplier History
      const suplierHistory = new SupplierHistoryEntity()
      suplierHistory.companyId = user.companyId
      suplierHistory.createdBy = user.id
      suplierHistory.supplierId = supplierEntity.id
      suplierHistory.description = `NCC cập nhật thông tin pháp lý`
      await manager.getRepository(SupplierHistoryEntity).save(suplierHistory)

      return { message: UPDATE_SUCCESS }
    })
  }

  /** Hàm so sánh thay đổi thông tin năng lực */
  private async compareCapacity(user: UserDto, data: SupplierUpdateCapacityDto) {
    const supplierCapacityRepo = this.supplierRepo.manager.getRepository(SupplierCapacityEntity)
    const supplierCapacityListRepo = this.supplierRepo.manager.getRepository(SupplierCapacityListDetailEntity)
    const supplierCapacityYearRepo = this.supplierRepo.manager.getRepository(SupplierCapacityYearValueEntity)

    if (!data.capacities?.length) return false
    for (let i = 0, lenCapacity = data.capacities.length; i < lenCapacity; i++) {
      const item = data.capacities[i]

      const supplierCapacityEntity = await supplierCapacityRepo.findOne({
        where: { id: item.id, companyId: user.companyId, isDeleted: false },
        select: { id: true, value: true },
      })
      if (!supplierCapacityEntity) throw new Error('Tiêu chí năng lực không tồn tại, vui lòng thử lại.')

      if (supplierCapacityEntity.value !== item.value) return true

      if (item.type === enumData.DataType.List.code) {
        const objChoose = await supplierCapacityListRepo.findOne({
          where: { supplierCapacityId: item.id, companyId: user.companyId, isDeleted: false, isChosen: true },
          select: { id: true },
        })

        if (objChoose && objChoose.id !== item.value) return true
        if (!objChoose && item.value) return true
      }

      if (item.listDetailYear?.length > 0) {
        const lstSupplierCapacityYear = await supplierCapacityYearRepo.find({
          where: { supplierCapacityId: item.id, companyId: user.companyId, isDeleted: false },
          order: { year: 'DESC' },
          select: { id: true, year: true, value: true },
        })

        const listNew = item.listDetailYear.sort((a, b) => +b.year - +a.year)
        if (lstSupplierCapacityYear.length !== listNew.length) return true

        for (let y = 0, capacityYearLength = listNew.length; y < capacityYearLength; y++) {
          let itemYear = listNew[y]
          let itemFindYear = lstSupplierCapacityYear[y]
          if (itemYear.year !== itemFindYear.year || itemYear.value !== itemFindYear.value) {
            return true
          }
        }
      }

      if (!item.childs?.length) continue
      for (let j = 0, lenChild = item.childs.length; j < lenChild; j++) {
        const child = item.childs[j]

        const objCheck = await supplierCapacityRepo.findOne({
          where: { id: child.id, companyId: user.companyId, isDeleted: false },
          select: { id: true, value: true },
        })
        if (!objCheck) throw new Error('Tiêu chí năng lực không tồn tại, vui lòng thử lại.')

        if (objCheck.value !== child.value) return true

        if (child.type === enumData.DataType.List.code) {
          const objChoose = await supplierCapacityListRepo.findOne({
            where: { supplierCapacityId: child.id, companyId: user.companyId, isDeleted: false, isChosen: true },
            select: { id: true },
          })

          if (objChoose && objChoose.id !== child.value) return true
          if (!objChoose && child.value) return true
        }

        if (child.listDetailYear && child.listDetailYear.length > 0) {
          const lstSupplierCapacityYear = await supplierCapacityYearRepo.find({
            where: { supplierCapacityId: child.id, companyId: user.companyId, isDeleted: false },
            order: { year: 'DESC' },
            select: { id: true, year: true, value: true },
          })

          const listNew = child.listDetailYear.sort((a, b) => +b.year - +a.year)
          if (lstSupplierCapacityYear.length !== listNew.length) return true

          const capacityYearLength = listNew.length
          for (let y = 0; y < capacityYearLength; y++) {
            let itemYear = listNew[y]
            let itemFindYear = lstSupplierCapacityYear[y]
            if (itemYear.year !== itemFindYear.year || itemYear.value !== itemFindYear.value) return true
          }
        }
      }
    }

    return false
  }

  async updateCapacity(user: UserDto, data: SupplierUpdateCapacityDto) {
    try {
      const flagCheck = await this.compareCapacity(user, data)
      if (!flagCheck) return { message: 'Vui lòng thay đổi thông tin năng lực trước.' }

      return await this.supplierRepo.manager.transaction(async (manager) => {
        const supplierServiceRepo = manager.getRepository(SupplierServiceEntity)
        const supplierExpertiseRepo = manager.getRepository(SupplierExpertiseEntity)
        const supplierCapacityRepo = manager.getRepository(SupplierCapacityEntity)
        const supplierCapacityListRepo = manager.getRepository(SupplierCapacityListDetailEntity)
        const supplierCapacityYearRepo = manager.getRepository(SupplierCapacityYearValueEntity)

        const supplierService: any = await supplierServiceRepo.findOne({
          where: { id: data.supplierServiceId, companyId: user.companyId, isDeleted: false },
          relations: {
            service: true,
            capacities: {
              supplierCapacityListDetails: true,
              supplierCapacityYearValues: true,
              childs: { supplierCapacityListDetails: true, supplierCapacityYearValues: true },
            },
          },
          select: { id: true, supplierId: true, service: { code: true, name: true }, capacities: true },
        })
        if (!supplierService) throw new Error(ERROR_NOT_FOUND_DATA)

        const expertise = await supplierExpertiseRepo.findOne({
          where: { supplierServiceId: supplierService.id, companyId: user.companyId, isDeleted: false },
          order: { createdAt: 'DESC' },
          select: { id: true, status: true, createdAt: true },
        })
        if (expertise?.status === enumData.SupplierExpertiseStatus.DangThamDinh.code) {
          throw new Error('Hệ thống đang thẩm định! Vui lòng cập nhật sau!')
        }

        const historyRepo = manager.getRepository(DataHistoryEntity)
        const history = historyRepo.create({
          companyId: user.companyId,
          createdBy: user.id,
          relationId: data.supplierServiceId,
          tableName: enumData.DataHistoryTable.SupplierCapacity,
          dataJson: supplierService.__capacities__,
          description: 'Nhà cung cấp cập nhật thông tin năng lực Item',
        })
        await historyRepo.save(history)

        const lenCapacity = data.capacities?.length || 0
        for (let i = 0; i < lenCapacity; i++) {
          let item = data.capacities[i]

          const supplierCapacityEntity = await supplierCapacityRepo.findOne({
            where: { id: item.id, companyId: user.companyId, isDeleted: false },
          })
          if (!supplierCapacityEntity) throw new Error(ERROR_NOT_FOUND_DATA)

          await supplierCapacityRepo.update(item.id, {
            value: item.value,
            updatedBy: user.id,
          })

          if (item.type === enumData.DataType.List.code) {
            // Gán ischosen về false hết và chọn lại
            await supplierCapacityListRepo.update(
              {
                supplierCapacityId: item.id,
                isDeleted: false,
                isChosen: true,
              },
              { isChosen: false, updatedBy: user.id },
            )

            if (item.value) {
              await supplierCapacityListRepo.update(
                {
                  id: item.value,
                  isDeleted: false,
                },
                { isChosen: true, updatedBy: user.id },
              )
            }
          }

          if (item.listDetailYear?.length > 0) {
            // Xoá hết data cũ và thêm lại
            await supplierCapacityYearRepo.delete({ supplierCapacityId: item.id })
            for (let y = 0, lenYear = item.listDetailYear.length; y < lenYear; y++) {
              let itemYear = item.listDetailYear[y]
              const listDetail = new SupplierCapacityYearValueEntity()
              listDetail.companyId = user.companyId
              listDetail.createdBy = user.id
              listDetail.supplierCapacityId = supplierCapacityEntity.id
              listDetail.isDeleted = false
              listDetail.value = itemYear.value
              listDetail.year = itemYear.year
              await supplierCapacityYearRepo.save(listDetail)
            }
          }

          if (!item.childs?.length) continue
          for (let c = 0, lenChild = item.childs.length; c < lenChild; c++) {
            const child = item.childs[c]

            const supplierCapacityCEntity = await supplierCapacityRepo.findOne({
              where: { id: child.id, companyId: user.companyId, isDeleted: false },
              select: { id: true },
            })
            if (!supplierCapacityCEntity) throw new Error(ERROR_NOT_FOUND_DATA)

            await supplierCapacityRepo.update(child.id, {
              value: child.value,
              updatedBy: user.id,
            })

            if (child.type === enumData.DataType.List.code) {
              // Gán ischosen về false hết và chọn lại
              await supplierCapacityListRepo.update(
                {
                  supplierCapacityId: child.id,
                  isDeleted: false,
                  isChosen: true,
                },
                { isChosen: false, updatedBy: user.id },
              )

              if (child.value) {
                await supplierCapacityListRepo.update(
                  {
                    id: child.value,
                    isDeleted: false,
                  },
                  { isChosen: true, updatedBy: user.id },
                )
              }
            }

            if (child.listDetailYear?.length > 0) {
              // Xoá hết data cũ và thêm lại
              await supplierCapacityYearRepo.delete({ supplierCapacityId: child.id })
              for (let y = 0, lenYear = child.listDetailYear.length; y < lenYear; y++) {
                const itemYear = child.listDetailYear[y]
                const listDetail = new SupplierCapacityYearValueEntity()
                listDetail.companyId = user.companyId
                listDetail.createdBy = user.id
                listDetail.supplierCapacityId = supplierCapacityCEntity.id
                listDetail.isDeleted = false
                listDetail.value = itemYear.value
                listDetail.year = itemYear.year
                await supplierCapacityYearRepo.save(listDetail)
              }
            }
          }
        }

        // Cập nhật trạng thái supplierService thành chưa thẩm định để duyệt lại
        await supplierServiceRepo.update(data.supplierServiceId, {
          status: enumData.SupplierServiceStatus.CapNhatThongTin.code,
          updatedBy: user.id,
        })

        // Supplier History
        const suplierHistory = new SupplierHistoryEntity()
        suplierHistory.companyId = user.companyId
        suplierHistory.createdBy = user.id
        suplierHistory.supplierId = supplierService.supplierId
        suplierHistory.description = `NCC cập nhật thông tin năng lực Item [${
          supplierService.__service__.code + ' - ' + supplierService.__service__.name
        }]`
        await manager.getRepository(SupplierHistoryEntity).save(suplierHistory)

        return { message: 'Cập nhật thông tin năng lực thành công' }
      })
    } catch (error) {
      throw error
    }
  }

  async getSupplierInfo(user: UserDto) {
    const res: any = await this.supplierRepo.findOne({
      where: { id: user.supplierId, companyId: user.companyId },
      relations: {
        supplierServices: {
          service: true,
          capacities: {
            supplierCapacityYearValues: true,
            supplierCapacityListDetails: true,
            childs: { supplierCapacityYearValues: true, supplierCapacityListDetails: true },
          },
        },
      },
      order: {
        supplierServices: {
          service: { name: 'ASC' },
          capacities: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
        },
      },
    })
    if (!res) throw new NotFoundException('Nhà cung cấp không tồn tại.')

    res.lstSupplierService = res.__supplierServices__ || []
    delete res.__supplierServices__
    for (const supplierService of res.lstSupplierService) {
      // Chỉ lấy các năng lực cấp 1
      supplierService.lstCapacity = supplierService.__capacities__.filter((v: any) => {
        return !v.parentId
      })
      supplierService.serviceName = supplierService.__service__.name
      delete supplierService.__service__
      for (const data1 of supplierService.lstCapacity) {
        data1.listDetailYear = data1.__supplierCapacityYearValues__ || []
        delete data1.__supplierCapacityYearValues__
        for (const data2 of data1.__childs__) {
          data2.listDetailYear = data2.__supplierCapacityYearValues__ || []
          delete data2.__supplierCapacityYearValues__
        }
      }
    }
    res.capital = +res.capital
    res.assets = +res.assets
    return res
  }
}
