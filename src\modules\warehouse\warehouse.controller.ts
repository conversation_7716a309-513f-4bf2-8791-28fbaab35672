import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { enumProject } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { WarehouseCreateDto } from './dto/warehouseCreate.dto'
import { WarehouseUpdateDto } from './dto/warehouseUpdate.dto'
import { WarehouseService } from './warehouse.service'

@ApiBearerAuth()
@ApiTags('Warehouse')
@Controller('warehouses')
export class WarehouseController {
  constructor(private readonly service: WarehouseService) {}

  @ApiOperation({ summary: 'Lấy danh sách kho' })
  @UseGuards(ApeAuthGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto) {
    return await this.service.find(user)
  }

  @ApiOperation({ summary: '<PERSON><PERSON>y danh sách kho phân trang' })
  @Roles(enumProject.Features.SETTING_012.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo kho' })
  @Roles(enumProject.Features.SETTING_012.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: WarehouseCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật kho' })
  @Roles(enumProject.Features.SETTING_012.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: WarehouseUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động kho' })
  @Roles(enumProject.Features.SETTING_012.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateIsDelete(user, data)
  }
}
