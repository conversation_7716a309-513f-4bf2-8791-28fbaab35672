import { MigrationInterface, QueryRunner } from "typeorm";

export class dbRemoveColNoNeed1670899933636 implements MigrationInterface {
    name = 'dbRemoveColNoNeed1670899933636'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid_tech\` DROP COLUMN \`dataCalType\``);
        await queryRunner.query(`ALTER TABLE \`bid_tech\` DROP COLUMN \`minTX\``);
        await queryRunner.query(`ALTER TABLE \`bid_tech\` DROP COLUMN \`maxTX\``);
        await queryRunner.query(`ALTER TABLE \`service_price\` DROP COLUMN \`dataCalType\``);
        await queryRunner.query(`ALTER TABLE \`service_price\` DROP COLUMN \`minTX\``);
        await queryRunner.query(`ALTER TABLE \`service_price\` DROP COLUMN \`maxTX\``);
        await queryRunner.query(`ALTER TABLE \`bid_price\` DROP COLUMN \`dataCalType\``);
        await queryRunner.query(`ALTER TABLE \`bid_price\` DROP COLUMN \`minTX\``);
        await queryRunner.query(`ALTER TABLE \`bid_price\` DROP COLUMN \`maxTX\``);
        await queryRunner.query(`ALTER TABLE \`bid_trade\` DROP COLUMN \`dataCalType\``);
        await queryRunner.query(`ALTER TABLE \`bid_trade\` DROP COLUMN \`minTX\``);
        await queryRunner.query(`ALTER TABLE \`bid_trade\` DROP COLUMN \`maxTX\``);
        await queryRunner.query(`ALTER TABLE \`qc_detail_tech_value\` DROP COLUMN \`dataCalType\``);
        await queryRunner.query(`ALTER TABLE \`qc_detail_tech_value\` DROP COLUMN \`minTX\``);
        await queryRunner.query(`ALTER TABLE \`qc_detail_tech_value\` DROP COLUMN \`maxTX\``);
        await queryRunner.query(`ALTER TABLE \`service_tech\` DROP COLUMN \`dataCalType\``);
        await queryRunner.query(`ALTER TABLE \`service_tech\` DROP COLUMN \`minTX\``);
        await queryRunner.query(`ALTER TABLE \`service_tech\` DROP COLUMN \`maxTX\``);
        await queryRunner.query(`ALTER TABLE \`service_trade\` DROP COLUMN \`dataCalType\``);
        await queryRunner.query(`ALTER TABLE \`service_trade\` DROP COLUMN \`minTX\``);
        await queryRunner.query(`ALTER TABLE \`service_trade\` DROP COLUMN \`maxTX\``);
        await queryRunner.query(`ALTER TABLE \`item_tech\` DROP COLUMN \`dataCalType\``);
        await queryRunner.query(`ALTER TABLE \`item_tech\` DROP COLUMN \`minTX\``);
        await queryRunner.query(`ALTER TABLE \`item_tech\` DROP COLUMN \`maxTX\``);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`item_tech\` ADD \`maxTX\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`item_tech\` ADD \`minTX\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`item_tech\` ADD \`dataCalType\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`service_trade\` ADD \`maxTX\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`service_trade\` ADD \`minTX\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`service_trade\` ADD \`dataCalType\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`service_tech\` ADD \`maxTX\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`service_tech\` ADD \`minTX\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`service_tech\` ADD \`dataCalType\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`qc_detail_tech_value\` ADD \`maxTX\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`qc_detail_tech_value\` ADD \`minTX\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`qc_detail_tech_value\` ADD \`dataCalType\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`bid_trade\` ADD \`maxTX\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`bid_trade\` ADD \`minTX\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`bid_trade\` ADD \`dataCalType\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`bid_price\` ADD \`maxTX\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`bid_price\` ADD \`minTX\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`bid_price\` ADD \`dataCalType\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`service_price\` ADD \`maxTX\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`service_price\` ADD \`minTX\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`service_price\` ADD \`dataCalType\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`bid_tech\` ADD \`maxTX\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`bid_tech\` ADD \`minTX\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`bid_tech\` ADD \`dataCalType\` varchar(255) NULL`);
    }

}
