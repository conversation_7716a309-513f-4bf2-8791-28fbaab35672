import { Controller, Get, Param, Put, Body, UseGuards, Post } from '@nestjs/common'
import { SupplierReviewalService } from './supplierReviewal.service'
import { CurrentUser, Roles } from '../common/decorators'
import { PaginationDto, UserDto } from '../../dto'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { enumProject } from '../../constants'
import { ApiOperation, ApiTags, ApiBearerAuth, ApiParam, ApiBody } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Supplier')
@Controller('supplier_reviewal')
export class SupplierReviewalController {
  constructor(private readonly service: SupplierReviewalService) {}

  @ApiOperation({ summary: 'Check quyền phụ trách mua hàng duyệt Item NCC' })
  @ApiParam({ name: 'id', description: 'id của Item' })
  @Roles(enumProject.Features.SUPPLIER_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('check_permission_access/:id')
  public async checkPermissionAccess(@CurrentUser() user: UserDto, @Param('id') serviceId: string) {
    return await this.service.checkPermissionAccess(user, serviceId)
  }

  @ApiOperation({ summary: 'Check quyền người duyệt Item NCC' })
  @ApiParam({ name: 'id', description: 'id của Item' })
  @Roles(enumProject.Features.SUPPLIER_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('check_permission_approve/:id')
  public async checkPermissionApprove(@CurrentUser() user: UserDto, @Param('id') serviceId: string) {
    return await this.service.checkPermissionApprove(user, serviceId)
  }

  @ApiOperation({ summary: 'Danh sách thông tin pháp lý năng lực Item NCC phân trang' })
  @Roles(enumProject.Features.SUPPLIER_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Nhân viên phụ trách mua hàng - duyệt năng lực của NCC' })
  @ApiBody({
    schema: {
      required: ['supplierServiceId'],
      properties: { supplierServiceId: { type: 'string' }, comment: { type: 'string' } },
    },
  })
  @Roles(enumProject.Features.SUPPLIER_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('employee_approve')
  public async employeeApproveSupplier(@CurrentUser() user: UserDto, @Body() data: { supplierServiceId: string; comment?: string }) {
    return await this.service.employeeApproveSupplier(user, data)
  }

  @ApiOperation({ summary: 'Nhân viên phụ trách mua hàng - từ chối năng lực của NCC' })
  @ApiBody({
    schema: {
      required: ['supplierServiceId'],
      properties: { supplierServiceId: { type: 'string' }, comment: { type: 'string' } },
    },
  })
  @Roles(enumProject.Features.SUPPLIER_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('employee_reject')
  public async employeeRejectSupplier(@CurrentUser() user: UserDto, @Body() data: { supplierServiceId: string; comment?: string }) {
    return await this.service.employeeRejectSupplier(user, data)
  }

  @ApiOperation({ summary: 'Người duyệt - duyệt năng lực của NCC' })
  @ApiBody({
    schema: {
      required: ['supplierServiceId'],
      properties: { supplierServiceId: { type: 'string' }, comment: { type: 'string' } },
    },
  })
  @Roles(enumProject.Features.SUPPLIER_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('approve_supplier')
  public async approveSupplier(@CurrentUser() user: UserDto, @Body() data: { supplierServiceId: string; comment?: string }) {
    return await this.service.approveSupplier(user, data)
  }

  @ApiOperation({ summary: 'Người duyệt - từ chối năng lực của NCC' })
  @ApiBody({
    schema: {
      required: ['supplierServiceId'],
      properties: { supplierServiceId: { type: 'string' }, comment: { type: 'string' } },
    },
  })
  @Roles(enumProject.Features.SUPPLIER_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('reject_supplier')
  public async rejectSupplier(@CurrentUser() user: UserDto, @Body() data: { supplierServiceId: string; comment?: string }) {
    return await this.service.rejectSupplier(user, data)
  }

  @ApiOperation({ summary: 'Lấy lịch sử NCC đăng ký thông tin pháp lý' })
  @Roles(enumProject.Features.SUPPLIER_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_supplier_law_history/:id')
  public async getSupplierLawHistoryEditData(@CurrentUser() user: UserDto, @Param('id') supplierId: string) {
    return await this.service.getSupplierLawHistoryEditData(user, supplierId)
  }

  @ApiOperation({ summary: 'Lấy lịch sử NCC đăng ký thông tin năng lực' })
  @Roles(enumProject.Features.SUPPLIER_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_supplier_capacity_history/:id')
  public async getSupplierCapacityHistoryEditData(@CurrentUser() user: UserDto, @Param('id') supplierServiceId: string) {
    return await this.service.getSupplierCapacityHistoryEditData(user, supplierServiceId)
  }

  @ApiOperation({ summary: 'Xóa thông tin đăng ký Item NCC' })
  @Roles(enumProject.Features.SUPPLIER_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Put('delete_supplier/:id')
  public async deleteSupplier(@CurrentUser() user: UserDto, @Param('id') supplierServiceId: string) {
    return await this.service.deleteSupplier(user, supplierServiceId)
  }

  @ApiOperation({ summary: 'Ngưng hoạt động/Hoạt động lại Item NCC' })
  @Roles(enumProject.Features.SUPPLIER_003.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Put('deactive_supplier/:id')
  public async deActiveSupplier(@CurrentUser() user: UserDto, @Param('id') supplierServiceId: string) {
    return await this.service.deActiveSupplier(user, supplierServiceId)
  }
}
