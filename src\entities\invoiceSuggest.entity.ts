import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ContractEntity } from './contract.entity'
import { InvoiceEntity } from './invoice.entity'
import { InvoiceSuggestHistoryEntity } from './invoiceSuggestHistory.entity'
import { InvoiceSuggestFileEntity } from './invoiceSuggestFile.entity'
import { POEntity } from './po.entity'
import { PaymentProgressEntity } from './paymentProgress.entity'

/** Quản lý thanh toán */
@Entity({ name: 'invoice_suggest' })
export class InvoiceSuggestEntity extends BaseEntity {
  /** Mã ĐNTT */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Hình thức */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string

  /** Hợp đồng */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  contractId: string
  /** Hợp đồng */
  @ManyToOne(() => ContractEntity, (p) => p.invoiceSuggests)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  /** PO */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  poId: string
  /** PO */
  @ManyToOne(() => POEntity, (p) => p.invoiceSuggests)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  /** Tiến độ thanh toán (Theo HĐ hoặc theo PO không chọn HĐ) */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  paymentPlanId: string
  @ManyToOne(() => PaymentProgressEntity, (p) => p.invoiceSuggests)
  @JoinColumn({ name: 'paymentPlanId', referencedColumnName: 'id' })
  paymentPlan: Promise<PaymentProgressEntity>

  /** Số tiền cần thanh toán */
  @Column({
    type: 'bigint',
    default: 0,
  })
  money: number

  /** Số tiền đã thanh toán (Lũy kế các thanh toán) */
  @Column({
    type: 'bigint',
    default: 0,
  })
  moneyPaid: number

  /** Số hóa đơn */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  invoiceNo: string

  /** Chiết khấu */
  @Column({
    nullable: true,
  })
  discount: number

  /** Thời gian quyết toán */
  @Column({
    nullable: false,
  })
  invoiceDate: Date

  /** Đơn vị hưởng (ID Doanh nghiệp lấy theo PO) */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
    default: '',
  })
  supplierId: string

  /** Đơn vị hưởng (Tên Doanh nghiệp lấy theo PO) */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  beneficiaryUnit: string

  /** Ngân hàng */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  bankName: string

  /** STK Ngân hàng */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  bankAccountNo: string

  /** Nội dung thanh toán */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Trạng thái ĐNTT: enum InvoiceSuggestStatus */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  status: string

  /** Các file liên quan ĐNTT */
  @OneToMany(() => InvoiceSuggestFileEntity, (p) => p.invoiceSuggest)
  files: Promise<InvoiceSuggestFileEntity[]>

  /** Lịch sử ĐNTT */
  @OneToMany(() => InvoiceSuggestHistoryEntity, (p) => p.invoiceSuggest)
  histories: Promise<InvoiceSuggestHistoryEntity[]>

  /** Các thanh toán của ĐNTT */
  @OneToMany(() => InvoiceEntity, (p) => p.invoiceSuggest)
  invoices: Promise<InvoiceEntity[]>
}
