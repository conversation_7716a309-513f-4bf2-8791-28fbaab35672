import { Injectable, NotAcceptableException } from '@nestjs/common'
import { PaginationDto, UserDto } from '../../dto'
import { PaymentRepository } from '../../repositories'
import {
  CREATE_SUCCESS,
  enumData,
  ERROR_NOT_FOUND_DATA,
  ERROR_YOU_DO_NOT_HAVE_PERMISSION,
  UPDATE_ACTIVE_SUCCESS,
  UPDATE_SUCCESS,
} from '../../constants'
import { In, Like } from 'typeorm'
import { v4 as uuidv4 } from 'uuid'
import * as moment from 'moment'
import { PaymentCreateDto, PaymentUpdateDto } from './dto'
import { PaymentEntity } from '../../entities/payment.entity'
import { PaymentPoEntity } from '../../entities/paymentPo.entity'
import { PaymentContractEntity } from '../../entities/paymentContract.entity'
import { PaymentBillEntity } from '../../entities/paymentBill.entity'

@Injectable()
export class PaymentService {
  constructor(private readonly repo: PaymentRepository) {}

  public async find(user: UserDto, data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.name) whereCon.name = Like(`%${data.name}%`)
    return await this.repo.find({ where: whereCon, order: { createdAt: 'DESC' } })
  }

  async codeDefault() {
    const code = `HS${moment(new Date()).format('YYYYMM')}`
    const objData = await this.repo.findOne({
      where: { code: Like(`%${code}%`) },
      order: { code: 'DESC' },
    })
    let sortString = '0000'
    if (objData) {
      sortString = objData.code.substring(code.length, code.length + 4)
    }
    const lastSort = parseInt(sortString, 10)
    sortString = ('0000' + (lastSort + 1)).slice(-4)

    return code + sortString
  }

  public async createData(user: UserDto, data: PaymentCreateDto) {
    const newCode = await this.codeDefault()
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(PaymentEntity)
      const paymentBillRepo = trans.getRepository(PaymentBillEntity)
      const paymentPoRepo = trans.getRepository(PaymentPoEntity)
      const paymentContractRepo = trans.getRepository(PaymentContractEntity)

      const payment = new PaymentEntity()
      payment.id = uuidv4()
      payment.code = newCode
      payment.status = enumData.PaymentStatus.NEW.code
      payment.name = data.name
      payment.supplierId = user.supplierId || data.supplierId
      payment.fileAttach = data.fileAttach
      payment.filePaymentRequest = data.filePaymentRequest
      payment.fileAcceptanceReport = data.fileAcceptanceReport
      payment.settingStringId = data.settingStringId
      payment.moneyAdvance = data.moneyAdvance
      payment.paymentType = data.paymentType

      payment.note = data.note
      payment.createdAt = new Date()
      payment.createdBy = user.id

      /**kiểm tra xem thanh toán được tạo từ trang client hay trang admin tạo */
      if (user.supplierId) {
        payment.isSupplierCreate = true
      } else {
        payment.isSupplierCreate = false
      }
      await repo.insert(payment)

      if (data.billIds && data.billIds.length > 0) {
        const listInSert = []
        for (const item of data.billIds) {
          const paymentBill = new PaymentBillEntity()
          paymentBill.id = uuidv4()
          paymentBill.billId = item
          paymentBill.paymentId = payment.id
          paymentBill.createdBy = user.id
          listInSert.push(paymentBill)
        }

        await paymentBillRepo.insert(listInSert)
      }

      if (data.poIds && data.poIds.length > 0) {
        const listInSert = []
        for (const item of data.poIds) {
          const paymentPO = new PaymentPoEntity()
          paymentPO.id = uuidv4()
          paymentPO.poId = item
          paymentPO.paymentId = payment.id
          paymentPO.createdBy = user.id
          listInSert.push(paymentPO)
        }

        await paymentPoRepo.insert(listInSert)
      }

      if (data.contractIds && data.contractIds.length > 0) {
        const listInSert = []
        for (const item of data.contractIds) {
          const paymentContract = new PaymentContractEntity()
          paymentContract.id = uuidv4()
          paymentContract.contractId = item
          paymentContract.paymentId = payment.id
          paymentContract.createdBy = user.id
          listInSert.push(paymentContract)
        }

        await paymentContractRepo.insert(listInSert)
      }
    })
    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: PaymentUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(PaymentEntity)

      const paymentBillRepo = trans.getRepository(PaymentBillEntity)
      const paymentPoRepo = trans.getRepository(PaymentPoEntity)
      const paymentContractRepo = trans.getRepository(PaymentContractEntity)

      entity.name = data.name
      entity.fileAttach = data.fileAttach
      entity.filePaymentRequest = data.filePaymentRequest
      entity.fileAcceptanceReport = data.fileAcceptanceReport
      entity.supplierId = user.supplierId || data.supplierId
      entity.note = data.note
      entity.settingStringId = data.settingStringId
      entity.moneyAdvance = data.moneyAdvance
      entity.paymentType = data.paymentType

      /**kiểm tra xem thanh toán được tạo từ trang client hay trang admin tạo */
      if (user.supplierId) {
        entity.isSupplierCreate = true
      } else {
        entity.isSupplierCreate = false
      }
      entity.createdAt = new Date()
      await repo.save(entity)
      await paymentBillRepo.delete({ paymentId: entity.id })
      await paymentPoRepo.delete({ paymentId: entity.id })
      await paymentContractRepo.delete({ paymentId: entity.id })

      if (data.billIds && data.billIds.length > 0) {
        const listInSert = []
        for (const item of data.billIds) {
          const paymentBill = new PaymentBillEntity()
          paymentBill.id = uuidv4()
          paymentBill.billId = item
          paymentBill.paymentId = entity.id
          paymentBill.createdBy = user.id
          listInSert.push(paymentBill)
        }

        await paymentBillRepo.insert(listInSert)
      }

      if (data.poIds && data.poIds.length > 0) {
        const listInSert = []
        for (const item of data.poIds) {
          const paymentPO = new PaymentPoEntity()
          paymentPO.id = uuidv4()
          paymentPO.poId = item
          paymentPO.paymentId = entity.id
          paymentPO.createdBy = user.id
          listInSert.push(paymentPO)
        }

        await paymentPoRepo.insert(listInSert)
      }

      if (data.contractIds && data.contractIds.length > 0) {
        const listInSert = []
        for (const item of data.contractIds) {
          const paymentContract = new PaymentContractEntity()
          paymentContract.id = uuidv4()
          paymentContract.contractId = item
          paymentContract.paymentId = entity.id
          paymentContract.createdBy = user.id
          listInSert.push(paymentContract)
        }

        await paymentContractRepo.insert(listInSert)
      }
    })

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}

    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)

    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.status) whereCon.status = data.where.status
    whereCon.supplierId = user.supplierId
    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { supplier: true },
      order: { createdAt: 'DESC' },
    })
    if (res[0].length == 0) return [[], 0]
    for (let item of res[0]) {
      item.statusName = enumData.PaymentStatus[item.status].name
      item.statusColor = enumData.PaymentStatus[item.status]?.color
      item.statusBgColor = enumData.PaymentStatus[item.status]?.bgColor
      item.statusBorderColor = enumData.PaymentStatus[item.status]?.borderColor

      item.supplierName = item?.__supplier__?.name

      delete item.__supplier__
    }

    return res
  }

  async findDetail(user: UserDto, data: { id: string }) {
    const res: any = await this.repo.findOne({
      where: { id: data.id },
      relations: {
        paymentBills: { bill: true },
        paymentPos: { po: true },
        paymentContracts: { contract: true },
        supplier: true,
        settingString: true,
      },
    })

    let paymentBills = []
    let paymentBillCodes = []
    for (let item of res.__paymentBills__) {
      paymentBills.push(item.billId)
      paymentBillCodes.push(item.__bill__.code)
    }
    res.billIds = paymentBills
    res.paymentBillCodes = paymentBillCodes

    let paymentPos = []
    let paymentPoCodes = []
    for (let item of res.__paymentPos__) {
      paymentPos.push(item.poId)
      paymentPoCodes.push(item.__po__.code)
    }
    if (res.paymentType === enumData.paymentType.A.code) {
      res.poIds = paymentPos[0]
    } else {
      res.poIds = paymentPos
    }
    res.paymentPoCodes = paymentPoCodes

    let paymentContracts = []
    let paymentContractCodes = []

    for (let item of res.__paymentContracts__) {
      paymentContracts.push(item.contractId)
      paymentContractCodes.push(item.__contract__.code)
    }
    res.contractIds = paymentContracts
    res.paymentContractCodes = paymentContractCodes

    res.currencyName = res.__settingString__?.name
    res.supplierName = res.__supplier__?.name

    delete res.__paymentBills__
    delete res.__paymentPos__
    delete res.__paymentContracts__
    delete res.__supplier__
    delete res.__settingString__

    return res
  }
  public async updateIsDelete(data: { id: string }, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async updateChecking(user: UserDto, data: { id: string }) {
    await this.repo.manager.transaction(async (transac) => {
      const repo = transac.getRepository(PaymentEntity)
      const entity = await repo.findOne({ where: { id: data.id } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      entity.status = enumData.PaymentStatus.CHECKING.code
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
      await repo.save(entity)
    })
    return { message: UPDATE_SUCCESS }
  }

  public async updateValidConfirm(user: UserDto, data: { id: string }) {
    await this.repo.manager.transaction(async (transac) => {
      const repo = transac.getRepository(PaymentEntity)
      const entity = await repo.findOne({ where: { id: data.id } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      entity.status = enumData.PaymentStatus.CONFIRMED.code
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
      await repo.save(entity)
    })
    return { message: UPDATE_SUCCESS }
  }

  public async updateRecheck(user: UserDto, data: { id: string }) {
    await this.repo.manager.transaction(async (transac) => {
      const repo = transac.getRepository(PaymentEntity)
      const entity = await repo.findOne({ where: { id: data.id } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      entity.status = enumData.PaymentStatus.REQUEST_RECHECK.code
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
      await repo.save(entity)
    })
    return { message: UPDATE_SUCCESS }
  }

  async requestApprovePayment(user: UserDto, id: string) {
    const entity = await this.repo.findOne({
      where: { id: id },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status === enumData.PaymentStatus.REQUEST_RECHECK.code) {
      const newStatus = enumData.PaymentStatus.WAIT_APPROVE.code
      await this.repo.update(id, {
        status: newStatus,
      })
    } else {
      const newStatus = enumData.PaymentStatus.WAIT_APPROVE.code
      await this.repo.update(id, {
        status: newStatus,
        updatedBy: user.id,
      })
    }

    return { message: 'Gửi yêu cầu phê duyệt cho hồ sơ thanh toán thành công.' }
  }

  async approvedPayment(data: { id: string }, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.status = enumData.PaymentStatus.PAYING.code
    entity.updatedBy = user.id
    await entity.save()

    return { message: 'Đã duyệt hồ sơ thanh toán thành công' }
  }

  public async updateRequestConfirm(user: UserDto, data: { id: string }) {
    await this.repo.manager.transaction(async (transac) => {
      const repo = transac.getRepository(PaymentEntity)
      const entity = await repo.findOne({ where: { id: data.id } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      entity.status = enumData.PaymentStatus.REQUEST_CONFIRM.code
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
      await repo.save(entity)
    })
    return { message: UPDATE_SUCCESS }
  }
}
