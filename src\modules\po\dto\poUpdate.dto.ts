import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsArray, IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { PoProductDto } from './poProduct.dto'
import { PoProgressDto } from './poProgress.dto'

export class POUpdateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  objectId: string

  @ApiPropertyOptional()
  @IsNotEmpty()
  serviceLevel1: string

  @ApiPropertyOptional()
  @IsNotEmpty()
  title: string

  @ApiProperty()
  @IsArray()
  @IsNotEmpty()
  anotherRoleIds: string[]

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  confirmId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  cancelId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  poPaymentId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  editPOId: string

  @ApiProperty()
  @IsNotEmpty()
  deliveryDate: Date

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  paymentPlanType: string

  @ApiProperty()
  @IsOptional()
  contractId: string

  @ApiProperty()
  @IsOptional()
  contractPaymentPlanId: string

  @ApiProperty()
  @IsOptional()
  supplierId: string

  @ApiProperty()
  @IsOptional()
  bidId: string

  @ApiProperty()
  @IsOptional()
  prId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  company: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  currency: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  email: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  phone: string

  @ApiProperty()
  @IsOptional()
  description: string

  @ApiProperty()
  @IsOptional()
  operator: string

  @ApiProperty()
  @IsOptional()
  type: string

  @ApiProperty()
  @IsOptional()
  region: string

  @ApiProperty()
  @IsOptional()
  isChangeProduct: boolean

  @ApiProperty()
  @IsOptional()
  isChangePaymentProgress: boolean

  @ApiProperty()
  @IsArray()
  lstProduct: PoProductDto[]

  @ApiProperty()
  @IsArray()
  lstPaymentProgress: PoProgressDto[]
}
