import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, ManyTo<PERSON>ne, JoinC<PERSON>umn } from 'typeorm'
import { BidTradeEntity } from './bidTrade.entity'

@Entity('bid_trade_list_detail')
export class BidTradeListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
  })
  value: number

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidTradeId: string
  @ManyToOne(() => BidTradeEntity, (p) => p.bidTradeListDetails)
  @JoinColumn({ name: 'bidTradeId', referencedColumnName: 'id' })
  bidTrade: Promise<BidTradeEntity>
}
