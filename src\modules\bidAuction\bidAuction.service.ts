import { Injectable, NotFoundException } from '@nestjs/common'
import {
  BidAuctionRepository,
  BidSupplierRepository,
  BidRepository,
  BidPriceColRepository,
  BidDealRepository,
  BidSupplierPriceRepository,
} from '../../repositories'
import { EmailService } from '../email/email.service'
import { UserDto } from '../../dto'
import { In, Like } from 'typeorm'
import { enumData, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION, UPDATE_SUCCESS } from '../../constants'
import { coreHelper } from '../../helpers'
import { BidAuctionCreateDto, BidAuctionSupplierSaveDto } from './dto'
import { NotAcceptableException } from '@nestjs/common/exceptions'
import {
  BidAuctionEntity,
  BidAuctionPriceEntity,
  BidAuctionSupplierEntity,
  BidAuctionSupplierPriceValueEntity,
  BidEntity,
  BidHistoryEntity,
  BidSupplierEntity,
  BidSupplierPriceEntity,
} from '../../entities'
@Injectable()
export class BidAuctionService {
  constructor(
    private readonly repo: BidAuctionRepository,
    private readonly bidRepo: BidRepository,
    private readonly bidDealRepo: BidDealRepository,
    private readonly bidPriceColRepo: BidPriceColRepository,
    private readonly bidSupplierRepo: BidSupplierRepository,

    private readonly emailService: EmailService,
  ) {}

  /** Lấy danh sách hạng mục chào giá */
  async getPrice(user: UserDto, bidId: string) {
    const bid = await this.bidRepo.findOne({ where: { id: bidId, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    // Lấy giá mới nhất các Doanh nghiệp chào (cùng số lượng)
    const lstDataPrice = await this.bidDealRepo.getPriceValueNewest(user, bidId)

    let bidPrices = await bid.prices

    let lstTemp1 = bidPrices.filter((c) => c.level == 1).sort((a, b) => a.sort - b.sort)
    const lstTemp2 = bidPrices.filter((c) => c.level == 2).sort((a, b) => a.sort - b.sort)
    const lstTemp3 = bidPrices.filter((c) => c.level == 3).sort((a, b) => a.sort - b.sort)
    for (let i = 0; i < lstTemp1.length; i++) {
      const temp = lstTemp1[i]
      let childInLv1 = lstTemp2.filter((c) => c.parentId == temp.id)
      for (let j = 0; j < childInLv1.length; j++) {
        const temp2 = childInLv1[j]
        const childInLv2 = lstTemp3.filter((c) => c.parentId == temp2.id)
        childInLv1.splice(j + 1, 0, ...childInLv2)
      }
      lstTemp1.splice(i + 1, 0, ...childInLv1)
    }
    bidPrices = lstTemp1

    const length = bidPrices.length
    let lstResult = []
    for (let i = 0; i < length; i++) {
      let item = bidPrices[i]
      const itemResult: any = {}
      itemResult.id = item.id
      itemResult.name = item.name
      itemResult.number = item.number

      // Các giá trị mà Doanh nghiệp đã đàm phán hoặc có hồ sơ hợp lệ
      const lstBidSupplierPriceValueByItem = lstDataPrice
        .filter((p) => p.bidPriceId === item.id)
        .sort((a, b) => {
          if (a.value === '' || a.value === '0') return 1
          return +a.value - +b.value
        })

      itemResult.valueTop1 = null
      itemResult.listTop1 = []
      if (lstBidSupplierPriceValueByItem.length > 0) {
        itemResult.valueTop1 = lstBidSupplierPriceValueByItem[0].value
        itemResult.number = lstBidSupplierPriceValueByItem[0].number
        itemResult.listTop1 = lstBidSupplierPriceValueByItem.filter((c) => c.value === itemResult.valueTop1)
      }

      lstResult.push(itemResult)
    }

    return lstResult
  }

  /** Lấy ds NCC để mời tham gia đấu giá */
  async loadSupplierData(user: UserDto, data: { bidId: string; statusFile: string[]; name?: string }) {
    const bid = await this.bidRepo.findOne({ where: { id: data.bidId, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    let whereCon: any = { companyId: user.companyId }
    whereCon.bidId = data.bidId
    if (data.name) whereCon.supplier = { name: Like(`%${data.name}%`) }
    if (data.statusFile?.length > 0) whereCon.statusFile = In(data.statusFile)

    const res: any[] = await this.bidSupplierRepo.find({
      where: whereCon,
      relations: { supplier: true },
    })
    if (res.length == 0) return res

    const lstAll = await this.bidSupplierRepo.find({
      where: { bidId: data.bidId, companyId: user.companyId },
      select: { id: true, scorePrice: true, scoreManualPrice: true },
    })
    const lstValue = lstAll.map((c) => c.scorePrice)
    const maxValue = Math.max(...lstValue)
    const dlc = coreHelper.calDLC(lstValue)

    const lstValueManual = lstAll.map((c) => c.scoreManualPrice)
    const maxValueManual = Math.max(...lstValueManual)
    const dlcManual = coreHelper.calDLC(lstValueManual)

    const dicStatusFile: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierFileStatus)
      lstStatus.forEach((c) => (dicStatusFile[c.code] = c.name))
    }
    for (const item of res) {
      item.statusFileName = dicStatusFile[item.statusFile]
      item.supplierName = item.__supplier__.name
      delete item.__supplier__
      let isHasTotal = false
      item.scoreTotal = 0
      item.scoreManualTotal = 0
      if (item.statusTech === enumData.BidSupplierTechStatus.DaXacNhan.code || item.statusTech === enumData.BidSupplierTechStatus.DaDuyet.code) {
        isHasTotal = true
        item.scoreTotal += (item.scoreTech * bid.percentTech) / 100
        item.scoreManualTotal += (item.scoreManualTech * bid.percentTech) / 100
      } else {
        item.scoreTech = -1
        item.scoreManualTech = -1
      }

      if (item.statusTrade === enumData.BidSupplierTradeStatus.DaXacNhan.code || item.statusTrade === enumData.BidSupplierTradeStatus.DaDuyet.code) {
        isHasTotal = true
        item.scoreTotal += (item.scoreTrade * bid.percentTrade) / 100
        item.scoreManualTotal += (item.scoreManualTrade * bid.percentTrade) / 100
      } else {
        item.scoreTrade = -1
        item.scoreManualTrade = -1
      }

      if (item.statusPrice === enumData.BidSupplierPriceStatus.DaXacNhan.code || item.statusPrice === enumData.BidSupplierPriceStatus.DaDuyet.code) {
        isHasTotal = true
        let priceScore = 0
        if (dlc > 0) {
          priceScore = bid.percentPrice - (maxValue - item.scorePrice) / dlc
        } else {
          priceScore = bid.percentPrice
        }
        item.scoreTotal += priceScore

        let priceManualScore = 0
        if (dlcManual > 0) {
          priceManualScore = bid.percentPrice - (maxValueManual - item.scoreManualPrice) / dlcManual
        } else {
          priceManualScore = bid.percentPrice
        }
        item.scoreManualTotal += priceManualScore
      } else {
        item.scorePrice = -1
        item.scoreManualPrice = -1
      }

      if (!isHasTotal) {
        item.scoreTotal = -1
        item.scoreManualTotal = -1
      }
    }
    res.sort((a, b) => b.scoreTotal - a.scoreTotal)
    let rank = 1
    const total = res.length
    res.forEach((item) => {
      item.rank = rank + '/' + total
      rank++
    })

    return res
  }

  /** Tạo đấu giá Item */
  async saveBidAuction(user: UserDto, data: BidAuctionCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidItem: any = await this.bidRepo.findOne({
      where: {
        id: data.bidId,
        companyId: user.companyId,
        isDeleted: false,
      },
      relations: { parent: true, service: true },
      select: { id: true, status: true, parentId: true, parent: { status: true }, service: { code: true, name: true } },
    })
    if (!bidItem) throw new Error('Không tìm thấy Item cần tạo đấu giá.')
    if (bidItem.status == enumData.BidStatus.DangDamPhanGia.code)
      throw new Error(`Item ${enumData.BidStatus.DangDamPhanGia.name}, không thể tạo đấu giá.`)
    if (bidItem.status == enumData.BidStatus.DangDauGia.code)
      throw new Error(`Item ${enumData.BidStatus.DangDauGia.name}, không thể tạo thêm đấu giá.`)
    if (bidItem.status == enumData.BidStatus.DongDauGia.code)
      throw new Error(`Item ${enumData.BidStatus.DongDauGia.name}, mỗi Item chỉ được đấu giá 1 lần, không thể tạo thêm đấu giá.`)
    if (bidItem.__parent__?.status != enumData.BidStatus.HoanTatDanhGia.code)
      throw new Error(`Chỉ đấu giá khi gói thầu ${enumData.BidStatus.HoanTatDanhGia.name}.`)

    let bidAuctionId = null
    await this.repo.manager.transaction(async (manager) => {
      const bidRepo = manager.getRepository(BidEntity)
      const bidHistoryRepo = manager.getRepository(BidHistoryEntity)
      const bidAuctionRepo = manager.getRepository(BidAuctionEntity)
      const bidAuctionPriceRepo = manager.getRepository(BidAuctionPriceEntity)
      const bidAuctionSupplierRepo = manager.getRepository(BidAuctionSupplierEntity)

      // Chuyển trạng thái cho Item thành đang đấu giá
      await bidRepo.update(bidItem.id, {
        status: enumData.BidStatus.DangDauGia.code,
        updatedBy: user.id,
      })

      const bidHistory = new BidHistoryEntity()
      bidHistory.bidId = bidItem.parentId
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.TaoDauGia.code
      bidHistory.description = `Item [${bidItem.__service__.code} - ${bidItem.__service__.name}]`
      await bidHistoryRepo.save(bidHistory)

      let bidAuction = new BidAuctionEntity()
      bidAuction.bidId = bidItem.parentId
      bidAuction.companyId = user.companyId
      bidAuction.createdBy = user.id
      bidAuction.endDate = data.endDate
      bidAuction.status = enumData.BidAuctionStatus.DangDauGia.code
      bidAuction = await bidAuctionRepo.save(bidAuction)
      bidAuctionId = bidAuction.id

      // đấu giá 1 item nên k cần for
      let bidAuctionItem = new BidAuctionEntity()
      bidAuctionItem.parentId = bidAuctionId
      bidAuctionItem.bidId = bidItem.id
      bidAuctionItem.companyId = user.companyId
      bidAuctionItem.createdBy = user.id
      bidAuctionItem.endDate = data.endDate
      bidAuctionItem.status = enumData.BidDealStatus.DangDamPhan.code
      bidAuctionItem = await bidAuctionRepo.save(bidAuctionItem)
      const bidAuctionItemId = bidAuctionItem.id

      // đàm phán 1 item nên k cần for
      for (const item of data.lstPrice) {
        const bidAuctionPrice = new BidAuctionPriceEntity()
        bidAuctionPrice.companyId = user.companyId
        bidAuctionPrice.createdBy = user.id
        bidAuctionPrice.maxPrice = item.maxPrice
        bidAuctionPrice.bidAuctionId = bidAuctionItemId
        bidAuctionPrice.bidPriceId = item.bidPriceId
        await bidAuctionPriceRepo.save(bidAuctionPrice)
      }

      for (const item of data.lstSupplierChoose) {
        const bidAuctionSupplier = new BidAuctionSupplierEntity()
        bidAuctionSupplier.companyId = user.companyId
        bidAuctionSupplier.createdBy = user.id
        bidAuctionSupplier.bidAuctionId = bidAuctionId
        bidAuctionSupplier.supplierId = item.supplierId
        bidAuctionSupplier.status = enumData.BidAuctionSupplierStatus.DangDauGia.code
        await bidAuctionSupplierRepo.save(bidAuctionSupplier)

        const bidAuctionSupplierItem = new BidAuctionSupplierEntity()
        bidAuctionSupplierItem.companyId = user.companyId
        bidAuctionSupplierItem.createdBy = user.id
        bidAuctionSupplierItem.bidAuctionId = bidAuctionItemId
        bidAuctionSupplierItem.supplierId = item.supplierId
        bidAuctionSupplierItem.status = enumData.BidAuctionSupplierStatus.DangDauGia.code
        await bidAuctionSupplierRepo.save(bidAuctionSupplierItem)
      }
    })

    if (bidAuctionId) this.emailService.ThongBaoNccThamGiaDauGia(bidAuctionId)

    return { message: UPDATE_SUCCESS }
  }

  /** Lấy thông tin đấu giá NCC */
  async getBidAuctionSupplier(user: UserDto, bidAuctionId: string) {
    if (!user.supplierId) {
      return {
        isError: true,
        message: ERROR_YOU_DO_NOT_HAVE_PERMISSION,
      }
    }
    if (!bidAuctionId) {
      return {
        isError: true,
        message: 'Không tìm thấy thông tin đấu giá.',
      }
    }

    const bidAuctionSupplierRepo = this.repo.manager.getRepository(BidAuctionSupplierEntity)
    const bidSupplierRepo = this.repo.manager.getRepository(BidSupplierEntity)

    const res: any = await this.repo.findOne({
      where: { id: bidAuctionId, companyId: user.companyId },
      relations: {
        bid: true,
        childs: {
          bid: { service: true },
          bidAuctionPrice: { bidPrice: { bidPriceListDetails: true, bidPriceColValue: true } },
        },
      },
      select: {
        id: true,
        bidId: true,
        endDate: true,
        bid: { name: true },
        childs: {
          id: true,
          bidId: true,
          bid: { id: true, service: { code: true, name: true } },
          bidAuctionPrice: true,
        },
      },
    })
    if (!res) {
      return {
        isError: true,
        message: 'Không tìm thấy thông tin đấu giá.',
      }
    }

    const bidAuctionSupplier = await bidAuctionSupplierRepo.findOne({
      where: {
        bidAuctionId: bidAuctionId,
        supplierId: user.supplierId,
        companyId: user.companyId,
      },
      select: { id: true, status: true },
    })
    if (!bidAuctionSupplier) {
      return {
        isError: true,
        message: 'Bạn không được mời tham gia lần đấu giá này.',
      }
    }

    const bidSupplier = await bidSupplierRepo.findOne({
      where: {
        bidId: res.bidId,
        supplierId: user.supplierId,
        companyId: user.companyId,
        isDeleted: false,
      },
      select: { id: true },
    })
    if (!bidSupplier) throw new Error('Bạn không có quyền tham gia gói thầu này.')

    const todate = new Date()
    if (res.endDate < todate) {
      if (bidAuctionSupplier.status == enumData.BidAuctionSupplierStatus.DangDauGia.code) {
        return {
          isError: true,
          message: 'Hết hạn đấu giá.',
        }
      } else {
        return {
          isSuccess: true,
          message: 'Đã đấu giá thành công.',
        }
      }
    }

    res.bidName = res.__bid__.name
    delete res.__bid__
    res.listChild = res.__childs__
    delete res.__childs__

    const getDataCell = (row: any, col: any, lstValue: any[] = []) => {
      row[col.id] = ''
      if (col.colType === enumData.ColType.MPO.code) {
        if (row.__bidPrice__.__bidPriceColValue__?.length > 0) {
          const cell = row.__bidPrice__.__bidPriceColValue__.find((c: any) => c.bidPriceColId === col.id)
          if (cell) row[col.id] = cell.value
        }
      } else {
        const cell = lstValue.find((c) => c.bidPriceColId === col.id && c.bidPriceId === row.bidPriceId)
        if (cell) row[col.id] = cell.value
      }
    }

    for (const child of res.listChild) {
      // Lấy thứ hạng của NCC trong lần đấu giá
      {
        const lstBidAuctionSupplier = await bidAuctionSupplierRepo.find({
          where: { bidAuctionId: child.id, companyId: user.companyId, status: enumData.BidAuctionSupplierStatus.DaDauGia.code },
          order: { score: 'DESC' },
        })

        let rank = 0
        const index = lstBidAuctionSupplier.findIndex((p) => p.supplierId === user.supplierId)
        if (index) rank = index

        child.currentRank = rank + 1
        child.numSupplierAuction = lstBidAuctionSupplier.length
      }

      const bidAuctionSupplier = await bidAuctionSupplierRepo.findOne({
        where: {
          bidAuctionId: child.id,
          supplierId: user.supplierId,
          companyId: user.companyId,
        },
        select: { id: true },
      })
      if (!bidAuctionSupplier) {
        child.isRemove = true
        continue
      }
      child.itemName = child.__bid__.__service__.code + ' - ' + child.__bid__.__service__.name
      delete child.__bid__
      child.lstAuctionPrice = child.__bidAuctionPrice__
      delete child.__bidAuctionPrice__

      const bidSupplierItem = await bidSupplierRepo.findOne({
        where: {
          bidId: child.bidId,
          supplierId: user.supplierId,
          companyId: user.companyId,
        },
        select: { id: true },
      })
      if (!bidSupplierItem) {
        child.isRemove = true
        continue
      }

      child.lstPriceCol = await this.bidPriceColRepo.find({
        where: { bidId: child.bidId, companyId: user.companyId, isDeleted: false },
        order: { sort: 'ASC', createdAt: 'ASC' },
      })

      const lstBidSupplierPriceValue = await bidSupplierItem.bidSupplierPrices
      const lstBidSupplierPriceColValue = await bidSupplierItem.bidSupplierPriceColValue

      for (const row of child.lstAuctionPrice) {
        for (const col of child.lstPriceCol) {
          getDataCell(row, col, lstBidSupplierPriceColValue)
        }

        const objValue = lstBidSupplierPriceValue.find((c) => c.bidPriceId === row.bidPriceId)
        if (objValue) row.oldValue = objValue.unitPrice

        // xóa giá trị cột động không cần thiết
        delete row.__bidPrice__.__bidPriceColValue__
      }

      let lstTemp1 = child.lstAuctionPrice
        .filter((c: any) => c.__bidPrice__.level == 1)
        .sort((a: any, b: any) => a.__bidPrice__.sort - b.__bidPrice__.sort)
      const lstTemp2 = child.lstAuctionPrice
        .filter((c: any) => c.__bidPrice__.level == 2)
        .sort((a: any, b: any) => a.__bidPrice__.sort - b.__bidPrice__.sort)
      const lstTemp3 = child.lstAuctionPrice
        .filter((c: any) => c.__bidPrice__.level == 3)
        .sort((a: any, b: any) => a.__bidPrice__.sort - b.__bidPrice__.sort)
      for (let i = 0; i < lstTemp1.length; i++) {
        const temp = lstTemp1[i]
        let childInLv1 = lstTemp2.filter((c: any) => c.__bidPrice__.parentId == temp.bidPriceId)
        for (let j = 0; j < childInLv1.length; j++) {
          const temp2 = childInLv1[j]
          const childInLv2 = lstTemp3.filter((c: any) => c.__bidPrice__.parentId == temp2.bidPriceId)
          childInLv1.splice(j + 1, 0, ...childInLv2)
        }
        lstTemp1.splice(i + 1, 0, ...childInLv1)
      }
      child.lstAuctionPrice = lstTemp1
    }

    // bỏ các item không được mời đấu giá
    res.listChild = res.listChild.filter((c) => !c.isRemove)

    return res
  }

  /** NCC nộp đấu giá của mình */
  async supplierSaveAuction(user: UserDto, data: BidAuctionSupplierSaveDto) {
    if (!user.supplierId) throw new NotFoundException('Không có quyền truy cập')

    // Lưu kết quả đấu giá của NCC
    await this.repo.manager.transaction(async (manager) => {
      const bidAuctionRepo = manager.getRepository(BidAuctionEntity)
      const bidSupplierRepo = manager.getRepository(BidSupplierEntity)
      const bidAuctionSupplierRepo = manager.getRepository(BidAuctionSupplierEntity)
      const bidAuctionSupplierPriceValueRepo = manager.getRepository(BidAuctionSupplierPriceValueEntity)
      const bidSupplierPriceRepo = new BidSupplierPriceRepository(BidSupplierPriceEntity, manager)

      const todate = new Date()
      const bidAuction = await bidAuctionRepo.findOne({ where: { id: data.id, companyId: user.companyId }, select: { id: true, endDate: true } })
      if (!bidAuction) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
      if (bidAuction.endDate < todate) throw new NotFoundException('Đã quá hạn thời gian đấu giá.')

      const bidSupplier = await bidSupplierRepo.findOne({
        where: {
          bidId: bidAuction.bidId,
          supplierId: user.supplierId,
          companyId: user.companyId,
          isDeleted: false,
        },
        select: { id: true, supplierId: true },
      })
      if (!bidSupplier) throw new Error('Bạn không có quyền tham gia gói thầu này.')

      const bidAuctionSupplier = await bidAuctionSupplierRepo.findOne({
        where: {
          bidAuctionId: data.id,
          supplierId: user.supplierId,
          companyId: user.companyId,
        },
      })
      if (!bidAuctionSupplier) throw new Error('Bạn không có quyền tham gia lần đấu giá này')

      for (const child of data.listChild) {
        const bidAuctionSupplierItem = await bidAuctionSupplierRepo.findOne({
          where: {
            bidAuctionId: child.id,
            supplierId: user.supplierId,
            companyId: user.companyId,
          },
          select: { id: true },
        })
        if (!bidAuctionSupplierItem) continue

        const bidSupplierItem = await bidSupplierRepo.findOne({
          where: {
            bidId: child.bidId,
            supplierId: user.supplierId,
            companyId: user.companyId,
            isDeleted: false,
          },
          select: { id: true, bidId: true, serviceId: true, supplierId: true },
        })
        if (!bidSupplierItem) continue

        await bidAuctionSupplierRepo.update(bidAuctionSupplierItem.id, {
          status: enumData.BidAuctionSupplierStatus.DaDauGia.code,
          submitDate: todate,
          updatedBy: user.id,
        })

        // Xoá dữ liệu đấu giá cũ của NCC với item tương ứng
        await bidAuctionSupplierPriceValueRepo.delete({
          bidAuctionSupplierId: bidAuctionSupplierItem.id,
        })

        // xóa kết quả lần nộp giá cuối của NCC với item tương ứng
        await bidSupplierPriceRepo.delete({ bidSupplierId: bidSupplierItem.id })

        for (let i = 0, len = child.lstAuctionPrice.length; i < len; i++) {
          const itemAuctionPrice = child.lstAuctionPrice[i]
          const priceValue = new BidAuctionSupplierPriceValueEntity()
          priceValue.companyId = user.companyId
          priceValue.createdBy = user.id
          priceValue.bidAuctionSupplierId = bidAuctionSupplierItem.id
          priceValue.bidPriceId = itemAuctionPrice.bidPriceId
          priceValue.value = itemAuctionPrice.value + ''
          await bidAuctionSupplierPriceValueRepo.save(priceValue)

          const itemPrice: any = {}
          itemPrice.bidPriceId = itemAuctionPrice.bidPriceId
          itemPrice.name = itemAuctionPrice.__bidPrice__.name
          itemPrice.level = itemAuctionPrice.__bidPrice__.level
          itemPrice.value = priceValue.value + ''
          itemPrice.number = itemAuctionPrice.__bidPrice__.number
          itemPrice.submitDate = todate
          itemPrice.submitType = 2
          await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplierItem, itemPrice)
        }
      }
      // cập nhật trạng thái lần đấu giá của NCC
      await bidAuctionSupplierRepo.update(
        {
          bidAuctionId: data.id,
          supplierId: user.supplierId,
        },
        {
          status: enumData.BidAuctionSupplierStatus.DaDauGia.code,
          submitDate: todate,
          updatedBy: user.id,
        },
      )
    })

    // Tính lại điểm mỗi lần đấu giá
    await this.repo.manager.transaction(async (manager) => {
      const bidAuctionRepo = manager.getRepository(BidAuctionEntity)
      const bidAuctionSupplierRepo = manager.getRepository(BidAuctionSupplierEntity)
      const bidAuctionSupplierPriceValueRepo = manager.getRepository(BidAuctionSupplierPriceValueEntity)

      for (const child of data.listChild) {
        const bidAuction = await bidAuctionRepo.findOne({ where: { id: child.id, companyId: user.companyId } })
        if (!bidAuction) throw new Error(ERROR_NOT_FOUND_DATA)

        const bid = await bidAuction.bid

        // lấy các NCC đã đấu giá
        const lstBidAuctionSupplier = await bidAuctionSupplierRepo.find({
          where: {
            bidAuctionId: child.id,
            status: enumData.BidAuctionSupplierStatus.DaDauGia.code,
          },
        })
        const lstBidAuctionSupplierId = lstBidAuctionSupplier.map((c) => c.id)
        const lstBidAuctionSupplierPriceValue = await bidAuctionSupplierPriceValueRepo.find({
          where: { bidAuctionSupplierId: In(lstBidAuctionSupplierId), companyId: user.companyId },
        })

        // Lấy template hồ sơ chào giá của gói thầu Item
        const lstBidPrice = await bid.prices
        const scoreDLC = bid.scoreDLC

        // Tính điểm cho từng hạng mục
        for (const bidPrice of lstBidPrice) {
          const lstData = lstBidAuctionSupplierPriceValue.filter((c) => c.bidPriceId === bidPrice.id)
          const lstValue = lstData.map((c) => +c.value)

          const minValue = Math.min(...lstValue)
          const dlc = coreHelper.calDLC(lstValue)

          for (const item of lstData) {
            let score = scoreDLC
            if (dlc > 0) {
              score = scoreDLC - (+item.value - minValue) / dlc
            }
            item.score = score
            await bidAuctionSupplierPriceValueRepo.update(item.id, { score, updatedBy: user.id })
          }
        }

        // Lọc qua danh sách Doanh nghiệp tham gia đấu giá, tính tổng điểm
        for (const bidAuctionSupplier of lstBidAuctionSupplier) {
          const lstBidAuctionSupplierPriceValue = await bidAuctionSupplier.bidAuctionSupplierPriceValue
          let score = 0
          for (const itemBidAuctionSupplierPriceValue of lstBidAuctionSupplierPriceValue) {
            score += itemBidAuctionSupplierPriceValue.score
          }
          bidAuctionSupplier.score = score
        }

        const lstScore = lstBidAuctionSupplier.map((c) => c.score)
        const dlcBidAuctionSupplier = coreHelper.calDLC(lstScore)
        const maxScorePrice = Math.max(...lstScore)
        // Lọc qua danh sách Doanh nghiệp tính lại độ lệch chuẩn
        for (const item of lstBidAuctionSupplier) {
          let score = scoreDLC
          if (dlcBidAuctionSupplier > 0) {
            score = scoreDLC - (maxScorePrice - item.score) / dlcBidAuctionSupplier
          }
          // Lưu điểm
          await bidAuctionSupplierRepo.update(item.id, { score, updatedBy: user.id })
        }
      }
    })
  }
}
