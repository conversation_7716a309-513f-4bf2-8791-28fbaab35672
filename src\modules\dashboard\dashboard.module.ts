import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { SupplierServiceRepository } from '../../repositories'
import { DashboardController } from './dashboard.controller'
import { DashboardService } from './dashboard.service'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([SupplierServiceRepository])],
  controllers: [DashboardController],
  providers: [DashboardService],
})
export class DashboardModule {}
