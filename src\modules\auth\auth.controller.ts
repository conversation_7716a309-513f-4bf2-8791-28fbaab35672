import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { enumProject } from '../../constants'
import { UserDto } from '../../dto'
import { UserAuthDto } from '../../helpers/apeAuthApiHelper'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, LocalAuthGuard, RoleGuard } from '../common/guards'
import { AuthService } from './auth.service'
import { ForgotPasswordDto, UpdatePasswordDto, UpdateUsernameDto } from './dto'

@ApiBearerAuth()
@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly service: AuthService) {}

  @ApiOperation({ summary: 'Đăng nhập cho nhân viên' })
  @UseGuards(LocalAuthGuard)
  @Post('login')
  public async login(@Req() req: IRequest) {
    return await this.service.login(req.user as UserAuthDto)
  }

  @ApiOperation({ summary: 'Đăng nhập cho Doanh nghiệp' })
  @UseGuards(LocalAuthGuard)
  @Post('login-client')
  public async loginClient(@Req() req: IRequest) {
    return await this.service.loginClient(req.user as UserAuthDto)
  }

  @ApiOperation({ summary: 'Lấy thông tin authorization Doanh nghiệp' })
  @UseGuards(ApeAuthGuard)
  @Post('authorization-client')
  public async authorizationClient(@Req() req: IRequest) {
    return await this.service.authorizationClient(req.user as UserDto)
  }

  @ApiOperation({ summary: 'Đổi mật khẩu' })
  @UseGuards(ApeAuthGuard)
  @Post('update-password')
  public async updatePassword(@Body() data: UpdatePasswordDto, @CurrentUser() user: UserDto, @Req() req: IRequest) {
    return await this.service.updatePassword(data, user, req)
  }

  @ApiOperation({ summary: 'Đổi tên tài khoản cho user supplier' })
  @UseGuards(ApeAuthGuard)
  @Post('update-username')
  public async updateUsername(@Body() data: UpdateUsernameDto, @CurrentUser() user: UserDto, @Req() req: IRequest) {
    return await this.service.updateUsername(data, user, req)
  }

  @ApiOperation({ summary: 'Gửi mã xác nhận khi quên mật khẩu' })
  @Post('send-confirm-code')
  public async sendConfirmCode(@Req() req: Request, @Body() data: { email: string }) {
    return await this.service.sendConfirmCode(req, data)
  }

  @ApiOperation({ summary: 'Nhập mã xác nhận khi supplier quên mật khẩu' })
  @Post('forgot-password')
  public async forgotPassword(@Req() req: Request, @Body() data: ForgotPasswordDto) {
    return await this.service.forgotPassword(req, data)
  }

  @ApiOperation({ summary: 'Load phân quyền user' })
  @UseGuards(ApeAuthGuard)
  @Post('load_permission_user')
  public async loadPermissionUser(@CurrentUser() user: UserDto) {
    return await this.service.loadPermissionUser(user)
  }

  @ApiOperation({ summary: 'Lấy ds phân quyền nhân viên' })
  @Roles(enumProject.Features.SETTING_006.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_permission_employee')
  public async loadPermissionEmployee(@CurrentUser() user: UserDto, @Body() data: { userId: string }) {
    return await this.service.loadPermissionEmployee(user, data)
  }

  @ApiOperation({ summary: 'Lưu phân quyền nhân viên' })
  @Roles(enumProject.Features.SETTING_006.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('save_permission_employee')
  public async savePermissionEmployee(@CurrentUser() user: UserDto, @Body() data: { userId: string; lstData: any[] }) {
    return await this.service.savePermissionEmployee(user, data)
  }
}
