import { MigrationInterface, QueryRunner } from "typeorm";

export class migrationMaterialPo1730702815178 implements MigrationInterface {
    name = 'migrationMaterialPo1730702815178'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`po_product\` ADD \`itemId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`po_product\` ADD CONSTRAINT \`FK_a464c0c11ed39e1f4a4f2ebcd71\` FOREIGN KEY (\`itemId\`) REFERENCES \`material\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`po_product\` DROP FOREIGN KEY \`FK_a464c0c11ed39e1f4a4f2ebcd71\``);
        await queryRunner.query(`ALTER TABLE \`po_product\` DROP COLUMN \`itemId\``);
    }

}
