import { MigrationInterface, QueryRunner } from "typeorm";

export class migrationMaterial1730269452176 implements MigrationInterface {
    name = 'migrationMaterial1730269452176'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`bid_item\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`bidId\` varchar(36) NOT NULL, \`quantityItem\` int NULL DEFAULT '0', \`itemId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`bid_item\` ADD CONSTRAINT \`FK_f09fd3f8cf766db4eaec650c320\` FOREIGN KEY (\`bidId\`) REFERENCES \`bid\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`bid_item\` ADD CONSTRAINT \`FK_1fa23e21b221a4fd8c28ed4a401\` FOREIGN KEY (\`itemId\`) REFERENCES \`material\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bid_item\` DROP FOREIGN KEY \`FK_1fa23e21b221a4fd8c28ed4a401\``);
        await queryRunner.query(`ALTER TABLE \`bid_item\` DROP FOREIGN KEY \`FK_f09fd3f8cf766db4eaec650c320\``);
        await queryRunner.query(`DROP TABLE \`bid_item\``);
    }

}
