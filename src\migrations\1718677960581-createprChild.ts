import { MigrationInterface, QueryRunner } from 'typeorm'

export class createprChild1718677960581 implements MigrationInterface {
  name = 'createprChild1718677960581'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`pr_approve_child\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`prChildId\` varchar(36) NOT NULL, \`level\` int NOT NULL DEFAULT '1', \`employeeId\` varchar(36) NULL, \`status\` varchar(36) NOT NULL, \`description\` text NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`pr_approver_child\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`prChildId\` varchar(36) NULL, \`level\` int NOT NULL DEFAULT '1', \`employeeId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`pr_history_child\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`createdByName\` varchar(250) NOT NULL, \`prChildId\` varchar(36) NOT NULL, \`statusCurrent\` varchar(150) NULL, \`statusConvert\` varchar(150) NULL, \`description\` varchar(500) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`pr_item_child\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`prChildId\` varchar(36) NOT NULL, \`serviceId\` varchar(36) NOT NULL, \`purchasePlanId\` varchar(36) NULL, \`quantity\` int NOT NULL, \`productName\` varchar(500) NOT NULL, \`suggestReason\` text NOT NULL, \`description\` text NULL, \`code\` varchar(36) NULL, \`unit\` varchar(500) NOT NULL, \`quantityBid\` int NOT NULL DEFAULT '0', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(
      `CREATE TABLE \`pr_child\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`branchId\` varchar(36) NULL, \`name\` varchar(1000) NOT NULL, \`code\` varchar(250) NOT NULL, \`empProposerId\` varchar(36) NOT NULL, \`empInChargeId\` varchar(36) NOT NULL, \`deliveryDate\` datetime NOT NULL, \`deliveryAddress\` varchar(1000) NOT NULL, \`quantity\` int NULL, \`status\` varchar(50) NOT NULL, \`prType\` varchar(50) NOT NULL, \`objectId\` varchar(36) NULL, \`reason\` varchar(1000) NULL, \`isAllowBid\` tinyint NOT NULL DEFAULT 1, \`noteReCheck\` text NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
    await queryRunner.query(`ALTER TABLE \`item_tech\` ADD \`prItemChildId\` varchar(36) NULL`)
    await queryRunner.query(`ALTER TABLE \`scheme\` ADD \`prChildId\` varchar(36) NULL`)
    await queryRunner.query(`ALTER TABLE \`bid\` ADD \`prChilId\` varchar(36) NULL`)
    await queryRunner.query(`ALTER TABLE \`bid\` ADD \`prItemChildId\` varchar(36) NULL`)
    await queryRunner.query(
      `ALTER TABLE \`pr_approve_child\` ADD CONSTRAINT \`FK_a2805cc72cab68598b65f74a823\` FOREIGN KEY (\`prChildId\`) REFERENCES \`pr_child\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_approve_child\` ADD CONSTRAINT \`FK_f1ddfe7dac60115a29f077582dd\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_approver_child\` ADD CONSTRAINT \`FK_5c8de7e3c687f5802f3474a7fd1\` FOREIGN KEY (\`prChildId\`) REFERENCES \`pr_child\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_approver_child\` ADD CONSTRAINT \`FK_cfcde32fd9945f8fed029387a4d\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_history_child\` ADD CONSTRAINT \`FK_f7ae28b4735a73afb1fc947996e\` FOREIGN KEY (\`prChildId\`) REFERENCES \`pr_child\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`item_tech\` ADD CONSTRAINT \`FK_a7252181033db912eaf7aa5e70c\` FOREIGN KEY (\`prItemChildId\`) REFERENCES \`pr_item_child\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_item_child\` ADD CONSTRAINT \`FK_38b4e8d025bb6538af8b373368b\` FOREIGN KEY (\`prChildId\`) REFERENCES \`pr_child\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_item_child\` ADD CONSTRAINT \`FK_05173fc42792f2f40900ec03f66\` FOREIGN KEY (\`serviceId\`) REFERENCES \`service\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_item_child\` ADD CONSTRAINT \`FK_6511f2e76138b0026cc71989a6e\` FOREIGN KEY (\`purchasePlanId\`) REFERENCES \`purchase_plan\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_child\` ADD CONSTRAINT \`FK_c9a46a16796a0eba5847f862503\` FOREIGN KEY (\`branchId\`) REFERENCES \`branch\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_child\` ADD CONSTRAINT \`FK_3ed3232d777fb2104d7432867d4\` FOREIGN KEY (\`empProposerId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_child\` ADD CONSTRAINT \`FK_c28fadf12120e69f0d88a0d3f76\` FOREIGN KEY (\`empInChargeId\`) REFERENCES \`employee\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`pr_child\` ADD CONSTRAINT \`FK_3210d4fd56fcbc3c5a557fd5aed\` FOREIGN KEY (\`objectId\`) REFERENCES \`object\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`scheme\` ADD CONSTRAINT \`FK_d4707c554affb45ab9fdfc695e1\` FOREIGN KEY (\`prChildId\`) REFERENCES \`pr_child\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid\` ADD CONSTRAINT \`FK_3ac0331f78950889ddeb7e39994\` FOREIGN KEY (\`prChilId\`) REFERENCES \`pr_child\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE \`bid\` ADD CONSTRAINT \`FK_93cd7713f0252a277b7062bdb96\` FOREIGN KEY (\`prItemChildId\`) REFERENCES \`pr_item_child\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`bid\` DROP FOREIGN KEY \`FK_93cd7713f0252a277b7062bdb96\``)
    await queryRunner.query(`ALTER TABLE \`bid\` DROP FOREIGN KEY \`FK_3ac0331f78950889ddeb7e39994\``)
    await queryRunner.query(`ALTER TABLE \`scheme\` DROP FOREIGN KEY \`FK_d4707c554affb45ab9fdfc695e1\``)
    await queryRunner.query(`ALTER TABLE \`pr_child\` DROP FOREIGN KEY \`FK_3210d4fd56fcbc3c5a557fd5aed\``)
    await queryRunner.query(`ALTER TABLE \`pr_child\` DROP FOREIGN KEY \`FK_c28fadf12120e69f0d88a0d3f76\``)
    await queryRunner.query(`ALTER TABLE \`pr_child\` DROP FOREIGN KEY \`FK_3ed3232d777fb2104d7432867d4\``)
    await queryRunner.query(`ALTER TABLE \`pr_child\` DROP FOREIGN KEY \`FK_c9a46a16796a0eba5847f862503\``)
    await queryRunner.query(`ALTER TABLE \`pr_item_child\` DROP FOREIGN KEY \`FK_6511f2e76138b0026cc71989a6e\``)
    await queryRunner.query(`ALTER TABLE \`pr_item_child\` DROP FOREIGN KEY \`FK_05173fc42792f2f40900ec03f66\``)
    await queryRunner.query(`ALTER TABLE \`pr_item_child\` DROP FOREIGN KEY \`FK_38b4e8d025bb6538af8b373368b\``)
    await queryRunner.query(`ALTER TABLE \`item_tech\` DROP FOREIGN KEY \`FK_a7252181033db912eaf7aa5e70c\``)
    await queryRunner.query(`ALTER TABLE \`pr_history_child\` DROP FOREIGN KEY \`FK_f7ae28b4735a73afb1fc947996e\``)
    await queryRunner.query(`ALTER TABLE \`pr_approver_child\` DROP FOREIGN KEY \`FK_cfcde32fd9945f8fed029387a4d\``)
    await queryRunner.query(`ALTER TABLE \`pr_approver_child\` DROP FOREIGN KEY \`FK_5c8de7e3c687f5802f3474a7fd1\``)
    await queryRunner.query(`ALTER TABLE \`pr_approve_child\` DROP FOREIGN KEY \`FK_f1ddfe7dac60115a29f077582dd\``)
    await queryRunner.query(`ALTER TABLE \`pr_approve_child\` DROP FOREIGN KEY \`FK_a2805cc72cab68598b65f74a823\``)
    await queryRunner.query(`ALTER TABLE \`bid\` DROP COLUMN \`prItemChildId\``)
    await queryRunner.query(`ALTER TABLE \`bid\` DROP COLUMN \`prChilId\``)
    await queryRunner.query(`ALTER TABLE \`scheme\` DROP COLUMN \`prChildId\``)
    await queryRunner.query(`ALTER TABLE \`item_tech\` DROP COLUMN \`prItemChildId\``)
    await queryRunner.query(`DROP TABLE \`pr_child\``)
    await queryRunner.query(`DROP TABLE \`pr_item_child\``)
    await queryRunner.query(`DROP TABLE \`pr_history_child\``)
    await queryRunner.query(`DROP TABLE \`pr_approver_child\``)
    await queryRunner.query(`DROP TABLE \`pr_approve_child\``)
  }
}
