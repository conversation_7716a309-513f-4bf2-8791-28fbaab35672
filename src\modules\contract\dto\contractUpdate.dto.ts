import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class ContractUpdate {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  supplierId: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  bidId: string

  @ApiPropertyOptional()
  @IsOptional()
  fileAttach: string

  @ApiPropertyOptional()
  @IsOptional()
  effectiveDate: Date

  @ApiPropertyOptional()
  @IsOptional()
  expiredDate: Date

  @ApiPropertyOptional()
  @IsOptional()
  status: string

  @ApiPropertyOptional()
  @IsOptional()
  reason: string

  @ApiPropertyOptional()
  @IsOptional()
  description: string

  @ApiPropertyOptional()
  @IsOptional()
  confirmContractId: string

  @ApiPropertyOptional()
  @IsOptional()
  anotherRoleIds: string[]

  @ApiPropertyOptional()
  @IsOptional()
  editContractId: string

  @ApiPropertyOptional()
  @IsOptional()
  branchId: string

  @ApiPropertyOptional()
  @IsOptional()
  createdBy: string

  @ApiPropertyOptional()
  objectId: string

  @ApiPropertyOptional()
  manageContractId: string

  @ApiPropertyOptional()
  value: number

  @ApiPropertyOptional()
  isChangePaymentProgress: boolean

  /** DS tiến độ thanh toán */
  @ApiProperty()
  lstPaymentProgress: any[]
}
