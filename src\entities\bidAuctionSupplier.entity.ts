import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { SupplierEntity } from './supplier.entity'
import { BidAuctionEntity } from './bidAuction.entity'
import { BidAuctionSupplierPriceValueEntity } from './bidAuctionSupplierPriceValue.entity'

@Entity('bid_auction_supplier')
export class BidAuctionSupplierEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  score: number

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidAuctionId: string
  @ManyToOne(() => BidAuctionEntity, (p) => p.bidAuctionSupplier)
  @JoinColumn({ name: 'bidAuctionId', referencedColumnName: 'id' })
  bidAuction: Promise<BidAuctionEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.bidAuctionSupplier)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /** Ngày nộp đấu giá */
  @Column({
    nullable: true,
  })
  submitDate: Date

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => BidAuctionSupplierPriceValueEntity, (p) => p.bidAuctionSupplier)
  bidAuctionSupplierPriceValue: Promise<BidAuctionSupplierPriceValueEntity[]>
}
