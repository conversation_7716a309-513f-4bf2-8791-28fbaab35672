import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

/** Interface để Tạo ngôn ngữ. */
export class LanguageCreateDto {
  @ApiProperty({ description: 'Key ngôn ngữ' })
  @IsNotEmpty()
  @IsString()
  key: string

  @ApiProperty({ description: 'Giá Trị ngôn ngữ' })
  @IsNotEmpty()
  @IsString()
  value: string

  @ApiProperty({ description: 'Loại ngôn ngữ' })
  @IsNotEmpty()
  @IsString()
  languageType: string

  @ApiProperty({ description: 'URL về ngôn ngữ', required: false })
  @IsOptional()
  @IsString()
  path: string

  @ApiProperty({ description: 'Ghi chú về ngôn ngữ', required: false })
  @IsOptional()
  @IsString()
  description: string
}
