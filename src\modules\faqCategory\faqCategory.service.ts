import { Injectable } from '@nestjs/common'
import { FaqCategoryRepository } from '../../repositories'
import { Like } from 'typeorm'
import { FaqCategoryCreateDto, FaqCategoryUpdateDto } from './dto'
import { ERROR_NOT_FOUND_DATA, UPDATE_SUCCESS, UPDATE_ACTIVE_SUCCESS, CREATE_SUCCESS } from '../../constants'
import { UserDto } from '../../dto'
import { apeAuthApiHelper } from '../../helpers'

@Injectable()
export class FaqCategoryService {
  constructor(private readonly repo: FaqCategoryRepository) {}

  public async find(user: UserDto) {
    const whereCon: any = { isDeleted: false, companyId: user.companyId }
    return await this.repo.find({ where: whereCon, order: { name: 'ASC' } })
  }

  public async createData(user: UserDto, data: FaqCategoryCreateDto) {
    const newEntity = this.repo.create(data)
    newEntity.companyId = user.companyId
    newEntity.createdBy = user.id
    await this.repo.save(newEntity)

    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: FaqCategoryUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.description = data.description
    entity.updatedBy = user.id
    await this.repo.save(entity)

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: any) {
    const whereCon: any = { companyId: user.companyId }

    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { name: 'ASC' },
    })

    return res
  }

  public async updateIsDelete(user: UserDto, data: { id: string }) {
    const entity = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async getFAQCategoryHomePage(req: Request) {
    const companyId = await apeAuthApiHelper.getCompanyId(req)
    const res: any[] = await this.repo.find({
      where: { companyId, isDeleted: false, faqs: { isDeleted: false } },
      relations: { faqs: true },
    })

    for (const category of res) {
      category.lstFaq = category.__faqs__ || []
      delete category.__faqs__
    }
    return res
  }
}
