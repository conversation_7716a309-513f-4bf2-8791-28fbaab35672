import { BadRequestException, Injectable, NotAcceptableException, NotFoundException } from '@nestjs/common'
import { BidDealRepository, BidRepository, BidSupplierPriceRepository, BidSupplierRepository } from '../../repositories'
import { EmailService } from '../email/email.service'
import { UserDto } from '../../dto'
import { In, Like, MoreThan, Repository } from 'typeorm'
import { ERROR_NOT_FOUND_DATA, enumData, ERROR_YOU_DO_NOT_HAVE_PERMISSION, UPDATE_SUCCESS } from '../../constants'
import { BidDealCreateDto } from './dto'
import { coreHelper } from '../../helpers'
import {
  BidDealEntity,
  BidDealPriceEntity,
  BidDealSupplierEntity,
  BidDealSupplierPriceValueEntity,
  BidEntity,
  BidHistoryEntity,
  BidPriceColEntity,
  BidSupplierEntity,
  BidSupplierPriceEntity,
  BidSupplierPriceValueEntity,
} from '../../entities'
import { BidDealSupplierSaveDto } from './dto/bidDealSupplierSave.dto'
import { AiService } from '../ai/ai.service'

@Injectable()
export class BidDealService {
  constructor(
    private readonly repo: BidDealRepository,
    private readonly bidRepo: BidRepository,
    private readonly bidSupplierRepo: BidSupplierRepository,
    private readonly aiService: AiService,
    private readonly emailService: EmailService,
  ) {}

  /** Lấy ds NCC để mời tham gia đàm phán giá */
  async loadSupplierData(user: UserDto, data: { bidId: string; statusFile: string[]; name?: string }) {
    const bid = await this.bidRepo.findOne({ where: { id: data.bidId, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    const whereCon: any = { companyId: user.companyId }
    whereCon.bidId = data.bidId
    if (data.name) whereCon.supplier = { name: Like(`%${data.name}%`) }
    if (data.statusFile?.length > 0) whereCon.statusFile = In(data.statusFile)

    const res: any[] = await this.bidSupplierRepo.find({
      where: whereCon,
      relations: { supplier: true },
    })
    if (res.length == 0) return res

    const lstAll = await this.bidSupplierRepo.find({
      where: { bidId: data.bidId, companyId: user.companyId },
      select: { id: true, scorePrice: true, scoreManualPrice: true },
    })
    const lstValue = lstAll.map((c) => c.scorePrice)
    const maxValue = Math.max(...lstValue)
    const dlc = coreHelper.calDLC(lstValue)

    const lstValueManual = lstAll.map((c) => c.scoreManualPrice)
    const maxValueManual = Math.max(...lstValueManual)
    const dlcManual = coreHelper.calDLC(lstValueManual)

    const dicStatusFile: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierFileStatus)
      lstStatus.forEach((c) => (dicStatusFile[c.code] = c.name))
    }
    for (const item of res) {
      item.statusFileName = dicStatusFile[item.statusFile]
      item.supplierName = item.__supplier__.name
      delete item.__supplier__
      let isHasTotal = false
      item.scoreTotal = 0
      item.scoreManualTotal = 0
      if (item.statusTech === enumData.BidSupplierTechStatus.DaXacNhan.code || item.statusTech === enumData.BidSupplierTechStatus.DaDuyet.code) {
        isHasTotal = true
        item.scoreTotal += (item.scoreTech * bid.percentTech) / 100
        item.scoreManualTotal += (item.scoreManualTech * bid.percentTech) / 100
      } else {
        item.scoreTech = -1
        item.scoreManualTech = -1
      }

      if (item.statusTrade === enumData.BidSupplierTradeStatus.DaXacNhan.code || item.statusTrade === enumData.BidSupplierTradeStatus.DaDuyet.code) {
        isHasTotal = true
        item.scoreTotal += (item.scoreTrade * bid.percentTrade) / 100
        item.scoreManualTotal += (item.scoreManualTrade * bid.percentTrade) / 100
      } else {
        item.scoreTrade = -1
        item.scoreManualTrade = -1
      }

      if (item.statusPrice === enumData.BidSupplierPriceStatus.DaXacNhan.code || item.statusPrice === enumData.BidSupplierPriceStatus.DaDuyet.code) {
        isHasTotal = true
        let priceScore = 0
        if (dlc > 0) {
          priceScore = bid.percentPrice - (maxValue - item.scorePrice) / dlc
        } else {
          priceScore = bid.percentPrice
        }
        item.scoreTotal += priceScore

        let priceManualScore = 0
        if (dlcManual > 0) {
          priceManualScore = bid.percentPrice - (maxValueManual - item.scoreManualPrice) / dlcManual
        } else {
          priceManualScore = bid.percentPrice
        }
        item.scoreManualTotal += priceManualScore
      } else {
        item.scorePrice = -1
        item.scoreManualPrice = -1
      }

      if (!isHasTotal) {
        item.scoreTotal = -1
        item.scoreManualTotal = -1
      }
    }
    res.sort((a, b) => b.scoreTotal - a.scoreTotal)
    let rank = 1
    const total = res.length
    res.forEach((item) => {
      item.rank = rank + '/' + total
      rank++
    })

    return res
  }

  /** Lấy danh sách hạng mục chào giá */
  async getPrice(user: UserDto, bidId: string) {
    const bid = await this.bidRepo.findOne({ where: { id: bidId, companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    // Lấy giá mới nhất các NCC chào (cùng số lượng)
    const lstDataPrice = await this.repo.getPriceValueNewest(user, bidId)

    let bidPrices = await bid.prices
    let lstTemp1: any[] = bidPrices.filter((c) => c.level == 1).sort((a, b) => a.sort - b.sort)
    const lstTemp2 = bidPrices.filter((c) => c.level == 2).sort((a, b) => a.sort - b.sort)
    const lstTemp3 = bidPrices.filter((c) => c.level == 3).sort((a, b) => a.sort - b.sort)

    const lstTemp = []
    for (let i = 0; i < lstTemp1.length; i++) {
      const temp = lstTemp1[i]
      let childInLv1: any[] = lstTemp2.filter((c) => c.parentId == temp.id)
      temp.lstChildId = childInLv1.map((c) => c.id)
      lstTemp.push(temp)
      for (let j = 0; j < childInLv1.length; j++) {
        const temp2 = childInLv1[j]
        let childInLv2: any[] = lstTemp3.filter((c) => c.parentId == temp2.id)
        temp2.lstChildId = childInLv2.map((c) => c.id)
        lstTemp.push(temp2)
        lstTemp.push(...childInLv2)
      }
    }

    let lstResult = []
    for (let i = 0; i < lstTemp.length; i++) {
      let item = lstTemp[i]
      const itemResult: any = {}
      itemResult.id = item.id
      itemResult.name = item.name
      itemResult.number = item.number
      itemResult.level = item.level
      itemResult.parentId = item.parentId
      itemResult.lstChildId = item.lstChildId

      // Các giá trị mà NCC đã đàm phán hoặc có hồ sơ hợp lệ
      const lstBidSupplierPriceValueByItem = lstDataPrice
        .filter((p) => p.bidPriceId === item.id)
        .sort((a, b) => {
          if (a.value === '' || a.value === '0') return 1
          return +a.value - +b.value
        })

      if (lstBidSupplierPriceValueByItem.length > 0) {
        itemResult.valueTop1 = lstBidSupplierPriceValueByItem[0].value
        itemResult.number = lstBidSupplierPriceValueByItem[0].number
        itemResult.listTop1 = lstBidSupplierPriceValueByItem.filter((c) => c.value === itemResult.valueTop1)
      } else {
        itemResult.valueTop1 = null
        itemResult.listTop1 = []
      }

      lstResult.push(itemResult)
    }

    const lstLv2 = lstResult.filter((c) => c.level == 2 && c.lstChildId && c.lstChildId.length > 0)
    for (const lv2 of lstLv2) {
      lv2.listTop1 = []
      lv2.valueTop1 = 0
      const lstChild = lstResult.filter((c) => c.parentId == lv2.id)
      for (const child of lstChild) {
        const value = +child.valueTop1
        lv2.valueTop1 += value
      }
    }

    const lstLv1 = lstResult.filter((c) => c.level == 1 && c.lstChildId && c.lstChildId.length > 0)
    for (const lv1 of lstLv1) {
      lv1.listTop1 = []
      lv1.valueTop1 = 0
      const lstChild = lstResult.filter((c) => c.parentId == lv1.id)
      for (const child of lstChild) {
        const value = +child.valueTop1
        lv1.valueTop1 += value
      }
    }

    return lstResult
  }

  /** Tạo đàm phán giá Item */
  async saveBidDeal(user: UserDto, data: BidDealCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidItem: any = await this.bidRepo.findOne({
      where: {
        id: data.bidId,
        companyId: user.companyId,
        isDeleted: false,
      },
      relations: { parent: true, service: true },
      select: { id: true, status: true, parentId: true, parent: { status: true }, service: { code: true, name: true } },
    })
    if (!bidItem) throw new Error('Không tìm thấy Item cần tạo đàm phán giá.')
    if (bidItem.status == enumData.BidStatus.DangDamPhanGia.code)
      throw new Error(`Item ${enumData.BidStatus.DangDamPhanGia.name}, không thể tạo thêm đàm phán giá.`)
    if (bidItem.status == enumData.BidStatus.DangDauGia.code)
      throw new Error(`Item ${enumData.BidStatus.DangDauGia.name}, không thể tạo đàm phán giá.`)
    if (bidItem.status == enumData.BidStatus.DongDauGia.code)
      throw new Error(`Item ${enumData.BidStatus.DongDauGia.name}, sau khi Item được đấu giá, không thể tạo đàm phán giá.`)
    if (bidItem.__parent__?.status != enumData.BidStatus.HoanTatDanhGia.code)
      throw new Error(`Chỉ đàm phán giá khi gói thầu ${enumData.BidStatus.HoanTatDanhGia.name}.`)

    let bidDealId = null
    await this.repo.manager.transaction(async (manager) => {
      const bidRepo = manager.getRepository(BidEntity)
      const bidHistoryRepo = manager.getRepository(BidHistoryEntity)
      const bidDealRepo = manager.getRepository(BidDealEntity)
      const bidDealSupplierRepo = manager.getRepository(BidDealSupplierEntity)
      const bidDealPriceRepo = manager.getRepository(BidDealPriceEntity)

      // Chuyển trạng thái cho Item thành đang đàm phán
      await bidRepo.update(bidItem.id, {
        status: enumData.BidStatus.DangDamPhanGia.code,
        updatedBy: user.id,
      })

      const bidHistory = new BidHistoryEntity()
      bidHistory.bidId = bidItem.parentId
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.TaoDamPhanGia.code
      bidHistory.description = `Item [${bidItem.__service__.code} - ${bidItem.__service__.name}]`
      await bidHistoryRepo.save(bidHistory)

      let bidDeal = new BidDealEntity()
      bidDeal.bidId = bidItem.parentId
      bidDeal.companyId = user.companyId
      bidDeal.createdBy = user.id
      bidDeal.isSendDealPrice = data.isSendDealPrice
      bidDeal.isRequireFilePriceDetail = data.isRequireFilePriceDetail
      bidDeal.isRequireFileTechDetail = data.isRequireFileTechDetail
      bidDeal.endDate = data.endDate
      bidDeal.status = enumData.BidDealStatus.DangDamPhan.code
      bidDeal = await bidDealRepo.save(bidDeal)
      bidDealId = bidDeal.id

      // đàm phán 1 item nên k cần for
      let bidDealItem = new BidDealEntity()
      bidDealItem.parentId = bidDealId
      bidDealItem.bidId = bidItem.id
      bidDealItem.companyId = user.companyId
      bidDealItem.createdBy = user.id
      bidDealItem.isSendDealPrice = data.isSendDealPrice
      bidDealItem.isRequireFilePriceDetail = data.isRequireFilePriceDetail
      bidDealItem.isRequireFileTechDetail = data.isRequireFileTechDetail
      bidDealItem.endDate = data.endDate
      bidDealItem.status = enumData.BidDealStatus.DangDamPhan.code
      bidDealItem = await bidDealRepo.save(bidDealItem)
      const bidDealItemId = bidDealItem.id

      // đàm phán 1 item nên k cần for
      for (let item of data.lstPrice) {
        const bidDealPrice = new BidDealPriceEntity()
        bidDealPrice.companyId = user.companyId
        bidDealPrice.createdBy = user.id
        bidDealPrice.bestPrice = item.bestPrice
        if (bidDeal.isSendDealPrice && item.suggestPrice) {
          bidDealPrice.suggestPrice = item.suggestPrice
        }
        bidDealPrice.maxPrice = item.maxPrice
        bidDealPrice.bidDealId = bidDealItemId
        bidDealPrice.bidPriceId = item.bidPriceId
        bidDealPrice.sort = item.sort
        bidDealPrice.number = item.number
        await bidDealPriceRepo.save(bidDealPrice)
      }

      for (let item of data.lstSupplierChoose) {
        const bidDealSupplier = new BidDealSupplierEntity()
        bidDealSupplier.companyId = user.companyId
        bidDealSupplier.createdBy = user.id
        bidDealSupplier.bidDealId = bidDealId
        bidDealSupplier.supplierId = item.supplierId
        bidDealSupplier.status = enumData.BidDealSupplierStatus.DangDamPhan.code
        await bidDealSupplierRepo.save(bidDealSupplier)

        // đàm phán 1 item nên k cần for
        const bidDealSupplierItem = new BidDealSupplierEntity()
        bidDealSupplierItem.companyId = user.companyId
        bidDealSupplierItem.createdBy = user.id
        bidDealSupplierItem.bidDealId = bidDealItemId
        bidDealSupplierItem.supplierId = item.supplierId
        bidDealSupplierItem.status = enumData.BidDealSupplierStatus.DangDamPhan.code
        await bidDealSupplierRepo.save(bidDealSupplierItem)
      }
    })

    if (bidDealId) this.emailService.ThongBaoNccThamGiaDamPhan(bidDealId)

    return { message: UPDATE_SUCCESS }
  }

  //#region NCC

  /** Lấy thông tin đàm phán giá NCC */
  async getBidDealSupplier(user: UserDto, bidDealId: string) {
    if (!user.supplierId) throw new NotFoundException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!bidDealId) throw new BadRequestException(ERROR_NOT_FOUND_DATA)

    const bidDealSupplierRepo = this.repo.manager.getRepository(BidDealSupplierEntity)
    const bidPriceColRepo = this.repo.manager.getRepository(BidPriceColEntity)

    const todate = new Date()
    const objCheck = await bidDealSupplierRepo.findOne({
      where: {
        bidDealId,
        supplierId: user.supplierId,
        companyId: user.companyId,
      },
      select: { id: true, status: true },
    })
    if (!objCheck) throw new NotFoundException('Bạn không được mời tham gia đàm phán giá này!')

    const res: any = await this.repo.findOne({
      where: { id: bidDealId, companyId: user.companyId },
      relations: {
        bid: true,
        childs: { bid: { service: true }, bidDealPrices: { bidPrice: { bidPriceListDetails: true, bidPriceColValue: true } } },
      },
      select: {
        id: true,
        isSendDealPrice: true,
        endDate: true,
        isRequireFileTechDetail: true,
        bidId: true,
        bid: { name: true },
        childs: { id: true, bidId: true, bid: { id: true, service: { code: true, name: true } }, bidDealPrices: true },
      },
    })
    if (!res) throw new NotFoundException('Không tìm thấy thông tin đàm phán giá.')

    const statusDamPhan = enumData.BidDealSupplierStatus.DangDamPhan.code
    res.isDisplayBtn = objCheck.status == statusDamPhan && res.endDate > todate && res.isSendDealPrice
    res.isDisplayBtnSavePrice = objCheck.status == statusDamPhan && res.endDate > todate && !res.isSendDealPrice

    res.bidName = res.__bid__.name
    delete res.__bid__
    res.listChild = res.__childs__
    delete res.__childs__

    const getDataCell = (row: any, col: any, lstValue: any[] = []) => {
      row[col.id] = ''
      if (col.colType === enumData.ColType.MPO.code) {
        if (row.__bidPrice__.__bidPriceColValue__?.length > 0) {
          const cell = row.__bidPrice__.__bidPriceColValue__.find((c: any) => c.bidPriceColId === col.id)
          if (cell) row[col.id] = cell.value
        }
      } else {
        const cell = lstValue.find((c) => c.bidPriceColId === col.id && c.bidPriceId === row.bidPriceId)
        if (cell) row[col.id] = cell.value
      }
    }

    for (const child of res.listChild) {
      const bidDealSupplier = await bidDealSupplierRepo.findOne({
        where: {
          bidDealId: child.id,
          supplierId: user.supplierId,
          companyId: user.companyId,
        },
        select: { id: true },
      })
      if (!bidDealSupplier) {
        child.isRemove = true
        continue
      }
      child.itemName = child.__bid__.__service__.code + ' - ' + child.__bid__.__service__.name
      delete child.__bid__
      child.lstDealPrice = child.__bidDealPrices__
      delete child.__bidDealPrices__

      child.lstPriceCol = await bidPriceColRepo.find({
        where: { bidId: child.bidId, companyId: user.companyId, isDeleted: false },
        order: { sort: 'ASC', createdAt: 'ASC' },
      })

      // map data
      const bidSupplier = await this.bidSupplierRepo.findOne({
        where: {
          bidId: child.bidId,
          supplierId: user.supplierId,
          companyId: user.companyId,
          isDeleted: false,
        },
        select: { id: true },
      })
      if (!bidSupplier) {
        child.isRemove = true
        continue
      }
      const lstBidSupplierPriceValue = await bidSupplier.bidSupplierPriceValue
      const lstBidSupplierPriceColValue = await bidSupplier.bidSupplierPriceColValue
      for (const row of child.lstDealPrice) {
        for (const col of child.lstPriceCol) {
          getDataCell(row, col, lstBidSupplierPriceColValue)
        }

        const objValue = lstBidSupplierPriceValue.find((c) => c.bidPriceId === row.bidPriceId)
        if (objValue) row.offerPrice = objValue.value
        // xóa giá trị cột động không cần thiết
        delete row.__bidPrice__.__bidPriceColValue__
      }

      let lstTemp1 = child.lstDealPrice
        .filter((c: any) => c.__bidPrice__.level == 1)
        .sort((a: any, b: any) => a.__bidPrice__.sort - b.__bidPrice__.sort)
      const lstTemp2 = child.lstDealPrice
        .filter((c: any) => c.__bidPrice__.level == 2)
        .sort((a: any, b: any) => a.__bidPrice__.sort - b.__bidPrice__.sort)
      const lstTemp3 = child.lstDealPrice
        .filter((c: any) => c.__bidPrice__.level == 3)
        .sort((a: any, b: any) => a.__bidPrice__.sort - b.__bidPrice__.sort)
      for (let i = 0; i < lstTemp1.length; i++) {
        const temp = lstTemp1[i]
        let childInLv1 = lstTemp2.filter((c: any) => c.__bidPrice__.parentId == temp.bidPriceId)
        for (let j = 0; j < childInLv1.length; j++) {
          const temp2 = childInLv1[j]
          const childInLv2 = lstTemp3.filter((c: any) => c.__bidPrice__.parentId == temp2.bidPriceId)
          childInLv1.splice(j + 1, 0, ...childInLv2)
        }
        lstTemp1.splice(i + 1, 0, ...childInLv1)
      }
      child.lstDealPrice = lstTemp1
    }

    // bỏ các item không được mời đàm phán giá
    res.listChild = res.listChild.filter((c) => !c.isRemove)

    return res
  }

  /** Lấy ds đàm phán mới nhất của các NCC */
  private async getBidDealSupplierNewest(bidDealRepo: Repository<BidDealEntity>, user: UserDto, bidId: string) {
    const lstSupplierId: string[] = []

    const lstBidDeal: any[] = await bidDealRepo.find({
      where: { bidId, companyId: user.companyId, bidDealSupplier: { status: enumData.BidDealSupplierStatus.DaGuiGiaMoi.code } },
      relations: { bidDealSupplier: true },
      select: { id: true, createdAt: true, bidDealSupplier: { id: true, supplierId: true } },
      order: { createdAt: 'DESC' },
    })

    for (const bidDeal of lstBidDeal) {
      const temp = bidDeal.__bidDealSupplier__.map((c) => c.supplierId)
      lstSupplierId.push(...temp)
    }

    return lstSupplierId.filter((value, index, self) => self.indexOf(value) === index)
  }

  /** Đề nghị đàm phán giá/ Chấp nhận giá đề nghị/ Lưu */
  async acceptBidDealSupplier(user: UserDto, data: BidDealSupplierSaveDto) {
    if (!user.supplierId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    // Xác nhận và lưu kết quả đàm phán giá
    const result = await this.repo.manager.transaction(async (manager) => {
      const bidDealSupplierRepo = manager.getRepository(BidDealSupplierEntity)
      const bidDealRepo = manager.getRepository(BidDealEntity)
      const bidDealSupplierPriceValueRepo = manager.getRepository(BidDealSupplierPriceValueEntity)
      const bidSupplierRepo = manager.getRepository(BidSupplierEntity)
      const bidSupplierPriceRepo = new BidSupplierPriceRepository(BidSupplierPriceEntity, manager)

      const todate = new Date()
      const bidDeal = await bidDealRepo.findOne({ where: { id: data.id, companyId: user.companyId }, select: { id: true, endDate: true } })
      if (!bidDeal) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
      if (bidDeal.endDate < todate) throw new NotFoundException('Đã quá hạn thời gian đàm phán giá.')

      const bidSupplier = await bidSupplierRepo.findOne({
        where: {
          bidId: bidDeal.bidId,
          supplierId: user.supplierId,
          companyId: user.companyId,
          isDeleted: false,
        },
        select: { id: true },
      })
      if (!bidSupplier) throw new Error('Bạn không có quyền tham gia gói thầu này.')

      const bidDealSupplier = await bidDealSupplierRepo.findOne({
        where: {
          bidDealId: data.id,
          supplierId: user.supplierId,
          companyId: user.companyId,
          status: enumData.BidDealSupplierStatus.DangDamPhan.code,
        },
        select: { id: true },
      })
      if (!bidDealSupplier) throw new Error('Bạn không có quyền tham gia lần đàm phán giá này.')

      for (const child of data.listChild) {
        const bidDealSupplierItem = await bidDealSupplierRepo.findOne({
          where: {
            bidDealId: child.id,
            supplierId: user.supplierId,
            companyId: user.companyId,
            status: enumData.BidDealSupplierStatus.DangDamPhan.code,
          },
        })
        if (!bidDealSupplierItem) continue

        const bidSupplierItem = await bidSupplierRepo.findOne({
          where: {
            bidId: child.bidId,
            supplierId: user.supplierId,
            companyId: user.companyId,
            isDeleted: false,
          },
          select: { id: true, bidId: true, serviceId: true, supplierId: true },
        })
        if (!bidSupplierItem) continue

        bidDealSupplierItem.status = enumData.BidDealSupplierStatus.DaGuiGiaMoi.code
        bidDealSupplierItem.filePriceDetail = child.filePriceDetail
        bidDealSupplierItem.fileTechDetail = child.fileTechDetail
        bidDealSupplierItem.linkDriver = child.linkDriver
        bidDealSupplierItem.submitDate = todate
        bidDealSupplierItem.updatedBy = user.id
        await bidDealSupplierRepo.save(bidDealSupplierItem)

        // Xoá dữ liệu đàm phán giá cũ của NCC với item tương ứng
        await bidDealSupplierPriceValueRepo.delete({
          bidDealSupplierId: bidDealSupplierItem.id,
        })

        // xóa kết quả lần nộp giá cuối của NCC với item tương ứng
        await bidSupplierPriceRepo.delete({ bidSupplierId: bidSupplierItem.id })

        for (let i = 0, len = child.lstDealPrice.length; i < len; i++) {
          const itemDealPrice = child.lstDealPrice[i]
          const priceValue = new BidDealSupplierPriceValueEntity()
          priceValue.companyId = user.companyId
          priceValue.createdBy = user.id
          priceValue.bidDealSupplierId = bidDealSupplierItem.id
          priceValue.bidPriceId = itemDealPrice.bidPriceId
          // default lấy giá đề nghị
          priceValue.value = itemDealPrice.suggestPrice + ''
          // nếu nhập giá đàm phán thì lấy giá đàm phán
          if (itemDealPrice.dealPrice > 0) {
            priceValue.value = itemDealPrice.dealPrice + ''
          }
          await bidDealSupplierPriceValueRepo.save(priceValue)

          const itemPrice: any = {}
          itemPrice.bidPriceId = itemDealPrice.bidPriceId
          itemPrice.name = itemDealPrice.__bidPrice__.name
          itemPrice.level = itemDealPrice.__bidPrice__.level
          itemPrice.value = priceValue.value
          itemPrice.number = itemDealPrice.number || itemDealPrice.__bidPrice__.number
          itemPrice.submitDate = todate
          itemPrice.submitType = 1
          await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplierItem, itemPrice)
        }
      }
      // cập nhật trạng thái lần đàm phán của NCC
      await bidDealSupplierRepo.update(
        {
          bidDealId: data.id,
          supplierId: user.supplierId,
          status: enumData.BidDealSupplierStatus.DangDamPhan.code,
        },
        {
          status: enumData.BidDealSupplierStatus.DaGuiGiaMoi.code,
          submitDate: todate,
          updatedBy: user.id,
        },
      )
    })

    // Tính lại điểm mỗi lần đàm phán giá
    await this.repo.manager.transaction(async (manager) => {
      const bidDealRepo = manager.getRepository(BidDealEntity)
      const bidDealSupplierRepo = manager.getRepository(BidDealSupplierEntity)
      const bidDealSupplierPriceValueRepo = manager.getRepository(BidDealSupplierPriceValueEntity)
      const bidSupplierRepo = manager.getRepository(BidSupplierEntity)
      const bidSupplierPriceValueRepo = manager.getRepository(BidSupplierPriceValueEntity)

      for (const child of data.listChild) {
        const bidDeal = await bidDealRepo.findOne({ where: { id: child.id, companyId: user.companyId }, select: { id: true, bidId: true } })
        if (!bidDeal) throw new Error(ERROR_NOT_FOUND_DATA)

        const bid = await bidDeal.bid
        // reset điểm giá chung
        await bidSupplierRepo.update({ bidId: bidDeal.bidId }, { scorePrice: 0, updatedBy: user.id })

        // lấy các NCC đã gửi giá mới
        const lstBidDealSupplierId = await this.getBidDealSupplierNewest(bidDealRepo, user, bidDeal.bidId)

        // reset điểm đàm phán
        await bidDealSupplierRepo.update({ id: In(lstBidDealSupplierId) }, { score: 0, updatedBy: user.id })
        const lstBidDealSupplier = await bidDealSupplierRepo.find({ where: { id: In(lstBidDealSupplierId), companyId: user.companyId } })

        const lstSupplierTempId1 = lstBidDealSupplier.map((c) => c.supplierId)
        // lấy giá trị đàm phán
        const lstBidDealSupplierPriceValue = await bidDealSupplierPriceValueRepo.find({
          where: { bidDealSupplierId: In(lstBidDealSupplierId), companyId: user.companyId },
        })

        // lấy NCC có hsơ hợp lệ nhưng chưa/không gửi giá đàm phán
        const lstBidSupplier = (await bid.bidSuppliers).filter(
          (c) => c.statusFile === enumData.BidSupplierFileStatus.HopLe.code && !lstSupplierTempId1.includes(c.supplierId),
        )
        const lstBidSupplierId = lstBidSupplier.map((c) => c.id)
        const lstSupplierTempId2 = lstBidSupplier.map((c) => c.supplierId)
        const lstSupplierId = [...lstSupplierTempId1, ...lstSupplierTempId2]
        // lấy giá trị từ hồ sơ đã nộp
        let lstBidSupplierPriceValue: BidSupplierPriceValueEntity[] = []
        if (lstBidSupplierId.length > 0) {
          lstBidSupplierPriceValue = await bidSupplierPriceValueRepo.find({
            where: { bidSupplierId: In(lstBidSupplierId), companyId: user.companyId },
          })
        }

        //#region Lọc qua danh sách các hạng mục hồ sơ chào giá của gói thầu - tính điểm cho từng hạng mục

        // Lấy template hồ sơ chào giá của gói thầu
        const lstBidPrice = await bid.prices
        const scoreDLC = bid.scoreDLC

        for (const bidPrice of lstBidPrice) {
          const lstTemp1 = lstBidDealSupplierPriceValue.filter((c) => c.bidPriceId === bidPrice.id)
          const lstTempValue1 = lstTemp1.map((c) => +c.value)
          const lstTemp2 = lstBidSupplierPriceValue.filter((c) => c.bidPriceId === bidPrice.id)
          const lstTempValue2 = lstTemp2.map((c) => +c.value)
          const lstValue = [...lstTempValue1, ...lstTempValue2]

          const minValue = Math.min(...lstValue)
          const dlc = coreHelper.calDLC(lstValue)

          for (const item of lstTemp1) {
            let score = scoreDLC
            if (dlc > 0) {
              score = scoreDLC - (+item.value - minValue) / dlc
            }
            item.score = score
            await bidDealSupplierPriceValueRepo.update(item.id, { score, updatedBy: user.id })
          }
          for (const item of lstTemp2) {
            let score = scoreDLC
            if (dlc > 0) {
              score = scoreDLC - (+item.value - minValue) / dlc
            }
            item.score = score
            await bidSupplierPriceValueRepo.update(item.id, { score, updatedBy: user.id })
          }
        }

        //#endregion

        //#region Lọc qua danh sách NCC được mời thầu (đã cập nhật giá mới hoặc có hồ sơ hợp lệ) để tính tổng điểm

        const lstBidSupplierInvite = (await bid.bidSuppliers).filter((c) => lstSupplierId.includes(c.supplierId))
        for (const bidSupplier of lstBidSupplierInvite) {
          // lấy dữ liệu đàm phán
          const bidDealSupplier = lstBidDealSupplier.find((c) => c.supplierId === bidSupplier.supplierId)

          let scorePrice = 0
          if (bidDealSupplier) {
            const lstBidDealSupplierPriceValue = await bidDealSupplier.bidDealSupplierPriceValue
            for (const item of lstBidDealSupplierPriceValue) {
              scorePrice += item.score
            }
            await bidDealSupplierRepo.update(bidDealSupplier.id, { score: scorePrice, updatedBy: user.id })
          } else {
            const lstBidSupplierPriceValue = await bidSupplier.bidSupplierPriceValue
            for (const item of lstBidSupplierPriceValue) {
              scorePrice += item.score
            }
          }
          bidSupplier.scorePrice = scorePrice
          await bidSupplierRepo.update(bidSupplier.id, { scorePrice, updatedBy: user.id })
        }

        //#endregion

        //#region Tính lại điểm dựa trên điểm tạm lưu ở trên

        const listBidSupplierScorePrice = lstBidSupplierInvite.map((p) => {
          return p.scorePrice
        })
        const dlcBidSupplier = coreHelper.calDLC(listBidSupplierScorePrice)
        const maxScorePrice = Math.max(...listBidSupplierScorePrice)
        // Lọc qua danh sách NCC tính lại độ lệch chuẩn
        for (const item of lstBidSupplierInvite) {
          let scorePrice = scoreDLC
          if (dlcBidSupplier > 0) {
            scorePrice = scoreDLC - (maxScorePrice - item.scorePrice) / dlcBidSupplier
          }
          // Lưu điểm
          await bidSupplierRepo.update(item.id, { scorePrice, updatedBy: user.id })
        }

        //#endregion
      }
    })

    // Gửi email
    this.emailService.GuiMpoNCCXacNhanDamPhan(data.id, user.supplierId)

    return result
  }

  /** Từ chối giá đề nghị */
  async rejectBidDealSupplier(user: UserDto, bidDealId: string) {
    if (!user.supplierId) throw new NotFoundException('Không có quyền truy cập')

    const result = await this.repo.manager.transaction(async (manager) => {
      const bidDealSupplierRepo = manager.getRepository(BidDealSupplierEntity)
      const bidDealRepo = manager.getRepository(BidDealEntity)

      const todate = new Date()
      const bidDeal = await bidDealRepo.findOne({
        where: { id: bidDealId, companyId: user.companyId, endDate: MoreThan(todate) },
      })
      if (!bidDeal) throw new Error('Hết thời gian đàm phán giá.')

      const check = await bidDealSupplierRepo.findOne({
        where: {
          bidDealId,
          supplierId: user.supplierId,
          companyId: user.companyId,
          status: enumData.BidDealSupplierStatus.DangDamPhan.code,
        },
      })

      if (!check) throw new Error(ERROR_NOT_FOUND_DATA)

      await bidDealSupplierRepo.update(
        {
          bidDealId,
          supplierId: user.supplierId,
          status: enumData.BidDealSupplierStatus.DangDamPhan.code,
        },
        {
          status: enumData.BidDealSupplierStatus.DaTuChoi.code,
          updatedBy: user.id,
        },
      )
    })

    this.emailService.GuiMpoNCCXacNhanDamPhan(bidDealId, user.supplierId)

    return result
  }

  /** Lấy kết quả đàm phán */
  async checkResultMessage(user: UserDto, bidDealId: string) {
    const check = await this.repo.manager.getRepository(BidDealSupplierEntity).findOne({
      where: {
        bidDealId,
        supplierId: user.supplierId,
        companyId: user.companyId,
        isDeleted: false,
      },
      select: { id: true, status: true },
    })
    if (!check) return { check: false, message: 'Không có quyền truy cập.' }

    const todate = new Date()
    const bidDeal = await check.bidDeal
    if (check.status === enumData.BidDealSupplierStatus.DaTuChoi.code || check.status === enumData.BidDealSupplierStatus.DaGuiGiaMoi.code) {
      return { check: true, message: 'Đã đàm phán thành công.' }
    } else if (todate >= bidDeal.endDate || bidDeal.status === enumData.BidDealStatus.DongDamPhanGia.code) {
      return { check: false, message: 'Hết hạn đàm phán.' }
    }

    return { check: false, message: 'Không có quyền truy cập.' }
  }

  /* hàm lấy ra giá tốt nhất bằng AI */
  async getPriceByGemini(data: any) {
    /* tạo ra script để call chat*/
    let scrip = process.env.SCRIPT_TEMPLATE_02
    scrip = scrip.replace('{{S_01}}', data.productName)
    if (data.priceData && data.priceData > 0) {
      scrip = process.env.SCRIPT_TEMPLATE
      scrip = scrip.replace('{{S_01}}', data.productName)
      scrip = scrip.replace('{{S_02}}', data.priceData)
    }
    /* lấy giá mới nhất */
    const returnNumber = await this.aiService.callGeminiAI(scrip)

    return { mess: returnNumber || 'Sever AI đăng bảo trì, Vui lòng liên hệ quản trị viên' }
  }

  async getPriceAi(data: any[]) {
    /* map array data.dataItem thành chuỗi array[0],array[1]*/
    let str = data.join(',')
    /* map data vào script để call AI*/
    const stringScript = `trả lời cho tôi giá gợi ý của "${str}" dựa theo giá thị trường bằng số duy nhất kiểu number và ngăn cách bởi dấu ;@;`
    let returnPrice: any = await this.aiService.callGeminiAI(stringScript)
    returnPrice = returnPrice.replace('\n', '')
    returnPrice = returnPrice.replace('.', '').replace(' .', '').replace('. ', '')
    const lstArray = returnPrice ? returnPrice.split(';@;') : []
    return { returnPrice: lstArray }
  }

  //#endregion
}
