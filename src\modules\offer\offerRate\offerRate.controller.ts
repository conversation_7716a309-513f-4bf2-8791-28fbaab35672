import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { PaginationDto, UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { ApeAuthGuard } from '../../common/guards'
import { OfferRateService } from './offerRate.service'
import { OfferRateService2 } from './offerRate2.service'

/** Đ<PERSON>h giá thầu */
@ApiBearerAuth()
@ApiTags('Offer')
@UseGuards(ApeAuthGuard)
@Controller('offer-rates')
export class OfferRateController {
  constructor(private readonly service: OfferRateService, private readonly service2: OfferRateService2) {}

  @ApiOperation({ summary: 'Danh sách gói thầu module đánh giá thầu phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  //#region bidTradeRate

  @ApiOperation({ summary: 'Lấy ds Doanh nghiệp tham gia thầu và tính điểm' })
  @Get('load-trade-rate/:id')
  public async loadTradeRate(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.loadTradeRate(user, bidId)
  }
  @ApiOperation({ summary: 'Lấy danh sách sách bidTrade và điểm cao nhất tương ứng' })
  @Post('load-best-trade-value')
  public async loadBestTradeValue(@CurrentUser() user: UserDto, @Body() data: { bidId: string; bidSupplierId: string }) {
    return await this.service.loadBestTradeValue(user, data)
  }
  @ApiOperation({ summary: 'Tạo đánh giá thương mại' })
  @Post('create-trade-rate')
  public async createTradeRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[] }) {
    return await this.service.createTradeRate(user, data)
  }
  @ApiOperation({ summary: 'Duyệt đánh giá thương mại' })
  @Post('approve-trade-rate')
  public async approveTradeRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[]; comment: string }) {
    return await this.service.approveTradeRate(user, data)
  }
  @ApiOperation({ summary: 'Từ chối đánh giá thương mại' })
  @Post('reject-trade-rate')
  public async rejectTradeRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[]; comment: string }) {
    return await this.service.rejectTradeRate(user, data)
  }

  //#endregion

  //#region bidPriceRate

  @ApiOperation({ summary: 'Lấy ds Doanh nghiệp tham gia thầu và tính điểm' })
  @Get('load-price-rate/:id')
  public async loadPriceRate(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.loadPriceRate(user, bidId)
  }
  @ApiOperation({ summary: 'Lấy danh sách sách bidPrice và điểm cao nhất tương ứng' })
  @Post('load-best-price-value')
  public async loadBestPriceValue(@CurrentUser() user: UserDto, @Body() data: { bidId: string; bidSupplierId: string }) {
    return await this.service.loadBestPriceValue(user, data)
  }
  @ApiOperation({ summary: 'Tạo đánh giá giá' })
  @Post('create-price-rate')
  public async createPriceRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[] }) {
    return await this.service.createPriceRate(user, data)
  }

  //#endregion

  //#region Phân tích giá

  @ApiOperation({ summary: 'Xếp hạng theo giá thấp nhất (Giá theo từng hạng mục)' })
  @Post('load-rank-by-min-price')
  public async loadRankByMinPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string; lstId: string[] }) {
    return await this.service2.loadRankByMinPrice(user, data)
  }

  @ApiOperation({ summary: 'Xếp hạng theo tổng giá dạng 1 (Giá theo từng Doanh nghiệp)' })
  @Post('load-rank-by-sum-price')
  public async loadRankBySumPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string; lstId: string[] }) {
    return await this.service2.loadRankBySumPrice(user, data)
  }

  @ApiOperation({ summary: 'Xếp hạng theo tổng giá dạng 2 (Giá theo từng Doanh nghiệp) (Mỗi dòng 1 Doanh nghiệp)' })
  @Post('load-supplier-rank-by-sum-price')
  public async loadSupplierRankBySumPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string; lstId: string[] }) {
    return await this.service2.loadSupplierRankBySumPrice(user, data)
  }

  //#endregion

  @ApiOperation({ summary: 'Báo cáo kết quả đánh giá' })
  @Get('get-data-report-rate-bid/:bidid')
  public async getDataReportRateBid(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.service2.getDataReportRateBid(user, bidId)
  }

  // @ApiOperation({ summary: 'In kết quả đánh giá' })
  // @Get('get_data_print_rate_bid/:bidid')
  // public async getDataPrintRateBid(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
  //   return await this.service2.getDataPrintRateBid(user, bidId)
  // }

  //#region Phê duyệt kết thúc thầu

  @ApiOperation({ summary: 'Gửi yêu cầu phê duyệt kết thúc thầu' })
  @Post('send-request-finish-bid')
  public async sendRequestFinishBid(@CurrentUser() user: UserDto, @Body() data: { id: string; fileScan: string; noteFinishBidMPO: string }) {
    return await this.service2.sendRequestFinishBid(user, data)
  }

  @ApiOperation({ summary: 'Phê duyệt kết thúc thầu' })
  @Post('approve-finish-bid')
  public async approveFinishBid(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service2.approveFinishBid(user, data)
  }

  //#endregion

  //#region Đàm phán/ Đấu giá

  @ApiOperation({ summary: 'DS item khi Đàm phán/ Đấu giá' })
  @Post('item-pagination')
  public async itemPagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service2.itemPagination(user, data)
  }

  //#endregion

  //#region Truy vấn thông tin gói thầu

  @ApiOperation({ summary: 'Danh sách gói thầu đã hoàn tất' })
  @Post('result-pagination')
  public async resultPagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.resultPagination(user, data)
  }
  //#endregion
}
