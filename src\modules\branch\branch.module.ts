import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { BranchService } from './branch.service'
import { BranchController } from './branch.controller'
import { BranchRepository, EmployeeRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BranchRepository, EmployeeRepository])],
  controllers: [BranchController],
  providers: [BranchService],
})
export class BranchModule {}
