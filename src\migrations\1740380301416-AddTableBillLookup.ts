import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTableBillLookup1740380301416 implements MigrationInterface {
    name = 'AddTableBillLookup1740380301416'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`bill_lookup\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`code\` varchar(50) NOT NULL, \`name\` varchar(200) NULL, \`abbreviation\` varchar(100) NULL, \`link\` varchar(4000) NULL, \`description\` varchar(4000) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE \`bill_lookup\``);
    }

}
