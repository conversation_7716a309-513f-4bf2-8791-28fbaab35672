import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsString } from 'class-validator'

export class AsnUpdateDto {
  @ApiProperty()
  @IsString()
  id: string

  @ApiProperty()
  @IsString()
  code: string

  @ApiProperty()
  @IsString()
  poId: string

  @ApiProperty()
  @IsString()
  branchId: string

  @ApiProperty()
  @IsString()
  warehouseId: string

  @ApiPropertyOptional()
  asnDate: Date

  @ApiPropertyOptional()
  quantity: number

  @ApiPropertyOptional()
  purchasePlanId: string

  @ApiPropertyOptional()
  description: string

  @ApiPropertyOptional()
  objectId: string

  @ApiProperty()
  __details__: AsnItemUpdateDto[]
}

export class AsnItemUpdateDto {
  @ApiProperty()
  @IsString()
  id: string

  @ApiProperty()
  @IsString()
  poId: string

  @ApiPropertyOptional()
  asnId: string

  @ApiPropertyOptional()
  serviceId: string

  @ApiPropertyOptional()
  quantity: number

  @ApiPropertyOptional()
  quantityInbound: number

  @ApiPropertyOptional()
  description: string

  @ApiPropertyOptional()
  itemCode: string
}
