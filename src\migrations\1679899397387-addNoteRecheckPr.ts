import { MigrationInterface, QueryRunner } from 'typeorm'

export class addNoteRecheckPr1679899397387 implements MigrationInterface {
  name = 'addNoteRecheckPr1679899397387'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`pr\` ADD \`noteReCheck\` text NULL`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`pr\` DROP COLUMN \`noteReCheck\``)
  }
}
