import { MigrationInterface, QueryRunner } from 'typeorm'

export class auctionStatus1710389616083 implements MigrationInterface {
  name = 'auctionStatus1710389616083'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`auction\` ADD \`status\` varchar(10) NOT NULL DEFAULT 'NEW'`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`auction\` DROP COLUMN \`status\``)
  }
}
