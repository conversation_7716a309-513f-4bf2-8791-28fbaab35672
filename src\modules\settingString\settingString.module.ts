import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { SettingStringService } from './settingString.service'
import { SettingStringController } from './settingString.controller'
import { SettingStringRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([SettingStringRepository])],
  controllers: [SettingStringController],
  providers: [SettingStringService],
})
export class SettingStringModule {}
