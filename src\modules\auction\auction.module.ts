import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { AuctionHistoryRepository, AuctionRepository, AuctionSupplierRepository } from '../../repositories'
import { AuctionService } from './auction.service'
import { AuctionController } from './auction.controller'
import { EmailModule } from '../email/email.module'

@Module({
  imports: [EmailModule, TypeOrmExModule.forCustomRepository([AuctionRepository, AuctionSupplierRepository, AuctionHistoryRepository])],
  controllers: [AuctionController],
  providers: [AuctionService],
})
export class AuctionModule {}
