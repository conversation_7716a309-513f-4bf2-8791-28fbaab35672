import { MigrationInterface, QueryRunner } from 'typeorm'

export class addChildPo1718874685322 implements MigrationInterface {
  name = 'addChildPo1718874685322'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`contract\` ADD \`isChild\` tinyint NULL DEFAULT 0`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`contract\` DROP COLUMN \`isChild\``)
  }
}
