import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { EmployeeEntity } from './employee.entity'
import { BidEntity } from './bid.entity'
import { BaseEntity } from './base.entity'

@Entity('bid_history')
export class BidHistoryEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.bidHistorys)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.bidHistorys)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /** <PERSON><PERSON> chú thêm */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string
}
