import { BaseEntity } from './base.entity'
import { Entity, Column, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, OneToMany } from 'typeorm'
import { BidEntity } from './bid.entity'
import { BidDealSupplierEntity } from './bidDealSupplier.entity'
import { BidDealPriceEntity } from './bidDealPrice.entity'

@Entity('bid_deal')
export class BidDealEntity extends BaseEntity {
  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /** Thời điểm kết thúc đàm phán giá */
  @Column({
    nullable: false,
  })
  endDate: Date

  /** Gửi giá đàm phán hoặc không gửi giá trong thông báo đàm phàn với nhà cung cấp */
  @Column({
    nullable: false,
    default: false,
  })
  isSendDealPrice: boolean

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.bidDeals)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  /** Id của đàm phán cha */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  parentId?: string
  /** Cha */
  @ManyToOne(() => BidDealEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: Promise<BidDealEntity>

  /** Con - 1 đàm phán sẽ có thể có nhiều đàm phán con */
  @OneToMany(() => BidDealEntity, (p) => p.parent)
  childs: Promise<BidDealEntity[]>

  /** Có bắt buộc File chi tiết giá */
  @Column({
    nullable: true,
    default: false,
  })
  isRequireFilePriceDetail: boolean

  /** Có bắt buộc File chi tiết kỹ thuật */
  @Column({
    nullable: true,
    default: false,
  })
  isRequireFileTechDetail: boolean

  @OneToMany(() => BidDealSupplierEntity, (p) => p.bidDeal)
  bidDealSupplier: Promise<BidDealSupplierEntity[]>

  @OneToMany(() => BidDealPriceEntity, (p) => p.bidDeal)
  bidDealPrices: Promise<BidDealPriceEntity[]>
}
