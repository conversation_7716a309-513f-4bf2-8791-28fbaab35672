import { BaseEntity } from './base.entity'
import { Entity, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>um<PERSON>, OneToMany } from 'typeorm'
import { BidEntity } from './bid.entity'
import { BidAuctionSupplierEntity } from './bidAuctionSupplier.entity'
import { BidAuctionPriceEntity } from './bidAuctionPrice.entity'

@Entity('bid_auction')
export class BidAuctionEntity extends BaseEntity {
  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /** Thời điểm kết thúc*/
  @Column({
    nullable: false,
  })
  endDate: Date

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.bidAuctions)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  /** Id của đấu giá cha */
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  parentId?: string
  /** Cha */
  @ManyToOne(() => BidAuctionEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: Promise<BidAuctionEntity>

  /** Con - 1 đấu giá sẽ có thể có nhiều đấu giá con */
  @OneToMany(() => BidAuctionEntity, (p) => p.parent)
  childs: Promise<BidAuctionEntity[]>

  @OneToMany(() => BidAuctionSupplierEntity, (p) => p.bidAuction)
  bidAuctionSupplier: Promise<BidAuctionSupplierEntity[]>

  @OneToMany(() => BidAuctionPriceEntity, (p) => p.bidAuction)
  bidAuctionPrice: Promise<BidAuctionPriceEntity[]>
}
