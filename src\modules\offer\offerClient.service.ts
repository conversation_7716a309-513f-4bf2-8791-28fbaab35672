import { Injectable } from '@nestjs/common'
import { ERROR_NOT_FOUND_DATA, UPDATE_SUCCESS } from '../../constants'
import { coreHelper } from '../../helpers'
import { enumData } from '../../constants/enumData'
import { UserDto } from '../../dto'
import { IsNull, Not } from 'typeorm'
import { SupplierCreateTradeItemDto, SupplierCreatePriceItemDto, SupplierCreateCustomPriceItemDto } from './dto'

import {
  OfferCustomPriceRepository,
  OfferPriceColRepository,
  OfferPriceRepository,
  OfferRepository,
  OfferServiceRepository,
  OfferSupplierPriceRepository,
  OfferSupplierRepository,
  OfferTradeRepository,
} from '../../repositories/offer.repository'
import { OfferSupplierPriceColValueEntity } from '../../entities/offerSupplierPriceColValue.entity'
import { OfferSupplierPriceEntity } from '../../entities/offerSupplierPrice.entity'
import { ServiceRepository } from '../../repositories'
import { OfferSupplierTradeValueEntity } from '../../entities/offerSupplierTradeValue.entity'
import { OfferSupplierCustomPriceValueEntity } from '../../entities/offerSupplierCustomPriceValues.entity'
import { OfferServiceEntity, OfferSupplierEntity, OfferSupplierPriceValueEntity } from '../../entities'
import { OfferSupplierTechValueEntity } from '../../entities/offerSupTechValue.entity'
import { OfferSupplierShipmentValueEntity } from '../../entities/offerSupplierShipmentValue.entity'
import { OfferShipmentPriceEntity } from '../../entities/offerShipmentPrice.entity'

@Injectable()
export class OfferClientService {
  constructor(
    private readonly repo: OfferRepository,
    private readonly bidTradeRepo: OfferTradeRepository,
    private readonly bidPriceRepo: OfferPriceRepository,
    private readonly bidPriceColRepo: OfferPriceColRepository,
    private readonly bidCustomPriceRepo: OfferCustomPriceRepository,
    private readonly bidSupplierRepo: OfferSupplierRepository,
    private readonly bidPrItemRepository: OfferServiceRepository,
  ) {}

  async loadDataOfferTrade(user: UserDto, data: { bidId: string; supplierId?: string; offerSupplierId?: string }) {
    const bidSupplierTradeValueRepo = this.repo.manager.getRepository(OfferSupplierTradeValueEntity)
    const offerPriceShipmentRepo = this.repo.manager.getRepository(OfferShipmentPriceEntity)
    const offerSupplierRepo = this.repo.manager.getRepository(OfferSupplierEntity)
    const listTrade: any[] = await this.bidTradeRepo.find({
      where: [
        { offerId: data.bidId, isDeleted: false, parentId: IsNull() },
        { offerServiceId: data.bidId, isDeleted: false, parentId: IsNull() },
      ],
      relations: { offerTradeListDetails: true, childs: { offerTradeListDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })

    const supplierId = user.supplierId
    if (supplierId && listTrade.length > 0 && data.bidId) {
      let where: any = [{ offerId: data.bidId, supplierId, isDeleted: false }]
      if (data.offerSupplierId) where = { id: data.offerSupplierId, isDeleted: false }
      const bidSupplier = await offerSupplierRepo.findOne({
        where: where,
        select: { id: true, status: true },
      })
      if (!bidSupplier) return listTrade

      // nếu đã nộp thầu => show các data đã nộp

      const dicValue: any = {}
      {
        const lstTradeValue = await bidSupplierTradeValueRepo.find({
          where: { offerSupplierId: data.offerSupplierId, isDeleted: false },
        })

        lstTradeValue.forEach((c) => (dicValue[c.offerTradeId] = c.value))
      }

      for (const data1 of listTrade) {
        data1.value = dicValue[data1.offerTradeId] || ''
      }
    }

    return listTrade
  }

  async loadDataOfferTradeView(user: UserDto, data: { bidId: string; supplierId?: string }) {
    const bidSupplierTradeValueRepo = this.repo.manager.getRepository(OfferSupplierTradeValueEntity)
    const bideItemRepo = this.repo.manager.getRepository(OfferServiceEntity)
    const listTrade: any[] = await this.bidTradeRepo.find({
      where: { offerId: data.bidId, isDeleted: false, parentId: IsNull() },
      relations: { offerTradeListDetails: true, childs: { offerTradeListDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })
    const bid = await bideItemRepo.findOne({ where: { offerId: data.bidId } })
    if (listTrade.length > 0 && bid) {
      const bidSupplier = await this.bidSupplierRepo.findOne({
        where: { id: data.supplierId },
        select: { id: true, status: true },
      })
      if (!bidSupplier) return listTrade

      // nếu đã nộp thầu => show các data đã nộp

      const dicValue: any = {}
      {
        const lstTradeValue = await bidSupplierTradeValueRepo.find({
          where: { offerSupplierId: bidSupplier.id, isDeleted: false },
        })
        lstTradeValue.forEach((c) => (dicValue[c.offerTradeId] = c.value))
      }

      for (const data1 of listTrade) {
        data1.value = dicValue[data1.id] || ''
        for (const data2 of data1.__childs__) {
          data2.value = dicValue[data2.id] || ''
        }
      }
    }

    return listTrade
  }
  async loadDataOfferPrice(user: UserDto, data: { bidId: string; supplierId?: string }) {
    const service = await this.bidPrItemRepository.findOne({ where: { offerId: data.bidId, isExGr: true } })
    if (service) data.bidId = service.id
    const res1: any[] = await this.bidPriceRepo.find({
      // where: { bidItemId: data.bidId },
      where: [{ offerId: data.bidId }, { offerServiceId: data.bidId }],

      relations: {
        offerPriceListDetails: true,
        offerPriceColValue: true,
        childs: { offerPriceListDetails: true, offerPriceColValue: true, childs: { offerPriceListDetails: true, offerPriceColValue: true } },
      },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } } },
    })
    const res2 = await this.bidPriceColRepo.getBidPriceColAll(user, data.bidId)

    const bideItemRepo = this.repo.manager.getRepository(OfferServiceEntity)
    const bid = await bideItemRepo.findOne({ where: [{ offerId: data.bidId }, { id: data.bidId }] })
    const supplierId = user.supplierId
    if (supplierId && res1.length > 0 && bid) {
      const bidSupplier = await this.bidSupplierRepo.findOne({
        // where: { bidItemId: bid.bidId, supplierId, isDeleted: false },
        where: { offerId: bid.offerId, supplierId, isDeleted: false },
        select: { id: true, status: true },
      })
      if (!bidSupplier) return res1

      const getDataCell = (row: any, col: any, lstValue: any[] = []) => {
        row[col.id] = ''
        if (col.colType === enumData.ColType.MPO.code) {
          if (row.__bidPriceColValue__?.length > 0) {
            const cell = row.__bidPriceColValue__.find((c: any) => c.offerPriceColId === col.id)
            if (cell) row[col.id] = cell.value
          }
        } else {
          const cell = lstValue.find((c) => c.offerPriceColId === col.id && c.offerPriceId === row.id)
          if (cell) row[col.id] = cell.value
        }
      }

      // Show các data đã nộp
      const OfferSupplierPriceColValue = await bidSupplier.offerSupplierPriceColValue
      const dicValue: any = {}
      {
        const lstPriceValue = await bidSupplier.offerSupplierPriceValue
        lstPriceValue.forEach((c) => (dicValue[c.offerPriceId] = c.value))
      }

      for (const data1 of res1) {
        data1.value = dicValue[data1.id] || ''
        for (const col of res2) {
          getDataCell(data1, col, OfferSupplierPriceColValue)
        }
        for (const data2 of data1.__childs__) {
          data2.value = dicValue[data2.id] || ''
          for (const col of res2) {
            getDataCell(data2, col, OfferSupplierPriceColValue)
          }
          for (const data3 of data2.__childs__) {
            data3.value = dicValue[data3.id] || ''
            for (const col of res2) {
              getDataCell(data3, col, OfferSupplierPriceColValue)
            }
          }
        }
      }
    }

    const res3 = await this.repo.findOne({ where: { id: data.bidId }, select: { id: true, fomular: true } })
    return [res1, res2, res3]
  }

  async loadDataOfferPriceView(user: UserDto, data: { bidId: string; supplierId?: string }) {
    const service = await this.bidPrItemRepository.findOne({ where: { offerId: data.bidId, isExGr: true } })
    if (service) data.bidId = service.id
    const res1: any[] = await this.bidPriceRepo.find({
      // where: { bidItemId: data.bidId },
      where: { offerServiceId: data.bidId },
      relations: {
        offerPriceListDetails: true,
        offerPriceColValue: true,
        childs: { offerPriceListDetails: true, offerPriceColValue: true, childs: { offerPriceListDetails: true, offerPriceColValue: true } },
      },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } } },
    })
    const res2 = await this.bidPriceColRepo.getBidPriceColAll(user, data.bidId)

    const bideItemRepo = this.repo.manager.getRepository(OfferServiceEntity)
    const bid = await bideItemRepo.findOne({ where: { id: data.bidId } })
    if (res1.length > 0 && bid) {
      const bidSupplier = await this.bidSupplierRepo.findOne({
        // where: { bidItemId: bid.bidId, supplierId, isDeleted: false },
        where: { supplierId: data.supplierId, isDeleted: false },
        select: { id: true, status: true },
      })
      if (!bidSupplier) return res1

      const getDataCell = (row: any, col: any, lstValue: any[] = []) => {
        row[col.id] = ''
        if (col.colType === enumData.ColType.MPO.code) {
          if (row.__bidPriceColValue__?.length > 0) {
            const cell = row.__bidPriceColValue__.find((c: any) => c.offerPriceColId === col.id)
            if (cell) row[col.id] = cell.value
          }
        } else {
          const cell = lstValue.find((c) => c.offerPriceColId === col.id && c.offerPriceId === row.id)
          if (cell) row[col.id] = cell.value
        }
      }

      // Show các data đã nộp
      const OfferSupplierPriceColValue = await bidSupplier.offerSupplierPriceColValue
      const dicValue: any = {}
      {
        const lstPriceValue = await bidSupplier.offerSupplierPriceValue
        lstPriceValue.forEach((c) => (dicValue[c.offerPriceId] = c.value))
      }

      for (const data1 of res1) {
        data1.value = dicValue[data1.id] || ''
        for (const col of res2) {
          getDataCell(data1, col, OfferSupplierPriceColValue)
        }
        for (const data2 of data1.__childs__) {
          data2.value = dicValue[data2.id] || ''
          for (const col of res2) {
            getDataCell(data2, col, OfferSupplierPriceColValue)
          }
          for (const data3 of data2.__childs__) {
            data3.value = dicValue[data3.id] || ''
            for (const col of res2) {
              getDataCell(data3, col, OfferSupplierPriceColValue)
            }
          }
        }
      }
    }

    const res3 = await this.repo.findOne({ where: { id: data.bidId }, select: { id: true, fomular: true } })
    return [res1, res2, res3]
  }

  async loadDataOfferCustomPrice(user: UserDto, data: { bidId: string; supplierId?: string }) {
    const bidSupplierCustomPriceValueRepo = this.repo.manager.getRepository(OfferSupplierCustomPriceValueEntity)

    const service = await this.bidPrItemRepository.findOne({ where: { offerId: data.bidId, isExGr: true } })
    if (service) data.bidId = service.id
    let res: any[] = await this.bidCustomPriceRepo.find({
      // where: { bidItemId: data.bidId },
      where: [{ offerServiceId: data.bidId }, { offerId: data.bidId }],
      order: { sort: 'ASC', createdAt: 'ASC' },
    })

    const bideItemRepo = this.repo.manager.getRepository(OfferServiceEntity)
    const bid = await bideItemRepo.findOne({ where: [{ id: data.bidId }, { offerId: data.bidId }] })

    const supplierId = user.supplierId
    if (supplierId && bid) {
      const bidSupplier = await this.bidSupplierRepo.findOne({
        where: { offerId: bid.offerId, supplierId, isDeleted: false, parentId: Not(IsNull()) },
        // where: { bidId: bid.bidId, supplierId, isDeleted: false },

        select: { id: true, status: true },
      })
      if (!bidSupplier) return res

      // nếu đã nộp thầu => show các data đã nộp

      res = await bidSupplierCustomPriceValueRepo.find({
        where: { offerSupplierId: bidSupplier.id, isDeleted: false },
        order: { sort: 'ASC', createdAt: 'ASC' },
      })
    }

    return res
  }

  async loadDataOfferCustomPriceView(user: UserDto, data: { bidId: string; supplierId?: string }) {
    const bidSupplierCustomPriceValueRepo = this.repo.manager.getRepository(OfferSupplierCustomPriceValueEntity)
    const service = await this.bidPrItemRepository.findOne({ where: { offerId: data.bidId, isExGr: true } })
    if (service) data.bidId = service.id
    let res: any[] = await this.bidCustomPriceRepo.find({
      // where: { bidItemId: data.bidId },
      where: { offerServiceId: data.bidId },
      order: { sort: 'ASC', createdAt: 'ASC' },
    })
    const bideItemRepo = this.repo.manager.getRepository(OfferServiceEntity)
    const bid = await bideItemRepo.findOne({ where: { id: data.bidId } })
    if (bid) {
      const bidSupplier = await this.bidSupplierRepo.findOne({
        where: { id: data.supplierId, isDeleted: false },
        // where: { bidId: bid.bidId, supplierId, isDeleted: false },
        select: { id: true, status: true },
      })
      if (!bidSupplier) return res
      // nếu đã nộp thầu => show các data đã nộp
      res = await bidSupplierCustomPriceValueRepo.find({
        where: { offerSupplierId: bidSupplier.id, isDeleted: false },
        order: { sort: 'ASC', createdAt: 'ASC' },
      })
    }

    return res
  }
  async createOfferSupplier(
    user: UserDto,
    data: {
      fileAttach: string
      fileTech: string
      filePrice: string
      linkDrive: string
      note: string
      bidId: string
      tradeInfo: SupplierCreateTradeItemDto[]
      priceInfo: SupplierCreatePriceItemDto[]
      customPriceInfo: SupplierCreateCustomPriceItemDto[]
      priceShipmentInfo: any[]
    },
  ) {
    // if (!user.supplierId) throw new UnauthorizedException('Phiên đăng nhập hết hạn, vui lòng đăng nhập và thử lại!')

    await this.OfferSupplierFromAdmin(user, { ...data, supplierId: user.supplierId })
  }

  async OfferSupplierFromAdmin(
    user: UserDto,
    data: {
      fileAttach: string
      fileTech: string
      filePrice: string
      linkDrive: string
      note: string
      bidId: string
      supplierId: string
      tradeInfo: SupplierCreateTradeItemDto[]
      priceInfo: SupplierCreatePriceItemDto[]
      customPriceInfo: SupplierCreateCustomPriceItemDto[]
      priceShipmentInfo: any[]
    },
  ) {
    const bidItem = await this.bidPrItemRepository.findOne({ where: { offerId: data.bidId, isDeleted: false } })
    if (!bidItem) throw new Error('Item không còn tồn tại!')

    const bid = await bidItem.offer

    const today = new Date()
    // if (today > bid.effectiveDate) throw new Error('Hết hạn nộp hồ sơ.')

    await this.repo.manager.transaction(async (manager) => {
      const bidSupplierRepo = manager.getRepository(OfferSupplierEntity)
      // const bid = await bidItem.bid
      let bidSupplier = await bidSupplierRepo.findOne({
        where: { offerId: bidItem.offerId, supplierId: data.supplierId, isDeleted: false },
      })
      if (!bidSupplier) throw new Error(ERROR_NOT_FOUND_DATA)

      await bidSupplierRepo.update(bidSupplier.id, {
        fileAttach: data.fileAttach,
        fileTech: data.fileTech,
        filePrice: data.filePrice,
        linkDrive: data.linkDrive,
        note: data.note,
        status: enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
        statusTech: enumData.BidSupplierTechStatus.DangDanhGia.code,
        statusTrade: enumData.BidSupplierTradeStatus.DangDanhGia.code,
        statusPrice: enumData.BidSupplierPriceStatus.DangDanhGia.code,
        updatedBy: user?.id,
      })
      /* tạo ra 1 bid sup con */
      const bidSupChild = { ...bidSupplier }
      delete bidSupChild.id
      bidSupChild.createdAt = new Date()
      bidSupChild.status = enumData.OfferSupplierStatus.NEW.code
      bidSupChild.parentId = bidSupplier.id
      bidSupplier = await bidSupplierRepo.save(bidSupChild)

      //#region Save Tech
      const bidSupplierTechValueRepo = manager.getRepository(OfferSupplierTechValueEntity)
      // Xóa hồ sơ nộp thầu cũ
      await bidSupplierTechValueRepo.delete({ offerSupplierId: bidSupplier.id })
      //#endregion

      //#region Save Trade
      const bidSupplierTradeValueRepo = manager.getRepository(OfferSupplierTradeValueEntity)
      // Xóa hồ sơ nộp thầu cũ
      await bidSupplierTradeValueRepo.delete({ offerSupplierId: bidSupplier.id })

      if (data.tradeInfo)
        for (let index = 0; index < data.tradeInfo.length; index++) {
          const item = data.tradeInfo[index]
          if (item.value || item.__childs__?.length > 0) {
            const bidTradeValue = new OfferSupplierTradeValueEntity()
            bidTradeValue.companyId = user?.companyId
            bidTradeValue.createdBy = user?.id
            bidTradeValue.offerSupplierId = bidSupplier.id
            bidTradeValue.offerTradeId = item.bidTradeId
            bidTradeValue.value = item.value
            await bidSupplierTradeValueRepo.save(bidTradeValue)

            const lengthC = item.__childs__.length
            for (let i = 0; i < lengthC; i++) {
              const itemC = item.__childs__[i]
              if (itemC.value) {
                let bidTradeValueC = new OfferSupplierTradeValueEntity()
                bidTradeValueC.companyId = user?.companyId
                bidTradeValueC.createdBy = user?.id
                bidTradeValueC.offerSupplierId = bidSupplier.id
                bidTradeValueC.offerTradeId = itemC.bidTradeId
                bidTradeValueC.value = itemC.value
                await bidSupplierTradeValueRepo.save(bidTradeValueC)
              }
            }
          }
        }
      //#endregion

      if (bid.offerTypeCode !== enumData.BiddingType.SHIPPING.code) {
        //#region Save Price

        const bidSupplierPriceValueRepo = manager.getRepository(OfferSupplierPriceValueEntity)
        const bidSupplierPriceColValueRepo = manager.getRepository(OfferSupplierPriceColValueEntity)
        const bidSupplierPriceRepo = new OfferSupplierPriceRepository(OfferSupplierPriceEntity, manager)
        const lstBidPriceCol = (await bidItem.offerPriceCol).filter((c) => !c.isDeleted)
        // Xóa lần nộp giá cuối
        await bidSupplierPriceRepo.delete({ offerSupplierId: bidSupplier.id })
        // Xóa hồ sơ nộp thầu cũ
        await bidSupplierPriceValueRepo.delete({ offerSupplierId: bidSupplier.id })
        // Xóa hồ sơ nộp thầu cũ
        await bidSupplierPriceColValueRepo.delete({ offerSupplierId: bidSupplier.id })

        // lv1
        for (let index = 0; index < data.priceInfo.length; index++) {
          const item = data.priceInfo[index] as any
          item.submitDate = today
          item.submitType = 0
          item.level = 1

          for (const col of lstBidPriceCol) {
            if (col.type === enumData.DataType.Number.code && col.fomular?.length > 0) {
              const value = await coreHelper.calFomular(col.fomular, lstBidPriceCol, item)
              if (value != null) {
                const bidSupplierPriceColValue = new OfferSupplierPriceColValueEntity()
                bidSupplierPriceColValue.companyId = user?.companyId
                bidSupplierPriceColValue.createdBy = user?.id
                bidSupplierPriceColValue.value = value
                bidSupplierPriceColValue.offerPriceId = item.id
                bidSupplierPriceColValue.offerSupplierId = bidSupplier.id
                bidSupplierPriceColValue.offerPriceColId = col.id
                await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                item[col.id] = value
              }
            } else {
              if (item[col.id]) {
                const bidSupplierPriceColValue = new OfferSupplierPriceColValueEntity()
                bidSupplierPriceColValue.companyId = user?.companyId
                bidSupplierPriceColValue.createdBy = user?.id
                bidSupplierPriceColValue.value = item[col.id]
                bidSupplierPriceColValue.offerPriceId = item.id
                bidSupplierPriceColValue.offerSupplierId = bidSupplier.id
                bidSupplierPriceColValue.offerPriceColId = col.id
                await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
              }
            }
          }

          if (bidItem.fomular && bidItem.fomular.length > 0) {
            item.value = await coreHelper.calFomular(bidItem.fomular, lstBidPriceCol, item)
          }

          if (item.value) {
            const bidPriceValue = new OfferSupplierPriceValueEntity()
            bidPriceValue.companyId = user?.companyId
            bidPriceValue.createdBy = user?.id
            bidPriceValue.offerSupplierId = bidSupplier.id
            bidPriceValue.value = item.value
            bidPriceValue.offerPriceId = item.id
            bidPriceValue.name = item.name
            bidPriceValue.unit = item.unit
            bidPriceValue.currency = item.currency
            bidPriceValue.number = item.number
            await bidSupplierPriceValueRepo.save(bidPriceValue)

            await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplier, item)
          }

          // lv2
          if (item.__childs__?.length > 0) {
            var priceInfoLv2 = item.__childs__
            for (let index2 = 0; index2 < priceInfoLv2.length; index2++) {
              const itemLv2 = priceInfoLv2[index2] as any
              itemLv2.submitDate = today
              itemLv2.submitType = 0
              itemLv2.level = 2

              for (const col of lstBidPriceCol) {
                if (col.type === enumData.DataType.Number.code && col.fomular?.length > 0) {
                  const value = await coreHelper.calFomular(col.fomular, lstBidPriceCol, itemLv2)
                  if (value != null) {
                    const bidSupplierPriceColValue = new OfferSupplierPriceColValueEntity()
                    bidSupplierPriceColValue.companyId = user?.companyId
                    bidSupplierPriceColValue.createdBy = user?.id
                    bidSupplierPriceColValue.value = value
                    bidSupplierPriceColValue.offerPriceId = itemLv2.id
                    bidSupplierPriceColValue.offerSupplierId = bidSupplier.id
                    bidSupplierPriceColValue.offerPriceColId = col.id
                    await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                    itemLv2[col.id] = value
                  }
                } else {
                  if (itemLv2[col.id]) {
                    const bidSupplierPriceColValue = new OfferSupplierPriceColValueEntity()
                    bidSupplierPriceColValue.companyId = user?.companyId
                    bidSupplierPriceColValue.createdBy = user?.id
                    bidSupplierPriceColValue.value = itemLv2[col.id]
                    bidSupplierPriceColValue.offerPriceId = itemLv2.id
                    bidSupplierPriceColValue.offerSupplierId = bidSupplier.id
                    bidSupplierPriceColValue.offerPriceColId = col.id
                    await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                  }
                }
              }

              if (bidItem.fomular && bidItem.fomular.length > 0) {
                itemLv2.value = await coreHelper.calFomular(bidItem.fomular, lstBidPriceCol, itemLv2)
              }

              if (itemLv2.value) {
                const bidPriceValue = new OfferSupplierPriceValueEntity()
                bidPriceValue.companyId = user?.companyId
                bidPriceValue.createdBy = user?.id
                bidPriceValue.offerSupplierId = bidSupplier.id
                bidPriceValue.value = itemLv2.value
                bidPriceValue.offerPriceId = itemLv2.id
                bidPriceValue.name = itemLv2.name
                bidPriceValue.unit = itemLv2.unit
                bidPriceValue.currency = itemLv2.currency
                bidPriceValue.number = itemLv2.number
                await bidSupplierPriceValueRepo.save(bidPriceValue)

                await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplier, itemLv2)
              }

              // lv3
              if (itemLv2.__childs__?.length > 0) {
                var priceInfoLv3 = itemLv2.__childs__
                for (let index3 = 0; index3 < priceInfoLv3.length; index3++) {
                  const itemLv3 = priceInfoLv3[index3] as any
                  itemLv3.submitDate = today
                  itemLv3.submitType = 0
                  itemLv3.level = 3

                  for (const col of lstBidPriceCol) {
                    if (col.type === enumData.DataType.Number.code && col.fomular?.length > 0) {
                      const value = await coreHelper.calFomular(col.fomular, lstBidPriceCol, itemLv3)
                      if (value != null) {
                        const bidSupplierPriceColValue = new OfferSupplierPriceColValueEntity()
                        bidSupplierPriceColValue.companyId = user?.companyId
                        bidSupplierPriceColValue.createdBy = user?.id
                        bidSupplierPriceColValue.value = value
                        bidSupplierPriceColValue.offerPriceId = itemLv3.id
                        bidSupplierPriceColValue.offerSupplierId = bidSupplier.id
                        bidSupplierPriceColValue.offerPriceColId = col.id
                        await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                        itemLv3[col.id] = value
                      }
                    } else {
                      if (itemLv3[col.id]) {
                        const bidSupplierPriceColValue = new OfferSupplierPriceColValueEntity()
                        bidSupplierPriceColValue.companyId = user?.companyId
                        bidSupplierPriceColValue.createdBy = user?.id
                        bidSupplierPriceColValue.value = itemLv3[col.id]
                        bidSupplierPriceColValue.offerPriceId = itemLv3.id
                        bidSupplierPriceColValue.offerSupplierId = bidSupplier.id
                        bidSupplierPriceColValue.offerPriceColId = col.id
                        await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                      }
                    }
                  }

                  if (bidItem.fomular && bidItem.fomular.length > 0) {
                    itemLv3.value = await coreHelper.calFomular(bidItem.fomular, lstBidPriceCol, itemLv3)
                  }

                  if (itemLv3.value) {
                    const bidPriceValue = new OfferSupplierPriceValueEntity()
                    bidPriceValue.companyId = user?.companyId
                    bidPriceValue.createdBy = user?.id
                    bidPriceValue.offerSupplierId = bidSupplier.id
                    bidPriceValue.value = itemLv3.value
                    bidPriceValue.offerPriceId = itemLv3.id
                    bidPriceValue.name = itemLv3.name
                    bidPriceValue.unit = itemLv3.unit
                    bidPriceValue.currency = itemLv3.currency
                    bidPriceValue.number = itemLv3.number
                    await bidSupplierPriceValueRepo.save(bidPriceValue)

                    await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplier, itemLv3)
                  }
                }
              }
            }
          }
        }
        //#endregion

        //#region Save CustomPrice

        const bidSupplierCustomPriceValueRepo = manager.getRepository(OfferSupplierCustomPriceValueEntity)
        // Xóa hồ sơ nộp thầu cũ
        await bidSupplierCustomPriceValueRepo.delete({ offerSupplierId: bidSupplier.id })

        for (let index = 0; index < data.customPriceInfo.length; index++) {
          const item = data.customPriceInfo[index]
          if (item.value) {
            let bidCustomPriceValue = new OfferSupplierCustomPriceValueEntity()
            bidCustomPriceValue.companyId = user?.companyId
            bidCustomPriceValue.createdBy = user?.id
            bidCustomPriceValue.offerSupplierId = bidSupplier.id
            bidCustomPriceValue.value = item.value
            bidCustomPriceValue.name = item.name
            bidCustomPriceValue.unit = item.unit
            bidCustomPriceValue.currency = item.currency
            bidCustomPriceValue.number = item.number
            bidCustomPriceValue.sort = item.sort
            await bidSupplierCustomPriceValueRepo.save(bidCustomPriceValue)
          }
        }
        //#endregion
      } else {
        //#region Save CustomPrice

        const bidSupplierPriceShipmentRepoRepo = manager.getRepository(OfferSupplierShipmentValueEntity)
        // Xóa hồ sơ nộp thầu cũ
        await bidSupplierPriceShipmentRepoRepo.delete({ offerSupplierId: bidSupplier.id })

        for (let index = 0; index < data.priceShipmentInfo.length; index++) {
          const item = data.priceShipmentInfo[index]
          if (item.value) {
            let offerShipmentPriceValue = new OfferSupplierShipmentValueEntity()
            offerShipmentPriceValue.companyId = user.companyId
            offerShipmentPriceValue.createdBy = user.id
            offerShipmentPriceValue.offerSupplierId = bidSupplier.id
            offerShipmentPriceValue.value = item.value
            // offerShipmentPriceValue.

            await bidSupplierPriceShipmentRepoRepo.save(offerShipmentPriceValue)
          }
        }
        //#endregion
      }
    })

    // await this.emailService.GuiMpoNccNopHoSoThau(data.bidId, data.supplierId)

    return { message: UPDATE_SUCCESS }
  }
}
