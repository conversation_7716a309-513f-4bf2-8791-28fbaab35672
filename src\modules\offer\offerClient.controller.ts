import { Controller, UseGuards, Post, Body, Get, Param, Put } from '@nestjs/common'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { ApeAuthGuard } from '../common/guards'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { OfferService } from './offer.service'
import { OfferTradeService } from './offerTrade.service'
import {
  BidCustomPriceCreateDto,
  BidCustomPriceUpdateDto,
  BidPriceColCreateDto,
  BidPriceColUpdateDto,
  BidPriceCreateDto,
  BidPriceUpdateDto,
  BidTradeCreateDto,
  BidTradeUpdateDto,
} from './dto2'
import { OfferPriceService } from './offerPrice.service'

@ApiTags('offer_client')
@Controller('offer_client')
export class OfferClientController {
  constructor(
    private readonly service: OfferService,
    private readonly offerTradeService: OfferTradeService,
    private readonly offerPriceService: OfferPriceService,
  ) {}

  @ApiOperation({ summary: 'Danh sách kế hoạch' })
  @Post('pagination_client_no_token')
  async paginationClientNoToken(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.paginationClientNoToken(user, data)
  }

  @UseGuards(ApeAuthGuard)
  @ApiOperation({ summary: 'Danh sách kế hoạch' })
  @Post('detail_client')
  async detailClient(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.detailClient(user, data)
  }

  // @UseGuards(ApeAuthGuard)
}
