import { OneToMany } from 'typeorm'
import { Entity, Column, Join<PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { AuctionHistoryEntity } from './auctionHistory.entity'
import { AuctionSupplierEntity } from './auctionSupplier.entity'
import { BaseEntity } from './base.entity'
import { BidEntity } from './bid.entity'
import { enumData } from '../constants'

/** Đấu giá */
@Entity({ name: 'auction' })
export class AuctionEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 500, nullable: false })
  title: string

  /** Giá khởi điểm */
  @Column({ type: 'bigint', nullable: false })
  price: number

  /** Gi<PERSON> thấp nhất */
  @Column({ type: 'bigint', nullable: true })
  minPrice?: number

  /** Thời điểm bắt đầu */
  @Column({ nullable: false })
  dateStart: Date

  /** Thời điểm kết thúc */
  @Column({ nullable: false })
  dateEnd: Date

  /** Trạng thái */
  @Column({ type: 'varchar', length: 10, nullable: false, default: enumData.AuctionStatus.NEW.code })
  status: string

  /** Ghi chú */
  @Column({ type: 'text', nullable: true })
  description?: string

  /** Số lượng NCC tham gia */
  @Column({ nullable: false, default: 0 })
  numSupplier: number

  /** Gói thầu */
  @Column({ type: 'varchar', length: 36, nullable: true })
  bidId?: string
  @ManyToOne(() => BidEntity, (p) => p.auctions)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid?: Promise<BidEntity>

  /** Các NCC tham gia */
  @OneToMany(() => AuctionSupplierEntity, (p) => p.auction)
  auctionSuppliers: Promise<AuctionSupplierEntity[]>

  /** Lịch sử */
  @OneToMany(() => AuctionHistoryEntity, (p) => p.auction)
  auctionHistorys: Promise<AuctionHistoryEntity[]>
}
