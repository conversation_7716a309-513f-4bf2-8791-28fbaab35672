import { Controller, UseGuards, Post, Body, Get, Param, Req } from '@nestjs/common'
import { HomePageService } from './homePage.service'
import { UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { enumProject } from '../../constants'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('HomePage')
@Controller('homePages')
export class HomePageController {
  constructor(private readonly service: HomePageService) { }

  @ApiOperation({ summary: 'Lấy danh sách các banner cấu hình động trang client' })
  @Post('get_banner')
  public async getBanner(@Req() req: Request, @Body() data: { position: string }) {
    return await this.service.getBannerClient(req, data)
  }

  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách các link cấu hình động trang client' })
  @Post('get_link')
  public async getLink(@Req() req: Request) {
    return await this.service.getLinkClient(req)
  }

  @ApiOperation({ summary: 'Lấy danh sách các text cấu hình động trang client' })
  @Post('get_setting_string')
  public async getSettingString(@Req() req: Request) {
    return await this.service.getSettingStringClient(req)
  }

  @ApiOperation({ summary: 'Lấy danh sách Item' })
  @Post('get_services')
  public async getServices(@Req() req: Request) {
    return await this.service.getServicesClient(req)
  }

  @ApiOperation({ summary: 'Lấy danh sách thông báo NCC' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_notifys/:num')
  public async getNotifys(@CurrentUser() user: UserDto, @Param('num') num: number) {
    return await this.service.getNotifys(user, num)
  }

  @ApiOperation({ summary: 'Xem chi tiết thông báo NCC' })
  @Roles(enumProject.Features.CLIENT_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_notify/:id')
  public async getNotify(@CurrentUser() user: UserDto, @Param('id') id: string) {
    return await this.service.getNotify(user, id)
  }
}
