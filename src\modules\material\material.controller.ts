import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { MaterialCreateDto } from './dto/materialCreate.dto'
import { MaterialUpdateDto } from './dto/materialUpdate.dto'
import { CurrentUser, Roles } from '../common/decorators'
import { PaginationDto, UserDto } from '../../dto'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { enumProject } from '../../constants'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { MaterialService } from './material.service'

@ApiBearerAuth()
@ApiTags('Material')
@Controller('material')
export class MaterialController {
  constructor(private readonly service: MaterialService) {}

  @ApiOperation({ summary: 'Danh sách đối tượng' })
  @UseGuards(ApeAuthGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: {}) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Danh sách đối tượng phân trang' })
  @Roles(enumProject.Features.SETTING_017.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo đối tượng' })
  @Roles(enumProject.Features.SETTING_017.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: MaterialCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật đối tượng' })
  @Roles(enumProject.Features.SETTING_017.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: MaterialUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động đối tượng' })
  @Roles(enumProject.Features.SETTING_017.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateIsDelete(user, data.id)
  }
}
