import { Controller, UseGuards, Post, Body, Get, Param } from '@nestjs/common'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { BidDealService } from './bidDeal.service'
import { CurrentUser, Roles } from '../common/decorators'
import { UserDto } from '../../dto'
import { BidDealCreateDto } from './dto'
import { enumProject } from '../../constants'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

/** Đàm phán giá */
@ApiBearerAuth()
@ApiTags('Bid')
@Controller('bid_deal')
export class BidDealController {
  constructor(private readonly service: BidDealService) {}

  @ApiOperation({ summary: '<PERSON><PERSON>y danh sách hạng mục chào giá' })
  @Roles(enumProject.Features.BID_009.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Get('get_price/:bidId')
  public async getPrice(@CurrentUser() user: UserDto, @Param('bidId') bidId: string) {
    return await this.service.getPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Lấy ds NCC để mời tham gia đàm phán giá' })
  @Roles(enumProject.Features.BID_009.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('load_supplier_data')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: { bidId: string; statusFile: string[]; name?: string }) {
    return await this.service.loadSupplierData(user, data)
  }

  @ApiOperation({ summary: 'Tạo đàm phán giá Item' })
  @Roles(enumProject.Features.BID_009.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('save_bid_deal')
  public async saveBidDeal(@CurrentUser() user: UserDto, @Body() data: BidDealCreateDto) {
    return await this.service.saveBidDeal(user, data)
  }

  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('call_ai')
  public async getPriceByGemini(@Body() data: any) {
    return await this.service.getPriceByGemini(data)
  }

  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('get_price_ai')
  public async getPriceAi(@Body() data: any) {
    return await this.service.getPriceAi(data.dataItem)
  }
}
