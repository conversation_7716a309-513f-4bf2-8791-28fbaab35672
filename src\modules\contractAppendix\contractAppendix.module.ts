import { Module } from '@nestjs/common'
import { ContractAppendixRepository } from '../../repositories'
import { TypeOrmExModule } from '../../typeorm'
import { ContractAppendixController } from './contractAppendix.controller'
import { ContractAppendixService } from './contractAppendix.service'
@Module({
  imports: [TypeOrmExModule.forCustomRepository([ContractAppendixRepository])],
  controllers: [ContractAppendixController],
  providers: [ContractAppendixService],
})
export class ContractAppendixModule {}
