export {}

declare global {
  interface Array<T> {
    /**
     * <PERSON><PERSON><PERSON> kết hợp với trả về phần tử mới.
     * Truyền vào call back function với (value, index, array) => [value filter,value map]
     *
     * Ví dụ
     * arrAge = [1, 2, 3, 4, 5].filterAndMap((item) => [item > 3, item*2])
     * ( Kết quả : arAge = [8, 10] )
     */
    filterAndMap<U>(callback: (value: T, index: number, array: T[]) => [any, U]): U[]

    /**
     * Trả về phần tử mới kết hợp với lọc trùng.
     * Truyền vào call back function với (value, index, array) => value mới
     *
     * Ví dụ
     * 1. arrAge = [1, 2, 3, 1, 2, 3].mapAndDistinct((item) => item)
     * ( Kết quả : arAge = [1, 2, 3] )
     *
     * 2. arrObjAge = [{age:1}, {age:1}].mapAndDistinct((item) => item.age)
     * ( Kết quả : arrObjAge = [1] )
     */
    mapAndDistinct<U>(callback: (value: T, index: number, array: T[]) => U): U[]

    /**
     * chuyển array sang Map<key,value>
     * Truyền vào call back function với (value, index, array) => key
     *
     * Ví dụ
     * 1. mapAge = [{age:1}, {age:2}].convertToMap((item) => item.age)
     * ( Kết quả : mapAge = Map(1-> {age:1}, 2-> {age:2}) )
     *
     */
    convertToMap<U>(callback: (value: T, index: number, array: T[]) => U): Map<U, T>
  }
}

Array.prototype.filterAndMap = function <T, U>(callback: (value: T, index: number, array: T[]) => [any, U]): U[] {
  let result: U[] = []
  for (let i = 0; i < this.length; i++) {
    const resCallback = callback(this[i], i, this)
    if (resCallback?.[0]) {
      result.push(resCallback?.[1])
    }
  }
  return result
}

Array.prototype.mapAndDistinct = function <T, U>(callback: (value: T, index: number, array: T[]) => U): U[] {
  const setValue: Set<any> = new Set()
  for (let i = 0; i < this.length; i++) {
    setValue.add(callback(this[i], i, this))
  }
  return [...setValue]
}

Array.prototype.convertToMap = function <T, U>(callback: (value: T, index: number, array: T[]) => U): Map<U, T> {
  const map = new Map()
  for (let i = 0; i < this.length; i++) {
    map.set(callback(this[i], i, this), this[i])
  }
  return map
}
