import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { enumProject } from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser, Roles } from '../common/decorators'
import { ApeAuthGuard, RoleGuard } from '../common/guards'
import { POService } from './po.service'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { POCreateDto, POCreateExcelDto, POUpdateDeliveryDateDto, POUpdateDto, POUpdateStatusDto } from './dto'

/** Quản lý PO */
@ApiBearerAuth()
@ApiTags('PO')
@Controller('po')
export class POController {
  constructor(private readonly service: POService) {}

  @ApiOperation({ summary: 'Danh sách PO' })
  @Roles(enumProject.Features.PAYMENT_001.code, enumProject.Features.PAYMENT_002.code)
  // @Roles(enumProject.Features.ASN_001.code, enumProject.Features.PAYMENT_001.code, enumProject.Features.PAYMENT_002.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: { contractId?: string; status?: any; products?: any }) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Tạo PO' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: POCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật PO' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: POUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật ngày gia hàng PO' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_delivery_date')
  public async updateDeliveryDate(@CurrentUser() user: UserDto, @Body() data: POUpdateDeliveryDateDto) {
    return await this.service.updateDeliveryDate(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái đơn PO' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_status')
  public async updateStatus(@CurrentUser() user: UserDto, @Body() data: POUpdateStatusDto) {
    return await this.service.updateStatus(user, data)
  }

  @ApiOperation({ summary: 'Chi tiết PO' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetail2(user, data)
  }

  @ApiOperation({ summary: 'Danh sách PO phân trang' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Import excel PO' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('create_data_excel')
  public async createDataExcel(@CurrentUser() user: UserDto, @Body() data: POCreateExcelDto) {
    return await this.service.createDataExcel(user, data)
  }

  @ApiOperation({ summary: 'Xác nhận PO' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_status_confirm')
  public async updateStatusConfirm(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateStatusConfirm(user, data)
  }

  @ApiOperation({ summary: 'Xác nhận giao hàng PO' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_status_delivery')
  public async updateStatusDelivery(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateStatusDelivery(user, data)
  }

  @ApiOperation({ summary: 'Duyệt PO' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_status_approved')
  public async updateStatusApproved(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateStatusApproved(user, data)
  }

  @ApiOperation({ summary: 'Hoàn thành PO' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_status_complete')
  public async updateStatusComplete(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateStatusComplete(user, data)
  }

  @ApiOperation({ summary: 'Từ chối nhận hàng PO' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_status_refuse')
  public async updateStatusRefuse(@CurrentUser() user: UserDto, @Body() data: { id: string; reason?: string }) {
    return await this.service.updateStatusRefuse(user, data)
  }

  @ApiOperation({ summary: 'Hủy PO' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('update_status_cancel')
  public async updateStatusCancel(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateStatusCancel(user, data)
  }

  @ApiOperation({ summary: 'Load HD của Po' })
  @Post('find_contract_po')
  @UseGuards(ApeAuthGuard, RoleGuard)
  public async findContractPO(@CurrentUser() user: UserDto, @Body() data: { poId: string }) {
    return await this.service.findContractPO(user, data)
  }

  @ApiOperation({ summary: 'Lấy List item của po' })
  @Post('load_po_product')
  @UseGuards(ApeAuthGuard, RoleGuard)
  public async loadPoProduct(@CurrentUser() user: UserDto, @Body() data: { poId: string }) {
    return await this.service.loadPoProduct(user, data)
  }

  @ApiOperation({ summary: 'Lấy List po của contract đã chọn ' })
  @Post('load_list_po_contract')
  @UseGuards(ApeAuthGuard, RoleGuard)
  public async loadListPoContract(@CurrentUser() user: UserDto, @Body() data: { contractId: string }) {
    return await this.service.loadListPoContract(user, data)
  }

  @ApiOperation({ summary: 'Danh sách PO theo supplier' })
  @Post('load_po_by_supplier')
  @UseGuards(ApeAuthGuard, RoleGuard)
  public async loadPoBySupplier(@CurrentUser() user: UserDto, @Body() data: { status: any }) {
    return await this.service.loadPoBySupplier(user, data)
  }
}

@ApiBearerAuth()
@ApiTags('PO')
@Controller('poHistory')
export class PoHistoryController {
  constructor(private readonly service: POService) {}

  @ApiOperation({ summary: 'Lịch sử PO phân trang' })
  @Roles(enumProject.Features.PO_001.code)
  @UseGuards(ApeAuthGuard, RoleGuard)
  @Post('pagination')
  public async paginationHistory(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.paginationHistory(user, data)
  }
}
