import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { AsnRepository, PORepository } from '../../repositories'
import { AsnController } from './asn.controller'
import { AsnService } from './asn.service'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([AsnRepository, PORepository])],
  controllers: [AsnController],
  providers: [AsnService],
  exports: [AsnService],
})
export class AsnModule {}
