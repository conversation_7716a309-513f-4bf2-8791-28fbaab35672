import { MigrationInterface, QueryRunner } from 'typeorm'

export class upLanguage1706699286290 implements MigrationInterface {
  name = 'upLanguage1706699286290'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`language_key\` (\`id\` varchar(36) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`createdBy\` varchar(36) NULL, \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`updatedBy\` varchar(36) NULL, \`isDeleted\` tinyint NOT NULL DEFAULT 0, \`companyId\` varchar(36) NULL, \`key\` varchar(250) NOT NULL, \`value\` varchar(1000) NOT NULL, \`languageType\` varchar(50) NOT NULL, \`path\` varchar(500) NULL, \`description\` text NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE \`language_key\``)
  }
}
