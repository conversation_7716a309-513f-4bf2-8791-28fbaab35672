import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { LanguageRepository } from '../../repositories'
import { LanguageService } from './language.service'
import { LanguageController } from './language.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([LanguageRepository])],
  controllers: [LanguageController],
  providers: [LanguageService],
})
export class LanguageModule {}
