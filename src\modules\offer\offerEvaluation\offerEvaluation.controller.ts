import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { ApeAuthGuard } from '../../common/guards'
import { UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { OfferEvaluationService } from './offerEvaluation.service'

/** Chọn NCC thắng thầu */
@ApiBearerAuth()
@ApiTags('Offer')
@UseGuards(ApeAuthGuard)
@Controller('offer-evaluation')
export class OfferEvaluationController {
  constructor(private readonly service: OfferEvaluationService) {}

  @ApiOperation({ summary: 'Load ds Doanh nghiệp để chọn trúng thầu' })
  @Post('load-supplier-data')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: { bidId: string; isMobile?: boolean }) {
    return await this.service.loadSupplierData(user, data)
  }

  @ApiOperation({ summary: '<PERSON>ọ<PERSON><PERSON> nghi<PERSON> trúng thầu, tr<PERSON><PERSON><PERSON> thầu theo từng Item' })
  @Post('evaluation-offer-supplier')
  public async evaluationBidSupplier(@CurrentUser() user: UserDto, @Body() data: { bidId: string; listItem: any[]; comment: string }) {
    return await this.service.evaluationBidSupplier(user, data)
  }

  @ApiOperation({ summary: 'Phê duyệt Doanh nghiệp thắng thầu' })
  @Post('approve-supplier-win-offer')
  public async approveSupplierWinBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string; comment: string }) {
    return await this.service.approveSupplierWinBid(user, data)
  }

  @ApiOperation({ summary: 'Yêu cầu đánh giá và chọn lại Doanh nghiệp thắng thầu' })
  @Post('reject-supplier-win-offer')
  public async rejectSupplierWinBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string; comment: string; recheck: any[] }) {
    return await this.service.rejectSupplierWinBid(user, data)
  }
}
