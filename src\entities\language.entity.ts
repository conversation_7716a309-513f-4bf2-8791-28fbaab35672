import { Entity, Column, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { LanguageConfigEntity } from './languageConfig.entity'

@Entity('language')
export class LanguageEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'text',
    nullable: true,
  })
  avatarUrl: string

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  @OneToMany(() => LanguageConfigEntity, (p) => p.language)
  configs: Promise<LanguageConfigEntity[]>
}
