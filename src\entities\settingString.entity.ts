import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'
import { BidEntity } from './bid.entity'
import { PaymentEntity } from './payment.entity'

@Entity('setting_string')
export class SettingStringEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @OneToMany(() => BidEntity, (p) => p.bidType)
  masterBidGuarantee: Promise<BidEntity[]>

  @OneToMany(() => PaymentEntity, (p) => p.settingString)
  payments: Promise<PaymentEntity[]>
}
