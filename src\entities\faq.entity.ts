import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { FAQCategoryEntity } from './faqCategory.entity'

@Entity({ name: 'faq' })
export class FAQEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 500,
    nullable: false,
  })
  title: string

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  categoryId: string
  @ManyToOne(() => FAQCategoryEntity, (p) => p.faqs)
  @JoinColumn({ name: 'categoryId', referencedColumnName: 'id' })
  category: Promise<FAQCategoryEntity>
}
