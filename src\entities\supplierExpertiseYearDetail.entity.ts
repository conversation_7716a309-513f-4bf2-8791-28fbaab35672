import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, ManyToOne, JoinColumn } from 'typeorm'
import { SupplierExpertiseDetailEntity } from './supplierExpertiseDetail.entity'

/** <PERSON><PERSON><PERSON> yêu cầu điều chỉnh của tiêu chí theo năm */
@Entity('supplier_expertise_year_detail')
export class SupplierExpertiseYearDetailEntity extends BaseEntity {
  @Column({
    nullable: false,
  })
  value: string

  @Column({
    nullable: false,
  })
  year: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  supplierExpertiseDetailId: string
  @ManyToOne(() => SupplierExpertiseDetailEntity, (p) => p.supplierExpertiseYearDetails)
  @JoinColumn({ name: 'supplierExpertiseDetailId', referencedColumnName: 'id' })
  supplierExpertiseDetail: Promise<SupplierExpertiseDetailEntity>
}
