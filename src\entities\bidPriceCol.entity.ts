import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, Join<PERSON><PERSON>umn, OneToMany } from 'typeorm'
import { BidPriceColValueEntity } from './bidPriceColValue.entity'
import { BidSupplierPriceColValueEntity } from './bidSupplierPriceColValue.entity'
import { BidEntity } from './bid.entity'

@Entity('bid_price_col')
export class BidPriceColEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'text',
    nullable: true,
  })
  fomular: string

  /** <PERSON><PERSON> bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string

  @Column({
    length: 50,
    nullable: false,
  })
  type: string

  @Column({
    length: 50,
    nullable: false,
  })
  colType: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  bidId: string

  @ManyToOne(() => BidEntity, (p) => p.bidPriceCols)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  @OneToMany(() => BidPriceColValueEntity, (p) => p.bidPriceCol)
  bidPriceColValue: Promise<BidPriceColValueEntity[]>

  @OneToMany(() => BidSupplierPriceColValueEntity, (p) => p.bidPriceCol)
  bidSupplierPriceColValue: Promise<BidSupplierPriceColValueEntity[]>
}
