import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { JwtModule, JwtModuleOptions } from '@nestjs/jwt'
import { AuthService } from './auth.service'
import { AuthController } from './auth.controller'
import { SupplierRepository, UserRepository } from '../../repositories'
import { LocalStrategy } from './local.strategy'
import { ApeStrategy } from './ape.strategy'
import { PassportModule } from '@nestjs/passport'
import { TypeOrmExModule } from '../../typeorm'
import { EmailModule } from '../email/email.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([UserRepository, SupplierRepository]),
    PassportModule.register({ session: true }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService): Promise<JwtModuleOptions> => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: configService.get<string>('JWT_EXPIRY') },
      }),
    }),
    EmailModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, LocalStrategy, ApeStrategy],
  exports: [AuthService],
})
export class AuthModule {}
